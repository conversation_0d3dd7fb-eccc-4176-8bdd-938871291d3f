﻿@using TBFront.Helpers
@inject ViewHelper viewHelper

@{
    var page = ViewData["page"];
}

<div class="modal fade" id="increaseRate" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-sm">
        <div class="modal-content">
            <div class="modal-header border-0 pb-2">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body pt-0">
                <span class="icon icon-change-price d-block text-center font-40 color-orange mb-3"></span>
                <p class="font-18 mb-2 text-center lh-1-1">Se ha detectado un cambio de tarifa</p>
                <p class="font-24 mb-0 text-center lh-1-1 color-orange">¡El precio de tu vuelo ha <span class="font-bold">subido</span>!</p>
            </div>
        </div>
    </div>
</div>