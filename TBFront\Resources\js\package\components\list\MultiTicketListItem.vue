<template>
    <div class="col">
        <div class="row row-item cursor-pointer" v-for="(flight, indexFlight) in flights"
            :key="indexFlight + Math.random()"
            :class="{ 'bg-level1': indexFlight % 2 != 0, 'd-none': indexFlight > (itemsToshow - 1) }"
            @click="getUpsellList(flight, group, luggages, indexFlight)"
            :id="`${group?.departure?.code}${indexFlight}`">

            <div class="col-7 col-md-12 px-sm-2 px-md-3">
                <div class="row align-items-center">

                    <div class="col-12 d-md-none lh-1 position-relative" v-show="isMultipleAirport">
                        <div class="d-flex justify-content-between">
                            <span class="iata">
                                {{ flight.departure.airportCode }}
                            </span>
                            <div style="flex-grow: 1;"></div>
                            <span class="iata">
                                {{ flight.arrival.airportCode }}
                            </span>

                        </div>
                    </div>

                    <div class="col-12 col-md-5 col-lg-4">

                        <div class="dashed-line-container">
                            <span class="dlc-time">{{ flight.departure.time }}</span>
                            <div class="dashed-line-space">
                                <span class="dashed-line-iata-depurate d-none d-md-block" v-if="isMultipleAirport">
                                    {{ flight.departure.airportCode }}
                                </span>
                                <div class="dashed-line"></div>
                            </div>
                            <span class="dlc-duration">{{ flight.flightDuration }}</span>
                            <span class="icon icon-plane-right dlc-icon d-none mx-auto"></span>

                            <div class="dashed-line-space">
                                <span class="dashed-line-iata-arrival d-none d-md-block" v-if="isMultipleAirport">
                                    {{ flight.arrival.airportCode }}
                                </span>
                                <div class="dashed-line"></div>
                            </div>


                            <span class="dlc-time position-relative">{{ flight.arrival.time }}
                                <span class="exponent d-md-none" v-if="flight.flightDays > 0">+{{ flight.flightDays
                                    }}</span>
                            </span>
                        </div>
                    </div>
                    <div class="col-12 col-md-4 col-lg-5">
                        <div class="info-line-container">
                            <span class="ilc-days" :class="{ 'invisible': flight.flightDays == 0 }"
                                data-bs-toggle="tooltip" data-bs-placement="top"
                                :data-bs-title="`${__(`multiticket.arrive_on`)} ${$filters.date(flight.arrival.date, 'ddd DD MMM YYYY')}.`"
                                @mouseover="handleMouseOver($event)" :id="`flightDays${idxGroup}${indexFlight}`">
                                <template v-if="flight.flightDays > 1">
                                    {{ __("multiticket.days", [flight.flightDays]) }}
                                </template>
                                <template v-else>
                                    {{ __("multiticket.day", [flight.flightDays]) }}
                                </template>
                            </span>
                            <div style="flex-grow: 1;" class="d-none d-lg-block"></div>
                            <span class="ilc-scales"
                                @click.stop.prevent="getFlightDetails(group?.departure?.name, group?.departure?.image, flight, group, 'starting')">
                                <template v-if="flight.stops == 0">{{__("multiticket.direct")}}</template>
                                <template v-else-if="flight.stops == 1">{{ flight.stops }} {{__("multiticket.stop")}} </template>
                                <template v-else>{{ flight.stops }} {{__("multiticket.stops")}} </template>
                            </span>
                            <div style="flex-grow: 1;" class="d-none d-lg-block"></div>
                            <span class="ilc-bags " v-if="!getIsLoadingLuggages"
                                @click.stop.prevent="getFlightDetails(group?.departure?.name, group?.departure?.image, flight, group, 'starting', true)">
                                <template v-if="luggages[indexFlight]">
                                    <span :id="`-hand${idxGroup}${indexFlight}`" @mouseover="handleMouseOver($event)"
                                        data-bs-toggle="tooltip" data-bs-placement="top"
                                        :title="luggages[indexFlight]['extra'][2]['title']"
                                        :class="luggages[indexFlight]['extra'][2]['class']"></span>

                                    <span :id="`-checked${idxGroup}${indexFlight}`" @mouseover="handleMouseOver($event)"
                                        data-bs-toggle="tooltip" data-bs-placement="top"
                                        :title="luggages[indexFlight]['extra'][1]['title']"
                                        :class="luggages[indexFlight]['extra'][1]['class']"></span>
                                </template>
                                <template v-else>
                                    <span :id="`-hand${idxGroup}${indexFlight}`" @mouseover="handleMouseOver($event)"
                                        data-bs-toggle="tooltip" data-bs-placement="top"
                                        :title="__(`messages.seeLuggage`)"
                                        class="icon-info"></span>
                                </template>
                            </span>
                            <span class="ilc-bags" v-else>
                                <span class="px-2 py-1 me-1 is-loading"></span>
                                <span class="px-2 py-1 is-loading"></span>
                            </span>
                        </div>
                    </div>
                    <div class="col-5 col-md-3 col-lg-3 d-none d-md-block">
                        <span class="list-price-flight"><span :class="{ 'cheap-active': flight.cheap }">
                                <CurrencyDisplay :amount="getAmountPerPerson(flight.fares[0].amount)"
                                    :showCurrencyCode="false" :reduceIsoFont="true" :plusSymbol="!isStarting"
                                    :applyCompression="true" />
                            </span><span class="icon icon-keyboard-right"></span>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-5 ps-0 pe-2 d-md-none">
                <div class="row">
                    <div class="col" :class="{
                            'space-price-multipleairport' : isMultipleAirport && !flight.cheap, 
                            'space-price' : !isMultipleAirport && !flight.cheap,
                            'space-price-multipleairport-cheap' : isMultipleAirport && flight.cheap, 
                            'space-price-cheap' : !isMultipleAirport && flight.cheap,
                            }">
                        <span class="list-price-flight"><span :class="{ 'cheap-active': flight.cheap }">
                                <CurrencyDisplay :amount="getAmountPerPerson(flight.fares[0].amount)"
                                    :showCurrencyCode="false" :reduceIsoFont="true" :plusSymbol="!isStarting"
                                    :applyCompression="true" />
                            </span><span class="icon-chevron-right-price"></span>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="row table-list-footer view-more cursor-pointer" :class="{ 'mt-0': showAll }" v-if="showButton"
            @click="showMoreItems">
            <span v-if="isStarting">
                <template v-if="!showAll">
                    <template v-if="markMoreItemsEconomic">
                        <span class="d-none d-md-inline">
                            <span v-html="__('multiticket.show_more_flights_departure_web_cheap')"></span>
                        </span>
                        <span class="d-md-none">
                            <span v-html="__('multiticket.show_more_flights_web_cheap_mobile')"></span>
                        </span>
                    </template>
                    <template v-else>
                        <span class="d-none d-md-inline">
                            <span v-html="__('multiticket.show_more_flights_departure_web')"></span>
                        </span>
                        <span class="d-md-none">
                            <span v-html="__('multiticket.show_more_flights_web_mobile')"></span>
                        </span>
                    </template>
                </template>
                <template v-else>
                    <span class="d-inline">{{ __("multiticket.hide_less_flights") }}</span>
                </template>
                <span :class="{ 'i-rotate-180': showAll }" class="icon icon-expand"></span>
            </span>
            <span v-if="!isStarting">
                <template v-if="!showAll">
                    <template v-if="markMoreItemsEconomic">
                        <span class="d-none d-md-inline">
                            <span v-html="__('multiticket.show_more_flights_arrival_web_cheap')"></span>
                        </span>
                        <span class="d-md-none">
                            <span v-html="__('multiticket.show_more_flights_web_cheap_mobile')"></span>
                        </span>
                    </template>
                    <template v-else>
                        <span class="d-none d-md-inline">
                            <span v-html="__('multiticket.show_more_flights_arrival_web')"></span>
                        </span>
                        <span class="d-md-none">
                            <span v-html="__('multiticket.show_more_flights_web_mobile')"></span>
                        </span>
                    </template>
                </template>
                <template v-else>
                    <span class="d-inline">{{ __("multiticket.hide_less_flights") }}</span>
                </template>
                <span :class="{ 'i-rotate-180': showAll }" class="icon icon-expand"></span>
            </span>

        </div>

    </div>
</template>

<script setup>
import { storeToRefs } from 'pinia';
import { computed, ref } from 'vue';
import { getDetail, getParamsDetailFlight, getFamilyFare, getParamsUpsell, getUpsell, getParamsRevalidate, getRevalidate } from '../../services/ApiFlightFrontServices';
import { useFlightFamilyFareStore } from '../../stores/flightFamilyFare';
import { useFlightDetailStore } from '../../stores/flightDetail';
import { useFlightUpsellStore } from '../../stores/flightUpsell';
import { useFlightRevalidateStore } from '../../stores/flightRevalidate';
import { getFlightsBySimpleFlightQuotes } from "../../services/fetchListService";
import { useMultiTicketStore } from '../../stores/multiTicket';
import jsonData from '../../../../json/iatasGrouper.json';
import { List } from '../../../utils/analytics/flightList.js'
import CurrencyDisplay from '../common/CurrencyDisplay.vue';

const { group, isStarting, idxGroup } = defineProps({
    group: {
        type: Object,
        default: {}
    },
    isStarting: {
        type: Boolean,
        default: true
    },
    idxGroup: {
        type: Number,
        default: 0
    }
});

const flightFamilyFareStore = useFlightFamilyFareStore();
const flightDetailStore = useFlightDetailStore();
const useFlightUpsell = useFlightUpsellStore();
const flightRevalidateStore = useFlightRevalidateStore();
const useMultiTicket = useMultiTicketStore();

const { getLuggage, getIsLoadingLuggages } = storeToRefs(flightFamilyFareStore);
const { getShowDetail } = storeToRefs(flightDetailStore);
const { getCheckOutDataStepOne } = storeToRefs(useMultiTicket);

const { setFlightDetailResponse, activeModalDetail, setExtraData } = flightDetailStore;
const { setFlightFamilyFareResponse, setIsLuggage } = flightFamilyFareStore;
const { changeOpenCloseModalUpsell, setFlightUpsell, setFlightSelected, activateLoadingUpsell } = useFlightUpsell; //set/actions
const { setFlightRevalidateResponse, setRevalidateStatus } = flightRevalidateStore;

const quoteConfig = window.__pt.settings.site.airlineConfiguration.international || {};
const request = window.__pt.data || {};
const flights = computed(() => {
    return isStarting ? group?.departure?.flights : group?.returning?.flights;
});

const flightsToken = computed(() => {
    return isStarting ? group?.departure?.token : group?.returning?.token;
});

const flightsTokenFQS = computed(() => {
    
    let quoteTokenFQS = group.quoteTokenFQS.split(",");
    return isStarting ? quoteTokenFQS[0] : quoteTokenFQS[1];
});

const startOrReturnStr = computed(() => isStarting ? "starting" : "returning");
const iatasAgrouper = computed(() => jsonData);

const itemsByLeg = 7;
const itemsToshow = ref(7);

const showButton = computed(() => flights?.value?.length > itemsByLeg);
const showAll = computed(() => itemsToshow.value === flights?.value?.length);

const showMoreItems = () => {
    itemsToshow.value = showAll.value ? itemsByLeg : flights.value.length;
};

const getAmountPerPerson = (amount) => {
    const paxes = window.__pt.data.adults + window.__pt.data.kids;
    return amount / paxes;
}

const markMoreItemsEconomic = computed(() => {
    let findMoreEconomic = flights.value.filter((f, idx) => idx >= itemsToshow.value && f.cheap);
    return findMoreEconomic.length > 0;
});

const handleMouseOver = (event) => {
    if (!window.__pt.settings.site.isMobileDevice()) {
        const span = document.getElementById(event.target.id);
        const tooltip = new bootstrap.Tooltip(span);
        tooltip.show();
    }
}

const luggages = computed(() => {
    let configLuggage = {}
    for (let index in flights.value) {

        let code = '';
        for (let seg in flights.value[index].segments) {
            code += flights.value[index].segments[seg].departure.code + "-";
            code += flights.value[index].segments[seg].arrival.code + "-";
            code += flights.value[index].segments[seg].operatingAirline.code + "-";
            code += flights.value[index].segments[seg].marketingAirline.code + "-";
        }
        code += flights.value[index].fares[0].fareGroup;
        configLuggage[index] = (getLuggage.value || {})[code];
    }

    return configLuggage
})

const getFlightDetails = async (name, image, flight, group, type, luggage = false) => {
    if (getShowDetail.value) return;
    activeModalDetail();
    const modalElement = document.getElementById('modalDetail');
    const modal = new bootstrap.Modal(modalElement);
    setExtraData({
        airlineLogoUri: image,
        airlineName: name
    });
    modal.show();
    
    let paramsDetail = {
        
        token: flightsToken.value,
        flightId: flight.id,
        flightType: type,
        airlineLogoUri: image,
        airlineName: name,
        fareId: flight.fares[0].fareId,
        fareKey: flight.fares[0].fareKey,
    };
    if(group.quoteToken.split(",")?.length > 1 && isStarting){
        paramsDetail.oneWayToken = group.quoteToken;
        paramsDetail.multiple = flight.fares[0].multiple
        paramsDetail.roundTrip = flight.fares[0].isRoundTrip
        paramsDetail.stepView = "starting";
    }
    if(group.quoteToken.split(",")?.length > 1 && !isStarting){
        paramsDetail.oneWayToken = group.quoteToken;
        paramsDetail.multiple = flight.fares[0].multiple
        paramsDetail.roundTrip = flight.fares[0].isRoundTrip
        paramsDetail.stepView = "returning";
    }
    let rq = getParamsDetailFlight(paramsDetail);

    let responseDetail = await getDetail(rq);
    let responseFamilyFare = await getFamilyFare(rq);

    setFlightDetailResponse(responseDetail);
    setFlightFamilyFareResponse(responseFamilyFare);
    List.modalDetail(group.departure.code, responseFamilyFare.familyFareName, type == 'starting' ? 'ida' : 'regreso', luggage);
    activeModalDetail();
    setIsLuggage(luggage);
}
const configFilter = (code=null) => {
    return (quoteConfig || []).find(item => (String(item.airlineCode)).toUpperCase() === (String(code)).toUpperCase())?? {}
}
const selectInputFlight = async  (flight, token, airline, taskIDs)  =>  {
    const taskID = taskIDs.split(",");
    let flightSelected = {};
    if (isStarting) {
       flightSelected = {
            step_action : 2,//4 es para ir directo a reservar y 2 es para ir a seleccionar vuelo de regreso
            departure_flight : flight,
            departure_token : token,
            departure : airline,
            departure_quoteTokenFQS : taskID[0]
        }        
        // if(request.isRoundtrip && (configFilter(flightSelected.departure_flight.airline?.code) || {})?.searchArrival){
        //     getFlightsBySimpleFlightQuotes(flightSelected.departure_flight.engine, false)
        // }
    } else {
        flightSelected = {
            step_action : 4,//4 es para ir directo a reservar y 2 es para ir a seleccionar vuelo de regreso
            returning_flight : flight,
            returning_token : token,
            returning : airline,
            returning_quoteTokenFQS : taskID[1]
        }
        if(airline?.returningQuoteToken) flightSelected.returningQuoteToken = airline.returningQuoteToken;
        if(airline?.outboundFlightId) flightSelected.outboundFlightId = airline.outboundFlightId;
        if(airline?.outputFareId) flightSelected.outputFareId = airline.outputFareId;
        if(flightSelected.returning_fare?.isRoundTrip){
            Logger.log("Precio anterior, oneway: ", flightSelected.returning_fare?.beforeDisplayAmount)
            Logger.log(`Diferencia de rountrip vs oneway: ${flightSelected.returning_fare.beforeDisplayAmount} - ${flightSelected.returning_fare.displayAmount} = `, flightSelected.returning_fare.beforeDisplayAmount - flightSelected.returning_fare.displayAmount)
        }
    }
}
const getUpsellList = async (flight, group, luggages, indexFlight) => {
    if (!isStarting) {
        const dateDeparture = new Date(flight.departureTime);
        const dateArrival = new Date(getCheckOutDataStepOne.value.departureFlight.arrivalTime);
        if (dateArrival >= dateDeparture) {
            const modalMessages = document.getElementById('modalMessages');
            const modal = new bootstrap.Modal(modalMessages);
            modal.show();
            return false;
        }
    }
    let luggage = luggages[indexFlight] ? luggages[indexFlight] : null; 
    const flights = {
        departureFlight: flight,
        groupFlight : group,
        luggages: luggage,
        totalAmount: getAmountPerPerson(flight.fares[0].amount)
    };
    const quoteTaskID = [{ taskID: flightsTokenFQS.value }];
    selectInputFlight(flight, flightsToken.value, group, quoteTaskID[0].taskID);
    setFlightSelected(flights);
    setRevalidateStatus(false);
    let token = isStarting ? group.departure.token : group.returning.token;
    let paramsUpsell = {
        outboundFlightId: flight.id,
        OutputFareId: flight.fares[0].fareId,
        fareKey: flight.fares[0].fareKey,
        token: token,
    };

    if (!isStarting && flight.fares[0].isRoundTrip) {
         paramsUpsell.DepartureToken = getCheckOutDataStepOne.value.summary.token;
         paramsUpsell.FlightQuoteId = getCheckOutDataStepOne.value.summary.fareKey;
    }
    if(group.quoteToken.split(",")?.length > 1 && isStarting){
        paramsUpsell.oneWayToken = group.quoteToken;
        paramsUpsell.multiple = flight.fares[0].multiple
        paramsUpsell.roundTrip = flight.fares[0].isRoundTrip
        paramsUpsell.stepView = "starting";
        paramsUpsell.code = group.departure.code;
    }
    if(group.quoteToken.split(",")?.length > 1 && !isStarting){
        paramsUpsell.oneWayToken = group.quoteToken;
        paramsUpsell.multiple = flight.fares[0].multiple
        paramsUpsell.roundTrip = flight.fares[0].isRoundTrip
        paramsUpsell.stepView = "returning";
        paramsUpsell.code = group.returning.code;
    }
    
    setFlightUpsell({});
    activateLoadingUpsell();
    changeOpenCloseModalUpsell();
    let paramsDetail = {
        token: token,
        flightId: flight.id,
        flightType: 'starting',
        airlineLogoUri: group?.departure?.image,
        airlineName: group?.departure?.name,
        fareId: flight.fares[0].fareId,
        fareKey: flight.fares[0].fareKey,
    };
    if(group.quoteToken.split(",")?.length > 1 && isStarting){
        paramsDetail.oneWayToken = group.quoteToken;
        paramsDetail.multiple = flight.fares[0].multiple
        paramsDetail.roundTrip = flight.fares[0].isRoundTrip
        paramsDetail.stepView = "starting";
    }
    if(group.quoteToken.split(",")?.length > 1 && !isStarting){
        paramsDetail.oneWayToken = group.quoteToken;
        paramsDetail.multiple = flight.fares[0].multiple
        paramsDetail.roundTrip = flight.fares[0].isRoundTrip
        paramsDetail.stepView = "returning";
    }
    const promises = new Promise(async (resolve, reject) => {
        const responses = {};
        const rqRevalidate = getParamsRevalidate(paramsUpsell);
        const rqFamilyFare = getParamsDetailFlight(paramsDetail);

        responses['revalidate'] = await getRevalidate(rqRevalidate);
        responses['revalidate']['isSelected'] = true;
        responses['revalidate']['id'] = paramsUpsell.fareKey;
        responses['revalidate']['taskID'] = quoteTaskID;
        responses['familyFare'] = await getFamilyFare(rqFamilyFare);

        if (responses['revalidate'] && responses['familyFare'] && !responses['revalidate'].error && !responses['familyFare'].error) {
            resolve(responses);
        }
        reject("Error revalidate");
    }).then(res => {
        setFlightRevalidateResponse(res['revalidate']);
        setFlightFamilyFareResponse(res['familyFare']);
    }).catch(err => {
        // Error handler when any requesst fails
        Logger.warn(err);
    }).finally(async () => {
        setRevalidateStatus(true);
    });
    await promises;
    /* changeOpenCloseModalUpsell();
    activateLoadingUpsell(); */

    let rq = getParamsUpsell(paramsUpsell);

    let responseUpsell = await getUpsell(rq);
    if(responseUpsell.packageRates && responseUpsell.packageRates.length == 0 ){
        responseUpsell.packageRates = null 
    }
    responseUpsell.quoteToken = flightsToken.value;
    responseUpsell.taskID = quoteTaskID;
    setFlightUpsell(responseUpsell);
}

const isMultipleAirport = computed(() => {
    const iataStarting = window.__pt.data.startingFromAirport;
    const iataReturning = window.__pt.data.returningFromAirport;
    
    return iatasAgrouper.value[iataStarting] != undefined && iatasAgrouper.value[iataStarting]
        || iatasAgrouper.value[iataReturning] != undefined && iatasAgrouper.value[iataReturning];
});


</script>
<style lang="scss" scoped>
.is-loading {
    background: #eee;
    background: linear-gradient(110deg, #ececec 8%, #f5f5f5 18%, #ececec 33%);
    border-radius: 5px;
    background-size: 200% 100%;
    animation: 1.5s shine linear infinite;
}

@keyframes shine {
    to {
        background-position-x: -200%;
    }
}
</style>