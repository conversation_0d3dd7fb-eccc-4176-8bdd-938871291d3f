﻿using TBFront.Mappers;
using TBFront.Models;
using TBFront.Models.Request;
using System.Globalization;
using System.Text;

namespace TBFront.Aplication.Analytics
{
    public class DataLayerHandler
    {
        public static List<Dictionary<string, object>> GetAnalyticsPaymentGateway(CheckoutBookingRequest booking)
        {

            //var hotelEvent = DataLayerHandler.GetHotelEvents(booking);
            var stepTwo = DataLayerHandler.GetPackageStepTwo(booking);
            var ecommerceEvents = DataLayerHandler.GetEcommerceEvents(booking);
            var userInfoEvent = DataLayerHandler.GetUserCheckout(booking.Customer);

            var events = userInfoEvent.Concat(stepTwo).Concat(ecommerceEvents);

            return events.ToList();

        }

        private static List<Dictionary<string, object>> GetHotelEvents(CheckoutBookingRequest booking)
        {
            var bookingQuote = booking.Quote;
            var starting = bookingQuote.FlightItinerary.Starting;

            var myCI = new CultureInfo("en-US");
            var tripMode = booking.Quote.Rate.IsRoundtrip ? "roundtrip" : "oneway";
            var BookingFlowStep = "Reservation"; // validar hay  "Transaction"  y Reservation

            var dictionary = new Dictionary<string, object>
            {
                { "event", "gtmEvent" },
                { "eventName", "gtmEvent" },
                { "eventCategory", "Flights Reservation" },
                { "eventAction", $"Domestic | {tripMode}" },
                { "eventLabel", $"{starting.Departure.Airport} | {starting.Arrival.Airport}" },
                { "eventValue", 0 },
                { "eventExtra", 0 },
                { "eventInteraction", false },
            };

            return new List<Dictionary<string, object>> {
                new Dictionary<string, object>
                {
                    { "PageType", BookingFlowStep }
                },
                dictionary
            };
        }


        private static List<Dictionary<string, object>> GetPackageStepTwo(CheckoutBookingRequest booking)
        {
            var bookingQuote = booking.Quote;
            var starting = bookingQuote.FlightItinerary.Starting;

            var myCI = new CultureInfo("en-US");
            var ServiceWeek = myCI.Calendar.GetWeekOfYear(bookingQuote.CheckIn, CalendarWeekRule.FirstDay, DayOfWeek.Monday);
            var tripMode = booking.Quote.Rate.IsRoundtrip ? "roundtrip" : "oneway";

            var dictionary = new Dictionary<string, object>
            {
                { "OriginAirportName", starting.DepartureFullName },
                { "DestinationAirportName", starting.ArrivalFullName },
                { "OriginAirport", starting.Departure.Airport },
                { "DestinationAirport", starting.Arrival.Airport },
                { "TripType", tripMode },
                { "TotalAdults", bookingQuote.Adults },
                { "TotalChilds", bookingQuote.Children },
                { "Airline", starting.AirlineCode },
                { "FlightName", $"{starting.Departure.Airport} to {starting.Arrival.Airport}" },
                { "ProductId", $"{starting.Departure.Airport}-{starting.Arrival.Airport}-{ServiceWeek}" },
                { "CheckIn", bookingQuote.CheckIn.ToString("yyyy-MM-dd") },
                { "CheckOut", bookingQuote.CheckOut.ToString("yyyy-MM-dd") },
                { "PayForm", "" },
                { "PayPlan", "" },
                { "TotalAmount", bookingQuote.Rate.TotalAmount },
                { "ReservationId", (!string.IsNullOrEmpty(booking.MasterLocatorID) ? booking.MasterLocatorID:"") },
                { "Week", ServiceWeek },
                { "IsWeekend", (bookingQuote.CheckIn.DayOfWeek == DayOfWeek.Saturday || bookingQuote.CheckIn.DayOfWeek == DayOfWeek.Sunday) },
                { "TripDays", bookingQuote.Days },
                { "Currency", bookingQuote.Currency },
                { "TotAmount", 0 },
                { "ConversionAmount", 0 },
                { "ConversionCurrency", "" },
                { "eventName", "GoogleRemarketingReservationFlight" },
                { "event", "checkoutStep2-flight" },
            };

            return new List<Dictionary<string, object>> {
                dictionary
            };
        }

        private static List<Dictionary<string, object>> GetEcommerceEvents(CheckoutBookingRequest booking)
        {
            var bookingQuote = booking.Quote;
            var starting = bookingQuote.FlightItinerary.Starting;
            var returning = bookingQuote.FlightItinerary.Returning;

            var originName = starting.DepartureFullName;
            var destinationName = starting.ArrivalFullName;
            var originNameNormalize = originName;
            var destinationNameNormalize = destinationName;
            var originIATA = bookingQuote.ExtraInfoFlight.StartingFrom;
            var destinationIATA = bookingQuote.ExtraInfoFlight.ReturningFrom;
            var airlineCode = starting.AirlineCode;
            var airlineCodeReturn = returning.AirlineCode;

            var dictionary = new Dictionary<string, object>
            {
                ["event"] = "ga4.trackEvent",
                ["eventName"] = "add_payment_info",
                ["eventParams.item_list_name"] = "flights_home",
                ["eventParams.layer"] = "flights",
                ["eventParams.locator"] = booking.MasterLocatorID,
                ["eventParams.transaction_id"] = booking.MasterLocatorID,
                ["eventParams.field_origin_iata"] = originIATA,
                ["eventParams.field_origin_name"] = originName,
                ["eventParams.field_destination_iata"] = destinationIATA,
                ["eventParams.field_destination_name"] = destinationName,
                ["eventParams.field_date1"] = bookingQuote.CheckIn.ToString("yyyy-MM-dd"),
                ["eventParams.field_date2"] = bookingQuote.CheckOut.ToString("yyyy-MM-dd"),
                ["eventParams.travelers_adults"] = bookingQuote.Adults.ToString(),
                ["eventParams.travelers_children"] = bookingQuote.Paxes.SelectMany(f => f.Children).Count(c => c.Year > 1).ToString(),
                ["eventParams.travelers_infants"] = bookingQuote.Paxes.SelectMany(f => f.Children).Count(c => c.Year <= 1).ToString(),
                ["eventParams.travelers_infants_inseat"] = "0",
                ["eventParams.travelers_infants_onlap"] = "0",
                ["eventParams.items.0.index"] = 1,
                ["eventParams.items.0.brand"] = airlineCode,
                ["eventParams.items.0.item_category"] = booking.Quote.Rate.IsRoundtrip ? "roundtrip" : "oneway",
                ["eventParams.items.0.item_category2"] = booking.Quote.IsDomesticRoute ? "national" : "international",
                ["eventParams.items.0.id"] = $"{originIATA}-{destinationIATA}-{airlineCode}",
                ["eventParams.items.0.name"] = $"{originNameNormalize}-{destinationNameNormalize}-{airlineCode}",
                ["eventParams.items.0.variant"] = starting.FareGroup,
                ["eventParams.items.0.price"] = bookingQuote.Rate.TotalAmount,
                ["eventParams.value"] = bookingQuote.Rate.TotalAmount,
                ["eventParams.items.0.quantity"] = bookingQuote.Adults + bookingQuote.Children
            };

            if (booking.Quote.Rate.IsRoundtrip)
            {
                dictionary["eventParams.items.1.brand"] = airlineCodeReturn;
                dictionary["eventParams.items.1.item_category"] = booking.Quote.Rate.IsRoundtrip ? "roundtrip" : "oneway";
                dictionary["eventParams.items.1.item_category2"] = booking.Quote.IsDomesticRoute ? "national" : "international";
                dictionary["eventParams.items.1.id"] = $"{destinationIATA}-{originIATA}-{airlineCodeReturn}";
                dictionary["eventParams.items.1.name"] = $"{destinationNameNormalize}-{originNameNormalize}-{airlineCodeReturn}";
                dictionary["eventParams.items.1.variant"] = returning.FareGroup;
            }

            return new List<Dictionary<string, object>> {
                new Dictionary<string, object>
                {
                    { "ecommerce", null }
                },
                dictionary
            };
        }

        private static List<Dictionary<string, object>> GetUserCheckout(Customer customer)
        {
            var user = UserMapper.UserCheckoutData(customer);
            var dictionary = new Dictionary<string, object>
            {
                { "event", "UserCollector" },
                { "eventName", "UserCollector" },
                { "UserName", user.Name },
                { "UserLastName", user.LastName },
                { "UserPhone", user.Phone },
                { "UserEmail", user.Email },
                { "UserHashPhone", user.HashPhone },
                { "UserHashEmail", user.HashEmail },
            };

            return new List<Dictionary<string, object>>
            {
                dictionary
            };

        }

        static string RemoveAccent(string texto = "")
        {
            if (texto == null)
            {
                return "";
            }
            string normalizedText = texto.Normalize(NormalizationForm.FormD);
            StringBuilder result = new StringBuilder();

            foreach (char c in normalizedText)
            {
                UnicodeCategory category = CharUnicodeInfo.GetUnicodeCategory(c);
                if (category != UnicodeCategory.NonSpacingMark)
                {
                    result.Append(c);
                }
            }

            return result.ToString().ToLower();
        }


    }
}
