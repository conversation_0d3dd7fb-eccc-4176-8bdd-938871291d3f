﻿using System.Text.Json.Serialization;
namespace TBFront.Models.Meta.Schema
{
    public class QuestionList
    {
        public QuestionList(){
            acceptedAnswer = new AnswerList();
        }
        [JsonPropertyName("@type")]
        public string? Type { get; set; }
        [JsonPropertyName("name")]
        public string? Name { get; set; }
        [JsonPropertyName("acceptedAnswer")]
        public AnswerList? acceptedAnswer { get; set; }

    }
}
