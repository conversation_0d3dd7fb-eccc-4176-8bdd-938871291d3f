<template>
    <div id="cSummary" class="c-summary pt-3 px-3 mb-3 c-pay-next-step summary-start">
        <div id="closeSummaryMobile" class="c-header-mobile d-block d-md-none d-lg-none p-4 d-none">
            <span class="icon icon-chevron-left text-white font-40"></span>
            <span class="text-center d-block">{{ __("checkout.your_reservation") }}</span>
        </div>
        <h3 class="font-28 f-r-medium my-2">{{ __("checkout.your_reservation") }}</h3>
        <div class="cs-info">
            <template v-if="summary.rate.isRoundtrip">
                <span class="icon icon-plane-right font-28 position-t-2 me-1"></span>
                <span class="font-18"> {{ __("messages.departure_flight") }}</span>
            </template>
            <span class="float-end">
                <!--<img width="100%" height="40px" style="max-width: 100px; object-fit: contain;" :src="summary.flightItinerary.starting.airlineLogoUrl" />-->
                <img class="img-airline ms-lg-1" height="auto" width="30" :src="summary.flightItinerary.starting.airlineLogoUrl" />
                <span class="font-12 font-bold d-lg-inline ms-1 fs-10-sm">{{summary.flightItinerary.starting.airline}}</span>
            </span>
            <p id="DepartureDate" class="mt-3 mb-2"> {{ $filters.date(summary.flightItinerary.starting.departure.date, 'dddd, DD MMMM YYYY')}}</p>
            <div class="mb-2 position-relative">
                <span>{{ __("messages.flight") }}: </span>
                <span id="DepartureFlightNumber">{{ summary.flightItinerary.starting.flightNumber }}</span>
                <span class="px-2">|</span>
                <span id="DepartureFlightScaleForOneWay" class="a-link-1" @click="openModal('modal-DetailFlightByLeg-starting', 'modalDetail', true)"
                      v-if="summary.flightItinerary.starting.scales == 0">{{ __("messages.unscaled") }}</span>
                <span id="DepartureFlightScaleForOneWay" class="a-link-1" @click="openModal('modal-DetailFlightByLeg-starting', 'modalDetail', true)"
                      v-if="summary.flightItinerary.starting.scales > 0">
                    {{ summary.flightItinerary.starting.scales }}
                    <template v-if="summary.flightItinerary.starting.scales == 1">
                        {{ __("messages.scale") }}
                    </template>
                    <template v-else>
                        {{ __("messages.scales") }}
                    </template>
                </span>
            </div>
            <p class="mb-2">
                <span>{{ __("messages.departure") }}: </span>
                <span id="DepartureDepartureAirport" class="f-r-medium">
                    {{ summary.flightItinerary.starting.departureName }} -
                    {{ summary.flightItinerary.starting.departure.time }}
                </span>
            </p>
            <p class="mb-2">
                <span>{{ __("messages.arrival") }}: </span>
                <span id="DepartureArrivalAirport" class="f-r-medium">
                    {{ summary.flightItinerary.starting.arrivalName }} -
                    {{ summary.flightItinerary.starting.arrival.time }}
                </span>
            </p>
            <p class="mb-2">
                <span>{{ __("messages.rate") }}: </span>
                <span id="DepartureFareName" class="a-link-1" @click="openModal('modal-DetailFlightBaggage-starting', 'DetailFlightBaggage', true)">
                    {{ getFareNameStart }}
                </span>
            </p>
        </div>
        <div class="cs-info" v-if="summary.rate.isRoundtrip">
            <div class="col-12 px-0">
                <hr />
            </div>
            <span class="icon icon-plane-left font-28 position-t-2 me-1"></span>
            <span class="font-18"> {{ __("messages.return_flight") }}</span>
            <span class="float-end">
                <img class="img-airline ms-lg-1" height="auto" width="30" :src="summary.flightItinerary.returning.airlineLogoUrl" />
                <span class="font-12 font-bold d-lg-inline ms-1 fs-10-sm">{{summary.flightItinerary.returning.airline}}</span>
            </span>
            <p id="ArrivalDate" class="mt-3 mb-2"> {{ $filters.date(summary.flightItinerary.returning.departure.date, 'dddd, DD MMMM YYYY')}}</p>
            <div class="mb-2 position-relative">
                <span>{{ __("messages.flight") }}: </span>
                <span>{{ summary.flightItinerary.returning.flightNumber }}</span>
                <span class="px-2">|</span>
                <span id="DepartureFlightScaleForRoundTrip" class="a-link-1" @click="openModal('modal-DetailFlightByLeg-returning', 'modalDetail', false)"
                      v-if="summary.flightItinerary.returning.scales == 0">{{ __("messages.unscaled") }}</span>
                <span id="DepartureFlightScaleForRoundTrip" class="a-link-1" @click="openModal('modal-DetailFlightByLeg-returning', 'modalDetail', false)"
                      v-if="summary.flightItinerary.returning.scales > 0">
                    {{ summary.flightItinerary.returning.scales }}
                    <template v-if="summary.flightItinerary.returning.scales == 1">
                        {{ __("messages.scale") }}
                    </template>
                    <template v-else>
                        {{ __("messages.scales") }}
                    </template>
                </span>
            </div>
            <p class="mb-2">
                <span>{{ __("messages.departure") }}: </span>
                <span id="ArriveDepartureAirport" class="f-r-medium">
                    {{ summary.flightItinerary.returning.departureName }} -
                    {{ summary.flightItinerary.returning.departure.time }}
                </span>
            </p>
            <p class="mb-2">
                <span>{{ __("messages.arrival") }}: </span>
                <span id="ArriveArriveAirport" class="f-r-medium">
                    {{ summary.flightItinerary.returning.arrivalName }} -
                    {{ summary.flightItinerary.returning.arrival.time }}
                </span>
            </p>
            <p class="mb-2">
                <span>{{ __("messages.rate") }}: </span>
                <span id="ArriveFareName" class="a-link-1" @click="openModal('modal-DetailFlightBaggage-returning', 'DetailFlightBaggage', false)">
                    {{ getFareNameReturn }}
                </span>
            </p>
        </div>
        <div class="row bg-yellow-05 py-3 " v-if="summary.rate.totalAmount > 0 && summary.rate.breakdown.length > 0">
            <template v-for="breakdown in summary.rate.breakdown" :key="breakdown.type">
                <div class="col-12 " v-if="showBreakdown(breakdown.type)">
                    <div class="row">
                        <div class="col-8 pr-0" v-if="breakdown.type == 0 ">
                            {{ breakdown.displayText }}  {{ __("breakdown.adults_txt") }}
                        </div>
                        <div class="col-8 pr-0" v-else-if="breakdown.type == 4">
                            {{ breakdown.displayText }} {{ __("breakdown.infant_txt") }}
                        </div>
                        <div class="col-8 pr-0" v-else-if="breakdown.type == 1">
                            {{ breakdown.displayText }} {{ __("breakdown.children_txt") }}
                        </div>
                        <div class="col-8 pr-0" v-else-if="breakdown.type == 2">
                            {{ __("breakdown.taxes_and_fees") }}
                        </div>
                        <div class="col-8 pr-0" v-else-if="breakdown.type == 3">
                            {{ __("breakdown.service_charge_txt") }}
                        </div>
                        <div class="col-8 pe-0 position-relative" v-else-if="breakdown.type == 5" v-click-outside="closeTooltip">
                            {{ __("breakdown.all_charges_txt") }}  <span class="icon icon-info pt-1 text-black cursor-pointer color-blue" @click="toggleTooltip()"></span>
                            <tooltip-charges v-if="showAllCharges" :breakdowns="summary.rate.breakdown" :toggle="toggleTooltip"></tooltip-charges>
                        </div>
                        <div class="col-4 text-end ps-0">
                            <CurrencyDisplay :amount="breakdown.amount" :showCurrencyCode="true" />
                        </div>
                    </div>
                </div>
            </template>
            <div class="col-4 col-md-3 pr-0 font-total font-bold">
                {{ __("messages.total") }}
            </div>
            <div id="Total" class="col-8 col-md-9 text-end font-total font-bold">
                <CurrencyDisplay :amount="summary.rate.totalAmount" :showCurrencyCode="true" />
            </div>
        </div>
        <div class="row bg-yellow-05 py-3 " v-else="summary.rate.totalAmount > 0">
            <Skeleton :typeSkeleton="'typePriceCheckout'" />
        </div>
    </div>
    <NotifyCurrency />
    <security-information :css="'d-none d-sm-block'"></security-information>
</template>
<script>
    import _ from 'lodash';
    import {useFlightDetailStore} from "../../../stores/flightDetail";
    import CurrencyDisplay from '../../common/CurrencyDisplay.vue';

    const configSite = __pt.settings.site;

    export default {
        props: {
            summary: {},
            itineary: {}
        },
        data() {
            return {
                config: configSite,
                showAllCharges: false
            }
        },
        setup(){
            const flightDetailStore = useFlightDetailStore();
            const { setFlightDetailResponse, activeModalDetail, setExtraData } = flightDetailStore;
            return {
                setFlightDetailResponse,
                activeModalDetail,
                setExtraData
            }
        },
        mounted() {
        },
        computed: {
            getFareNameStart() {
                let name = _.get(this.itineary, ["detailStarting", "detailFamilyFare", "familyFareName"], this.summary.flightItinerary.starting.fareGroup);
                return name;
            },
            getFareNameReturn() {
                let name = _.get(this.itineary, ["detailReturning", "detailFamilyFare", "familyFareName"], this.summary.flightItinerary.returning.fareGroup);
                return name;
            }
        },
        methods: {
            openModal(id, type, is_starting = true) {
                switch (type) {
                    case 'modalDetail':
                        const itineary = this.itineary[is_starting ? 'detailStarting' : 'detailReturning']['detail'];
                        this.setFlightDetailResponse(itineary)
                        this.setExtraData({
                            airlineLogoUri: itineary.flightSegments[0].generalInfo.airlineLogoUri,
                            airlineName: itineary.flightSegments[0].generalInfo.airlineName,
                            view: is_starting ? this.__(`messages.departure_flight`) : this.__(`messages.return_flight`)
                        });
                        const modalElement = document.getElementById('modalDetailNational');
                        const modal = new bootstrap.Modal(modalElement);
                        modal.show();
                        break;
                    case 'DetailFlightBaggage':
                        $(`#${id}`).modal("show");
                        break;
                        
                }
            },
            showBreakdown(type) {

                if (!this.config.breakdownList || !this.config.breakdownList.length) {
                    return true;
                }
                return this.config.breakdownList.indexOf(type) != -1
            },
            toggleTooltip() {
                this.showAllCharges = !this.showAllCharges;
            },
            closeTooltip() {
                if (this.showAllCharges) {
                    this.toggleTooltip();
                }
            }
        },
        components: {
            CurrencyDisplay
        }
    }
</script>
<style lang="scss" scoped>
.font-total {
    font-size: 24px;
}

@media (min-width: 768px) and (max-width: 1199.98px) {
    .font-total {
        font-size: 20px;
    }

}
</style>