@import '../variables';
.btn {
	font-size: 1rem;
}

.btn-primary:focus, .btn-primary:active, .btn-check:active + .btn-primary, .btn-check:checked + .btn-primary, .btn-primary.active, .btn-primary:active, .show > .btn-primary.dropdown-toggle {
	background-color: var(--bg-primary) !important;
	border-color: var(--border-primary) !important;
	box-shadow: none !important;
	color: var(--text-oncolor) !important;
}

.btn-primary {
	background-color: var(--bg-primary) !important;
	border-color: var(--border-primary) !important;
	box-shadow: none !important;
	color: var(--text-oncolor) !important;
	transition: all .2s linear;
}
.btn-primary:hover {
	background-color: var(--bg-primary-hover) !important;
}

.btn-orange {
	background-color: var(--bg-secondary) !important;
	border-color: var(--bg-secondary) !important;
	box-shadow: none !important;
	color: var(--text-oncolor) !important;
	transition: all .2s linear;
}
.btn-orange:hover {
	background-color: var(--bg-secondary-hover) !important;
}

.btn-secondary, .btn-secondary:hover, .btn-secondary:focus, .btn-secondary:active, .btn-check:active+.btn-secondary, .btn-check:checked+.btn-secondary, .btn-secondary.active, .btn-secondary:active, .show>.btn-secondary.dropdown-toggle {
	background-color:  $color-primary !important;
	border-color: $color-primary !important;
	box-shadow: none !important;
	color:$white !important;
}


.btn-outline-primary {
	color: #333333;
	border-color: $color-secundary;
}
.btn-outline-primary:hover {
	color: #333333;
	background-color: $white;
	border-color: $color-secundary;
}

button.gm-ui-hover-effect {
	top: 0 !important;
	right: 20px !important;
	width: 24px !important;
	height: 24px !important;
	z-index: 3;
}

button.gm-ui-hover-effect span {
	width: 24px !important;
	height: 24px !important;
}

.btn-link{
	color: $color-primary;
	&:hover{
		color: $color-primary-hover;
		text-decoration: none;
	}
	&:focus{
		box-shadow: none;
	}
}

/*MOBILE*/

.mobile-button {
	padding: 10px 20px;
	position: fixed;
	bottom: 20px;
	left: 50%;
	transform: translateX(-50%);
	border: 1px solid $color-primary;
	box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.5);
	z-index: 500;
}

.btn-blue {
	background: #2196f3 !important;
	border: none;
	border-radius: 4px;
	color: #fff !important;
	font-family: Roboto-Medium;
	transition: all .2s linear;

	&:hover {
		background: #2196f3 !important;
		border: none !important;
		border-radius: 4px !important;
		color: #fff !important;
		font-family: Roboto-Medium !important;
		transition: all .2s linear !important;
	}
}
/* -------------------------------------------- NO MODIFICAR -------------------------------------------- */
/* DS BUTTONS ---------------------------------------------- */
// Button reset ----------------------------
button {
    box-sizing: border-box;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    -webkit-tap-highlight-color: transparent;
    margin: 0;
    padding: 0;
    display: inline-block;
    background: none;
    border: none;
    outline: none;
    text-align: center;
    font: inherit;
    line-height: inherit;
    color: inherit;
    box-shadow: none;
    cursor: pointer;

    i {
        display: inline-flex;
        font-size: 1.5rem;
        pointer-events: none;
    }
    &:disabled {
        background: var(--bg-disabled);
        border-color: var(--border-disabled);
        color: var(--text-disabled);
        box-shadow: none;
        cursor: not-allowed;

        &:hover {
            background: var(--bg-disabled);
            border-color: var(--border-disabled);
            color: var(--text-disabled);
            box-shadow: none;
        }
    }
    &:focus-visible {
        outline: 2px solid var(--border-info);
        outline-offset: 2px;
    }
}

// Shared Props ----------------------------
@mixin baseBtnProps {
    padding: 1rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    border-radius: 8px;
    font: var(--body-bold);
    line-height: 1.2em;
    letter-spacing: 0.01em;
    transition: all 150ms ease-out;
    cursor: pointer;
}
@mixin md {
    padding: 0.75rem 1rem;
    font: var(--body-sm-bold);
    letter-spacing: 0.02em;
}
@mixin xs {
    padding: 0.5rem 0.75rem;
    border-radius: 4px;
    font: var(--body-sm-bold);
    letter-spacing: 0.02em;
}

// Para CTAs primarios ----------------------------
.btnPrimary {
    @include baseBtnProps();
    min-width: 80px;
    background: var(--bg-primary);
    border: 2px solid var(--border-primary);
    color: var(--text-oncolor);
    box-shadow: var(--shadow-100); 
    
    &:hover {
        background: var(--bg-primary-hover);
        border-color: var(--border-primary-strong);
        color: var(--text-oncolor);
        box-shadow: var(--shadow-300)
    }
    &--md {
        @include md();
    }
    &--xs {
        @include xs();
    }
}

// Para accionables secundarios ----------------------------
.btnSecondary {
    @include baseBtnProps();
    min-width: 80px;
    background: transparent;
    border: 2px solid var(--border-primary);
    color: var(--text-primary);
    
    &:hover {
        border-color: var(--border-primary-hover);
        color: var(--text-primary-strong);
    }
    &--md {
        @include md();
    }
    &--xs {
        @include xs();
    }
}

// Para demás accionables ----------------------------
.btnTertiary {
    @include baseBtnProps();
    padding: 0 !important;
    background: transparent;
    color: var(--text-primary);

    &:hover {
        color: var(--text-primary-strong);
    }
    &--md {
        @include md();
    }
    &--xs {
        @include xs();
    }
}

// Para accionables ubicados encima de otro elemento ----------------------------
.btnOnBg {
    @include baseBtnProps();
    background: hsla(var(--elevation-hsl), 0.75);
    border: 2px solid var(--border-subtle);
    color: var(--text-oncolor);

    &:hover {
        background: hsla(var(--elevation-hsl), 0.9);
        color: var(--text-oncolor);
    }
    &:focus-visible {
        border-color: var(--border-subtle-hover)
    }
    // Sizes
    &--md {
        @include md();
    }
    &--xs {
        @include xs();
    }
    // Position
    &--center {
        position: absolute;
        z-index: 1;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
}

// Para CTAs sin texto ----------------------------
.btnIcon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-base);
    border-radius: 50%;
    color: var(--text-subtle);
    box-shadow: var(--shadow-100);
    transition: all 150ms ease-out;
    user-select: none;
    
    &:hover {
        background: var(--bg-level3);
        box-shadow: var(--shadow-300)
    }
    // Para CTAs primarios sin texto
    &--primary {
        background: var(--bg-primary);
        border: 2px solid var(--border-primary);
        color: var(--text-oncolor);

        &:hover {
            background: var(--bg-primary-hover);
            border-color: var(--border-primary-strong);
        }
    }
    // Para CTAs sin texto sobre imagenes o fondos
    &--onBg {
        position: absolute;
        z-index: 1;
        box-shadow: var(--shadow-400);
        opacity: 0.7;

        &:hover, &:focus-visible {
            opacity: 1;
        }

    }
    // Position
    &--left {
        top: 50%;
        left: 4px;
        transform: translateY(-50%);
    }
    &--right {
        top: 50%;
        right: 4px;
        transform: translateY(-50%);
    }
    // Sizes
    &--md {
        width: 40px;
        height: 40px
    }
    &--xs {
        width: 32px;
        height: 32px
    }
}
/* DS BUTTONS ---------------------------------------------- */