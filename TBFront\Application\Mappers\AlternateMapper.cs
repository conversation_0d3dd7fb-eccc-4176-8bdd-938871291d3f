﻿using TBFront.Helpers;
using TBFront.Models.Meta.Alternate;
using TBFront.Models.Places.Request;
using TBFront.Models.Places.Response;
using TBFront.Options;

namespace TBFront.Application.Mappers
{
    public class AlternateMapper
    {
        public static AlternateMain Map(List<Culture> cultures, PlaceRequest request)
        {
            var main = new AlternateMain();
            foreach (var culture in cultures)
            {
                main.Alternates.Add(new AlternatePage
                {
                    Culture = culture.CultureCode,
                    InternalCulture = culture.InternalCultureCode,
                    Name = culture.Name,
                    Url = request.Path
                });
            }
            return main;
        }

        public static AlternateMain Map(List<FrontPlaceResponse> items, Types.PageType pageType)
        {
            var main = new AlternateMain();

            foreach (var places in items)
            {
                if (places?.PlaceResponses is null)
                    continue;

                var starting = places.PlaceResponses.FirstOrDefault(a => string.Equals(a.Code, places.StartingAirport, StringComparison.OrdinalIgnoreCase));
                var returning = places.PlaceResponses.FirstOrDefault(a => string.Equals(a.Code, places.ReturningAirport, StringComparison.OrdinalIgnoreCase));

                if (starting == null || returning == null)
                    continue;

                var startingItem = FlightParamsHelper.GetAirportDetailByPlaceResponse(starting);
                var returningItem = FlightParamsHelper.GetAirportDetailByPlaceResponse(returning);

                var url = pageType == Types.PageType.DestinationFlightList
                    ? $"{returning.Code}".ToLower()
                    : $"{starting.Code}/{returning.Code}".ToLower();

                var returingUri =  FlightParamsHelper.GenerateSlug(returningItem.CityName);

                main.Alternates.Add(new AlternatePage
                {
                    Culture = places.Culture,
                    InternalCulture = places.InternalCulture,
                    Name = starting.Name,
                    Url = url,
                    StartingUri = FlightParamsHelper.GenerateSlug(startingItem.CityName),
                    ReturingUri = returingUri,
                    IsUrlList = true,
                });
            }

            return main;
        }


    }
}