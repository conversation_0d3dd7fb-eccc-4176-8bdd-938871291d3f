<template>
    <template v-for="phone in getPhoneData(props.phone, props.special)">
        <div class="col-12 col-md-6" >
            <a :href="phone.PhoneNumber" class="text-dark d-flex justify-content-between mb-3 link-int px-2 py-1 rounded mx-2">
                <span class="font-bold">{{phone.City}}</span>
                <span>{{phone.Phone}}</span>
            </a>
        </div>
    </template>
</template>

<script setup>
    import { onMounted, onBeforeUnmount } from 'vue';
    import { Generic } from '../../../utils/analytics/generics.js'

    const props = defineProps({
        special: "",
        phone: "",
        mobile: false
    });

    const getPhoneData = (data, specials) => {
        const phones = JSON.parse(data);
        const specialCities = JSON.parse(specials);
        let dataPhones = [];
        for (let phone in phones) {
            dataPhones.push({
                City: specialCities[phones[phone].City] ? specialCities[phones[phone].City] : phones[phone].City,
                Phone: phones[phone].Phone,
                PhoneNumber: "tel:" + phones[phone].Phone.replace(/\s/g, ''),
            });
        }
        return dataPhones;
    };

    const onPhoneNumbersOpen = () => {
        const phoneNumbers = document.getElementById('idClickPhone');
        if (!phoneNumbers.classList.contains('hli-active')) {
            Generic.phones();
        }
    };

    onMounted(() => {
        const phoneNumbers = document.getElementById('idClickPhone');
        phoneNumbers.addEventListener('click', onPhoneNumbersOpen);
    });

    onBeforeUnmount(() => {
        const phoneNumbers = document.getElementById('idClickPhone');
        phoneNumbers.addEventListener('click', onPhoneNumbersOpen);
    });
</script>