﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;

namespace TBFront.Models.Request
{
    public class CalendarPromotionsRequest
    {

        [FromQuery(Name = "option")]
        public string Option { get; set; }
        [FromQuery(Name = "view")]
        public string View { get; set; }
        [FromQuery(Name = "transporte")]
        public string  Transport { get; set; }
        [FromQuery(Name = "origen")]
        public string Origin { get; set; }
        [FromQuery(Name = "destino")]
        public string Destination { get; set; }
        [FromQuery(Name = "from")]
        public string From { get; set; }
        [FromQuery(Name = "to")]
        public string To { get; set; }
        [FromQuery(Name = "adultos")]
        public string Adults { get; set; }
        [FromQuery(Name = "ninos")]
        public string Kids { get; set; }

    }
}
