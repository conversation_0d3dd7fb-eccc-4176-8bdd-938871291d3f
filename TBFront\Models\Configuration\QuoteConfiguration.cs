﻿namespace TBFront.Models.Configuration
{
    public class QuoteConfiguration
    {
        public ConfigType International { get; set; }
        public ConfigType National { get; set; }
    }

    public class ConfigType
	{
        public bool IsMatrixActive { get; set; }
        public List<QuoteConfigurations> QuoteConfigurations { get; set; }
        public List<string> PriorityOrder { get; set; }
    }

    public class QuoteConfigurations
	{
        public string ConfigName { get; set; }
        public List<int> Engine { get; set; } = new List<int>();
        public List<string> CarrierCode { get; set; } = new List<string>();
        public List<string> BlockedCarrierCode { get; set; } = new List<string>();
        public bool IsActive { get; set; }
    }
}
