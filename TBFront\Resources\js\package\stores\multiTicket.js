import { defineStore } from "pinia";

export const useMultiTicketStore = defineStore('multiTicket', {
	state: () => ({
		isStepTwo: false,
		checkOutDataStepOne: {},
		airlinesFiltersApplied: [],
		stopsFiltersApplied: "",
		existResults: true
	}),
	getters: {
		getIsStepTwo: (state) => state.isStepTwo,
		getCheckOutDataStepOne: (state) => state.checkOutDataStepOne,
		getAirlinesFiltersApplied: (state) => state.airlinesFiltersApplied,
		getStopsFiltersApplied: (state) => state.stopsFiltersApplied,
		getExistResults: (state) => state.existResults,
	},
	actions: {
		setIsStepTwo(flag) {
			this.isStepTwo = flag;
		},
		setCheckOutDataStepOne(data) {
			this.checkOutDataStepOne = data;
		},
		setAirlinesFiltersPush(event){
			this.airlinesFiltersApplied.push(event);
		},
		//setStopsFiltersPush(event){
		//	this.stopsFiltersApplied.push(event);
		//},
		setAirlinesFilters(obj = []){
			this.airlinesFiltersApplied = obj;
		},
		setStopsFilters(obj = ""){
			this.stopsFiltersApplied = obj;
		},
		setExistResults(flag) {
			this.existResults = flag;
		},
		
	}
});