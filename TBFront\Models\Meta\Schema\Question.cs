﻿using System.Text.Json.Serialization;
namespace TBFront.Models.Meta.Schema
{
    public class Question
    {
        public Question()
        {
            mainEntity = new List<QuestionList>();
        }
        [JsonPropertyName("@context")]
        public string? Context { get; set; }

        [JsonPropertyName("@type")]
        public string? Type { get; set; }

        public List<QuestionList>? mainEntity { get; set; }
    }
}
