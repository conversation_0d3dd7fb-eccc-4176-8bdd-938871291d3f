﻿using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Extensions.Options;
using System.Text;
using System.Text.Json;
using TBFront.Controllers;
using TBFront.Interfaces;
using TBFront.Models.Configuration;
using TBFront.Models.Kerberus;
using TBFront.Options;

namespace TBFront.Infrastructure.HttpService.Kerberus
{
    public class KerberusService : IQueryHandlerAsync<KerberusRequest, string>
    {
        private readonly ILogger<KerberusService> _logger;
        private readonly HttpClient _httpClient;
        private readonly ContactMeConfiguration _configuration;
        private readonly static JsonSerializerOptions _jsonSerializerOptions = new() { PropertyNameCaseInsensitive = true };

        public KerberusService(HttpClient httpClient, IOptions<SettingsOptions> options, ILogger<KerberusService> logger)
        {
            _httpClient = httpClient;
            _configuration = options.Value.ContactMeConfiguration;
            _httpClient.BaseAddress = new Uri(_configuration.Kerberus);
            _logger = logger;
        }

        public async Task<string> QueryAsync(KerberusRequest request, CancellationToken ct)
        {
            var response = "OK";
            try
            {
                var querystring = new Dictionary<string, string?>
                {
                    { "id", request.Id.ToString() },
                    { "indicativo", request.Prefix.ToString() },
                    { "numerodestino", request.Phone },
                    { "tiempoespera", request.WaitingTime.ToString() },
                    { "intentos", request.Attempts.ToString() }
                };

                var uriService = QueryHelpers.AddQueryString($"{_configuration.Path}", querystring);
                var httpResponseMessage = await _httpClient.GetAsync(uriService, ct);

                using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync();
                response = new StreamReader(contentStream).ReadToEnd();

            }
            catch (Exception e)
            {
                _logger.LogError($"[Error] KerberusService Message: {e.Message} - Request: {JsonSerializer.Serialize(request)} - Response:  {response}");
            }

            return response;

        }

    }
}
