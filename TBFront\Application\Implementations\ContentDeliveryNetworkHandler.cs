﻿using Microsoft.Extensions.Options;
using TBFront.Application.Mappers;
using TBFront.Interfaces;
using TBFront.Models.ContentDeliveryNetwork.Exchange;
using TBFront.Models.ContentDeliveryNetwork.FaqContent;
using TBFront.Models.ContentDeliveryNetwork.LegalContent;
using TBFront.Models.ContentDeliveryNetwork.Seo;
using TBFront.Options;

namespace TBFront.Application.Implementations
{
    public class ContentDeliveryNetworkHandler : IContentDeliveryNetworkHandler
    {
        private readonly IContentDeliveryNetworkService _service;
        private readonly SettingsOptions _options;
        public ContentDeliveryNetworkHandler(IContentDeliveryNetworkService service, IOptions<SettingsOptions> options)
        {
            _service = service;
            _options = options.Value;
        }
        public async Task<ExchangeResponse> QueryAsync(ExchangeRequest request, CancellationToken ct)
        {
            var response = await _service.QueryAsync(request, ct);
            return response;
        }

        public async Task<FaqContentResponse> QueryAsync(FaqContentRequest request, CancellationToken ct)
        {
            request.SiteName = _options.SiteName.ToLower();

            var response = await _service.QueryAsync(request, ct);

            var content = GeneralContentMapper.Map(request, response);

            return content;
        }

        public async Task<List<LegalContentResponse>> QueryAsync(LegalContentRequest request, CancellationToken ct)
        {
            request.SiteName = _options.SiteName.ToLower();

            var response = await _service.QueryAsync(request, ct);

            var content = GeneralContentMapper.Map(request,response);

            return content;
        }
        public async Task<SeoResponse> QueryAsync(SeoRequest request, CancellationToken ct)
        {
            request.Path = request.Path.Trim('/').Replace("/", "_");

            request.SiteName = _options.SiteName.ToLower();

            var response = await _service.QueryAsync(request, ct);

            return response;
        }

    }
}
