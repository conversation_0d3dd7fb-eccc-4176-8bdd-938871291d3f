﻿using TBFront.Models.AB;

namespace TBFront.Infrastructure.HttpService.GrowthBookAPI.Dtos
{
    public class GrowthBookConfiguration
    {
        public string ClientSdk { get; set; }
        public string FeaturesUrl { get; set; }
        public string ServerSdk { get; set; }
        public List<ServerGrowthBookExperiment> ServerExperiments { get; set; } = new List<ServerGrowthBookExperiment>();

    }

    public class ServerGrowthBookExperiment
    {
        public string Code { get; set; }
        public string Feature { get; set; }
        public string CookieVariation { get; set; }
        public bool Active { get; set; }
        public string SdkKey { get; set; }
        public string Environment { get; set; }
        public List<ExperimentConfig> Config { get; set; } = new List<ExperimentConfig>();
    }
}
