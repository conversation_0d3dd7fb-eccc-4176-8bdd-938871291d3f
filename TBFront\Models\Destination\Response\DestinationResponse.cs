﻿using ProtoBuf;
using System.Text.Json.Serialization;

namespace TBFront.Models.Destination.Response
{
    [ProtoContract]
    public class DestinationResponse
    {
        [ProtoMember(1)]
        public string Code { get; set; } = string.Empty;

        [ProtoMember(2)]
        public string Name { get; set; } = string.Empty;

        [ProtoMember(3)]
        public List<DestinationRoute> National { get; set; } = new List<DestinationRoute>();

        [ProtoMember(4)]
        public List<DestinationRoute> International { get; set; } = new List<DestinationRoute>();
        
        [ProtoMember(5)]
        public bool IsNational{ get; set; } 

    }

    [ProtoContract]
    public class DestinationRoute
    {
        [ProtoMember(1)]
        public string Code { get; set; } = string.Empty;

        [ProtoMember(2)]
        public string Name { get; set; } = string.Empty;

        [ProtoMember(3)]
        public decimal Price { get; set; }

        [ProtoMember(4)]
        public DateTime Date { get; set; }

        [ProtoMember(5)]
        public DateTime ReturnDate { get; set; }

        [ProtoMember(6)]
        public string Airline { get; set; } = string.Empty;

        [ProtoMember(7)]
        [JsonPropertyName("hotelsUri")]
        public string HotelsUri { get; set; } = string.Empty;

        [ProtoMember(8)]
        [JsonPropertyName("campaignToken")]
        public string CampaignToken { get; set; } = string.Empty;
    }
}
