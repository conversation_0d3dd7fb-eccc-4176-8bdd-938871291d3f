import * as dayjs from 'dayjs';
import { defineStore } from "pinia";
import { oneWay, roundTrip } from '../../constants';
import { algoliaClickSuggestion } from "../../utils/analytics/algolia";
import { getAirportTypeAlgolia, getTripModeByFlightType } from "../../utils/helpers/data";
import { getUrlWithQueryString } from "../../utils/helpers/queryString";
import { StorageService } from "../../utils/helpers/storage";
import { __ } from "../../utils/helpers/translate";
import { useUserSelectionStore } from "./user-selection";

const bookerSettings = window.__pt.settings.site.booker;
const siteSettings = window.__pt.settings.site;




export const useBookerStore = defineStore('booker', {
	state: () => ({
		flightType: roundTrip,
		startingAirport: '',
		returningAirport: '',
		startingAirportText: '',
		returningAirportText: '',
		dates: {
			start: null,
			end: null
		},
		paxes: {
			adults: bookerSettings.adults.default,
			kids: bookerSettings.kids.default,
			ageKids: []
		},
		selectedSuggestion: {
			startingAirport: {
				"id": "7983",
				"displayText": __('booker.originSimpleName'),
				"displayDestinationHtml": "Colombia",
				"displayHtml": __('booker.originDisplayHtml'),
				"cityFullName": __('booker.originDefaultName'),
				"type": 0,
				"isActive": true,
				"code": "BOG",
				"country": "CO",
				"positions": 1,
				"items": {
					"hotel": 0,
					"objectID": "",
					"queryID": ""
				}
			},
			returningAirport: null
		},
		history: {
			from: [],
			to: []
		},
		recentResearches: {
			from: [],
			to: []
		},
		errorPaxes: false,
		extraInformation: {}
	}),
	getters: {
		getExtraInformation: (state) => {
			return state.extraInformation;
		},
	},
	actions: {
		changePaxes(type, qty) {
			const totalPaxes = this.paxes["adults"] + this.paxes["kids"];
			let pax = this.paxes[type];
			let paxConfig = bookerSettings[type];
			if (pax > paxConfig.min && qty < 0 && totalPaxes <= 9) {
				this.paxes[type] += qty;
				this.errorPaxes = false;
			}
			else if (pax < paxConfig.max && qty > 0 && totalPaxes < 9) {
				this.paxes[type] += qty;
				this.errorPaxes = false;
			} else if (totalPaxes === 9) {
				this.errorPaxes = true;
			}

			if (type === 'kids') {
				this.paxes.ageKids = this.paxes.ageKids.slice(0, this.paxes[type]);
			}
		},
		removeAirportSelected(type) {
			this[type] = '';
		},
		changeSelectedSuggestion(type, value, position) {
			this.selectedSuggestion[type] = value;
			if (position && value && value.items) {
				algoliaClickSuggestion(value.items, position, getAirportTypeAlgolia(type), window.__pt.settings.site.algoliaFlightIndex);
			}
		},
		checkIfExist(history, code) {
			return history.some(e => {
				if (e.code === code) {
					return true;
				}
				return false;
			})
		},
		addPlacesToHistory(userSelectionStore) {
			let objectSplitedFrom = null;
			let objectSplitedReturn = null;

			const startingCode = this.selectedSuggestion.startingAirport.code ?? this.selectedSuggestion.startingAirport.airportCode
			const returningCode = this.selectedSuggestion.returningAirport.code ?? this.selectedSuggestion.returningAirport.airportCode;


			if (typeof this.selectedSuggestion.startingAirport.city != "undefined") {
				objectSplitedFrom = this.restructureString(this.selectedSuggestion.startingAirport);
			}

			if (typeof this.selectedSuggestion.returningAirport.city != "undefined") {
				objectSplitedReturn = this.restructureString(this.selectedSuggestion.returningAirport);
			}
			
			const objectResultFrom = objectSplitedFrom === null ? this.selectedSuggestion.startingAirport : objectSplitedFrom
			
			if (this.selectedSuggestion && this.selectedSuggestion.startingAirport && !this.checkIfExist(this.history.from, startingCode)) {
				this.history.from.unshift(objectResultFrom);
			}

			if (this.selectedSuggestion && this.selectedSuggestion.startingAirport) {
				this.recentResearches.from.unshift(objectResultFrom);
			}

			const objectResultTo = objectSplitedReturn === null ? this.selectedSuggestion.returningAirport : objectSplitedReturn

			this.history.from = this.history.from.slice(0, 4);
			this.recentResearches.from = this.recentResearches.from.slice(0, 4);

			const userResearchInfo = {
				...objectResultTo,
				...userSelectionStore.getSearchParams,
			}

			delete userResearchInfo.site;

			if (this.selectedSuggestion && this.selectedSuggestion.returningAirport && !this.checkIfExist(this.history.to, returningCode)) {
				this.history.to.unshift(userResearchInfo);
			}

			if (this.selectedSuggestion && this.selectedSuggestion.returningAirport) {
				this.recentResearches.to.unshift(userResearchInfo);
			}
			this.history.to = this.history.to.slice(0, 4);
			this.recentResearches.to = this.recentResearches.to.slice(0, 4);
			StorageService.set(bookerSettings.historyStorageKey, this.history);
			StorageService.set(bookerSettings.historyResearchStorageKey, this.recentResearches);

		},
		clearPlaceInput(type) {
			if (type === 'startingAirport') {
				this.startingAirport = '';
				this.startingAirportText = '';
				this.selectedSuggestion.startingAirport = null;
			} else if ('returningAirport') {
				this.returningAirport = '';
				this.returningAirportText = '';
				this.selectedSuggestion.returningAirport = null;
			}
		},
		initBooker(userSelection) {
			this.paxes.adults = userSelection.adults;

			if (userSelection.ageKids) {
				this.paxes.kids = userSelection.ageKids.length;
				this.paxes.ageKids = userSelection.ageKids;
			}

			this.startingAirport = userSelection.startingFromAirport;
			this.returningAirport = userSelection.returningFromAirport;
			this.flightType = userSelection.flightType;
			this.dates.start = new dayjs(userSelection.startingFromDateTime).toDate();
			if (userSelection.flightType === roundTrip) {
				this.dates.end = new dayjs(userSelection.returningFromDateTime).toDate();
			} else {
				this.dates.end = new dayjs(userSelection.startingFromDateTime).toDate();
			}
			this.startingAirportText = `${userSelection.startingAirportDetail.cityFullName}`;
			this.returningAirportText = `${userSelection.returningAirportDetail.cityFullName}`;

			if (userSelection.returningAirportDetail != null) {
				this.selectedSuggestion.returningAirport = userSelection.returningAirportDetail;
			}

			if (userSelection.startingAirportDetail != null) {
				this.selectedSuggestion.startingAirport = userSelection.startingAirportDetail;
			}
		},
		getUserSelection() {
			const userSelection = {
				adults: this.paxes.adults,
				ageKids: [...this.paxes.ageKids],
				startingFromAirport: this.startingAirport,
				returningFromAirport: this.returningAirport,
				flightType: this.flightType,
				checkIn: new dayjs(this.dates.start).format("YYYY-MM-DD"),
				checkOut: new dayjs(this.dates.end).format("YYYY-MM-DD"),
				startingFromDateTime: new dayjs(this.dates.start).format("YYYY-MM-DD"),
				returningFromDateTime: new dayjs(this.dates.end).format("YYYY-MM-DD"),
				origin: this.startingAirportText,
				destination: this.returningAirportText,
				tripMode: getTripModeByFlightType(this.flightType),
				countryflight: this.selectedSuggestion.startingAirport?.country ?? StorageService.get("countryflight"),
				countryflightreturn: this.selectedSuggestion.returningAirport?.country ?? StorageService.get("countryflightreturn"),
			};

			return userSelection;
		},
		submitForm() {
			const userSelectionStore = useUserSelectionStore();
			let refreshPage = false;

			if (this.startingAirport != userSelectionStore.startingFromAirport || this.returningAirport != userSelectionStore.returningFromAirport) {
				refreshPage = true;
			}

			userSelectionStore.changeFilters([]);
			userSelectionStore.$patch(this.getUserSelection());

			StorageService.set("countryflightreturn", this.getUserSelection().countryflightreturn)
			StorageService.set("countryflight", this.getUserSelection().countryflight)


			this.addPlacesToHistory(userSelectionStore);
			let queryString = userSelectionStore.getNewQueryString;
			if (window.__pt.data.airlineCode) {
				queryString.airlineCode = window.__pt.data.airlineCode
			}

			const paramsQuery = getUrlWithQueryString(userSelectionStore.getNewQueryString);

			let params = userSelectionStore.getSearchParams;
			if (window.__pt.data.airlineCode) {
				params.carrierCode = window.__pt.data.airlineCode
			}
			const { cultureCode, pathFlight } = window.__pt.cultureData;
			let fullUrl = `${window.__pt.settings.site.siteUrl}/${cultureCode}${pathFlight}${paramsQuery}`;

			window.location.href = fullUrl;
			
		},
		setTypeFlight(val) {
			this.flightType = val;
		},
		setExtraInformation(val, view) {
			this.extraInformation[view] = val;
		},
		restructureString(val) {
			const airportCode = `(${val.airportCode})`
			const removeCodeAirport = val.cityFullName.includes(",") ? val.cityFullName.replace(airportCode, "").split(",") : val.cityFullName.replace(airportCode, "").split("-");
			let objectSplited = {
				"displayText": removeCodeAirport[0],
				"displayDestinationHtml": removeCodeAirport[1],
				"displayHtml": removeCodeAirport[0],
				"code": val.airportCode,
				"country": val.countryISO,
				"cityFullName": val.cityFullName,
			}
			return objectSplited;
		},
		setStartingAirportText() {
			this.startingAirportText = "";
			this.startingAirport = "";
		},
		setReturningAirportText() {
			this.returningAirportText = "";
			this.returningAirport = "";
		}
	}
});	