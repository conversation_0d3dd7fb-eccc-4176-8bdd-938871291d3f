﻿using System.Text.Json.Serialization;

namespace TBFront.Models.Meta.Schema
{
    public class Product
    {
        [JsonPropertyName("@context")]
        public string? Context { get; set; }

        [JsonPropertyName("@type")]
        public string? Type { get; set; }

        [JsonPropertyName("image")]
        public string? Image { get; set; }

        [JsonPropertyName("name")]
        public string? Name { get; set; }

        [JsonPropertyName("sku")]
        public int Sku { get; set; }

        [JsonPropertyName("mpn")]
        public int Mpn { get; set; }

        [JsonPropertyName("description")]
        public string? Description { get; set; }

        [JsonPropertyName("position")]
        public int Position { get; set; }

        [JsonPropertyName("url")]
        public string? Url { get; set; }

        [JsonPropertyName("brand")]
        public Brand? Brand { get; set; }

        [JsonPropertyName("aggregateRating")]
        public AggregateRating? AggregateRating { get; set; }

        [JsonPropertyName("offers")]
        public Offer? Offers { get; set; }

        [Json<PERSON>ropertyName("review")]
        public Review? Review { get; set; }
        
        public Product()
        {
            Brand = new Brand();
            AggregateRating = new AggregateRating();
            Offers = new Offer();
            Review = new Review();

        }

    }
}
