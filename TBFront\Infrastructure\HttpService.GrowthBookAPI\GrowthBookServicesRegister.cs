﻿using TBFront.Interfaces;
using TBFront.Infrastructure.HttpService.GrowthBookAPI.Dtos;

namespace TBFront.Infrastructure.HttpService.GrowthBookAPI
{
    public static class GrowthBookServicesRegister
    {
        public static void AddGrowthBookServicesRegister(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddHttpClient<GrowthBookService>("");

            services.AddSingleton(s => configuration.GetSection("GrowthBookConfiguration").Get<GrowthBookConfiguration>());

            services.AddSingleton<IGrowthBookService, GrowthBookService>();
        }
    }
}
