@using TBFront.Helpers;
@using Microsoft.Extensions.Options;
@using TBFront.Options;
@using TBFront.Models.ContentDeliveryNetwork.Seo;
@using TBFront.Models.Meta.Metatags;
@using TBFront.Models.Response;
@using System.Web;
@using TBFront.Types;
@inject ViewHelper viewHelper
@inject IOptions<SettingsOptions> settingOptions
@inject StaticHelper staticHelper
@{
	var seoContent = ViewData["seoContents"] as SeoResponse;
	var title = ViewData["Title"] as string;
}
<seo-questions
	:faqs='@Json.Serialize(seoContent.Seo.Faqs)'
	:paragraphs='@Json.Serialize(seoContent.Seo.Meta.Paragraphs)'
	question-title="@viewHelper.Localizer("seo_question", title)"
	info-title="@viewHelper.Localizer("seo_info", title)">
</seo-questions>