import { __ } from './translate';
import { easepick } from '@easepick/bundle';
import { createPinia } from 'pinia';
import { createApp } from 'vue'

import { useBookerStore } from '../../package/stores/booker';
import { usePromotionStore } from '../../package/stores/promotion';
import { oneWay, roundTrip } from '../../constants.js';
import { useCalendarStore } from '../../package/stores/calendar';

const bookerStore = useBookerStore();
const promotionStore = usePromotionStore();
const calendarStore = useCalendarStore();

let _picker = null;
let _datePicker = {};
let _picker2 = null;
let _datePicker2 = {};

export const deleteDatepicker = () => {
    if (_picker) {
        _picker.destroy();
    }

    if (_picker2) {
        _picker2.destroy();
    }
};

export const datepicker = (landing) => {
    deleteDatepicker();
    createDatepicker(landing);
};

export const createDatepicker = (landing) => {
    let plugins = ["LockPlugin"]
    let config = window.__pt.settings.site;
    let styles_uri = `${config.siteUrl}${config.assets}/${config.siteName}/css/booker.css?id=${+(new Date())}`;
    if (landing === "home") {
        if (bookerStore.flightType === roundTrip) {
            plugins.push("RangePlugin");
        }
        _datePicker = {
            element: document.getElementById("calendar-checkIn-flight"),
            css: [
                styles_uri
            ],
            zIndex: 10,
            screenTop: 0,
            grid: 2,
            calendars: 2,
            autoApply: true,
            readonly: true,
            lang: window.__pt.cultureData.cultureCode,
            date: bookerStore.dates.start,
            format: "DD MMM YYYY",
            plugins: plugins,
            LockPlugin: {
                minDate: new Date(),
                minDays: 1,
            },
            RangePlugin: {
                tooltip: true,
                startDate: bookerStore.dates.start,
                endDate: bookerStore.dates.end,
                tooltipNumber(num) {
                    return num;
                },
                locale: {
                    one: __("booker.day"),
                    other: __("booker.days"),
                }
            },
            setup(picker) {
                picker.on('select', (e) => {
                    const { start, end, date } = e.detail;

                    if (bookerStore.flightType === roundTrip) {
                        bookerStore.dates.start = start;
                        bookerStore.dates.end = end;
                    } else {
                        bookerStore.dates.start = date.toJSDate();
                        bookerStore.dates.end = date.toJSDate();
                    }
                });
            },
        };
    } else if (landing === "promotions") {
        if (promotionStore.tripMode === 1) {
            plugins.push("RangePlugin");
        }
        _datePicker = {
            element: document.getElementById("calendar-checkIn-flight2"),
            css: [
                styles_uri
            ],
            zIndex: 10,
            screenTop: 0,
            grid: 2,
            calendars: 2,
            autoApply: true,
            readonly: true,
            lang: window.__pt.cultureData.cultureCode,
            date: promotionStore.startingFromDepartureDateTime,
            format: "DD MMM YYYY",
            plugins: plugins,
            LockPlugin: {
                minDate: new Date(),
                minDays: 1,
            },
            RangePlugin: {
                tooltip: true,
                startDate: promotionStore.startingFromDepartureDateTime,
                // endDate: promotionStore.returningFromDateTime,
                tooltipNumber(num) {
                    return num;
                },
                locale: {
                    one: __("booker.day"),
                    other: __("booker.days"),
                }
            },
            setup(picker) {
                picker.on('select', (e) => {
                    const { start, end, date } = e.detail;
                    if (promotionStore.tripMode === 1) {
                        promotionStore.startingFromDateTime = start;
                        // promotionStore.returningFromDateTime = end;
                    } else {
                        promotionStore.startingFromDepartureDateTime = date.toJSDate("YYY-MM-DD");
                        // promotionStore.returningFromDateTime = date.toJSDate("YYY-MM-DD");
                    }
                    promotionStore.changeCalendarSelected = date;
                });
            },
        };

        _datePicker2 = {
            element: document.getElementById("calendar-checkIn-flight1"),
            css: [
                styles_uri
            ],
            zIndex: 10,
            screenTop: 0,
            grid: 2,
            calendars: 2,
            autoApply: true,
            readonly: true,
            lang: window.__pt.cultureData.cultureCode,
            date: promotionStore.startingFromDateTime,
            format: "DD MMM YYYY",
            plugins: plugins,
            LockPlugin: {
                minDate: new Date(),
                minDays: 1,
            },
            RangePlugin: {
                tooltip: true,
                startDate: promotionStore.startingFromDateTime,
                endDate: promotionStore.returningFromDateTime,
                tooltipNumber(num) {
                    return num;
                },
                locale: {
                    one: __("booker.day"),
                    other: __("booker.days"),
                }
            },
            setup(picker) {
                picker.on('select', (e) => {
                    const { start, end, date } = e.detail;
                    if (promotionStore.tripMode === 1) {
                        promotionStore.startingFromDateTime = start;
                        promotionStore.returningFromDateTime = end;
                    } else {
                        promotionStore.startingFromDateTime = date.toJSDate("YYY-MM-DD");
                        promotionStore.returningFromDateTime = date.toJSDate("YYY-MM-DD");
                    }
                    promotionStore.changeCalendarSelected = start;
                });
            },
        };
        _picker2 = new easepick.create(_datePicker2);
    } else if (landing === "groups") {
        plugins.push("RangePlugin");

        _datePicker = {
            element: document.getElementById("calendar"),
            css: [
                styles_uri
            ],
            zIndex: 10,
            screenTop: 0,
            grid: 2,
            calendars: 2,
            autoApply: true,
            readonly: true,
            lang: window.__pt.cultureData.cultureCode,
            date: new Date(),
            format: "DD MMMM YYYY",
            plugins: plugins,
            LockPlugin: {
                minDate: new Date(),
                minDays: 1,
            },
            RangePlugin: {
                tooltip: true,
                tooltipNumber(num) {
                    return num;
                },
                locale: {
                    one: __("booker.day"),
                    other: __("booker.days"),
                }
            },
            setup(picker) {
                picker.on('select', (e) => {
                    const { start, end } = e.detail;
                    calendarStore.setDates(start.toJSDate(), end.toJSDate());
                });

                picker.on('view', (event) => {
                    const { view, target } = event.detail;
                    if (view === 'Main') {
                        target.parentElement.classList.add("group-calendar");
                    }
                });
            },
        };
    }
    _picker = new easepick.create(_datePicker);

} 