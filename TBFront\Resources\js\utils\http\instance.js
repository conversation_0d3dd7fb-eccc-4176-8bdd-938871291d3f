import axios from 'axios';

export const api = axios.create({
    //baseURL: window.__pt.settings.site.bff.uri,
    headers: {
        //'Cache-Control': 'no-cache'
    },
    //timeout: window.__pt.settings.site.bff.timeOut
});

export const authenticationApi = axios.create({
    //baseURL: window.__pt.settings.site.auth.uri,
    headers: {
        'Cache-Control': 'no-cache'
    },
    //timeout: window.__pt.settings.site.auth.timeOut
});

export const axiosProvider = () => axios;
