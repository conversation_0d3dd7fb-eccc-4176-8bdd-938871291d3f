﻿using Microsoft.Extensions.Options;
using System.Net.Http.Headers;
using System.Net.Mime;
using System.Text;
using System.Text.Json;
using TBFront.Application.Implementations;
using TBFront.Infrastructure.HttpService.FlightQuote.Dtos;
using TBFront.Interfaces;
using TBFront.Models.Flight.CreateBooking;
using TBFront.Models.Flight.Quote;
using TBFront.Models.Flight.Revalidate;
using TBFront.Models.Flight.Summary;
using TBFront.Models.Flight.Upsell;
using TBFront.Options;

namespace TBFront.Infrastructure.HttpService.FlightQuote
{
	public class FlightQuoteService : IFlightService
	{
		private readonly HttpClient _httpClient;
		private readonly FlightQuoteConfiguration _configuration;
		private readonly static JsonSerializerOptions _jsonSerializerOptions = new() { PropertyNameCaseInsensitive = true };
		private readonly ICacheService _cacheService;
		private readonly IHttpClientFactory _clientFactory;
		private readonly SettingsOptions _options;
		private readonly ILogger<FlightQuoteService> _logger;

		public FlightQuoteService(HttpClient httpClient, FlightQuoteConfiguration configuration, ICacheService cacheService, IHttpClientFactory clientFactory, IOptions<SettingsOptions> options, ILogger<FlightQuoteService> logger)
		{
			_configuration = configuration;
			_httpClient = httpClient;
			_httpClient.DefaultRequestHeaders.Add("Accept", MediaTypeNames.Application.Json);
			_httpClient.BaseAddress = new Uri(_configuration.UriBase);
			_cacheService = cacheService;
			_clientFactory = clientFactory;
			_options = options.Value;
			_logger = logger;
		}

		public async Task<FlightQuoteResponse> QueryAsync(FlightQuoteRequest request, CancellationToken ct)
		{

			var key = request.KeyRedis;
			var response = await _cacheService.RedisGetCache<FlightQuoteResponse>(key, ct); ;

			if (response != null)
			{
				return response;
			}

			var uriService = $"{_configuration.UriTravelItinerary}";
			var payload = JsonSerializer.Serialize(request);
			var body = new StringContent(payload);
			body.Headers.ContentType = new MediaTypeHeaderValue("application/json");
			var httpResponseMessage = await _httpClient.PostAsync(uriService, body);

			using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync();
			response = await JsonSerializer.DeserializeAsync<FlightQuoteResponse>(contentStream, _jsonSerializerOptions, ct);

			if (response is not null && response.Response is not null)
			{
				response.Success = true;
				_cacheService.RedisSetCache(key, response);
			}

			return response;

		}

		public async Task<FlightUpsellResponse> QueryAsync(FlightUpsellRequest request, CancellationToken ct)
		{
			var key = "upsell";

			var response = await _cacheService.RedisGetCache<FlightUpsellResponse>(key, ct); ;

			if (response != null)
			{
				return response;
			}

			var uriService = $"{_configuration.UriUpsell}";
			var payload = JsonSerializer.Serialize(request);
			var body = new StringContent(payload);
			body.Headers.ContentType = new MediaTypeHeaderValue("application/json");
			var httpResponseMessage = await _httpClient.PostAsync(uriService, body);

			using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync();
			response = await JsonSerializer.DeserializeAsync<FlightUpsellResponse>(contentStream, _jsonSerializerOptions, ct);

			if (response is not null && response.Response is not null)
			{
				response.Success = true;
				_cacheService.RedisSetCache(key, response);
			}

			return response;
		}

		public async Task<RevalidateResponse> QueryAsync(RevalidateRequest request, CancellationToken ct)
		{

			var uriService = $"{_configuration.UriRevalidate}";
			var payload = JsonSerializer.Serialize(request);
			var body = new StringContent(payload);
			body.Headers.ContentType = new MediaTypeHeaderValue("application/json");
			var httpResponseMessage = await _httpClient.PostAsync(uriService, body);

			using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync();
			var response = await JsonSerializer.DeserializeAsync<RevalidateResponse>(contentStream, _jsonSerializerOptions, ct);

			return response;
		}

		public async Task<CreateBookingResponse> QueryAsync(CreateBookingRequest request, CancellationToken ct)
		{

			var httpClient = _clientFactory.CreateClient();
			var uriService = $"{_configuration.UriApiPTH}{_configuration.UriCreateBooking}";
			var payload = JsonSerializer.Serialize(request);

			var body = new StringContent(payload, Encoding.UTF8, "application/json");


			httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", GetAuthBasic(_configuration.User, _configuration.Password));

			var httpResponseMessage = await httpClient.PostAsync(uriService, body, ct);
			using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync(ct);
			var response = await JsonSerializer.DeserializeAsync<CreateBookingResponse>(contentStream, _jsonSerializerOptions, ct);

			return response;
		}

		public async Task<SummaryResponse> QueryAsync(SummaryRequest request, CancellationToken ct)
		{
			var summaryResponse = new SummaryResponse();
			try
			{
				var httpClient = _clientFactory.CreateClient();

				var queryParameters = $"IdReservation={request.IdReservation}&CustomerEmail={request.CustomerEmail}&Channel={request.Channel}";
				var uriService = $"{_configuration.UriApiPTH}{_configuration.UriSummary}?{queryParameters}";

				httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", GetAuthBasic(_configuration.UserSummary, _configuration.PasswordSummary));

				var httpResponseMessage = await httpClient.GetAsync(uriService, ct);
				using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync(ct);
				summaryResponse = await JsonSerializer.DeserializeAsync<SummaryResponse>(contentStream, _jsonSerializerOptions, ct);

				if (!string.IsNullOrEmpty(summaryResponse?.Id))
				{
					summaryResponse.Status = true;
				}
			}
			catch (Exception e)
			{
				_logger.LogError($"[Error] SummaryResponse {e.Message}: Summary Request: {JsonSerializer.Serialize(request)} - Summary Response: {JsonSerializer.Serialize(summaryResponse)} ");
			}

			return summaryResponse ?? new SummaryResponse();
		}



		private static string GetAuthBasic(string username, string password)
		{
			var svcCredentials = Convert.ToBase64String(ASCIIEncoding.ASCII.GetBytes(username + ":" + password));
			return $"{svcCredentials}";
		}

	}
}
