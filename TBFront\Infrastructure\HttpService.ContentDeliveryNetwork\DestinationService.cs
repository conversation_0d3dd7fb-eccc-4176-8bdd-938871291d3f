﻿using System.Net.Mime;
using System.Text.Json;
using TBFront.Infrastructure.HttpService.ContentDeliveryNetwork.Dtos;
using TBFront.Interfaces;
using TBFront.Models.Destination.Request;
using TBFront.Models.Destination.Response;


namespace TBFront.Infrastructure.HttpService.ContentDeliveryNetwork
{
    public class DestinationService : IQueryHandlerAsync<DestinationRequest, List<DestinationResponse>>
    {

        private readonly HttpClient _httpClient;
        private readonly DestinationConfiguration _configuration;
        private readonly ICacheService _cacheService;
        private readonly static JsonSerializerOptions _jsonSerializerOptions = new() { PropertyNameCaseInsensitive = true };
        public DestinationService(HttpClient httpClient, DestinationConfiguration configuration, ICacheService cacheService)
        {
            _cacheService = cacheService;
            _configuration = configuration;
            _httpClient = httpClient;
            _httpClient.DefaultRequestHeaders.Add("Accept", MediaTypeNames.Application.Json);
            _httpClient.BaseAddress = new Uri(_configuration.Uri);
        }



        public async Task<List<DestinationResponse>> QueryAsync(DestinationRequest request, CancellationToken ct)
        {

            var key = $"destination-{request.Version}";
            var destinationResponse = await _cacheService.GetCache<List<DestinationResponse>>(key, ct);

            if (destinationResponse != null && destinationResponse.Count > 0)
            {
                return destinationResponse;
            }

            var queryParameters = $"?&version={request.Version}";

            var url = (request.Version == "origin" ? _configuration.OriginPath : _configuration.DestinationPath) + queryParameters;

            var httpResponseMessage = await _httpClient.GetAsync(url, ct);

            if (httpResponseMessage.IsSuccessStatusCode)
            {
                destinationResponse = await httpResponseMessage.Content.ReadFromJsonAsync<List<DestinationResponse>>(_jsonSerializerOptions, ct);

                if (destinationResponse is not null && destinationResponse.Count > 0)
                {
                    _cacheService.SetCache(key, destinationResponse);
                }

            }


            return destinationResponse;
        }
    }
}
