﻿using Microsoft.AspNetCore.DataProtection.KeyManagement;
using System.Net.Http.Headers;
using System.Net.Mime;
using System.Text.Json;
using TBFront.Infrastructure.HttpService.Itinerary.Dtos;
using TBFront.Interfaces;
using TBFront.Models.Auth;
using TBFront.Models.BookingItinerary;
using TBFront.Models.MKTCollection.Response;

namespace TBFront.Infrastructure.HttpService.PaymentGateway
{
    public class ItineraryService : IQueryHandlerAsync<ItineraryRequest, ItineraryResponse>
    {

        private readonly HttpClient _httpClient;
        private readonly ItineraryConfiguration _configuration;
        private readonly AuthConfiguration _auth;
        private readonly IHttpClientFactory _clientFactory;
        private readonly ICacheService _cacheService;



        private readonly static JsonSerializerOptions _jsonSerializerOptions = new() { PropertyNameCaseInsensitive = true };
        public ItineraryService(HttpClient httpClient, ItineraryConfiguration configuration, AuthConfiguration authConfiguration, IHttpClientFactory clientFactory, ICacheService cacheService)
        {
            _configuration = configuration;
            _httpClient = httpClient;
            _httpClient.DefaultRequestHeaders.Add("Accept", MediaTypeNames.Application.Json);
            _httpClient.BaseAddress = new Uri(_configuration.Uri);
            _clientFactory = clientFactory;
            _cacheService = cacheService;
            _auth = authConfiguration;
        }



        public async Task<ItineraryResponse> QueryAsync(ItineraryRequest request, CancellationToken ct)
        {

            var uriService = $"{_configuration.PathItinerary}";

            var requestItinerary = GetQueryItynerary(request.Id, request.Email);

            var token = await AuthToken(ct);

            var payload = JsonSerializer.Serialize(requestItinerary);

            var body = new StringContent(payload);

            body.Headers.ContentType = new MediaTypeHeaderValue("application/json");

            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token.AuthenticationResult.Token);

            var httpResponseMessage = await _httpClient.PostAsync(uriService, body, ct);

            using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync(ct);

            var response = await JsonSerializer.DeserializeAsync<ItineraryResponse>(contentStream, _jsonSerializerOptions, ct);

            return response;
        }

        private AuthRequest GetAuthTokenParams()
        {
            var data = new AuthRequest
            {
                ClientId = _auth.ClientId,
                ClientSecret = _auth.ClientSecret,
                Organization = "PTH",
                GrantType = "client_credential"
            };

            return data;
        }
        private async Task<AuthResponse> AuthToken(CancellationToken ct)
        {

            var key = "Auth_Token";

            var response = await _cacheService.RedisGetCache<AuthResponse>(key, ct);

            if (response is null)
            {
                var httpClient = _clientFactory.CreateClient();

                var request = GetAuthTokenParams();

                var body = new StringContent(JsonSerializer.Serialize(request));
                body.Headers.ContentType = new MediaTypeHeaderValue("application/json");

                var httpResponseMessage = await httpClient.PostAsync($"{_auth.AuthUrl}", body, ct);

                using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync(ct);

                response = await JsonSerializer.DeserializeAsync<AuthResponse>(contentStream, _jsonSerializerOptions, ct);

                if (response is not null && response.AuthenticationResult is not null && !string.IsNullOrEmpty(response.AuthenticationResult.Token))
                {
                    _cacheService.RedisSetCache(key, response);
                }
            }
           

            return response;
        }

        private static ItineraryRequest GetQueryItynerary(string id, string email)
        {
            var itineraryRequest = new ItineraryRequest();

            var query = "{ travelItinerary(bookingId:{id},customerEmail:\"{email}\") { bookingId organizationId isQuote customerName customerFirstName customerLastName customerEmail correlationId createdDate minServiceDate channelId branchId tags currency bookingServices{ serviceId description serviceType adults kids confirmationCode startDate endDate serviceCarrierName serviceCarrierCode serviceProviderId serviceCarrierDescription isCancelled specialRequest mealPlan cancellationDate isOnRequest mealPlanCode collectType startHour endHour providerCancellationPolicies { isDefault limitDays chargePercentage chargeNights chargeAmount chargeCurrency startDate endDate } rate { taxScheme } serviceCharge { amountDiscount serviceAmountTotal serviceAmountBalance serviceAmountPaid } serviceInfo { engine serviceNumber luggage segments{ departureCode departureName departureDate arrivalCode arrivalName arrivalDate operatingCode operatingName } passengers { name seat ticketNumber identityDocument birthDate } } } bookingPayments { paymentType paymentDescription paymentNumber paymentAmount chargedAmount } } }";
            query = query.Replace("{id}", id).Replace("{email}", email);

            itineraryRequest.Query = query;

            return itineraryRequest;
        }
    }
}
