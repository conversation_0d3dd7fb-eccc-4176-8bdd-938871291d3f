﻿using Microsoft.Extensions.Options;
using System.Text.Json;
using TBFront.Application.Mappers;
using TBFront.Helpers;
using TBFront.Infrastructure.HttpService.PaymentGateway.Dtos;
using TBFront.Interfaces;
using TBFront.Models;
using TBFront.Models.Blacklist.Request;
using TBFront.Models.Flight.BLink.Request;
using TBFront.Models.Flight.BLink.Response;
using TBFront.Models.Flight.CreateBooking;
using TBFront.Models.Flight.Quote;
using TBFront.Models.Flight.Revalidate;
using TBFront.Models.Flight.Summary;
using TBFront.Models.Flight.Upsell;
using TBFront.Models.PaymentGateway;
using TBFront.Models.Request;
using TBFront.Models.Response;
using TBFront.Options;
using TBFront.Services;
using TBFront.Types;

namespace TBFront.Application.Implementations
{
    public class FlightQuoteHandler : IFlightQuoteHandler
    {
        public const string SessionKeyName = "session_id";

        private readonly ILogger<FlightQuoteHandler> _logger;
        private readonly IFlightService _flightQuoteService;
        private readonly IPaymentGatewayService _paymentGatewayService;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly SettingsOptions _options;
        private readonly HashService _hashService;
        private readonly PaymentGatewayConfiguration _paymentGatewayconfiguration;

        private readonly IBlacklistHandler _blacklistHandler;
        private readonly IBlinkService _blinkService;

        public FlightQuoteHandler(IFlightService flightQuoteService,
            IPaymentGatewayService paymentGatewayService,
            IOptions<SettingsOptions> options,
            HashService hashService,
            IHttpContextAccessor httpContextAccessor,
            ILogger<FlightQuoteHandler> logger,
            PaymentGatewayConfiguration paymentGatewayconfiguration,
            IBlacklistHandler blacklistHandler,
            IBlinkService blinkService
        )
        {
            _flightQuoteService = flightQuoteService;
            _paymentGatewayService = paymentGatewayService;
            _httpContextAccessor = httpContextAccessor;
            _options = options.Value;
            _hashService = hashService;
            _logger = logger;
            _paymentGatewayconfiguration = paymentGatewayconfiguration;
            _blacklistHandler = blacklistHandler;
            _blinkService = blinkService;
        }

        public async Task<FlightQuoteResponse> QueryAsync(FlightListRequest listRequest, CancellationToken ct)
        {
            var request = FlightQuoteMapper.MapFlightListRequest(listRequest, _options);
            request.KeyRedis = FlightQuoteMapper.Key(listRequest.Mode, listRequest.StartingFromAirport, listRequest.ReturningFromAirport, listRequest.StartingFromDateTime, listRequest.ReturningFromDateTime, listRequest.Adults, listRequest.Agekids);

            var response = await _flightQuoteService.QueryAsync(request, ct);

            var result = FlightQuoteMapper.Map(response);

            return result;
        }

        public async Task<FlightUpsellResponse> QueryAsync(FlightUpsellRequest request, CancellationToken ct)
        {
            var response = await _flightQuoteService.QueryAsync(request, ct);

            return response;
        }

        public async Task<RevalidateResponse> QueryAsync(CheckoutRevalidateRequest request, CancellationToken ct)
        {
            var revalidateRequest = RevalidateMapper.Request(request, _options);
            var response = await _flightQuoteService.QueryAsync(revalidateRequest, ct);
            response.Summary = RevalidateMapper.Revalidate(response.Response, request.IsRoundtrip, request.TotalAmount);

            return response;
        }

        public async Task<CheckoutBookingResponse> QueryAsync(CheckoutBookingRequest request, CancellationToken ct)
        {
            //inicializar variables en caso que falle, tener logs
            var response = new CheckoutBookingResponse();

            var createBookingRequest = new CreateBookingRequest();
            var blinkCreateBookingRequest = new BLinkRequest();
            var createBookingResponse = new CreateBookingResponse();
            var createBlinkResponse = new BLinkBookResponse();

            var paymentgatewayRequest = new PaymentGatewayRequest();
            var paymentgatewayResponse = new PaymentGatewayResponse();

            var summaryRequest = new SummaryRequest();
            var summaryResponse = new SummaryResponse();

            var id = "";
            var email = "";
            var sessionId = _httpContextAccessor.HttpContext.Request.Cookies[SessionKeyName];

            try
            {
                // 0. validar blacklist
                var requestBlacklist = new BlacklistRequest(request.FingerprintHash, request.Customer.Email);
                var blocked = await _blacklistHandler.QueryAsync(requestBlacklist, ct);

                if (!blocked.IsBlock)
                {
                    // 1. Crear reserva en BackVuelos
                    createBookingRequest = CreateBookingMapper.CreateBooking(_options, request, request.Customer.IpClient);
                    createBookingResponse = await _flightQuoteService.QueryAsync(createBookingRequest, ct);

                    if (string.IsNullOrEmpty(createBookingResponse.Id))
                    {
                        response.MessageInternal = createBookingResponse.ExceptionMessage;
                        throw new ArgumentException(StatusType.BOOKING_CREATE_ERROR);
                    }

                    //hashear datos del cliente
                    id = _hashService.Encrypt(createBookingResponse.Id);
                    email = _hashService.Encrypt(request.Customer.Email);


                    request.KeyValidation = request.Revalidate.QuoteTaskID;
                    request.MasterLocatorID = createBookingResponse.Id;

                    //Recuperar summary de vuelos
                    summaryRequest = SummaryMapper.Request(createBookingResponse.Id, request.Customer.Email, request.Quote.ChannelId);
                    summaryResponse = await _flightQuoteService.QueryAsync(summaryRequest, ct);

                    // 2. Crear token de PGA
                    paymentgatewayRequest = PaymentGatewayMapper.Request(request, createBookingResponse, _options, _paymentGatewayconfiguration, sessionId, id, email, summaryResponse);
                    paymentgatewayResponse = await _paymentGatewayService.QueryAsync(paymentgatewayRequest, ct);

                    // Mapear respuestas para front
                    response = CreateBookingMapper.Response(request, paymentgatewayResponse, createBookingResponse, _options, id, email, summaryResponse);
                }
                else
                {

                    blinkCreateBookingRequest = CreateBookingMapper.BookingRequestToBLinkRequest(request, _options, request.Customer.IpClient);
                    createBlinkResponse = await _blinkService.QueryAsync(blinkCreateBookingRequest, ct);
                    response.Status = StatusType.ERROR_FINGERPRINT;
                    response.BookingId = createBlinkResponse.BookResponse.MasterLocatorID;

                }

            }
            catch (Exception e)
            {
                response.Message = e.Message;
                response.Status = StatusType.ERROR;

                if (!string.IsNullOrEmpty(id))
                {
                    response.UrlRedirect = CreateBookingMapper.GetLinkVoucher(id, email, _options);
                }

                if (string.IsNullOrEmpty(response.MessageInternal))
                {
                    response.MessageInternal = e.Message;
                }

                _logger.LogError($"[Error] CheckoutBooking: {e.Message} - Main Request: {JsonSerializer.Serialize(request)}  -  Main Response {JsonSerializer.Serialize(response)}");
                _logger.LogError($"[Error] CheckoutBooking: Flights Request: {JsonSerializer.Serialize(createBookingRequest)}   -  Flights Response: {JsonSerializer.Serialize(createBookingResponse)} ");
                _logger.LogError($"[Error] CheckoutBooking: PGA Request: {JsonSerializer.Serialize(paymentgatewayRequest)}   -  PGA Response: {JsonSerializer.Serialize(paymentgatewayResponse)} ");
                _logger.LogError($"[Error] SummaryResponse: Summary Request: {JsonSerializer.Serialize(summaryRequest)}   -  Summary Response: {JsonSerializer.Serialize(summaryResponse)} ");
                _logger.LogError($"[Error] Blink: Request: {JsonSerializer.Serialize(blinkCreateBookingRequest)}   -  Blink Response: {JsonSerializer.Serialize(createBlinkResponse)} ");
                _logger.LogError($"[Error] CheckoutBooking: StackTrace: {JsonSerializer.Serialize(paymentgatewayRequest)}");

            }


            return response;
        }

        public async Task<SummaryResponse> QueryAsync(VoucherInfo request, CancellationToken ct)
        {
            var summaryRequest = SummaryMapper.Request(request.Id, request.Email, request.Channel);
            var summaryResponse = await _flightQuoteService.QueryAsync(summaryRequest, ct);

            return summaryResponse;
        }
    }
}
