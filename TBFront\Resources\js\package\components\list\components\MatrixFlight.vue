<template>
	<div class="row flight-best-rate hide-xs font-16 mx-0 mb-0">
		<section class="container rounded c-info-price my-4 d-lg-block position-relative cip-matriz">
			<div class="flight-best-rate-content position-relative row mx-0">
				<div class="flight-best-rate-item d-inline-block flight-best-rate-item-left col px-0">
					<div class="flight-best-rate-info">
						<div v-if="getIsStepTwo" class="flight-best-rate__all-flights" @click="selectOption(null, null, null)">
						</div>
						<div v-else
							class="col px-0 text-center d-flex justify-content-center align-items-center cc-h pointer py-2 header-airline" @click="selectOption(null, null, null)">
							<div class="option d-flex gap-2 i-matriz-tablet h-100">
								<div class="position-t-2 ci-txt" style="font-size: 1rem;">
									{{ isRoundtrip ? __("multiticket.price_RoundTrip_per_person") : __("multiticket.per_pax")}}
								</div>
							</div>
						</div>

						<div class="text-link col cc-h pointer bg-gray-250 py-2 text-center height border-f"
							v-for="(stop, stopKey) in getAvilableStops" :key="stopKey"
							:class="{'m-active': isFilterApplied(stopKey, null,null)}"
							@click="selectOption(stopKey, null,null)">
							<span class="option">{{ __(`filters.${stop.title}`) }}</span>
						</div>
					</div>
				</div>
				<div class="flight-best-rate-item d-inline-block col px-0" v-for="(airline, airlineKey) in getAirlines"
					:class="getNameClass(airline.col)" :key="airlineKey">
					<div class="col px-0 text-center d-flex justify-content-center align-items-center cc-h pointer py-2 header-airline"
						:class="{'m-active': isFilterApplied(null, null, airline.code)}"
						@click="selectOption(null, null, airline.code)">
						<div class="option d-flex gap-2 i-matriz-tablet h-100">
							<img v-lazy="airline.airlineLogoUrl" v-if="!imgErrors[airline.name]" :alt="airline.name"
								width="30" height="30" @error="handleImgError(airline.name)" />
							<div class="font-14 position-t-2 ci-txt">
								{{airline.name}}
							</div>
						</div>
					</div>
					<div class="bg-gray-200 overflow-hidden">
						<div class="col cc-h pointer py-2 text-center border-f height"
							v-for="(info, stopInfoKey) in airline.stops.stopsInfo" :key="stopInfoKey"
							:class="{'m-active': isFilterApplied(stopInfoKey, info, airline.code) && info.rate >= 0, 'no-selectable': info.rate <= 0}"
							@click="selectOption(stopInfoKey, info, airline.code, info.rate >= 0)">
							<span class="text-link option">
								<span v-if="info.rate >= 0">
									{{getIsStepTwo && (info.rate >= 0) ? "+" : ""}}	<CurrencyDisplay :amount="info.rate" :showCurrencyCode="true" :reduceIsoFont="true" :applyCompression="true" />
								</span>
								<span v-else>
									--
								</span>
							</span>

						</div>
					</div>
				</div>
				<div class="position-absolute matrix-control left d-none-h" id="cCtrlLeft" v-if="getCantidadMatriz > 4">
					<span name="chevronleft" class="icon icon-chevron-left font-24"
						@click="getShowMoreAirlines('left')"></span>
				</div>
				<div class="position-absolute matrix-control right" id="cCtrlRight" v-if="getCantidadMatriz > 4">
					<span name="chevronright" class="icon icon-chevron-right font-24"
						@click="getShowMoreAirlines('right')"></span>
				</div>
			</div>
			<div class="d-flex justify-content-center align-items-center loading position-absolute top-0 w-100 h-100"
				v-if="getLoading">
				<div class="lds-dual-ring"></div>
				<p class="text-uppercase font-16 fw-medium ms-2 mt-3 mb-1 loading-text">{{__('filters.searching')}}...
				</p>
			</div>
		</section>
	</div>
</template>

<script setup>
	import { ref } from 'vue';
	import { storeToRefs } from 'pinia';

	import { useFlightMatrixStore } from "../../../stores/flightMatrix";
	import { useUserSelectionStore } from '../../../stores/user-selection';
	import { useFlightStore } from '../../../stores/flight';
	import { getFilteredList } from '../../../services/ApiFlightFrontServices';
	import { cheapestFlights } from '../../../../constants';
	import CurrencyDisplay from '../../common/CurrencyDisplay.vue';
	import { List } from '../../../../utils/analytics/flightList.js';
	import { useMultiTicketStore } from '../../../stores/multiTicket';
	import { usePromotionStore } from '../../../stores/promotion';
	import { sleep } from '../../../../utils/helpers/data.js';


	const storeFlightMatrix = useFlightMatrixStore();
	const storeUserSelection = useUserSelectionStore();
	const storeFlight = useFlightStore();
	const useMultiTicket = useMultiTicketStore();
	const promotionStore = usePromotionStore();

	const { getIsStepTwo, getCheckOutDataStepOne } = storeToRefs(useMultiTicket);
	const { increment, decrement, setLoading } = storeFlightMatrix;
	const { setFlightResponses, resetFlightResponse, getStartQuoteTokens, getReturnQuoteTokens } = storeFlight;
	const { changeFilters, setFlagFilters } = storeUserSelection;
	const { getTripMode } = storeToRefs(promotionStore);


	const { getAvilableStops, getAirlines, getCantidadMatriz, getEscondido, getLoading } = storeToRefs(storeFlightMatrix);
	const { getFiltersApplied, isCheapestFlightsApplied, getFiltersAppliedArray } = storeToRefs(storeUserSelection);

	const isRoundtrip = window?.__pt?.data?.isRoundtrip;

	const getNameClassTop = () => Object.keys(getAvilableStops.value).length == 1 ? 'top-1' : 'top-2';
	const getNameClass = (index) => 'col-00' + index + (index > 4 ? ' d-none-h' : '');

	const getShowMoreAirlines = (e) => {
		var t = getCantidadMatriz.value;
		var escondido = getEscondido.value
		"right" == e ? (e = 4 + escondido, document.getElementById("cCtrlLeft").classList.remove("d-none-h"), document.querySelector(".col-00" + escondido).classList.add("d-none-h"), document.querySelector(".col-00" + e).classList.remove("d-none-h"), increment(), e == t && document.getElementById("cCtrlRight").classList.add("d-none-h")) :
			(e = 4 + --escondido, document.getElementById("cCtrlRight").classList.remove("d-none-h"), document.querySelector(".col-00" + escondido).classList.remove("d-none-h"), document.querySelector(".col-00" + e).classList.add("d-none-h"), decrement(), 1 == escondido && document.getElementById("cCtrlLeft").classList.add("d-none-h"))
	};

	const imgErrors = ref({})

	const selectOption = async (scales, rate, airlineCode, isValid = true) => {
		if (isValid) {
			let filters = [];
			setLoading(true);
			if ((airlineCode && scales == null && rate == null) || (scales && rate == null) || (rate && rate.rate != -1)) {
				if (scales == '0') {
					filters.push("nonStopsRates");
				}

				if (scales === '1' || scales === 'more') {
					filters.push("oneStopRates");
				}

				if (airlineCode) {
					filters.push(airlineCode);
				}

				if (isCheapestFlightsApplied.value) {
					filters.push(cheapestFlights);
				}

				if (getFiltersApplied.value == filters.join(',')) {
					filters = [];
				}
			} else {
				filters = [];
			}

			if (filters.length == 0 && isCheapestFlightsApplied.value) {
				filters.push(cheapestFlights);
			}
			setFlagFilters(filters.length == 0);
			changeFilters(filters);
			if (getFiltersAppliedArray.value.length > 0 && getFiltersAppliedArray.value[0] != 'cheapestFlights') {
				List.flightFilters(getFiltersAppliedArray.value);
			}
			const params = {
				token: storeFlight.getAllQuoteTokens.join(','),//(!getIsStepTwo.value ? getStartQuoteTokens.join(',') : getReturnQuoteTokens.join(',')),
				filterApplied: getFiltersApplied.value,
				site: window.__pt.settings.site.apiFlights.siteConfig,
				tripMode: getTripMode.value,
				simpleFlightQuotes: true,
				step: true,
				roundTripToken: storeFlight.getreturningQuoteToken.join(',')
			};
			if (getIsStepTwo.value) {
				params.DepartureToken = getCheckOutDataStepOne.value.summary.token;
				params.FlightQuoteId = getCheckOutDataStepOne.value.summary.fareKey;
			}
			params.allQuotes = !window.__pt.data.isNational && getTripMode.value == 1;
			const response = await getFilteredList(params);
			resetFlightResponse();
			setFlightResponses(response.response);

			setLoading(false);
		}
	};
	const IsStepTwo = () => {

		return getIsStepTwo.value;

	}

	const isFilterApplied = (scales, rate, airlineCode) => {
		let filters = [];
		if ((airlineCode && scales == null && rate == null) || (scales && rate == null) || rate.rate != -1) {
			if (scales == '0') {
				filters.push("nonStopsRates");
			}

			if (scales === '1' || scales === 'more') {
				filters.push("oneStopRates");
			}

			if (airlineCode) {
				filters.push(airlineCode);
			}

			if (isCheapestFlightsApplied.value) {
				filters.push(cheapestFlights);
			}
		};
		return filters.join(',') == getFiltersApplied.value;
	};

	const handleImgError = (img) => {
		imgErrors.value[img] = true;
	};

</script>
<style lang="scss" scoped>
	.flight-best-rate {
		background-color: #fff;
		border-radius: 12px;
		margin-bottom: 1.5rem;
		min-height: 137px;
		min-width: 100%;

		.flight-best-rate-content {
			white-space: nowrap;

			.flight-best-rate-item {
				display: inline-block;
				width: 25%;
				height: auto;
				text-align: center;
				font-size: 12px;
				vertical-align: top;
			}

			.flight-best-rate-item-left {
				height: auto;
				font-size: 11px;
				text-align: right;
				background-color: #fcfcfc;
				border-radius: 10px;
			}

			.flight-best-rate-info {
				.flight-best-rate__all-flights {
					color: #004dbf;
					padding-right: 6px;
					white-space: normal;
					cursor: pointer;
					padding-top: 3rem;
				}
			}


			.flight-best-rate__all-flights {
				color: #004dbf;
				line-height: 28px;
				cursor: pointer;

				&.best {
					font-size: 14px;
					font-weight: bold;
				}

				&.active, &.active:hover {
					color: #444;
					background-color: #fff;
					line-height: 26px;
					border: 2px solid #ffc065;
					margin-top: -1px;
					cursor: auto;
				}
			}

			.flight-best-rate__all-flights {
				&:hover {
					text-decoration: underline;
					color: #004dbf;
					background-color: #fff;
				}
			}
		}
	}

	.col {
		flex: 1 0 0%;
		font-size: 1rem;
	}

	.font-24 {
		font-size: 24px !important
	}

	.pointer {
		cursor: pointer
	}

	.bg-gray-200 {
		background-color: #f5f5f5
	}

	.bg-gray-250 {
		background-color: #e2e8f0
	}

	@media (max-width: 767px) {
		.hide-xs {
			display: none !important
		}
	}

	.position-t-2 {
		position: relative;
		top: 2px
	}

	.c-info-price {
		border: 1px solid #003b98;
		border-radius: 6px;
		padding: 0;
	}

	.c-info-price .cc-h:hover {
		border: 1px solid #2196f3;
		cursor: pointer
	}

	.top-1 {
		top: 14.1rem;
	}

	.top-2 {
		top: 15.6rem;
	}

	.d-none-h {
		display: none !important
	}

	.c-info-price .cc-h {
		border: 1px solid #fff;
	}
	.cip-matriz.c-info-price .cc-h {
		border: 1px solid #fff !important;
	}

	.cip-matriz.c-info-price .cc-h.m-active {
		border: 1px solid #003b98 !important;
		position: relative;
		top: 1px;
		width: 100%;
	}

	.overflow-hidden {
		overflow: hidden
	}

	.height {
		height: 2.8rem;
	}

    .color-price {
        color: #2196f3;
        vertical-align: sub;
    }

	.matrix-control {
		bottom: 0;
		margin: auto;
		top: 35%;
		background: white;
		border: 1px solid #c4c4c4;
		border-radius: 50%;
		width: 30px;
		height: 30px;
		text-align: center;
		cursor: pointer;

		&.right {
			right: -15px;
			.icon {
				position: relative;
				right: 12px;
			}
		}

		&.left {
			left: -15px;
			.icon {
				position: relative;
				right: 12px;
			}
		}
	}

	.m-active {
		border: 1px solid #003b98 !important;
		position: relative;
		width: 100%;

		.option {
			&:after {
				content: "\e91b";
				position: absolute;
				color: #003b98;
				font-family: icomoon !important;
				right: 9px;
			}
		}
	}

	.loading {
		background: rgba(255,255,255, .7);
	}

	.lds-dual-ring {
		opacity: 1 !important
	}

	.lds-dual-ring:after {
		content: " ";
		display: block;
		width: 40px;
		height: 40px;
		border-radius: 50%;
		border: 6px solid #004dbf;
		border-color: #004dbf transparent #004dbf transparent;
		animation: lds-dual-ring 1.2s linear infinite
	}

	@keyframes lds-dual-ring {
		0% {
			transform: rotate(0)
		}

		100% {
			transform: rotate(360deg)
		}
	}

	.no-selectable {
		cursor: inherit !important;
		border: none !important;
	}
	        .header-airline {
                min-height: 48px;
        }
</style>