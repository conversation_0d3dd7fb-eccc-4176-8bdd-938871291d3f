﻿using System.Text.Json.Serialization;

namespace TBFront.Models.Meta.Schema
{
    public class SchemaHotel
    {
        [JsonPropertyName("@context")]
        public string Context { get; set; } 

        [JsonPropertyName("@type")]
        public string Type { get; set; } 

        [JsonPropertyName("name")]
        public string? Name { get; set; }

        [JsonPropertyName("image")]
        public string? Image { get; set; }

        [JsonPropertyName("address")]
        public PostalAddress? PostalAddress { get; set; }

        [JsonPropertyName("description")]
        public string? Description { get; set; }

        [JsonPropertyName("geo")]
        public GeoCoordinates? GeoCoordinates { get; set; }

        [JsonPropertyName("starRating")]
        public Rating? Rating { get; set; }

        [JsonPropertyName("priceRange")]
        public string? PriceRange { get; set; }

        [JsonPropertyName("aggregateRating")]
        public AggregateRating? AggregateRating { get; set; }

        

        public SchemaHotel()
        {          
            AggregateRating = new AggregateRating();
            PostalAddress = new PostalAddress();
            GeoCoordinates = new GeoCoordinates();
            Rating = new Rating();
           
        }

    }
}
