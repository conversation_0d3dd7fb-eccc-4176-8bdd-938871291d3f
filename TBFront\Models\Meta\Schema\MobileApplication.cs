﻿using System.Text.Json.Serialization;

namespace TBFront.Models.Meta.Schema
{
    public class MobileApplication
    {
        [JsonPropertyName("@context")]
        public string Context { get; set; } = string.Empty;

        [JsonPropertyName("@type")]
        public string Type { get; set; } = string.Empty;

        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        [JsonPropertyName("operatingSystem")]
        public string OperatingSystema { get; set; } = string.Empty;

        [JsonPropertyName("fileSize")]
        public string FileSize { get; set; } = string.Empty;

        [JsonPropertyName("applicationCategory")]
        public string ApplicationCategory { get; set; } = string.Empty;

        [JsonPropertyName("description")]
        public string Description { get; set; } = string.Empty;

        [JsonPropertyName("interactionCount")]
        public int InteractionCount { get; set; }

        [JsonPropertyName("aggregateRating")]
        public AggregateRating AggregateRating { get; set; } =  new AggregateRating();

        [JsonPropertyName("offers")]
        public Offer Offer { get; set; } = new Offer();

        [JsonPropertyName("author")]
        public Author Author { get; set; } = new Author();

        public MobileApplication()
        {
            this.AggregateRating =  new AggregateRating();
            this.Offer = new Offer();
            this.Author = new Author();
        }

    }
}
