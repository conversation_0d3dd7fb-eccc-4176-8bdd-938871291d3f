﻿<template>
    <div id="sticky-internationals" class="filters w-100 d-flex justify-content-center d-lg-none cfi-mobile" :class="'one-way'" data-bs-toggle="modal" data-bs-target="#filters-modal">
        <div id="idFiltersMobile" class="filter d-flex justify-content-center align-items-center">
            <i class="icons icon-filters me-1 font-20"></i>
            <span class="font-18">{{__('filters.filters')}} <template v-if="countFilterApply > 0">{{ ` (${countFilterApply})` }}</template></span>
        </div>
    </div>

    <div class="modal fade" id="filters-modal" tabindex="-1" aria-labelledby="filters-modal" aria-hidden="true">
        <div class="modal-dialog modal-dialog-scrollable modal-fullscreen">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="btn ms-0 px-0 color-primary" data-bs-dismiss="modal" aria-label="Close"><i class="icons icon-chevron-left"></i></button>
                    <h4 class="modal-title mx-auto">{{__('filters.filters')}}</h4>
                    <button type="button" class="btn ms-0 px-0 color-primary invisible"><i class="icons icon-chevron-left"></i></button>
                </div>
                <div class="modal-body">
                    <div class="content" v-if="!getLoading">
                        <div class="airlines">
                            <h5 class="filter-title mb-0">{{__('filters.airlines')}}</h5>
                            <div class="d-flex flex-column pt-2">
                                <div class="custom-control custom-checkbox py-2 d-flex">
                                    <input type="checkbox" class="custom-control-input" id="filter-check-airline-all" :checked="!airlinesFiltersApplied.length" @click="allElements('airlines', $event.target.checked)" />
                                    <label class="custom-control-label c-text font-14 position-t-2 text-black ms-2 mb-0 cursor-pointer" for="filter-check-airline-all">{{__('filters.allAirlines')}}</label>
                                </div>
                                <div class="custom-control custom-checkbox py-2 d-flex" v-for="airline in getAirlines">
                                    <input @click.stop="validateFlight('airlines', $event)" type="checkbox" class="custom-control-input" :id="`filter-check-airline-${airline.code}`" :value="airline.code" v-model="airlinesFiltersApplied">
                                    <label class="custom-control-label c-text font-14 position-t-2 text-black ms-2 mb-0 cursor-pointer" :for="`filter-check-airline-${airline.code}`">
                                        <img class="me-2" height="25" width="25" :src="airline.airlineLogoUrl">
                                        <span>{{airline.name}}</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="stops mt-4">
                            <h5 class="filter-title mb-0">{{__('filters.stops')}}</h5>
                            <div class="d-flex flex-column pt-2">
                                <div class="custom-control custom-checkbox py-2 d-flex">
                                    <input type="checkbox" class="custom-control-input" id="filter-check-stops-all" :checked="!stopsFiltersApplied.length" @click="allElements('stops', $event.target.checked)" />
                                    <label class="custom-control-label c-text font-14 position-t-2 text-black ms-2 mb-0 cursor-pointer" for="filter-check-stops-all">{{__('filters.allFlights')}}</label>
                                </div>
                                <div class="custom-control custom-checkbox py-2 d-flex" v-for="stop in getAvilableStops">
                                    <input @click.stop="validateFlight('stops', $event)" type="checkbox" class="custom-control-input" :id="`filter-check-stops-${stop.title}`" :value="stop.title" v-model="stopsFiltersApplied">
                                    <label class="custom-control-label c-text font-14 position-t-2  text-black ms-2 mb-0 cursor-pointer" :for="`filter-check-stops-${stop.title}`">
                                        <span>{{__(`filters.${stop.title}`)}}</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex justify-content-center align-items-center w-100 h-100 flex-column" v-else>
                        <div class="lds-dual-ring"></div>
                        <p class="text-uppercase font-16 fw-semibold mt-3 mb-1 loading-text">{{__('filters.searching')}}...</p>
                    </div>
                </div>
                <div class="modal-footer bg-body-tertiary">
                    <button v-show="validating" class="btn btn-primary btn-lg w-100 fw-bold" type="button">Validando...</button>
                    <button v-show="existResults && !validating" class="btn btn-primary btn-lg w-100 fw-bold" type="button" @click="applyFilters()" data-bs-dismiss="modal">{{__('filters.apply')}}</button>
                    <button v-show="!existResults && !validating" class="btn btn-secondary btn-lg w-100 fw-bold" type="button" disabled>¡Ups! No encontramos vuelos</button>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
    import { storeToRefs } from 'pinia';
    import { onMounted, onBeforeUnmount, ref, computed } from 'vue';

    import { getFilteredList } from '../../../services/ApiFlightFrontServices';

    import { useFlightStore } from '../../../stores/flight';
    import { useFlightMatrixStore } from "../../../stores/flightMatrix";
    import { useUserSelectionStore } from '../../../stores/user-selection';
    import { cheapestFlights } from '../../../../constants';
    import { List } from '../../../../utils/analytics/flightList.js';
    import { sleep } from '../../../../utils/helpers';
    import { useMultiTicketStore } from '../../../stores/multiTicket';
    import { usePromotionStore } from '../../../stores/promotion';

    const storeFlightMatrix = useFlightMatrixStore();
    const storeFlight = useFlightStore();
    const storeUserSelection = useUserSelectionStore();
    const useMultiTicket = useMultiTicketStore();
    const promotionStore = usePromotionStore();


    const { isOneWay } = defineProps({
        isOneWay: {
            type: Boolean,
            default: false
        }
    });

    const { getIsStepTwo, getCheckOutDataStepOne, getStopsFiltersApplied, getAirlinesFiltersApplied, getExistResults } = storeToRefs(useMultiTicket);
    const { setAirlinesFiltersPush, setStopsFiltersPush, setExistResults, setAirlinesFilters, setStopsFilters } = useMultiTicket;
    const { setFlightResponses, resetFlightResponse } = storeFlight;
    const { setLoading } = storeFlightMatrix;
    const { changeFilters, setFlagFilters } = storeUserSelection;
    const { getAvilableStops, getAirlines, getLoading } = storeToRefs(storeFlightMatrix);
    const { isCheapestFlightsApplied, getFiltersAppliedArray } = storeToRefs(storeUserSelection);
    const { getTripMode } = storeToRefs(promotionStore);

    //const airlinesFiltersApplied = ref([]);
    //const stopsFiltersApplied = ref([]);
    const scrollPos = ref(0);
    //let existResults = ref(true);
    let validating = ref(false);

    const airlinesFiltersApplied = computed(() => {
        return getAirlinesFiltersApplied.value;
    });
    const stopsFiltersApplied = computed(() => {        
        return getStopsFiltersApplied.value;
    });
    const existResults = computed(() => {
        return getExistResults.value;
    });

    const countFilterApply = computed(() => {        
        return stopsFiltersApplied.value.length + airlinesFiltersApplied.value.length;
    });

    const applyFilters = async () => {
        const params = {
            token: storeFlight.getAllQuoteTokens.join(','),
            filterApplied: getFiltersApplied(),
            site: window.__pt.settings.site.apiFlights.siteConfig,
            tripMode : getTripMode.value,
            simpleFlightQuotes : true,
            step : true
        };
        if (getIsStepTwo.value) {
            params.DepartureToken = getCheckOutDataStepOne.value.summary.token;
            params.FlightQuoteId = getCheckOutDataStepOne.value.summary.fareKey;			
        } 
        params.allQuotes = !window.__pt.data.isNational;
        const response = await getFilteredList(params);
        resetFlightResponse();
        await sleep(50)
        setFlightResponses(response.response);
        if (getFiltersAppliedArray.value.length > 0 && getFiltersAppliedArray.value[0] != 'cheapestFlights') {
            List.flightFilters(getFiltersAppliedArray.value);
        }
        setLoading(false);
    };

    const getFiltersApplied = () => {
        const filters = [];
        filters.push(...airlinesFiltersApplied.value);
        filters.push(...stopsFiltersApplied.value);
        if (isCheapestFlightsApplied.value) {
            filters.push(cheapestFlights);
        }
        setFlagFilters(filters.length == 0);
        changeFilters(filters);
        return filters.join(',');
    };

    const allElements = (type, v) => {
        if (type === 'airlines' && v) {
            setAirlinesFilters([])
        }
        else if (type === 'stops' && v) {
            setStopsFilters([])
        }
        //setExistResults(true)
        existResults.value = true;
    };

    const handleScroll = () => {
    const bodyTop = document.body.getBoundingClientRect().top;
    const stickyElement = document.getElementById("sticky-internationals");

    if (bodyTop < scrollPos.value) {
        stickyElement.classList.add("d-none");
    } else {
        stickyElement.classList.remove("d-none");
    }
    scrollPos.value = bodyTop;

    window.addEventListener("scroll", function() {
        // Verifica si la altura de la ventana es menor a 670px (iPhone SE tiene 667px de altura)
        if (window.innerHeight < 667) {
            const scrollPosition = window.scrollY || document.documentElement.scrollTop;

            if (stickyElement) {
                if (scrollPosition >= 0 && scrollPosition <= 150) {
                    stickyElement.classList.add("small-mobile");
                } else {
                    stickyElement.classList.remove("small-mobile");
                }
            }
        }
    });
};

    const validateFlight = async (type, event) => {
        if (event.target.checked) {
            if (type === 'airlines') {
                setAirlinesFiltersPush(event.currentTarget.value);
            } else {
                setStopsFiltersPush(event.currentTarget.value);
            }
        } else {
            let newValues = [];
            if (type === 'airlines') {
                newValues = airlinesFiltersApplied.value.filter(item => item !== event.currentTarget.value);
                setAirlinesFilters(newValues);
            } else {                              
                newValues = stopsFiltersApplied.value.filter(item => item !== event.currentTarget.value);
                setStopsFilters(newValues);
            }
        }
        validating.value = true;
        const params = {
            token: storeFlight.getAllQuoteTokens.join(','),
            filterApplied: getFiltersApplied(),
            site: window.__pt.settings.site.apiFlights.siteConfig,
            tripMode : getTripMode.value,
            simpleFlightQuotes : true,
            step : true
        };
        if (getIsStepTwo.value) {
            params.DepartureToken = getCheckOutDataStepOne.value.summary.token;
            params.FlightQuoteId = getCheckOutDataStepOne.value.summary.fareKey;			
        }
        params.allQuotes = !window.__pt.data.isNational;
        const response = await getFilteredList(params);
        let existFlights = false;
        for (let index in response.response) {
            if (response.response[index].airlines.length > 0) {
                existFlights = true;
            }
        }
        //existResults.value = existFlights;
        setExistResults(existFlights)
        validating.value = false;
        setLoading(false);
    };

    onMounted(() => {
        window.addEventListener('scroll', handleScroll);
    });

    onBeforeUnmount(() => {
        window.removeEventListener('scroll', handleScroll);
    });



</script>