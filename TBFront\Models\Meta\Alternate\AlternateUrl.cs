﻿
namespace TBFront.Models.Meta.Alternate
{
    public class AlternateUrl
    {
        public string Name { get; set; }
        public string Culture { get; set; }
        public string Url { get; set; }
        public bool IsDefault { get; set; }

    }
    public class AlternateMain
    {
        public string Name { get; set; } = string.Empty;
        public string Culture { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
        public List<AlternatePage> Alternates { get; set; } = new List<AlternatePage>();
    }

    public class AlternatePage
    {
        public string Name { get; set; } = string.Empty;
        public string Culture { get; set; } = string.Empty;
        public string InternalCulture { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
        public string StartingUri { get; set; } = string.Empty;
        public string ReturingUri { get; set; } = string.Empty;
        public string UrlPath { get; set; } = string.Empty;
        public bool IsUrlList { get; set; }
        public string IdItem { get; set; } = string.Empty;
        public bool IsDefault { get; set; }
    }

}