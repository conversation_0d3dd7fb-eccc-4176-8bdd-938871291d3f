﻿using TBFront.Types;
using System.Text.RegularExpressions;

namespace TBFront.Agent
{
    public class AgentBrowser
    {
        private readonly Crawlers _crawlers;
        private readonly Device _device;

        private readonly Regex _compiledRobotRegex;
        private readonly Regex _compiledMobileRegex;
        private readonly Regex _compiledTabletRegex;


        public AgentBrowser()
        {
            _crawlers = new Crawlers();
            _device = new Device();


            _compiledRobotRegex = CompiledRegex(_crawlers.CrawlerBrowser);
            _compiledMobileRegex = CompiledRegex(_device.DeviceBrowser);
            _compiledTabletRegex = CompiledRegex(_device.DeviceTabletBrowser);

        }


        public bool IsCrawler(string userAgent = "")
        {
            //return true;
            return _compiledRobotRegex.Match(userAgent).Success;
        }

        public bool IsMobile(string userAgent = "")
        {
            return _compiledMobileRegex.Match(userAgent).Success;
        }

        public bool IsTablet(string userAgent = "")
        {
           return userAgent == "true";         
        }

        public string Device(string userAgent = "")
        {
            var isMobile = IsMobile(userAgent);
            var isTablet = IsTablet(userAgent);
            var device = DeviceType.Desktop;

            if (isMobile)
            {
                device = DeviceType.Mobile;
            }

            if (isTablet)
            {
                device = DeviceType.Tablet;
            }

            return device;
        }

        private Regex CompiledRegex(string[] patterns)
        {
            return new Regex(@"(" +  String.Join("|", patterns) + ")", RegexOptions.IgnoreCase | RegexOptions.Multiline | RegexOptions.Compiled);
        }

    }
}
