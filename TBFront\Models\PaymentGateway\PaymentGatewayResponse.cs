﻿using ProtoBuf;

namespace TBFront.Models.PaymentGateway
{
    [ProtoContract]
    public class PaymentGatewayResponse
    {
        [ProtoMember(1)]
        public int PaymentGatewayResponseId { get; set; }

        [ProtoMember(2)]

        public int PaymentGatewayRequestId { get; set; }

        [ProtoMember(3)]

        public string RedirectUrl { get; set; }

        [ProtoMember(4)]
        public string Token { get; set; }

        [ProtoMember(5)]
        public bool IsSuccess { get; set; }

        [ProtoMember(6)]
        public IEnumerable<string> Errors { get; set; } = Enumerable.Empty<string>();

        [ProtoMember(7)]
        public IEnumerable<string> Warnings { get; set; } = Enumerable.Empty<string>();
    }
}
