﻿using TBFront.Models.Collection;
using TBFront.Types;

namespace TBFront.Mappers
{
    public class HomeMapper
    {
        public static string Path(string path)
        {
            var segments = path?.Split('/', StringSplitOptions.RemoveEmptyEntries) ?? [];
            var hasSegments = segments is not null && segments.Length > 0;
            if (!hasSegments || segments.Length == 1)
            {
                return "/";
            }
            return "/" + segments[1];
        }
        public static Section Collection(CollectionSchemaResponse content, string type)
        {
            var collection = content.Data!.Sections!.Find(data => data.CardType == type);
            return collection ?? new Section();
        }
        public static string GetPath(string path)
        {
            var page = ProductType.FlightsPage;
            path = path.ToLower().Trim().Replace("/", "");
            switch (path)
            {
                case ProductType.Hotels:
                    page = ProductType.HotelsPage;
                    break;
                case ProductType.Packages:
                    page = ProductType.PackagesPage;
                    break;
                case ProductType.Flights:
                    page = ProductType.FlightsPage;
                    break;
                case ProductType.FlightsUS:
                    page = ProductType.FlightsPageUS;
                    break;
                case ProductType.DestinationTB:
                    page = ProductType.DestinationTB;
                    break;
                case ProductType.ArlinesPage:
                    page = ProductType.ArlinesPagesUS;
                    break;
                case ProductType.ArlinesPagesUS:
                    page = ProductType.ArlinesPagesUS;
                    break;
            }
            return page;
        }

    }
}