﻿using ProtoBuf;
namespace TBFront.Models.ContentDeliveryNetwork.FaqContent
{
    [ProtoContract]
    public class FaqContentResponse
    {
        [ProtoMember(1)]
        public string Name { get; set; } = string.Empty;

        [ProtoMember(2)]
        public IEnumerable<string> Country { get; set; } = [];

        [ProtoMember(3)]
        public bool Static { get; set; }

        [ProtoMember(4)]
        public IEnumerable<FaqContent> Content { get; set; } = [];
    }

    [ProtoContract]
    public class FaqContent
    {
        [ProtoMember(1)]
        public string Code { get; set; } = string.Empty;

        [ProtoMember(2)]
        public IEnumerable<FrequentlyAskedQuestion> Content { get; set; } = [];
    }

    [ProtoContract]
    public class FrequentlyAskedQuestion
    {
        [ProtoMember(1)]
        public string Question { get; set; } = string.Empty;

        [ProtoMember(2)]
        public string Answer { get; set; } = string.Empty;

    }
}
