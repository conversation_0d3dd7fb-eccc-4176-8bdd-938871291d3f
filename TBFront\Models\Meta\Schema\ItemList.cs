﻿using System.Text.Json.Serialization;

namespace TBFront.Models.Meta.Schema
{
    public class ItemList
    {
        [JsonPropertyName("@context")]
        public string? Context { get; set; }

        [JsonPropertyName("@type")]
        public string? Type { get; set; }

        [JsonPropertyName("url")]
        public string? Url { get; set; }

        [JsonPropertyName("numberOfItems")]
        public int NumberOfItems { get; set; }

        [JsonPropertyName("itemListElement")]
        public List<HotelList>? ItemListElement { get; set; }

        public ItemList()
        {
            ItemListElement = new List<HotelList>();
        }

    }
}
