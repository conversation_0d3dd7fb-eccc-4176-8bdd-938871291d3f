class StorageHandler {
    prefix = "tb.";

    getSession(key) {
        let content = null;
        if (window.sessionStorage) {
            let data = window.sessionStorage.getItem(`${this.prefix}${key}`);
            try {
                content = JSON.parse(data);
            } catch {
                content = data;
            }
        }
        return content;
    }

    setSession(key, data) {
        let content = null;
        if (window.sessionStorage) {
            content = JSON.stringify(data);
            window.sessionStorage.setItem(`${this.prefix}${key}`, content);
        }
    }

    get(key) {
        let content = null;
        if (window.localStorage) {
            let data = window.localStorage.getItem(`${this.prefix}${key}`);
            try {
                content = JSON.parse(data);
            } catch {
                content = data;
            }
        }
        return content;
    }

    set(key, data) {
        let content = null;
        if (window.localStorage) {
            content = JSON.stringify(data);
            window.localStorage.setItem(`${this.prefix}${key}`, content);
        }
    }

}

export const StorageService = new StorageHandler();