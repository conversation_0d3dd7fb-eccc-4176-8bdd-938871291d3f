﻿@using TBFront.Helpers

@inject ViewHelper viewHelper



<div class="row">

    <div class="col-12">
        <p class="h4">@viewHelper.Localizer("start_session_or_create")</p>
    </div>

</div>


<div class="row my-4">
    <div class="col-12">
        <label for="email_login">@viewHelper.Localizer("enter_email2")</label>
        <div class="input-group mb-3" ng-class="{'is--danger' : vm.hasError(form_login, 'email') }">
            <div class="input-group-prepend">
                <span class="input-group-text">
                   <span class="font-icons icons-email"></span>
                </span>
            </div>
            <input type="email" required id="email_login" ng-model="vm.userData.email" name="email" placeholder="@viewHelper.Localizer("enter_email")" class="form-control input-custom">
        </div>
        <div class="invalid-feedback" ng-show="vm.hasError(form_login, 'email')">
            @viewHelper.Localizer("enter_email2")
        </div>
    </div>

</div>


<div class="row">
    <div class="col-12">
        <button type="submit" name="button" class="btn btn-primary btn-block p-3">@viewHelper.Localizer("login_continue")</button>
    </div>
</div>

