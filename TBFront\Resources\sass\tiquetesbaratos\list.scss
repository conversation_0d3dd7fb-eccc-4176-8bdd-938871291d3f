@import './app', './base/_variables', './_variables.scss', 'base/_core', 'components/footer', 'components/header';
@import "./nationals/multiticket";
@import "./nationals/info-flights-nationals";
@import "./components/_loading.scss";
@import "./components/_filters.scss";
@import "./components//multi-ticket-list.scss";
@import "./components/_pill-filters.scss";

.pagination {
    align-items: center;
    display: flex;
    justify-content: center;
    list-style: none;
    padding-left: 0;

    li:last-child {
        .p-btn {
            span {
                display: inline-block;
                //width: 24px;
                //height: 24px;
                //text-indent: -9999px
            }
        }
    }

    li:first-child {
        padding: 0.5rem;
        border-top-left-radius: 50%;
        border-top-right-radius: 50%;
        border-bottom-right-radius: 50%;
        border-bottom-left-radius: 50%;
        color: $color-primary;
    }


    li {
        margin-right: 0.5rem;

        .p-btn {
            cursor: pointer;
            display: inline-block;
            position: relative;
            background: $white;
            line-height: 1.5;
            font-size: 14px;
            border-radius: .25rem;
            font-weight: 500;
            text-align: center;
            white-space: nowrap;
            user-select: none;
            border: none;
            transition: 0.2s ease-in-out;
            color: $color-primary;
            box-shadow: 0 3px 1px -2px rgba(0,0,0,0.02),0 2px 2px 0 rgba(0,0,0,0.14),0 1px 5px 0 rgba(0,0,0,0.12);
            padding: 0.75rem;

            &:hover {
                background-color: $color-primary;
                color: $white;
                opacity: 0.5;
            }
        }
    }

    .active {
        .p-btn {
            color: $white;
            background: $color-primary;
        }
    }

    .inactive {
        .p-btn {
            color: #ccc;
            background: $white;
            box-shadow: none;
            text-decoration: none;
            background: #E5E5E5;
        }
    }
}

.evtNoRound {
    border-radius: 0 10px 0 0;
}

.custom-control-label{
    span {
        color: #000;
    }
}

.ct-airline {
    .ctl-line {
        border: 1px solid;
        border-radius: 50px;
    }
}

.card {
    --bs-card-spacer-y: 0.75rem;
    --bs-card-spacer-x: 1.25rem;
    --bs-card-title-spacer-y: 0.5rem;
    --bs-card-border-width: 1px;
    --bs-card-border-color: rgba(0, 0, 0, 0.125);
    --bs-card-border-radius: 0.25rem;
    --bs-card-inner-border-radius: calc(0.25rem - 1px);
    --bs-card-cap-padding-y: 0.375rem;
    --bs-card-cap-padding-x: 1.25rem;
    --bs-card-cap-bg: rgba(0, 0, 0, 0.03);
    --bs-card-bg: #fff;
    --bs-card-img-overlay-padding: 1.25rem;
    --bs-card-group-margin: 15px;
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    height: inherit !important;
    color: inherit !important;
    word-wrap: break-word;
    background-color: var(--bs-card-bg);
    background-clip: unset !important;
    border: none !important;
    border-radius: 0px !important;
}

.card-header {
    background: blue;
    background: linear-gradient(180deg, #037bba, #035aaf 79%);
    border: 1px solid #035aaf;
    color: #fff;

    &:first-child {
        border-radius: 0 !important;
    }

    .c-search {
        background-color: #2196f3;
        border-radius: 50px;
        padding: 10px;
    }
}

/* CARD HOTELES */
.pth-card {
    position: relative;
    min-width: 0;
    word-wrap: break-word;
    background-color: $white;
    background-clip: border-box;
    border: 1px solid #E6E6E6;
    border-radius: 0.25rem;

    .card-hotel-header {
        display: none;
    }

    .card-hotel-container {
        display: grid;
        grid-template-columns: 255px 69fr 31fr;
        margin: 0;
        min-height: 227px;
    }

    a {
        color: inherit;
        text-decoration: inherit;
    }

    .card-label {
        font-size: .75rem;
        margin-bottom: 0;
    }

    .price.pth-typography--paragraph1 {
        font-size: calc(.9375rem + .75vw);
        font-family: "Poppins",sans-serif;
        font-weight: 500;
        margin-bottom: 0;
        display: inline;
    }

    .card-hotel-img {
        position: relative;
        margin: 0;
        padding: 0;
        grid-column: 1 / 2;
        max-height: 227px;

        .hotel-image {
            border-radius: 0.25rem 0 0 0.25rem;
        }

        .card-mealplan {
            position: absolute;
            top: 10px;
            left: 10px;
            padding: 1px 5px;
            line-height: 1.5;
            font-size: 14px;
            letter-spacing: 0.02em;
            background-color: $white;
            color: $gray-900;
            border-radius: 3px;
            box-shadow: 0 4px 4px rgba($color: $black, $alpha: 0.25);

            i {
                font-size: 18px;
                vertical-align: middle;
                color: #489A00;
            }
        }
    }

    .card-hotel-body {
        position: relative;
        padding: 15px;
        grid-column: 2 / 3;
        max-width: 350px;

        .card-subtittle {
            line-height: 1.5;
            font-size: 14px;
            letter-spacing: 0.02em;
            color: $gray-800;
            width: inherit;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .card-title {
            line-height: 1.5;
            font-size: 16px;
            font-weight: 600;
            color: $gray-900;
            margin-bottom: 5px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        .card-rating {
            height: 25px;
            display: flex;
            align-items: center;
            font-size: 13px;

            p {
                line-height: 1.5;
                color: $gray-800;
            }

            span {
                color: $gray-200;
                margin: 0 10px;
            }

            i {
                width: fit-content !important;
            }
        }

        .card-highlights {
            .badge-services {
                display: inline-block;
                line-height: 1.5;
                font-size: 14px;
                letter-spacing: 0.02em;
                color: $gray-600;
                width: inherit;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;

                i {
                    color: #489A00;
                    font-size: 16px;
                }
            }
        }
    }

    .card-hotel-footer {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        justify-content: center;
        padding: 15px;
        background-color: $gray-10;
        text-align: right;
        min-height: 117px;
        height: auto;
        line-height: 1.5;
        letter-spacing: 0.02em;
        border-radius: 0 0.25rem 0.25rem 0;
        grid-column: 3 / 4;
        width: 210px;

        .card-footer_weekoffer {
            font-size: 12px;
            color: #489A00;
            margin-bottom: 2px;
            text-align: right;
            width: -webkit-fill-available;

            span {
                display: block;
                width: inherit;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }

        .card-footer_room {
            font-size: 14px;
            color: #333333;
            margin-bottom: 2px;
            text-align: right;

            span {
                display: block;
                min-width: max-content;
                margin-right: 0 !important;
            }
        }

        .card-footer_discount {
            font-size: 14px;
            color: #4C4C4C;
            display: flex;
            justify-content: flex-end;

            .card-label {
                line-height: 1.8;
                min-width: max-content;
                font-size: 14px;
            }

            .discount-tag {
                width: fit-content;
                margin-left: 15px;

                .disccount-value, .sale-value {
                    color: $white;
                    font-weight: 500;
                    margin-bottom: 0;
                    line-height: 1.8;
                    font-size: 12px;
                    min-width: max-content;
                    letter-spacing: normal;
                }

                .disccount-icon {
                    background-color: #489A00;
                    padding: 0 6px;
                    height: 20px;
                    width: min-content;
                    position: relative;
                    right: 0;
                    border-radius: 0 3px 3px 0;

                    &:before {
                        content: "";
                        display: inline-block;
                        width: 0;
                        height: 0;
                        border-top: 10px solid transparent;
                        border-right: 10px solid #489A00;
                        border-bottom: 10px solid transparent;
                        position: absolute;
                        right: 100%;
                        top: 0;
                        bottom: 0;
                    }
                }

                .sale-icon {
                    background-color: #CB3234;
                    padding: 0 6px;
                    height: 20px;
                    width: min-content;
                    position: relative;
                    right: 0;
                    border-radius: 0 3px 3px 0;

                    &:before {
                        content: "";
                        display: inline-block;
                        width: 0;
                        height: 0;
                        border-top: 10px solid transparent;
                        border-right: 10px solid #CB3234;
                        border-bottom: 10px solid transparent;
                        position: absolute;
                        right: 100%;
                        top: 0;
                        bottom: 0;
                    }
                }
            }
        }

        .card-footer_price {
            font-size: 20px;
            font-weight: 600;
            color: #333333;
            margin: 5px 0;

            .icons-flame1 {
                color: #CB3234;
            }
        }

        .card-footer_taxes {
            font-size: 12px;
            color: #4C4C4C;
            margin-bottom: 2px;
        }

        .card-footer_roomcapacity {
            font-size: 14px;
            color: #4C4C4C;
            margin-bottom: 2px;
            text-align: right;

            span {
                display: block;
                min-width: max-content;
                margin-right: 0 !important;
            }
        }

        .card-footer_cancel {
            font-size: 12px;
            color: #666666;
        }
        /* CASO NO DISPONIBLE */
        .card-no-available {
            font-size: 14px;
            color: #333333;
            margin-bottom: 5px;

            &:first-child {
                font-weight: 600;
            }
        }
    }

    .card-hotel-fechasugerida {
        display: none;
    }

    &:hover {
        text-decoration: none;
        box-shadow: 0 5px 5px -3px rgba(0,0,0,0.02),0 8px 10px 1px rgba(0,0,0,0.14),0 3px 14px 2px rgba(0,0,0,0.12);
        transition: 0.2s ease-in-out;
    }

    .card-body {
        min-height: 200px !important;
        height: auto;
    }
}

.pth-card-sugerido {
    border-color: $color-primary;
    border-width: 2px;

    .card-hotel-header {
        display: block;
        padding: 11px 10px;
        height: 40px;
        color: $white;
        font-size: 14px;
        line-height: 1.3;
        letter-spacing: 0.02em;
        font-weight: 500;
        background-color: $color-primary;

        p {
            margin: 0;
        }
    }

    .card-hotel-img {
        .hotel-image {
            border-radius: 0 0 0 0.25rem;
        }
    }

    .card-hotel-footer {
        border-radius: 0 0 0.25rem 0;
    }
}

.pth-card-fechasugerida {

    .card-hotel-container {
        grid-template-columns: 255px minmax(69%, 1fr);
    }

    .card-hotel-img {
        grid-row: 1 / 3;
        max-height: none;
    }

    .card-hotel-body {
        grid-row: 1 / 2;
        padding-bottom: 10px;
        max-width: 100%;

        .card-highlights {
            display: none !important;
        }
    }

    .card-hotel-footer {
        display: none;
    }

    .card-hotel-fechasugerida {
        grid-column: 2 / 3;
        grid-row: 2 / 3;
        height: auto;
        border-radius: 0 0 0.25rem 0;
        display: flex;
        flex-direction: column;
        padding: 0 15px 15px;
        font-size: 14px;


        .icons-error {
            color: $color-primary;
            margin-right: 8px;
        }

        p, span {
            font-weight: 400;
            line-height: 1.5;
            letter-spacing: 0.02em;
            color: #666666;
            margin-bottom: 10px;
        }

        .card-fechasugerida_fechaelegida {
            color: $gray-700;
            font-weight: 500;
        }

        .card-fechasugerida_propuestas {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            width: inherit;
            overflow-x: scroll;

            .card-fechasugerida_fechas {
                padding: 10px 8px;
                background-color: #FCFBFD;
                border-radius: 0.25rem;
                border: 1px solid #9584C8;
                font-size: 12px;
                text-align: center;
                display: grid;
                gap: 5px;
                min-width: 133px;

                p {
                    margin: 0;
                    color: #4C4C4C;
                }

                p:first-child, p:last-child {
                    color: #333333;
                    white-space: nowrap;
                }
            }
        }
    }
}

.pt-breadcrumbs {
    margin-bottom: 0.5rem;

    .pt-breadcrumbs__item {
        font-size: .85rem;

        &:after {
            content: "\002F";
            display: inline-block;
            padding-left: 0.25rem;
            padding-right: 0.25rem;
        }
    }
}

.banner-telephone {
    color: $color-primary;
    background: rgb(248, 248, 248);
    font-weight: 500;
    padding: 10px;
    text-align: center;
    margin: 8px 0px;
    font-size: 14px;
}

.banner-information {
    border: 1px solid #e0e0e0;
    border-left: 10px solid $color-primary;
    border-radius: 0.25rem;
    width: 100%;
    padding: 10px;
    padding-left: 15px;
    background: $white;


    .banner-information__content-message p {
        margin: 0;
    }
}

.card-booker {
    border: 1px solid $gray-300;
    min-height: auto;
    background-color: $white;
    justify-content: center;
}

.form-check-input:checked {
    opacity: 1;
}



/** Formas de pago (Opcional hasta saber la maqueta)  **/
.payment-methods-banner {
    background-color: $white;
    border: 1px solid $gray-400;
    text-align: center;
    cursor: pointer;
    font-weight: 500;
    border-radius: 5px;

    span {
        color: $color-primary;
    }
}

//FILTROS ACTIVOS
// .content-tags {
//     margin-bottom: 0.5rem !important;
// }

.tag {
    display: inline-block;
    margin: 5px;
}


/*  ads */
.skyscraper {
    div {
        width: 160px;
        height: 600px;
        background-color: #e5e5e5;
        margin-bottom: 20px;
    }
}

pth-rate-container {
    height: 105px;
    display: block;
}

.filter-mobile-button {
    background-color: $color-primary;
    color: $white;
    border-radius: 5rem;
    padding: 0.5rem 1.25rem;
    position: fixed;
    bottom: 5px;
    left: 40%;
    z-index: 1;
}

.filter-map-mobile-button {
    background-color: $color-primary;
    color: $white;
    border-radius: 5rem;
    padding: 0.5rem 1.25rem;
    position: fixed;
    bottom: 20px;
    left: calc(50%);
    transform: translateX(-50%);
    z-index: 1;
    width: 206px;
    text-align: center;
    box-shadow: 1px -1px 24px 1px rgba(0, 0, 0, 0.37);
    -webkit-box-shadow: 1px -1px 24px 1px rgba(0, 0, 0, 0.37);
    -moz-box-shadow: 1px -1px 24px 1px rgba(0, 0, 0, 0.37);
    display: flex;
    align-items: center;
    justify-content: space-evenly;

    div {
        display: inline-block;
        font-size: 12px;
    }

    div:last-child {
        padding-left: 7px;
    }

    i {
        font-size: 18px;
        vertical-align: middle;
    }
}
h1 {
    font-size: 28px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
}
// ESTILOS CARDS
.card-title, .card-highlights {
    margin-bottom: 0rem;
}

.card-img-top {
    overflow: hidden;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -o-user-select: none;
    user-select: none;
    object-fit: cover;
    height: 100%;
    color: transparent;
}

.card-img-top:not([src]) {
    visibility: hidden;
}

.small {
    margin: 0;
}

.search-paxes {
    z-index: 99;
}

/*@media ($tablet-landscape){
    .c-multiticket {
        .ch-row {
            .header-color {
                p {
                    span {
                        &:first-child {                        
                            left: 22px;
                            top: 11px;
                        }                
                    }
                }
            }
        }
    }
}*/

skeleton-secondary {
    &:empty {
        max-width: 270px;
        display: block;
        float: none;
        margin-bottom: 1rem;
    }
}


.vr {
    display: inline-block;
    align-self: stretch;
    width: 1px;
    min-height: 1em;
    background-color: currentColor;
    opacity: .25;
}

.l-page-main {
    display: block;
    height: 100vh;
}

.card-price > .row {
    min-height: 220px;
}

.bg-dark {
    color: $white;
    background-color: #2C6B86 !important;
    font-weight: normal;
    padding: 0.2rem 0.35rem;
}

/* MEDIA QUERIES */
@media (min-width: 991px) and (max-width: 1279px) {
    .skyscraper {
        display: none;
    }

    .skyscraper-view {
        flex: 0 0 75%;
        max-width: 75%
    }

    .pth-card {
        .card-hotel-container {
            grid-template-columns: 210px 56fr 44fr;
        }

        .card-hotel-body {
            width: 268px;
        }

        .card-hotel-footer {
            max-width: 210px;

            .card-footer_price {
                font-size: 16px;
            }
        }
    }

    .pth-card-fechasugerida {
        .card-hotel-container {
            grid-template-columns: 210px minmax(69%, 1fr);
        }

        .card-hotel-fechasugerida {
            .card-fechasugerida_propuestas {
                .card-fechasugerida_fechas {
                    min-width: 126px;
                    padding: 10px 6px;
                }
            }
        }
    }
}

@media ($phone) {
    .skyscraper {
        display: none;
    }

    .slick-arrow {
        display: none;
    }

    .pth-card .card-body, pth-rate-container {
        min-height: auto !important;
        height: auto;
    }

    .card-img-top {
        overflow: hidden;
        height: 100%;
        object-fit: cover;
    }

    .pth-card .card-highlights {
        min-height: auto;
    }

    h1 {
        font-size: 16px;
        padding-left: 15px;
        font-weight: bold;
        margin-bottom: 0;
    }

    .card-title {
        line-height: 130%;
    }

    .pagination {
        li {
            // display: none;

            &:first-child {
                display: block;
            }

            &:last-child {
                display: block;
            }
        }
        // .active {
        //     display: none;
        // }
    }

    .bookerdesktoplist {
        display: none;
    }

    #modal-filters .modal-dialog {
        margin: 0;
    }

    .pth-card {
        .card-hotel-container {
            display: grid;
            grid-template-columns: 131px 1fr;
            min-height: auto;
        }

        .card-hotel-img {
            display: grid;
            grid-column: 1 / 2;
            grid-row: 1 / 3;
            max-width: none;
            max-height: none;

            .card-mealplan {
                font-size: 10px;
            }

            .hotel-image {
                border-radius: 0;
            }
        }

        .card-hotel-body {
            grid-column: 2 / 3;
            grid-row: 1 / 2;
            max-width: 375px;
            padding: 10px;

            .card-title {
                font-weight: 400;
            }

            .card-rating {
                font-size: 12px;

                span {
                    margin: 0 2px;
                }

                .font-icons:before {
                    font-size: 18px !important;
                }
            }
        }

        .card-hotel-footer {
            grid-column: 2 / 3;
            grid-row: 2 / 3;
            max-width: none;
            background-color: transparent;
            padding: 5px 10px 10px;
            display: block;
            height: max-content;
            width: auto;

            .card-footer_discount {
                .card-label {
                    font-size: 12px;
                }
            }

            .card-footer_room, .card-footer_roomcapacity {
                font-size: 12px;
                margin-bottom: 0;
            }

            .card-footer_weekoffer {
                margin-bottom: 0;

                span {
                    white-space: normal;
                }
            }

            .card-footer_price {
                font-size: 16px;
                margin: 2px 0;
            }

            .card-footer_taxes {
                margin-bottom: 0px;
            }
            /* CASO NO DISPONIBLE */
            .card-no-available {
                font-size: 12px;
                text-align: start;

                &:first-child {
                    border-top: 1px solid #D9D9D9;
                    margin-top: -5px;
                    padding-top: 10px;
                }
            }
        }
    }

    .pth-card-fechasugerida {

        .card-hotel-container {
            grid-template-columns: 131px minmax(61%, 1fr);
            grid-template-rows: auto;
        }

        .card-hotel-img {
            grid-column: 1 / 2;
            grid-row: 1 / 2;
            max-height: 82px;
        }

        .card-hotel-footer {
            display: none;
        }

        .card-hotel-fechasugerida {
            grid-column: 1 / 3;
            margin-top: 10px;

            p {
                font-size: 12px;
                margin: 0;
                margin-bottom: 10px;
            }

            .card-fechasugerida_propuestas {
                grid-template-columns: 1fr 1fr;

                .card-fechasugerida_fechas {
                    min-width: none;
                    gap: 0;

                    p:last-child {
                        font-size: 14px;
                    }
                }
            }
        }
    }
}

@media (max-width:374px) {
    .card-hotel-body {
        .card-rating {
            flex-wrap: wrap;
            height: auto !important;

            span {
                margin: 0 10px !important;
            }
        }
    }
}

.modal-header-redi {
    justify-content: initial !important;
    padding: 0.2rem 1rem;
    border-bottom: 0 !important;
    position: -webkit-sticky;
    position: sticky;
    top: 0;
}

.modal-header-redi button {
    align-self: center !important;
}

.modal-header-redi p {
    width: 100%;
    text-align: center;
    margin-left: -22px;
}

.modal-header-redi .close {
    padding: 0rem 1rem;
    margin: 0;
    margin-left: -17px;
}

.modal-header-redi .d-flex a {
    display: flex !important;
}

.a-filter-class {
    display: flex !important;
    text-decoration: none !important;
}
// BADGE
.badge {
    font-weight: 400;
}

.form-check-label {
    .font-icons {
        vertical-align: top;
    }
}


.sliderLoader {
    position: absolute;
    width: 100%;
    height: 100%;
    top: -15px;
    z-index: 1;
}

.card-content-img-alerts {
    position: absolute;
    top: 0;
}

.top-promotions {
    top: 32px !important;
}

.top-meal-plan {
    top: 40px !important;
}

.hotel-image, .dynamic {
    background-color: #fbfbfb;
}

.card-price .slick-slider {
    height: 100%;

    .slick-track {
        height: 100%;
    }
    //CLASE PARA QUE EL BORDER TOP SOLO SE VEA EN MOBILE, NO DEBERIA VERSE EN OTRAS RESOLUCIONES//
}

.slick-slide {
    height: 215px;
}

.speech-bubble {
    background: $white;
    color: $color-primary;
    border: 1px solid $color-primary;
    display: inline-block;
    cursor: pointer;
    font-size: 14px;
    line-height: 2em;
    margin-bottom: 1em;
    padding: 0 1em;
    position: relative;
    text-align: center;
    vertical-align: top;
    min-width: 5em;
}

.speech-bubble:after {
    content: '';
    position: absolute;
    left: calc(50% - 4px);
    top: -41px;
    width: 10px;
    height: 10px;
    margin-top: 35px;
    overflow: hidden;
    transform: rotate(45deg);
    background: $white;
    border-left: 1px solid $color-primary;
    border-top: 1px solid $color-primary;
}

.speech-bubble.rounded {
    border-radius: .4em;
}

.speech-bubble-active {
    background: $color-primary;
    color: $white;
    z-index: 10;
}

.speech-bubble-active:after {
    background: $color-primary;
}

.speech-bubble:hover {
    background: $purple-100;
    color: $color-primary;
    z-index: 10;
    cursor: pointer;
}

.speech-bubble:hover:after {
    background: $purple-100;
}

@media (min-width: 768px) {
    .card-footer {
        border-top: none !important;
    }
}

.handle_area {
    z-index: 1;
    position: absolute;
    height: 100%;
    width: 50px;

    &.left {
        left: 0;
    }

    &.right {
        right: 0;
    }
}


.recommended-wrapper {
    overflow-x: scroll;
    overflow-y: hidden;
    white-space: nowrap;

    .recommended-div {
        width: 250px;
        display: inline-block;
        margin-right: 10px;
    }
}

.discount-tag {
    position: inherit
}

.loading-filters {
    height: 22px;

    .loader__circle {
        width: 27px;
        height: 27px;
    }
}




/*** Sliders ***/

.slider.slider-horizontal {
    width: 100%;
}

.slider .slider-handle {
    background: #fff;
    border: 1px solid #e4e4e4;
}

.slider-handle {
    height: 30px;
    width: 30px;
}

.slider-selection {
    background: $color-primary;
}

.slider.slider-horizontal .slider-track {
    margin-top: 2px;
    height: 4px;
}


.dolar-input-abs {
    left: 5px;
    top: 6px;
    user-select: none;
}

.label-input-abs {
    left: 10px;
    top: -8px;
    font-size: 12px;
    background: #fff;
    user-select: none;
    color: #73737A;
}

.order_by_menu_mobile_area {

    .show {
        display: flex !important;
        align-items: flex-end !important;
    }

    .modal-dialog {
        width: 100%;
        margin-bottom: 0 !important;
    }

    .modal.fade .modal-dialog {
        transition: none !important;
        transform: none !important;
    }

    .fade {
        transition: none !important;
    }
}

.order_by_menu_mobile {
    max-width: 570px;
    width: 90%;
    background-color: #fff;
}

.link-text {
    color: #186CDF;
}

.min_text-14 {
    font-size: 14px !important;
}

.min_text-12 {
    font-size: 12px !important;
}


.min_text-16 {
    font-size: 16px !important;
}

.min_text-18 {
    font-size: 18px !important;
}

.c-pointer {
    cursor: pointer;
}

.text-has-filter {
    color: #87D6F7;

    &:before {
        content: '';
        display: inline-block;
        width: 10px;
        height: 10px;
        -moz-border-radius: 7.5px;
        -webkit-border-radius: 7.5px;
        border-radius: 7.5px;
        background-color: #87D6F7;
        position: absolute;
        left: 105px;
        top: 7px;
    }
}

.list .card-header {
    background-color: #ffffff;
}

.rounded-pill-bg {
    background-color: #F0F6FF;
    border: #CCE0FF solid 1px;
    color: #4D4D52;
    font-size: 14px;

    span {
        color: #4D4D52;
    }

    &:hover {
        background-color: #CCE0FF;
    }
}

#modal-filters {

    .modal-dialog {
        width: 100%;
        max-width: 100%;
        margin: 0 auto;
    }

    .modal-body {
        display: flex;
        justify-content: center;
    }

    .content-fil {
        width: 510px;
    }
}

.box_range_input {
    max-width: auto;

    @media screen and (min-width: 590px) {
        max-width: 120px
    }
}

.btn-apply {
    color: #186CDF;
    border: solid 1px #186CDF;
}

.btn-search {
    font-size: 16px;
    padding: 0.75rem;
}

.max_slider_box {
    height: calc(1.5em + .75rem + 2px);
}

.min_slider_box {
    height: calc(1.5em + .75rem + 2px);
}

.icons-star {
    color: #ffc107;
    font-size: 22px;

    &::before {
        line-height: normal;
    }
}


@media (min-width: 768px) and (max-width: 990px){
    .page-header-title-web{
        margin-left: 0px;
    }

    .page-header-subtitle-web{
        margin-left: 0px;
    }

    .page-header-title-mobile{
        margin-left: 0px;
    }

    .page-header-subtitle-mobile{
        margin-left: 0px;
    }
    
}
@media (max-width: 767px){
    .page-header-title-web{
        padding-left: 0px;
    }
    .page-header-subtitle-web{
        margin-left: 0px;
    }

    .page-header-title-mobile{
        margin-left: 0px;
    }
    .page-header-subtitle-mobile{
        margin-left: 0px;
    }
}


/*__________________________________________________________________________________*/



.logo-viva {
    background-image: url(/assets-tb/img/tiquetesbaratos/logos/logo-viva.svg);
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: contain;
    height: 29px;
    margin: auto;
    position: relative;
    top: 5px;
    width: 90px;
}

.logo-AV {
    background-image: url(/assets-tb/img/tiquetesbaratos/logos/logo-avianca.svg);
    background-size: contain;
    background-position: 50%;
    background-repeat: no-repeat;
    height: 29px;
    margin: auto;
    width: 90px;
}

.logo-LA {
    background-image: url(/assets-tb/img/tiquetesbaratos/logos/logo-latam.png);
    background-size: contain;
    background-position: 50%;
    background-repeat: no-repeat;
    height: 29px;
    margin: auto;
    width: 90px;
}

.logo-easy-fly {
    background-image: url(/assets-tb/img/tiquetesbaratos/logos/logo-easy-fly.png);
    background-size: contain;
    background-position: 50%;
    background-repeat: no-repeat;
    height: 29px;
    margin: auto;
    width: 90px;
}

.logo-satena {
    background-image: url(/assets-tb/img/tiquetesbaratos/logos/logo-satena.png);
    background-size: contain;
    background-position: 50%;
    background-repeat: no-repeat;
    height: 29px;
    margin: auto;
    width: 90px;
}

/*__________________________________________________________________________________*/

.c-box {
    position: relative;
    z-index: 10;

    &:before {
        background: linear-gradient(180deg,rgba(0,0,0,.4416141456582633),hsla(0,0%,100%,0) 5%);
        content: "";
        height: 90%;
        left: 0;
        position: absolute;
        right: 0;
        top: 0;
        z-index: 0;
    }

    .cb-in {
        z-index: 1;
    }

    .nav-tabs {
        z-index: 0;

        .nav-link {
            background-color: $background-color_1;
            border-radius: 4px 4px 0 0;
        }

        .nav-link.active {
            background-color: $background-color_15;
            color: $color_1;
        }
    }

    .tab-content {
        z-index: 1;

        .c-input {
            border: none;
            border-bottom: 1px solid #525051;
            padding-bottom: 8px;

            &:focus {
                outline: none;
            }
        }

        .c-select {
            -webkit-appearance: none;
            -moz-appearance: none;
            background-color: $background-color_1;
            border: none;
            border-bottom: 1px solid #525051;
            padding-bottom: 9px;

            &:focus {
                outline: none;
            }
        }

        .c-input-box {
            input {
                font-size: 14px;
            }

            .icon {
                position: absolute;
                color: $color_6;
            }
        }
    }
}

.c-field {
    .placeholder {
        color: $color_7;
        font-family: $font-family_3;
        font-size: 14px;
        left: 24px;
        line-height: 14px;
        pointer-events: none;
        position: absolute;
        transform-origin: 0 50%;
        transition: transform .2s,color .2s;
        top: 5px;
    }

    .c-input {
        &:focus {
            ~ {
                .placeholder {
                    transform: translateY(-18px) translateX(0) scale(.84) !important;
                }
            }
        }

        &:not(:placeholder-shown) {
            ~ {
                .placeholder {
                    transform: translateY(-18px) translateX(0) scale(.84) !important;
                    color: $color_8;
                }
            }
        }
    }

    .placeholder-active {
        transform: translateY(-18px) translateX(0) scale(.84) !important;
    }

    .placeholder-active-not {
        transform: inherit !important;
    }
}

.c-title-home {
    position: relative;
    top: 20px;
}


.bg-economic {
    background-color: #ffff54 !important;
}

.c-box.c-flights {
    background: transparent;
    padding: 0 !important;

    .btn-blue {
        position: absolute;
        right: -3px;
        top: -16px;
    }

    &:before {
        background: transparent;
    }

    .c-box-mobile {
        background: #037bba;
        background: linear-gradient(180deg,#037bba,#035aaf 79%);

        p {
            font-family: $font-family_2;
        }

        .c-search {
            background-color: $background-color_15;
            border-radius: 50px;
            padding: 10px;
        }
    }

    .icon-keyboard-up {
        background-color: $background-color_15;
        border-radius: 50px;
        padding: 8px;
        position: relative;
        top: -8px;
    }
}
.position-t-5 {
    position: relative;
    top: 5px;
}
.bg-gray-250 {
    background-color: #e2e8f0;
}
.btn-yellow {
    background: #ffff54;
    border: 1px solid #ffff54;
    border-radius: 4px;
    color: $color_1 !important;
    transition: all .2s linear;
    .c-text {
        color: #333132;
    }
    .form-check-label {
        &:before {
            content: "";
            padding-left: 10px;
            position: absolute;
            width: 20px;
            height: 20px;
            left: -25px;            
        }        
    }
    @media ($phone) {
        .form-check-label {
            &:before {
                left: 20px;
            }
        }               
    }
}
.c-tab-btn {
    border: 1px solid #0e213a;
    border-radius: 4px 4px 0 0;
    padding: 9px 12px;
    position: relative;

    &:before {
        background-color: #fff;
        bottom: -2px;
        content: "";
        height: 5px;
        left: 0;
        position: absolute;
        right: 0;
        z-index: 1;
    }
}
.hide-line {
    border-radius: 4px;

    &:before {
        background-color: $background-color_8;
        content: "";
        height: 0;
    }
}
.c-early-dates {
    .border {
        border-color: $border-color_3 !important;
        border-radius: 4px;
    }

    .ml-body {
        background-color: $background-color_8;
        //left: 15px;
        bottom: 0;
        //right: 15px;
    }

    .c-modal-lateral-mobile {
        margin-top: -1px;
        background-color: $background-color_2;
        @media ($desktop) {
            position: relative;
            z-index: 0;
        }
    }
    .p-table {
        padding-bottom: 20px;        
    }
    .m-table {
        margin-bottom: 10px !important;        
    }

    .hide-line {
        padding-bottom: 10px;
    }
    @media ($desktop), ($tablet) {
        .icon-date {
            position: relative;
            top: -1px;
        }
    }
}
.c-info-price {
    .border-f {
        border: 1px solid #fff;
    }
}
.line-fligths-1 {
    .f-01 {
        color: $color_15;
        position: absolute;
        top: 6px;
        left: -16px;
        @media (max-width: 380px) {
            left: -62px !important;            
        }
    }
}
.line-fligths-2 {
    .f-01 {
        color: $color_15;
        position: absolute;
        top: 5px;
        left: -18px;
        @media (min-width: 991px) and (max-width: 1279px) {
            left: -19px;    
        }
    }
    
}


.c-line-schedule {
    border-bottom: 1px dashed #000;
    position: relative;
    width: 100%;

    .i-circle {
        left: -2px;
        height: 10px;
        position: absolute;
        top: -5px;
        width: 10px;
        border: 2px solid #c4c4c4;
        border-radius: 50px;
        @media (min-width:992px) and (max-width:1024px) {
            display: block !important;            
        } 
    }
    .icon {
        position: absolute;
        right: -5px;
        top: -8px;
        @media (max-width: 767px),(min-width:768px) and (max-width:991px) {
            left: auto;
            top: -9px;
            right: auto;
            border: none;                        
        }
        @media (min-width:768px) and (max-width:991px) {
            left: 23px;
        }  
    }
    @media (min-width:768px) and (max-width:991px), ($phone) {
        border: none;
        top: 19px;        
    }      
}

.c-mobile-filters {
    display: none;
}

.bg-gray-200 {
    background-color: #f5f5f5;
}

.row-color-1 {
    background-color: #f5f5f5;
}

.row-color-2 {
    background-color: #fff;
}
.c-mobile-filters {
    top: auto !important;
    bottom: 96px;
}

.c-box-flights {
    border-radius: 9px;
    overflow: initial;

    .c-view-more {
        border-radius: 0 0 0 5px;
        margin-left: -15px;
        margin-right: -15px;
        margin-top: -20px;
        position: relative;
        z-index: 1;
        @media ($phone) {
            margin-top: 0 !important;
        }
    }

    .title-list {
        margin-bottom: 0;
    }

    .c-view-detail {
        overflow: auto;
    }
}



input[type=radio] {
    border: 0;
    clip: rect(0 0 0 0);
    height: 0;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 0;
    cursor: pointer;
}

.c-row-line {
    margin-right: -15px;
    margin-left: -15px;
}

.c-click-detail {
    .icon {
        position: absolute;
        right: -6px;
        top: 4px;
        z-index: 1;
    }
    .sp {
        color: var(--text-link) !important;
        @media ($phone) {
            position: relative;
            top: 8px;
            white-space: nowrap;
        }
    }
}

.cf-radio-xs {
    @media ($phone) {
        bottom: 0;
        position: absolute;
        right: 20px;
        top: 0;
    }
}

.if-close {
    position: absolute;
    top: 25px;
    right: 15px;
}


.linke {
    color: #2196f3;
}

.c-if-round {
    border: 1px solid #dee2e6;
    border-radius: 9px;
    overflow: hidden;
    padding: 0 15px;
    @media ($phone) {
        border: none;
        border-radius: 0;
    }
}


.c-modal-lateral-mobile {
    .img-lateral {
        left: 5px;
        position: absolute;
    }
}
.position-t-3 {
    position: relative;
    top: 3px;
}

.c-txt-total {
    font-size: 14px;
}

.c-txt-rate {
    font-size: 18px;
}

.c-mobile-filters {
    display: none;
}

.tab-footer-offers {
    background-color: #fff;
    border-bottom: 8px solid #ffff54;
    box-shadow: 0 1px 0 0 #ccc;

    span {
        color: var(--text-primary);
    }

    .txt {
        &:hover {
            cursor: pointer;
            text-decoration: underline;
        }
    }
}

.c-line-schedule {
    .icon {
        height: 20px;
        margin: auto;
        top: -7px;
        width: 20px;
    }
}




.line-flights {
    margin: auto;
    .bb-white {
        .md-info {
            @media ($tablet) {
                position: relative;
                top: 1px;
                .icon {
                    top: 1px;
                }
            }
        }
        .icon {
            color: $color_11;
            position: relative;
            top: -3px;
        }
    }
}

.cf-layer {
    border: none;
    padding: 0;
    border-radius: 0 0 9px 9px;
    position: relative;
    z-index: 0;
    .radio {
        label {
            &:before {
                height: 1rem;
                width: 1rem;
                @media ($tablet) {
                    position: absolute;
                    right: 0;
                    top: 3px;
                }
            }
        }
        .cni-rates {
            @media ($tablet) {
                position: relative;
                right: 24px;
            }            
        }
    }
}

.one-flight {
    .cf-layer {
        border: none;
        border-radius: 0;
        padding-bottom: 0;
        padding-top: 0;
    }

    .c-flights-list {
        border: none;
    }

    .row-round {
        margin-left: 1px;
    }

    .c-view-more {
        border-radius: 0 0 9px 9px;
    }

    .c-info-flight {
        .bg-gray {
            margin: 0 1px 0 -14px;
        }
    }

    .c-bar-info {
        margin-left: 0 !important;
        margin-right: 0 !important;
    }
}

.row-round {
    border-top-left-radius: 9px;
    border-top-right-radius: 9px;
}

.i-desk {
    font-size: 14px;
    left: -4px;
    position: relative;
    top: 0;
}

.cf-layer {
    h3 {
        * {
            font-size: 20px !important;
        }
    }

    .title-list {
        .icon {
            top: 2px;
        }
    }
}

.one-flight {
    .ch-row {
        right: 14px;
    }

    .c-view-more {
        margin-left: 0;
        margin-right: 0;
    }
}

.c-view-more {
    box-shadow: 0 0 10px #999;
    margin-top: -40px;
    position: relative;
    z-index: 0;
    @media ($phone) {
        margin: 0 -10px;
        border-radius: 0 0 8px 8px;
    }
}

.c-view-detail {
    color: $color_12;

    .gray {
        color: $color_13;
    }

    .circle-top {
        border: 2px solid #c4c4c4;
        border-radius: 50px;
        height: 10px;
        left: 0;
        margin: auto;
        position: absolute;
        top: 0;
        right: 0;
        width: 10px;
        z-index: 1;
    }

    .circle-bottom {
        border: 2px solid #c4c4c4;
        border-radius: 50px;
        bottom: 25px;
        height: 10px;
        left: 0;
        margin: auto;
        position: absolute;
        right: 0;
        width: 10px;
        z-index: 1;
    }

    .line {
        border: 1px dashed #c4c4c4;
        border-radius: 50px;
        bottom: 25px;
        left: 0;
        margin: auto;
        position: absolute;
        right: 0;
        top: 0;
        width: 1px;
        z-index: 0;
    }

    p {
        font-size: 14px;
    }
}

.title-radio {
    left: 40px;
    position: absolute;
    top: -2px;
}



.border-left {
    border-left: 1px solid #dee2e6 !important;
}
/*
.mt-2, .my-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
}*/

hr {
    margin-top: 1rem;
    margin-bottom: 1rem;
    border: 0;
    border-top: 1px solid rgba(0, 0, 0, .1);
}

.f-p-medium {
    font-family: 'Poppins-medium';
}

.radio {
    input[type=radio] {
        cursor: pointer;
    }

    label {
        cursor: pointer;
        margin: 0;
        z-index: 98;
        @media ($phone) {
            top: 3px;
            width: 71px;
        }
        &:before {
            content: "";
            display: block;
            width: 1.2em;
            height: 1.2em;
            border-radius: 100%;
            vertical-align: -3px;
            border: 1px solid #000;
            padding: .13em;
            background-color: $background-color_1;
            background-clip: content-box;
            transition: all .2s linear;
            margin: auto;
        }
    }

    input {
        &:hover {
            + {
                label {
                    &:before {
                        border-color: $border-color_3;
                        transition: all .2s linear;
                    }
                }
            }
        }

        &:checked {
            + {
                label {
                    &:before {
                        background-color: $background-color_13;
                        border-color: $border-color_4;
                        padding: .13em;
                        transition: all .2s linear;
                    }
                }
            }
        }

        &:disabled {
            + {
                label {
                    border-color: $border-color_5;
                    color: $color_5;

                    &:before {
                        border-color: $border-color_5;
                    }
                }
            }

            &:checked {
                + {
                    label {
                        &:before {
                            background-color: $background-color_14;
                        }
                    }
                }
            }
        }
    }
}

input[type=radio] {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}

.cd-flight-info {
    top: 10px;
    z-index: 1;
    @media ($tablet) {
        font-size: 10px !important;
    }
}
.row-color-1 {
    .cd-flight-info {
        background-color: #f5f5f5;        
    }    
}
.row-color-2 {
    .cd-flight-info {
        background-color: #fff;        
    }    
}





.c-AV {
    border: $border-color_9 !important;

    .header-color {
        background-color: $background-color_24 !important;
        &:before {
            background-color: $background-color_24;
            content: "";
            position: absolute;
            left: -1px;
            top: 0;
            right: 0;
            width: 2px;
            bottom: 0;
        }  
    }

    .line-bottom {
        border-bottom: 1px solid #fbeae8;
    }

    .line-top {
        border-top: 1px solid #fbeae8;
    }

    .bg-1 {
        background-color: $background-color_25;
    }

    .bg-2 {
        background-color: $background-color_26;
    }

    .bg-3 {
        background-color: $background-color_27;
    }

    .bg-4 {
        background-color: $background-color_28;
    }

    .ctl-line {
        border-color: $border-color_9;

        * {
            color: $border-color_9;
        }
    }
}

.c-LA {
    border-color: $border-color_10 !important;

    .header-color {
        background-color: $background-color_29 !important;
        &:before {
            background-color: $background-color_29;
            content: "";
            position: absolute;
            left: -1px;
            top: 0;
            right: 0;
            width: 2px;
            bottom: 0;
        }    
    }

    .line-bottom {
        border-bottom: 1px solid #eae5f3;
    }

    .bg-1 {
        background-color: $background-color_30;
    }

    .bg-2 {
        background-color: $background-color_31;
    }

    .bg-3 {
        background-color: $background-color_32;
    }

    .bg-4 {
        background-color: $background-color_33;
    }

    .bg-5 {
        background-color: $background-color_34;
    }

    .bg-6 {
        background-color: $background-color_35;
    }

    .ctl-line {
        border-color: $border-color_10;

        * {
            color: $border-color_10;
        }
    }
}

.c-JA {
}

.c-viva {
    border-color: $border-color_8 !important;

    .header-color {
        background-color: $background-color_20 !important;
        &:before {
            background-color: $background-color_20;
            content: "";
            position: absolute;
            left: -1px;
            top: 0;
            right: 0;
            width: 2px;
            bottom: 0;
        }
    }

    .line-bottom {
        border-bottom: 1px solid #cce8ee;
    }

    .bg-1 {
        background-color: $background-color_21;
    }

    .bg-2 {
        background-color: $background-color_22;
    }

    .bg-3 {
        background-color: $background-color_23;
    }

    .ctl-line {
        border-color: $border-color_8;

        * {
            color: $border-color_8;
        }
    }
}



.c-easy-fly {
    border-color: $border-color_11 !important;

    .header-color {
        background-color: $background-color_36 !important;
        &:before {
            background-color: $background-color_36;
            content: "";
            position: absolute;
            left: -1px;
            top: 0;
            right: 0;
            width: 2px;
            bottom: 0;
        } 
    }

    .line-bottom {
        border-bottom: 1px solid rgba(254,118,17,.3);
    }

    .bg-1 {
        background-color: $background-color_37;
    }

    .bg-2 {
        background-color: $background-color_38;
    }

    .bg-3 {
        background-color: $background-color_39;
    }

    .bg-4 {
        background-color: $background-color_40;
    }

    .ctl-line {
        border-color: $border-color_11;

        * {
            color: $border-color_11;
        }
    }
}

.c-satena {
    border-color: $border-color_11 !important;

    .header-color {
        background-color: $background-color_36 !important;
        &:before {
            background-color: $background-color_36;
            content: "";
            position: absolute;
            left: -1px;
            top: 0;
            right: 0;
            width: 2px;
            bottom: 0;
        } 
    }

    .line-bottom {
        border-bottom: 1px solid rgba(54,96,134,.2);
    }

    .bg-1 {
        background-color: $background-color_41;
    }

    .bg-2 {
        background-color: $background-color_42;
    }

    .bg-3 {
        background-color: $background-color_43;
    }

    .bg-4 {
        background-color: $background-color_44;
    }

    .ctl-line {
        border-color: $border-color_12;

        * {
            color: $border-color_12;
        }
    }
}









.bb-white {
    border-bottom: 2px solid #fff;
}

.c-flights-list {
    border-radius: 9px 12px 9px 9px;
    border-width: 2px !important;

    .row {
        @media ($desktop) {
            margin: auto !important;
        }
        /*@media ($tablet) {
            margin: 0 !important;
        }
        @media ($phone-portrait){
            margin-bottom: 10px !important;
        }*/          
    }

    .c-bar-info {
        margin-right: -1.034rem;
        margin-bottom: 10px;

        span {
            display: block;
            text-align: center;
        }
    }

    &:before {
        content: "";
        overflow: hidden;
    }

    p {
        color: $color_1;
        margin: 0;

        &.flight-number {
            color: #000;
        }

        strong {
            color: $color_1;
        }
    }
}

.cf-logo {
    border-radius: 9px 0 0 0;
    position: relative;
    z-index: 1;

    div {
        position: relative;
        top: 5px;
        @media ($phone) {
            margin: initial !important;                
        }
    }

    &:before {
        content: "";
    }
}

.tab-footer-primary {
    background-color: #fcfcfc;
}

.position-revert {
    position: revert;
}

.c-sticky {
    position: sticky;
    top: 0;
    z-index: 10;    
    @media ($desktop), ($tablet) {
        z-index: 2;
    }
}

.c-flights-xs {
    border-top-right-radius: 9px;
}

.c-info-flight {
    .bg-gray {
        margin: 0 -15px;
    }

    .icon {
        color: $color_11;
        position: relative;
        top: -2px;
    }

    .icon-plane-left {
        color: $color_14;
        position: relative;
        top: 5px;
    }

    .icon-plane-right {
        color: $color_14;
        position: relative;
        top: 5px;
    }

    .pipe {
        font-size: 8px;
        position: relative;
        top: 5px;
    }
}

.int-xs {
    font-size: 14px !important;
}

.ch-right {
    margin-left: 150px;
}

.ch-left {
    float: left;
    width: 150px;
}

.bg-gray {
    background-color: #f2f2f2;
}

.border-radius-left {
    border-radius: 9px 0 0 0;
}

.c-tf {
    position: -webkit-sticky;
    position: sticky;
    top: 100px;
    width: 250px;
    z-index: 1;
    border-radius: 0 4px 4px 0;
    padding: 2px 10px;
}

.c-type-fly {
    border-radius: 0 4px 4px 0;
    padding: 2px 10px;
    background-color: $background-color_49;
    color: $color_1;
    font-size: 14px;
    left: 0;
    position: absolute;
    top: -24px;
}

.header-color {
    position: relative;
    z-index: 1;
}

.c-out-off {
    color: $color_15;
    padding-top: 3px;
}

.cbt-date {
    position: absolute;
    left: -84px;
    top: -7px;
}

.c-info-price {
    .border-f {
        border: 1px solid #fff;
    }
}

.i-matriz-tablet {
    align-items: center;
    .ci-txt {
        @media ($tablet) {
            font-size: 11px !important;
            margin-right: 25px;
            top: 0 !important;
        }            
    }
}

.row-color-1 {
    background-color: $background-color_46;
}

.row-color-2 {
    background-color: $background-color_1;
}

.c-view-detail-info {
    overflow: hidden;

    .circle-top {
        border: 1px solid #64748b;
        border-radius: 50px;
        height: 18px;
        left: 0;
        margin: auto;
        position: absolute;
        top: 0;
        right: 0;
        width: 18px;
        z-index: 1;
    }

    .circle-bottom {
        border: 1px solid #64748b;
        border-radius: 50px;
        bottom: 25px;
        height: 18px;
        left: 0;
        margin: auto;
        position: absolute;
        right: 0;
        width: 18px;
        z-index: 1;
    }

    .line {
        border: 1px dashed #64748b;
        border-radius: 50px;
        bottom: 25px;
        left: 0;
        margin: auto;
        position: absolute;
        right: 0;
        top: 0;
        width: 1px;
        z-index: 0;
    }
}

.f-round {
    .header-color {
        .pr-lg-0 {
            padding-right: 20px !important;
        }
    }
}

.position-t-2 {
    position: relative;
    top: -2px;
}

@media (min-width:768px) and (max-width:1024px) {
    .c-box {
        .tab-content {
            .c-input-box {
                .c-button {
                    .btn {
                        font-size: 12px;
                    }
                }
            }
        }

        .s-nav-md {
            margin-top: -30px;
        }
    }

    .c-box-mobile {
        display: none;
    }

    .c-box, .c-flights {
        .c-btn-up {
            display: none;
        }
    }

    .c-line-schedule {
        .icon {
            height: 20px;
            margin: auto;
            width: 20px;
        }
    }

    .c-view-more {
        box-shadow: 0 0 10px #999;
        margin-left: -20px;
        margin-right: -20px;
        margin-top: -40px;
        position: relative;
        z-index: 0;
    }

    .line-fligths-1 {
        .f-01 {
            left: -112%;
            top: -1px;
        }
    }

    .line-fligths-2 {
        .f-01 {
            left: 52%;
            top: -2px;
        }
    }

    .row-active {
        &:hover {
            background-color: $background-color_1;

            &:before {
                border: 1px solid #003b98;
                border-radius: 50px;
                bottom: 4px;
                content: "";
                left: 4px;
                position: absolute;
                right: 4px;
                top: 4px;
            }
        }
    }

    .c-click-detail {
        .icon {
            right: 18px;
            top: 8px;
        }
    }
}

@media (max-width: 767px) {
    .c-mobile-filters {
        background-color: $background-color_51;
        border: 1px solid #003b98;
        border-radius: 50px;
        display: block;
        margin: auto;
        position: -webkit-sticky;
        position: sticky;
        top: 85vh;
        width: 94%;
        z-index: 9999;

        .icon {
            color: $color_2;
            position: relative;
            top: 5px;
        }

        .border-filters {
            position: relative;

            &:before {
                border-left: 1px solid gray !important;
                content: "";
                left: 10px;
                position: absolute;
                height: 90%;
                top: 3px;
            }
        }
    }
    .c-box-flights {
        .radio {
            position: relative;
        }
        .c-row-line {
            img {
                margin: auto;
                display: block;
            }
        }
        .border-left {
            border: none !important;
        }
        .c-view-more {
            border-bottom: 1px solid #eee !important;
        }
        .title-list {
            padding: 5px 10px 0;
            .icon {
                @media ($phone) {
                    position: relative;
                    top: 2px;
                }                
            }
        }
        .c-view-detail {
            .circle-bottom {
                bottom: 32%;
            }
        }
    }
    /*
    .c-line-schedule {
        .icon {
            height: 20px;
            margin: auto;
            left: 0;
            right: 0;
            top: 12px;
            width: 20px;
            font-size: 12px !important;
            left: 12px;
            top: 15px;
        }
    }
    */
    .line-fligths-1 {
        .f-01 {
            left: -78px;
            top: -1px;
        }
    }
    .line-fligths-2 {
        .f-01 {
            left: 39px;
            top: -1px;
        }
    }
    .c-info-flight-xs {
        background-color: $background-color_45;
        border-radius: 10px;

        .icon-info {
            color: $color_11;
            position: relative;
            top: -1px;
        }

        .pipe {
            font-size: 8px;
            position: relative;
            top: -2px;
        }
    }
    .date-mobile {
        position: absolute;
        right: 6px;
        top: 14px;
        font-size: 14px !important;
    }
    .logo-viva {
        margin: initial;
    }

    .logo-avianca {
        margin: initial;
    }

    .logo-latam {
        margin: initial;
    }

    .logo-easy-fly {
        margin: initial;
    }

    .logo-satena {
        margin: initial;
    }

/*    .title-list {
        span {
            &:first-child {
                font-size: 28px;
            }

            &:nth-child(2) { 
                font-size: 24px;
            }

            &:nth-child(3) {
                font-size: 20px;
            }

            &:nth-child(4) {
                font-size: 20px;
            }
        }
    }*/

    .c-title-list {
        .title-list {
            display: none;
        }
    }

    .c-round-flight {
        .c-title-go {
            display: block;
        }
    }

    .c-flights-list {
        p {
            font-size: 14px;
        }

        .mobile-r {
            background-color: $background-color_8;
            color: $color_12;
        }
    }

    .lf-line-2 {
        background-color: $background-color_46;
    }

    .line-flights {
        .bw-bottom {
            .radio {
                label {
                    &:before {
                        top: 2px;
                    }
                }
            }
        }

        .radio {
            label {
                &:before {
                    position: absolute;
                    right: -33px;
                    top: 4px;
                    height: 1.3rem;
                    width: 1.3rem;
                }
            }
        }

        p {
            font-size: 18px;
        }

        border: 1px solid #c4c4c4;
        border-radius: 9px;
        box-shadow: 0 .125rem .25rem rgba(0,0,0,.075) !important;
        overflow: hidden;

        .l-w {
            background-color: $background-color_1;
            text-align: center;
        }

        .bb-white {
            border-bottom: 2px solid #fff;

            .color-red {
                position: relative;
                top: 2px;
            }
        }
    }

    .one-flight {
        .row-round {
            margin-left: 0;
            margin-right: 0;
        }
    }
}

.line-fligths-2 {
    .f-01 {
        @media (max-width: 400px) {
            left: 52% !important
        }        
    }
}

.cb-flights {
    box-shadow: -2px 2px 10px #999;
    bottom: 0;
    position: sticky;
    z-index: 999;
}

.z-bar-national {
    z-index: 98 !important;
}

@media (min-width: 768px) and (max-width: 1024px), (min-width: 991px) and (max-width: 1199px), (min-width: 1025px) {
    .hide-tablet-desk {
        display: none;
    }
}

@media (min-width: 768px) and (max-width: 1024px), (min-width: 1025px) {
    .ch-row {
        left: 0;
        position: absolute;
        right: 0;
    }

    .c-flights-list {
        border-radius: 9px 12px 9px 9px;
        border-width: 2px !important;

        .c-bar-info {
            margin-right: -1.034rem;
            margin-bottom: 10px;
        }
    }
}

@media (min-width: 768px) and (max-width: 1024px), (min-width: 991px) and (max-width: 1199px), (min-width: 1025px) {
    .hide-tablet-desk {
        display: none;
    }
}
    
@media (min-width:1025px) {
    .c-box {
        .tab-content {
            .c-input-box {
                .c-button {
                    .btn {
                        position: relative;
                        top: -17px;
                    }
                }
            }
        }

        .s-nav-md {
            margin-top: -30px;
        }
    }

    .c-box-mobile {
        display: none;
    }

    .c-box.c-flights {
        .c-btn-up {
            display: none;
        }
    }

    .c-flights-list {
        border-radius: 9px 12px 9px 9px;
        border-width: 2px !important;

        .c-bar-info {
            margin-right: -1.034rem;
            margin-bottom: 10px;
        }
    }


    .cf-layer {
        border: 2px solid #dee2e6;
        padding: 0;
        overflow: hidden;
    }

    .one-flight {
        .ch-row {
            right: 14px;
        }

        .c-view-more {
            margin-left: 0;
            margin-right: 0;
        }
    }

    .c-view-more {
        box-shadow: 0 0 10px #999;
        margin-left: -20px;
        margin-right: -20px;
        margin-top: -40px;
        position: relative;
        z-index: 0;
    }
}


/* ajustes list international */

.c-radio {
    position: relative;
}
.c-line {
    .c-radio {
        @media ($phone) {
            top: 8px;
            right: -6px;
        }
    }
}
.cn-cheap-active {
    margin-right: 10px;
    padding: 0 10px !important;
    @media ($phone) {
        padding: 0 6px !important;        
    }
}
@media (min-width: 1025px), (min-width: 768px) and (max-width: 1024px) {
    #detailInter {
        .row {
            justify-content: space-between;
        }

        #flight-info {
            width: 60% !important;
        }

        #package-info {
            width: 38% !important;
        }
        
        #flight-info.w-100 {
            width: 100% !important;
        }
    }
}

.c-capsule-light {
    background-color: #f0f9ff;
    .icon {
        top: -1px;
    }
}

.content-info {
    position: relative;
    display: inline-table;
    width: 100%;
    height: auto;
}

.line-steps {
    position: relative;
    width: 5%;
    display: table-cell;
    height: 100%;
}

.circle-top1 {
    border: 2px solid #c4c4c4;
    border-radius: 50px;
    height: 10px;
    margin: auto;
    width: 10px;
    z-index: 1;
    position: absolute;
    top: 5px;
    left: 6px;
    display: block;
    background-color: #fff;
}

.line1 {
    bottom: -9px;
    left: 0;
    top: 20px;
    border: 1px dashed #c4c4c4;
    border-radius: 50px;
    margin: auto;
    position: absolute;
    right: 0;
    width: 1px;
    z-index: 0;
}

.nav-tab button {
    position: relative;
    width: 40%;
    height: 40px;
    line-height: 40px;
    display: inline-block;
    color: #495057;
    background-color: #fff;
    border: 1px solid #dee2e6;
    text-align: center;
    border-bottom: #fff;
}

.nav-tab button.active {
    border-top: 5px solid #2196f3 !important;
    color: #2196f3;
    position: relative;
    bottom: -1px;
    border-bottom: 1px solid #fff;
    z-index: 2;
    line-height: 100%;
    position: relative;    
}
@media ($phone) {
    .nav-tab .active {
        &:before {
            background-color: #fff;
            content: "";
            height: 5px;
            position: absolute;
            bottom: -3px;
            width: 100%;
            left: 0;
            right: 0;
        }
    }
}

.cn-flights-list {
    .cn-line {
        margin: 0 !important;
    }
}

.cn-flights-list .cn-line:nth-child(even) {
    background-color: #f9fcfc; /* Color de fondo para las líneas pares */
    border-bottom: 1px solid #eee;
    .c-hrs {
        background-color: #f9fcfc;
    }
}

.cn-flights-list .cn-line:nth-child(odd) {
    background-color: #fff; /* Color de fondo para las líneas impares */
    border-bottom: 1px solid #eee;
    .c-hrs {
        background-color: #fff;
    }
}

.cb-scale {
    .a-link {
        white-space: nowrap;
        @media ($phone) {
            font-size: 14px;
            position: relative;
            right: -2px;
        }
    }
}

.input-group {
    .icon {
        color: var(--icon-strong) !important;
    }
}

.cn-sidebar {
    .cns-header {
        background-color: $white;
        position: relative;
    }
    .cns-body {
        background-color: $white;
        left: 0 !important;
        right: 0 !important;
        .cnsb-cont {
            left: 15px;
        }   
    }
}

.c-f-paginator {
    .cfp-ctrl {
        background-color: rgba(24, 108, 223, .5) !important;
    }    
}

/*
.btn-link {
    color: $color-primary;
}
.btn-link:hover {
    color: $color-primary !important;
    text-decoration: underline;
}*/

.btn-link{
	color: var(--text-link) !important;
	font-weight: 500;
	&:hover{
        color: var(--text-link-hover);
		text-decoration: underline;
	}
	&:focus{
		box-shadow: none;
	}
}

.c-detail-flight-rate {
    margin-left: 18px;    
}

@media (min-width: 768px) and (max-width: 1024px), (min-width: 1025px) {
    .c-m-family {
        width: -moz-fit-content !important;
        width: fit-content !important;
    }
}
.c-m-family {
    background-color: #f1f5f9;
    border: 1px solid #cdd9ea;
    border-radius: 5px;
    width: 100%;
    p {
        color: $navy-600;
        font-size: 16px;
    }
}
.color-primary {
    color: $color-primary;
}

.radio label:before {
    content: "";
    display: block;
    width: 1.2em;
    height: 1.2em;
    border-radius: 100%;
    vertical-align: -3px;
    border: 1px solid #000;
    background-color: #fff;
    background-clip: content-box;
    transition: all .2s linear;
    margin: auto;
}

@media (min-width: 768px) and (max-width: 1024px), (min-width: 1025px) {
    .row-active:hover:before {
        border: 1px solid #003b98;
        border-radius: 50px;
        bottom: 4px;
        content: "";
        left: 4px;
        position: absolute;
        right: 4px;
        top: 4px;
        z-index: 98;
    }
}

.img-airline {
    @media (min-width: 991px) and (max-width: 1279px) {
        width: 20px;
    }    
}

.fs-10-sm {
    @media (min-width: 991px) and (max-width: 1279px) {
        font-size: 10px !important;
        letter-spacing: -0.5px;
    }   
}









/* national styles */

.ch-row {
    .row {
        margin: 0;
        @media ($phone) {
            margin: 0 -2px;        
        }
    }    
}
.sw.ch-row {
    box-shadow: 0 0.3rem 1rem rgba(0, 0, 0, 0.10);    
}

@media (min-width: 991px) and (max-width: 1279px) {
    .c-flights-list {
        .row.c-sticky {
            margin: 0;
        }        
    }
}

.txt-time {
    @media ($phone) {
        font-size: 14px;
    }
}



.chc-header .rate {
    font-size: 23px !important;
    @media ($phone) {
        font-size: 20px !important;
    }
    .text-truncate {
        white-space: nowrap !important;
        width: 55%;
    }
}

.c-detail-modal-desktop {    
    .icon-close {
        @media ($phone) {
            color: #333;
            position: absolute !important;
            right: 0 !important;
            top: 0;
            margin: initial !important;
            width: initial !important;
        }            
    }
    .modal-body {
        @media ($phone) {
            height: 100vh;
        }
    }    
}


.h-mobile-1 {    
    .icon {
        display: block;
        width: 30px;
        margin-top: 10px;
        right: -20px;
        position: relative;
    }
    @media ($phone) {
        height: 130px;
        left: 0;
        padding: 0 15px;
        position: absolute;
        right: 0;
        top: 0;
    }    
}


@media (max-width: 767px) {
    .b-mobile-1 {
        bottom: 0;
        left: 0;
        overflow-x: hidden;
        overflow-y: auto;
        padding: 0 15px;
        position: absolute;
        right: 0;
        top: 130px;
    }
}

.cmb-xs {
    .btn-yellow {       
        height: 100%;
        .custom-checkbox {
            position: relative;
            top: 3px;
        }             
    }
    .icon-date {       
        position: relative;
        top: -2px;
    }    
}
.cmb-lg {
    .icon-date {       
        position: relative;
        top: -2px;        
    }    
}

.info-travel {
    p {
        display: inline-block;
    }
    .p01 {
        position: relative;
        right: -4px;
    }
}

.form-check-input:checked {
    background-color: $color_9;
    border-color: $color_9;
}
.form-check-input:focus {
    border-color: #ccc;
    outline: 0;
    box-shadow: none;
}




/* common */
.z-98 {
    z-index: 98 !important;
}
.z-99 {
    z-index: 99 !important;
}
.z-999 {
    z-index: 999 !important;
}

.z-99-xs {
    @media ($phone) {
        z-index: 99 !important;
    }    
}



.filters.one-way {
    .filter {
        padding-top: 1rem;
        padding-bottom: 1rem;
    }
}

.filters.round {
    .filter {
        padding-top: 0.5rem;
        padding-bottom: 0.5rem;
    }
}

.cfi-mobile {
    @media (max-width: 767px) and (orientation: landscape) {
        left: 0;
        margin: auto;
        right: 0;
        width: 50% !important;
    }
    @media  (min-width: 768px) and (max-width: 1024px) and (orientation: landscape) {
        display: none !important;
    } 
}

.a-link-simple {
    color: #186cdf !important;
    &:hover {
        cursor: pointer;
        text-decoration: underline;        
    }
}

.border-1 {
    border-width: 2px !important;
    border-style: solid !important;
}

.cta-link {
    color: $color_9;
}
.c-mobile-btns {
    .custom-control-label:before {
        top: 0;
    }
    .custom-control-label:after {
        top: 4px;
    }
    .ti-txt {
        @media ($phone) {
            font-size: 14px;        
        }
        @media (max-width: 392px) {
            font-size: 13px;
        }
    }    
}
.c-early-dates {
    .custom-control-label:before {
        top: 1px;
    }
    .custom-control-label:after {
        top: 3px;
    }
    .custom-checkbox {
        .custom-control-label:after {
            top: 5px;
        }
    }       
}



.custom-control{
    position:relative;
    display:block;
    min-height:1.5rem;
    padding-left:1.5rem;
}
 .custom-control-input{
    position:absolute;
    z-index:-1;
    opacity:0;
}
 .custom-control-input:checked~.custom-control-label:before{
    border-color:#007bff;
    background-color:#007bff;
}
 .custom-control-input:focus~.custom-control-label:before{
    box-shadow:0 0 0 .2rem rgba(0,123,255,.25);
}
 .custom-control-input:focus:not(:checked)~.custom-control-label:before{
    border-color:#80bdff;
}
 .custom-control-input:not(:disabled):active~.custom-control-label:before{
    color:#fff;
    background-color:#b3d7ff;
    border-color:#b3d7ff;
}
 .custom-control-input:disabled~.custom-control-label{
    color:#6c757d;
}
 .custom-control-input:disabled~.custom-control-label:before{
    background-color:#e9ecef;
}
 .custom-control-label{
    position:relative;
    margin-bottom:0;
    vertical-align:top;
}
 .custom-control-label:before{
    position:absolute;
    width:1rem;
    height:1rem;
    pointer-events:none;
    background-color:#fff;
    border:1px solid #adb5bd;
}
 .custom-control-label:after{
    top:.25rem;
    left:-1.5rem;
    display:block;
    width:1rem;
    height:1rem;
    content:"";
    background:no-repeat 50%/50% 50%;
}
 .custom-checkbox .custom-control-label:before{
    border-radius:.25rem;
}
 .custom-checkbox .custom-control-input:checked~.custom-control-label:after{
    background-image:url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z'/%3E%3C/svg%3E");
}
 .custom-checkbox .custom-control-input:disabled:checked~.custom-control-label:before{
    background-color:rgba(0,123,255,.5);
}
 .custom-control-label:before{
    transition:background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
}
 .custom-checkbox .custom-control-label:before{
    border-width:2px;
    border-radius:4px;
    border-color:#000;
}
 .custom-checkbox .custom-control-label:after{
    transition:background-size .15s ease-in-out;
    background-repeat:no-repeat;
    background-position-y:center;
    background-position-x:50%;
    background-size:0;
    cursor: pointer;
}
 .custom-checkbox .custom-control-input:checked~.custom-control-label:after{
    transition:background-size .15s ease-in-out;
    background-size:100%;
}
 .custom-control-input:checked~.custom-control-label:before{
    color:#fff;
    border-color:#000;
    background-color:#2196f3;
}
 .custom-control-label:after{
    position:absolute;
    top:.46rem;
    left:-1.26rem;
    width:.69rem;
    height:.69rem;
}
 .custom-control-label:before{
    top:.25rem;
    left:-1.5rem;
    display:block;
    width:1.2rem;
    height:1.2rem;
    content:"";
}

@media (min-width: 1025px), (min-width: 768px) and (max-width: 1024px) {
    .c-md-national #detailInter {
        #package-info.cpi-info {
            width: 100% !important;
        }
    }
}

.c-line-f {
    @media (min-width: 1025px) {
        top: -4px;
    }
}

.cnli-container {
    .txt-time {
        @media (min-width: 1281px), (min-width: 1025px) and (max-width: 1280px), ($tablet) {
            font-size: 18px;
        }
    }    
}

.ps-18-md {
    @media (min-width: 1281px), (min-width: 1025px) and (max-width: 1280px) {
        font-size: var(--body);
    }    
}

.line-flights, .c-row-line {
    input[type=radio] {
        width: 0;
        height: 0;
        opacity: 0;
    }
}
.c-radio {
    input[type=radio] {
        opacity: 0;
        width: 0;
        height: 0;
    }
}

/* icons equpaje */
.c-eq {
    .icon {
        color: #676B70;
    }
    .color-green {
        color: var(--text-semantic-text-success);
    }
    .icon-big-bag-out, .icon-carry-on-bag, .icon-none {
        color: #96949C !important;
    }

}

.line-fligths-2 {
    .f-01 {      
        @media (min-width: 400px) and (max-width: 767px) {
            left: 52% !important;
        }        
    }
}
.line-fligths-1 {
    .f-01 {      
        @media (min-width: 992px) and (max-width: 1024px) {
            left: -48% !important;
        }        
    }
}
.line-fligths-1 {
    .f-01 {      
        @media (min-width: 528px) and (max-width: 767px) {
            left: -73px !important;
        }        
    }
}
.line-fligths-1 {
    .f-01 {      
        @media (min-width: 500px) and (max-width: 527px) {
            left: -72px !important;
        }        
    }
}
.line-fligths-1 {
    .f-01 {      
        @media (min-width: 477px) and (max-width: 499px) {
            left: -69px !important;
        }        
    }
}

.line-fligths-1 {
    .f-01 {      
        @media (min-width: 395px) and (max-width: 476px) {
            left: -64px !important;
        }        
    }
}
.line-fligths-1 {
    .f-01 {      
        @media (min-width: 380px) and (max-width: 394px) {
            left: -60px !important;
        }        
    }
}


.line-fligths-1 {
    .f-01 {      
        @media (min-width: 360px) and (max-width: 379px) {
            left: -59px !important;
        }        
    }
}

.c-row-line .c-eq {
    @media (max-width: 394px) {
        position: relative;
        right: 5px;
    }      
}

.c-row-line .c-eq {
    @media (min-width: 395px) and (max-width: 420px) {
        position: relative;
        right: 2px;
    }      
} 

.c-row-line .c-eq {
    @media (min-width: 450px) and (max-width: 767px) {
        position: relative;
        right: -12px;
    }      
} 


/**/
.top-md-5 {
    @media ($tablet) {
        position: relative;
        top: 5px;
    }    
}

.c-md-national {
    --bs-modal-margin: 15px;
}
.c-color-header {
    @media (min-width: 768px) {
        border-radius: 0 11px 0 0;
        overflow: hidden;
        position: relative;
        right: -2px;
        @-moz-document url-prefix() {
            right: -1px;            
        }
    }
    @media ($tablet) {
        border-radius: 0 8px 0 0;
        right: 2px;
        width: 100.3%;        
    }
    @media ($phone) {
        margin: 0 !important;
        /*@-moz-document url-prefix() {
            position: relative;
            right: -2px;
            width: 100%;            
        }*/
    }
}
.c-multiticket {
    .c-bg-w {
        @media (min-width: 768px) {
            position: relative;
            right: -2px;
        }        
    }
}






