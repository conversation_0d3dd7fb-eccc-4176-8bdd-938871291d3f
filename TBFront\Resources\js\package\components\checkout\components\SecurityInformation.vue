<template>
	<div :class="`c-detail-info p-3 mb-2 ${css}`" >
		<p class="text-center mb-2">
			<span class="icon icon-lock font-28 position-t-2"></span>
			<span>{{__("checkout.security_title")}}</span>
		</p>
		<p class="text-center f-r-medium mb-2">{{__("messages.domain")}}</p>
		<p class="text-center mb-2">
			<span class="text-strong f-r-medium ">{{__("checkout.security_alert")}} </span>
			<span>{{__("checkout.security_text")}}</span>
		</p>
	</div>

</template>

<script>
	export default {
		data() {
			return {

			}
		},
		props: ["css"],

		mounted() {
		},
		methods: {
		}
	}
</script>