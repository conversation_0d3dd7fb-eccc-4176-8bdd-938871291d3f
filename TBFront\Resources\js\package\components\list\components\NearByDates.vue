<template>
	<section id="containerDatesMobile" class="container c-early-dates px-md-0 d-none d-lg-block cmb-lg my-3" :class="{'c-sencillo' : !isRountrip}">
		<div class="row pt-lg-2 d-none d-md-flex">
			<div class="col-12 col-lg-6 align-content-center">
				<span class="d-grid main-header-list" v-if="!getIsStepTwo && isRountrip && isInternational">
					<span>
						<span class="icon icon-plane-right "></span> {{ __("multiticket.departure_flight") }}
					</span>
					<span>
						{{ startingPlace.cityName }} -  {{ returningPlace.cityName }} {{ $filters.date(getParams.startingFromDateTime, 'ddd D MMM YYYY') }}
					</span>
				</span>
				<span class="d-grid main-header-list" v-if="getIsStepTwo && isRountrip && isInternational">
					<span>
						<span></span> {{ __("multiticket.returning_flight") }}
					</span>
					<span>
						{{ returningPlace.cityName }} -  {{ startingPlace.cityName }} {{ $filters.date(getParams.returningFromDateTime, 'ddd D MMM YYYY') }}
					</span>
				</span>

				<div class="col-12"  v-if="!isInternational">
                    <template v-if="props.isRountrip">
                        <template v-if="flightSelected?.step_action <= 2">
                            <div class="d-flex align-items-center">
                                <span class="icon icon-plane-right font-28 me-2" id="avion"></span>
                                <h3 class="mb-0">{{__("flightList.flight_departure")}}</h3>
                            </div>
                        </template>
                        <template v-if="flightSelected?.step_action > 2">
                            <div class="d-flex align-items-center">
                                <span class="icon icon-plane-left font-28 me-2" id="avion"></span>
                                <h3 class="mb-0">{{__("flightList.flight_arrival")}}</h3>
                            </div>
                        </template>
                    </template>
					<p v-if="!props.isInternational" class="mb-md-3 mb-lg-0">{{__("messages.message_list_title").replace("{0}", `${culture.currencySymbol.trim()} ${culture.currency.trim()}`)}}</p>
				</div>

			</div>

			
			<div class="col-6 col-lg-3 cursor-pointer align-content-end" v-if="isInternational">
				<div class="c-tab-btn text-center p-table" :class="{'hide-line': !showDates, 'pointer': showDates}" v-if="!getIsStepTwo"
					@click="toggleDates()">
					<span class="icon icon-date px-1"></span>
					<span>{{__("messages.near_dates")}} </span>
					<i class="icon icon-keyboard-up font-24 px-1 position-t-4 "
						:class="{'i-rotate-180': !showDates}"></i>
				</div>
			</div>
			<div class="col-6 col-lg-3 cursor-pointer align-content-end" @click.stop.prevent="searchCheapestFlights()" v-if="isInternational">
				<div class="btn-yellow px-2 py-2 d-center " :class="{'m-table': showDates}">

					<div class="custom-control custom-checkbox py-1 d-flex justify-content-center">
						<input type="checkbox" class="custom-control-input" id="customCheck1"
							v-model="isCheapestFlightsApplied">
						<label class="custom-control-label cursor-pointer" for="customCheck1">
							<span class="d-block d-sm-none">
								{{ __("messages.more_flights") }}
							</span>
							<span class="d-none d-sm-block font-14">
								{{ __("messages.cheapest_flights") }}
							</span>
						</label>
					</div>
				</div>
			</div>
			

		</div>
		<div class="c-modal-lateral-mobile">
			<div class="ml-header shadow d-flex align-items-center">
				<div class="row w-100 d-md-none d-lg-none hide-md hide-lg">
					<div class="col-4 text-center">
						<span class="icon icon-arrow-back1 font-24"></span>
					</div>
					<div class="col-8 text-center pl-0">
						<span class="pr-5 font-18 font-roboto-medium">{{__("messages.near_dates")}}</span>
					</div>
				</div>
			</div>
			<CalendarDatesQuotes v-if="showDates" :typeFlight="getTripMode"></CalendarDatesQuotes>
		</div>
	</section>
	<section id="containerDatesMobile" class="container c-mobile-btns mb-3 hide-lg cmb-xs hide-md-pro" :class="{'mt-2' : isRountrip}">
		<div>
			<div class="col-12 col-lg-6 mb-3 px-lg-0 px-md-1">
				<span class="d-grid main-header-list" v-if="!getIsStepTwo && isRountrip && isInternational">
					<span>
						<span class="icon icon-plane-right "></span> {{ __("multiticket.departure_flight") }}
					</span>
					<span>
						{{ startingPlace.cityName }} -  {{ returningPlace.cityName }} {{ $filters.date(getParams.startingFromDateTime, 'ddd D MMM YYYY') }}
					</span>
				</span>
				<span class="d-grid main-header-list" v-if="getIsStepTwo && isRountrip && isInternational">
					<span>
						<span class="icon icon-plane-right "></span> {{ __("multiticket.returning_flight") }}
					</span>
					<span>
						{{ returningPlace.cityName }} -  {{ startingPlace.cityName }} {{ $filters.date(getParams.returningFromDateTime, 'ddd D MMM YYYY') }}
					</span>
				</span>
				<p v-if="!props.isInternational" class="mb-0 mb-md-3 mb-lg-0 ti-txt">{{__("messages.message_list_title").replace("{0}", `${culture.currencySymbol.trim()} ${culture.currency.trim()}`)}}</p>
			</div>
		</div>
		<div class="offcanvas offcanvas-end w-100" tabindex="-1" id="offcanvasCalendar"
			aria-labelledby="offcanvasCalendarLabel">
			<div class="offcanvas-header">
				<div @click="toggleDates()" class="col-2 pl-4 pl-md-2" data-bs-dismiss="offcanvas" aria-label="Close">
					<span class="icon icon-arrow-back1 font-24 pl-2"></span>
				</div>
				<div class="col-10">
					<span class="font-18 font-roboto-medium ml-5 pl-4 d-block">{{__("messages.near_dates")}}</span>
				</div>
			</div>
			<div class="offcanvas-body">
				<CalendarDatesQuotes v-if="showDates" :typeFlight="getTripMode"></CalendarDatesQuotes>
			</div>
		</div>
	</section>
</template>
<script setup>
	import { ref, computed } from 'vue';
	import { storeToRefs } from 'pinia';
	import { cheapestFlights } from '../../../../constants';
	import { __ } from '../../../../utils/helpers/translate';
	import { getFilteredList } from '../../../services/ApiFlightFrontServices';

	import { useFlightStore } from '../../../stores/flight';
	import { useFlightMatrixStore } from '../../../stores/flightMatrix';
	import { usePromotionStore } from '../../../stores/promotion';
	import { useUserSelectionStore } from '../../../stores/user-selection';
    import { List } from '../../../../utils/analytics/flightList.js';
    import {useFlightUpsellStore} from "../../../stores/flightUpsell";
	import { useMultiTicketStore } from '../../../stores/multiTicket';
	import { sleep } from '../../../../utils/helpers/data.js';

	const flightStore = useFlightStore();
	const flightMatrixStore = useFlightMatrixStore();
	const userSelectionStore = useUserSelectionStore();
	const promotionStore = usePromotionStore();

	const { resetFlightResponse, setFlightResponses, setLoading } = flightStore;
    const { addFilter, removeFilter, setFlagFilters } = userSelectionStore;
	const { setLoading: setLoadingMatrix } = flightMatrixStore;

	const { getAllQuoteTokens, getParams, getreturningQuoteToken } = storeToRefs(flightStore);
    const { getFiltersApplied, isCheapestFlightsApplied, getFiltersAppliedArray } = storeToRefs(userSelectionStore);
	const { getTripMode } = storeToRefs(promotionStore);
	const culture = window.__pt.cultureData || {};

    /*** VERSION 2 **/
    const useFlightUpsell = useFlightUpsellStore();
    const { getFlightSelected, getFareKey } = storeToRefs(useFlightUpsell);
    let flightSelected = ref(getFlightSelected.value)
    /*** END VERSION 2 **/

	const useMultiTicket = useMultiTicketStore();
	const { getIsStepTwo } = storeToRefs(useMultiTicket);


	let showDates = ref(false);

	const returningPlace = computed(() =>  { 
    	return getParams.value.returningAirportPlace
 
	})
	const startingPlace = computed(() =>  { 
    	return getParams.value.startingAirportPlace 
	})

	const searchCheapestFlights = async () => {
		setLoading(true);
		setLoadingMatrix(true);

		if (!isCheapestFlightsApplied.value) {
			addFilter(cheapestFlights);
		} else {
			removeFilter(cheapestFlights);
		}

		const params = {
			token: getAllQuoteTokens.value.join(','),
			filterApplied: getFiltersApplied.value,
			site: window.__pt.settings.site.apiFlights.siteConfig,
			roundTripToken: getreturningQuoteToken.value.join(',')
		};
		if (getIsStepTwo.value) {
			params.DepartureToken = flightSelected.value.groupFlight.departure.token;
			params.FlightQuoteId = getFareKey.value;
		} 
		params.allQuotes = !window.__pt.data.isNational && getTripMode.value == 1;

		// if (window.__pt.data.isNational) {amount
			params.tripMode = getTripMode.value;
            params.simpleFlightQuotes = true;
			params.step = true;
        // }
		const response = await getFilteredList(params);
		
        setFlagFilters(getFiltersAppliedArray.value.length == 0);
		if (isCheapestFlightsApplied.value) {
			List.cheapestFlights();
		}
		
		setFlightResponses(response.response);
		setTimeout(()=>{
            setLoading(false);
        }, 500)
		setLoadingMatrix(false);
	};

	const toggleDates = () => {
		if (!showDates.value) {
			List.nearbyDates();
		}
		showDates.value = !showDates.value;
	}

    const props = defineProps({
		isInternational: false,
		mobile: false,
        isRountrip: true
    })
</script>