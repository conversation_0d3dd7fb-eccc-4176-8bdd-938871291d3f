<template>
    <!--<template v-if="(flightSelected.departure_fare && flightSelected.departure_flight) || (flightSelected.returning_fare && flightSelected.returning_flight)">-->
    <section id="cBarInfloFlights" class="cb-flights bg-white p-3 hide-xs pt-2 pb-4" v-if="!isResponsive">
        <!--if selected flight-->
        <div class="bar-line" v-if="flightSelected.barLineBottomShow" id="barLine" :class="{'first-click': flightSelected.firstClick}">
        </div>
        <div id="bottomLine" class="bar-line-bottom"></div>
        <div class="container pt-2 container px-md-0">
            <div class="row">
                <div :class="[{'col-5 offset-5':!isRoundTrip},{'col-5 ps-0 border-end pe-md-4':isRoundTrip}]">
                    <template v-if="flightSelected.departure_fare">
                        <div class="d-flex align-items-center justify-content-end">
                            <div id="vueloIda1" class="row w-100">
                                <div v-if="flightSelected?.departure_flight" class="col-2 d-flex align-items-center justify-content-center">
                                    <img v-if="flightSelected.departure_flight.airline?.code" class="w-100"
                                         :src="'/assets-tb/img/tiquetesbaratos/logos/'+flightSelected.departure_flight.airline?.code+'.png'" /><!-- imagen importante-->
                                </div>
                                <div class="col-8 px-0">
                                    <div class="c-cs c-fs pb-0">
                                        <template v-if="isRoundTrip">
                                            <span class="mr-1 font-14"><strong>{{__(`flightList.departure`)}}: </strong></span>
                                        </template>
                                        <span class="mr-1 font-14" :id="'idaSelectedDepartureAirportArrivalAirport'">
                                            {{
                                                gDestination('from') ?? information.startingAirportPlace.cityName
                                            }} ({{ information.startingAirportPlace.airportCode }}) - {{ gDestination('to') ?? information.returningAirportPlace.cityName }} ({{ information.returningAirportPlace.airportCode }})
                                        </span>
                                        <div class="position-relative information-footer font-14">
                                            <span id="idaSelectedDepartureDateTimeAndArrivalDateTime" v-if="flightSelected?.departure_flight">
                                                {{ flightSelected.departure_flight.departure?.time }}  <span class="icon icon-plane-right"></span> {{
                                                    flightSelected.departure_flight.arrival?.time
                                                }}
                                            </span>
                                            <span> | </span>
                                            <span @click="getFlightDetails(flightSelected.departure_flight,flightSelected.departure_fare,'starting', flightSelected.departure,'FLIGHT_FAMILY')" class="a-link-simple " id="idaSelectedFamilyInformation"> {{ flightSelected.departure_fare.fareName }}</span>
                                            <span> | </span>
                                            <span @click="getFlightDetails(flightSelected.departure_flight,flightSelected.departure_fare,'starting', flightSelected.departure,'FLIGHT_DETAIL')"
                                                  class="a-link-simple" id="idaSelectedStops"> {{ (flightSelected.departure_flight.stops >= 1) ? flightSelected.departure_flight.stops: '' }} {{ __(`messages.stops_${flightSelected.departure_flight.stops >=2 ? 2 : flightSelected.departure_flight.stops}`) }} </span>
                                            <span> | </span>
                                            <span id="idaSelectedPricePerAdult"> <strong> {{$filters.currency(flightSelected.departure_fare.displayAmount) }}</strong></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-2 px-0 d-flex align-items-center">
                                    <a @click="sendData(1, true)" class="a-link-1 ml-2 f-light font-13" id="relink" v-if="(flightSelected.step_action >= 3)">{{__("flightList.change_flight")}}</a>
                                </div>
                            </div>
                        </div>
                    </template>
                    <template v-if="!flightSelected.departure_fare">
                        <div class=" h-100">
                            <div class="col-12 d-flex align-items-center justify-content-center h-100 py-2">
                                <span class="me-2 c-step c-step-green">1</span>
                                <span v-html="__(`flightList.${isRoundTrip ? 'select_your_departure_flight_2' : 'select_your_flight'}`)"></span>
                            </div>
                        </div>
                    </template>
                </div>
                <template v-if="isRoundTrip">
                    <div class="col-5">
                        <div class="row w-100">
                            <div class="col-2 d-flex align-items-center justify-content-center" v-if="flightSelected.returning_fare">
                                <img v-if="flightSelected.returning_flight.airline?.code" class="w-100"
                                     :src="'/assets-tb/img/tiquetesbaratos/logos/'+flightSelected.returning_flight.airline?.code+'.png'" /><!-- imagen importante-->
                            </div>
                            <div class="col-8 px-0">
                                <div class="c-cs c-fs pb-0 pb-0" v-if="flightSelected.returning_fare">
                                    <span class="mr-1 font-14"><strong>{{__(`flightList.returning`)}}: </strong></span>
                                    <span class="mr-1 font-14" id="regresoSelectedDepartureAirportArrivalAirport">
                                        {{ gDestination('to') ?? information.returningAirportPlace.cityName }} ({{ information.returningAirportPlace.airportCode }}) - {{ gDestination('from') ?? information.startingAirportPlace.cityName }} ({{ information.startingAirportPlace.airportCode }})
                                    </span>
                                    <div class="position-relative information-footer font-14">
                                        <span id="regresoSelectedDepartureDateTimeAndArrivalDateTime">
                                            {{ flightSelected.returning_flight.departure?.time }}  <span class="icon icon-plane-right"></span>
                                            {{ flightSelected.returning_flight.arrival?.time }}
                                        </span>
                                        <span> | </span>
                                        <span @click="getFlightDetails(flightSelected.returning_flight,flightSelected.returning_fare,'returning', flightSelected.returning,'FLIGHT_FAMILY')" class="a-link-simple"
                                              id="regresoSelectedFamilyInformation"> {{ flightSelected.returning_fare.fareName }}</span>
                                        <span> | </span>
                                        <span class="a-link-simple mr-2"
                                              @click="getFlightDetails(flightSelected.returning_flight,flightSelected.returning_fare,'returning', flightSelected.returning,'FLIGHT_DETAIL')"
                                              id="regresoSelectedStops"> {{ (flightSelected.returning_flight.stops >= 1) ? flightSelected.returning_flight.stops: '' }} {{ __(`messages.stops_${flightSelected.returning_flight.stops >=2 ? 2 : flightSelected.returning_flight.stops}`) }}</span>
                                        <span> | </span>
                                        <span id="regresoSelectedPricePerAdult"><strong> {{$filters.currency(flightSelected.returning_fare.displayAmount) }}</strong></span>

                                    </div>
                                </div>
                            </div>
                            <!--<div class="col-2 px-0 d-flex align-items-center">
                    <a @click="selectInputFlightMobile(3, false)"  class="a-link-1 ml-2 f-light font-13" id="relink" v-if="(flightSelected.step_action <= 2 && flightSelected.returning_fare && validateVersionAB(2))">{{__("flightList.change_flight")}}</a>
                </div>-->
                        </div>
                        <div id="msjVueloRegreso" :step-action="flightSelected.step_action" class=" h-100 d-flex align-items-center justify-content-center" v-if="!flightSelected.returning_fare">
                            <div class="col-12 d-flex align-items-center justify-content-center h-100 d-none" v-html="__(`flightList.select_your_returning_flight`)"></div>
                            <div class="col-12 d-flex align-items-center justify-content-center h-100 py-2">
                                <span class="me-2 c-step" :class="(flightSelected.step_action >= 3) ? 'c-step-green': 'c-step-gray color-gray-650'">2</span>
                                <span :class="{'color-gray-650': flightSelected.step_action < 3}" v-html="__(`flightList.select_your_returning_flight_2`)"></span>
                            </div>
                        </div>
                    </div>
                </template>
                <!--v2-->
                <template v-if="((flightSelected.step_action <= 2 && flightSelected.departure_fare && !flightSelected.returning_fare) && isRoundTrip)">
                    <div class="col-2 d-flex align-items-center justify-content-center px-0">
                        <button @click="sendData(3)" class="btn btn-primary ps-3 pe-0 py-3 z-999 font-14 btn-ab f-p-medium">{{__("flightList.show_arrival_flights")}} <span class="icon icon-keyboard-right font-22"></span></button>
                    </div>
                </template>
                <!--v2-->
                <div id="BookButton" class="col-2 d-flex align-items-center justify-content-center" v-if="flightSelected.departure_fare && (flightSelected.returning_fare|| !isRoundTrip)">
                    <button @click="submit" class="btn btn-primary px-5 py-3 z-999">{{__(`flightList.book`)}}<span class="icon icon-keyboard-right font-22"></span></button>

                </div>
            </div>
        </div>
    </section>
</template>
<script>
import {storeToRefs} from "pinia";
import { useFlightStore } from '../../../../stores/flight';
import { useFlightUpsellStore } from '../../../../stores/flightUpsell';
import {useFlightRevalidateStore} from "../../../../stores/flightRevalidate";
import {getDetail, getFamilyFare, getParamsDetailFlight, getMatrix, getFilteredList} from "../../../../services/ApiFlightFrontServices";
import {useFlightDetailStore} from "../../../../stores/flightDetail";
import {useFlightFamilyFareStore} from "../../../../stores/flightFamilyFare";
import {List} from "../../../../../utils/analytics/flightList";
import {scrollToElement} from "../../../../../utils/helpers/domEvent";
import {responsiveObserver} from "../../../../../utils/helpers/responsiveObserver";
import { useUserSelectionStore } from '../../../../stores/user-selection';
import { useMultiTicketStore } from '../../../../stores/multiTicket';
import { usePromotionStore } from '../../../../stores/promotion';
import { useFlightMatrixStore } from '../../../../stores/flightMatrix';
import { sleep } from '../../../../../utils/helpers';
import {
    actionFlights, configFilter,
    getFlightsBySimpleFlightQuotes,
    getReturningFlights
} from "../../../../services/fetchListService";
export default {
    data() {
        return {
            oldFlightSelected:{},
            firstClick: true,
            configSite: window.__pt.settings.site.airlineConfiguration.national ?? [],
        }
    },
    props: {
        isRoundTrip: { type: Boolean, default: false },
        submit: Function
    },
   setup(){
        const storeFlight = useFlightStore()
        const useFlightUpsell = useFlightUpsellStore();
        const flightRevalidateStore = useFlightRevalidateStore();
        const flightFamilyFareStore = useFlightFamilyFareStore();
        const useUserSelection = useUserSelectionStore();
        const useMultiTicket = useMultiTicketStore();
        const promotionStore = usePromotionStore();
        const storeFlightMatrix = useFlightMatrixStore();
        const { getParams, getAllQuoteTokens, getReturnQuoteTokens } = storeToRefs(storeFlight); //get
        const { setFlightResponses, resetFlightResponse } = storeFlight;
        const { getTotalAmount } = storeToRefs(flightRevalidateStore);
        const { getFlightSelected } = storeToRefs(useFlightUpsell);
        const flightDetailStore = useFlightDetailStore();
        const { setFlightDetailResponse, activeModalDetail, setExtraData } = flightDetailStore;
        const { setFlightFamilyFareResponse } = flightFamilyFareStore;
        const { getShowDetail } = storeToRefs(flightDetailStore);
        const { setFlightSelected } = useFlightUpsell; //set/actions
        const isResponsiveRef = responsiveObserver.getResponsiveStatus();
        const { changeFilters } = useUserSelection;
        const { setAirlinesFilters, setStopsFilters, setIsStepTwo, setCheckOutDataStepOne } = useMultiTicket;
        const { getTripMode } = storeToRefs(promotionStore);
        const { setLoading, setFlightMatrix } = storeFlightMatrix;
        return {
            getTotalAmount,
            getParams,
            getFlightSelected,
            setFlightDetailResponse,
            setFlightFamilyFareResponse,
            getShowDetail,
            activeModalDetail,
            setFlightSelected,
            isResponsiveRef,
            setExtraData,
            changeFilters,
            setAirlinesFilters,
            setStopsFilters,
            setIsStepTwo,
            getAllQuoteTokens,
            getTripMode,
            resetFlightResponse,
            setFlightResponses,
            getReturnQuoteTokens,
            setLoading,
            setFlightMatrix,
            setCheckOutDataStepOne
        }
    },
    computed: {
        flightSelected() {
            return this.getFlightSelected;
        },
        information() {
            return this.getParams
        },
        isResponsive() {
            return this.isResponsiveRef;
        }
    },
    methods:{
        async getFlightDetails(flight, fare, flightType, airline, type) {
            const modalElement = document.getElementById('modalDetailNational');
            if (this.getShowDetail) return;
            this.activeModalDetail();
            const modal = new bootstrap.Modal(modalElement);
            modal.show();
            this.setExtraData({
                airlineLogoUri: flight.airline.airlineLogoUrl,
                airlineName: flight.airline.name,
                view: flightType == 'starting' ? this.__(`messages.departure_flight`) : this.__(`messages.return_flight`)
            });
            this.paramsDetail = {
                id: flight.flightId,
                flightId: 0,
                airlineLogoUri: 0,
                airlineName: airline.departure?.name?.trim() || airline.returning?.name?.trim(),
                fareId: 0,
                token: airline.quoteToken,
                //token: ((group.departure.token ) ? group.departure.token : group.returning.token) ,
                flightType: flightType,
                familyFare: fare?.fareGroup,
                fareKey: fare?.fareKey,
            };
            let rq = getParamsDetailFlight(this.paramsDetail);

            switch ((String(type)).toUpperCase()) {
                case "FLIGHT_DETAIL":
                    let responseDetail = await getDetail(rq);
                    this.setFlightDetailResponse(responseDetail);

                    List.modalDetail(airline.departure?.code?.trim() || airline.returning?.code?.trim(), "", "",false);
                    this.setFlightFamilyFareResponse({});
                    break;
                case "FLIGHT_FAMILY":
                    let responseFamilyFare = await getFamilyFare(rq);
                    this.setFlightFamilyFareResponse({...responseFamilyFare,params: rq});
                    List.modalDetail(airline.departure?.code?.trim() || airline.returning?.code?.trim(), responseFamilyFare.familyFareName, true);
                    this.setFlightDetailResponse({});
                    break;
            }
            this.activeModalDetail();

        },
        selectInputFlightMobile(step_action, clearAll=true) {
            //if (this.isResponsive) {
            let flight_selected = (this.flightSelected);
            flight_selected.step_action = step_action;
            scrollToElement("#containerDatesMobile");
            let action_event = "show_arrival_list"
            if (step_action >= 2) {
                if(step_action === 3 && clearAll === false){
                    flight_selected.returning_flight = {};
                    flight_selected.returning_fare = null;
                    this.flightSelected.returning = null;
                }
            } else if (step_action <= 1) {
                if(this.isRoundTrip && (configFilter(this.flightSelected.departure_flight.airline?.code) || {})?.searchArrival){
                    getFlightsBySimpleFlightQuotes(this.flightSelected.departure_flight.engine, true, true, true)
                }
                if(clearAll){
                    flight_selected.returning_flight = {};
                    flight_selected.returning_fare = null;

                    flight_selected.departure_flight = {};
                    flight_selected.departure_fare = null;

                    this.flightSelected.departure = null;
                    this.flightSelected.returning = null;
                    action_event = "reset_departure_list"
                }else{
                    flight_selected.departure_flight = {};
                    flight_selected.departure_fare = null;

                    this.flightSelected.departure = null;
                }
            }
            if(step_action === 3) {
                setTimeout(() => {
                    if(this.isRoundTrip){
                        const params = {
                            departureToken: this.flightSelected.departure_token,
                            flightQuoteId: this.flightSelected.departure_fare.fareKey,
                            code: this.flightSelected.departure_flight.airline?.code
                        }
                        let configuration = configFilter(this.flightSelected?.departure_flight?.airline?.code) || {};
                        getReturningFlights(params, configuration, this.flightSelected?.departure_flight?.engine, (res)=>{
                            (Object?.values(res?.returning?.flightList)).forEach((flightList)=>{
                                let roundtrip_fare = null;
                                let roundtrip_flight = null;
                                let diff_fare = 0
                                actionFlights(flightList.flights, {
                                    callbackFare: (fare, $indexFare, flight)=>{
                                        if(fare.isRoundTrip){
                                            const calc_dif = fare.beforeDisplayAmount - fare.displayAmount
                                            if(diff_fare === 0 || diff_fare < calc_dif){
                                                roundtrip_fare = fare;
                                                roundtrip_flight = flight
                                                diff_fare = calc_dif
                                            }
                                        }
                                    }
                                })

                                if(roundtrip_fare && roundtrip_flight) List.roundtripNationalFlight(this.flightSelected.departure_fare, roundtrip_flight, roundtrip_fare)
                                
                            })
                        })   
                    }
                }, 500)
            }
            flight_selected.loadingList = true;
            this.actionFlightSelected(flight_selected)
            /*this.resetFlightResponse();*/
            this.changeFilters([]);
            this.setAirlinesFilters([]) //-> reiniciamos filtros
            this.setStopsFilters('')
            this.setIsStepTwo(step_action === 3);
            setTimeout(() => {
                flight_selected.loadingList = false;
                this.actionFlightSelected(flight_selected)
            }, 1000); //loading ficticio
        },
        async sendData(step_action, clearAll = true) {
            let flight_selected = (this.flightSelected);
            flight_selected.step_action = step_action;
            scrollToElement("#containerDatesMobile");
            let action_event = "show_arrival_list"
            const modal = new bootstrap.Modal(document.getElementById('LoaderFullPage'), null);
            modal.show();
            await sleep(1000);

            const params = {
                token: this.getAllQuoteTokens.join(','),//(!getIsStepTwo.value ? getStartQuoteTokens.join(',') : getReturnQuoteTokens.join(',')),
                filterApplied: '',
                site: window.__pt.settings.site.apiFlights.siteConfig,
                tripMode: this.getTripMode,
                simpleFlightQuotes: true,
                step: true
            };
            if (step_action === 3) {
                params.DepartureToken = this.flightSelected.departure_token;
                params.FlightQuoteId = this.flightSelected.departure_fare.fareKey;
            }
            const response = await getFilteredList(params);
            this.resetFlightResponse();
            await sleep(50)
            this.changeFilters([]);
            await this.setFlightResponses(response.response);

            if (step_action <= 1) {
                await this.setMatrix();
                if (clearAll) {
                    flight_selected.returning_flight = {};
                    flight_selected.returning_fare = null;

                    flight_selected.departure_flight = {};
                    flight_selected.departure_fare = null;

                    this.flightSelected.departure = null;
                    this.flightSelected.returning = null;
                    action_event = "reset_departure_list"
                } else {
                    flight_selected.departure_flight = {};
                    flight_selected.departure_fare = null;

                    this.flightSelected.departure = null;
                }
            } else {
                this.evtReturningFlights(response);
                await this.setMatrix(this.flightSelected.departure_token, this.flightSelected.departure_fare.fareKey);
                const checkoutData = {
                    summary: {
                        token: this.flightSelected.departure_token,
                        fareKey: this.flightSelected.departure_fare.fareKey
                    }
                };
                this.setCheckOutDataStepOne({ ...checkoutData });  //  ->>>>>>>>>>>>>>>>>>>
            }
            flight_selected.loadingList = true;
            this.actionFlightSelected(flight_selected)
            this.setIsStepTwo(step_action === 3);
            flight_selected.loadingList = false;
            this.actionFlightSelected(flight_selected);
            this.setAirlinesFilters([]); //-> reiniciamos filtros
            this.setStopsFilters('');
            await sleep(1000);
            modal.hide();
        },
        async setMatrix(token, fareKey) {
            if (this.getReturnQuoteTokens.length) {
                this.setLoading(true);
                const response = await getMatrix({ token: (this.getReturnQuoteTokens.join(',')), step: true, departureToken: token, flightQuoteId: fareKey });//
                this.setFlightMatrix(response);
                this.setLoading(false);
            }
        },
        evtReturningFlights(data) {
            for (const element of data.response) {
                (Object.values(element.returning.flightList)).forEach((flightList) => {
                    let roundtrip_fare = null;
                    let roundtrip_flight = null;
                    let diff_fare = 0
                    actionFlights(flightList.flights, {
                        callbackFare: (fare, $indexFare, flight) => {
                            if (fare.isRoundTrip) {
                                const calc_dif = fare.beforeDisplayAmount - fare.displayAmount
                                if (diff_fare === 0 || diff_fare < calc_dif) {
                                    roundtrip_fare = fare;
                                    roundtrip_flight = flight
                                    diff_fare = calc_dif
                                }
                            }
                        }
                    })
                    if (roundtrip_fare && roundtrip_flight) List.roundtripNationalFlight(this.flightSelected.departure_fare, roundtrip_flight, roundtrip_fare)
                })
            }
        },
        actionFlightSelected(flight_selected = this.flightSelected) {
            this.setFlightSelected(flight_selected)
        },
        gDestination(key){
            return undefined
        }
    },
    

}
</script>
<style>
.btn-ab {
    .icon {
        left: -4px;
        position: relative;
        top: 0;
    }
}
.font-13 {
	font-size: 13px !important;
}
.information-footer{
    display: flex;
    flex-wrap: nowrap;
    grid-gap: 2px;
    align-items: center;
}
</style>