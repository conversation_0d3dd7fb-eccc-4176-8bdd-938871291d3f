<template>
	<Skeleton v-show="(Object.keys(groups).length == 0 && getProgressBar <= 99) || getLoading" :typeSkeleton="'typeList'" v-for="skeleton in skeletons" />
	<section id="InternationalListContainer" class="container px-md-0 pb-0 py-md-4">
		<div :id="group.departure.code" class="c-box-flights border px-3 mb-3 mb-md-4" v-for="(group, indexe) in groups">

			<div class="row chf-flight position-sticky top-0 z-98">
				<div class="col-12 col-md-4 col-lg-3 bg-white pt-md-2 ps-2 pe-0 chs-logo">
					<div class="chsl">
						<img class="img-airline ms-lg-1" height="auto" width="35" v-lazy="group.departure.image">
						<span class="font-15 ms-1 fs-10-sm">{{group.departure.name}}</span>
					</div>
				</div>
			</div>
			<div class="row c-line">
				<div class="col-12 col-md-8 col-lg-9 px-0 px-md-3 mb-md-2">
					<div class="row">
						<div class="col-12 px-md-0">
							<div class="col-12 bg-white position-sticky sticky-int z-96 pb-1">
								<h3 class="title-list position-relative mt-md-3 px-md-3">
									<span class="icon icon-plane-right font-20"></span>
									<span class="font-poppins-medium font-16 ms-2">{{__("messages.departure_flight")}}</span>
									&nbsp;
									<span class="font-poppins font-16 date-mobile ms-1">
										{{ $filters.date(group.departure.departure.date, 'dddd, DD  MMMM  YYYY') }}
									</span>
								</h3>
							</div>
							<div class="mt-1 c-if-round mx-md-2 c-border-xs">
								<!-- start row -->
								<template v-for="(flight,index) in group.departure.flights">
									<div v-show="configList[indexe]['departure']['rows'][index]['hiddenRows']"
										 @click.stop.prevent="getFlightRevalidate(flight, index, group, indexe, 'departure')"
										 class="c-row-line cursor-pointer"
										 :class="{'row-color-1' : index % 2 == 0, 'row-color-2' : index % 2 != 0 }">
										<div :id="group.departure.code+index" class="row position-relative mx-0 pt-md-1 pb-0 pb-md-0 row-active overflow-hidden pb-1 pb-md-0 d-md-flex justify-content-md-between">
											<div class="col-9 col-md-6 col-lg-5 px-0 pe-2 order-1">
												<div class="row mx-0 cl-iatas">
													<div class="col-3 col-md-4 col-lg-2 px-0">
														<span class="font-10 cli-left" v-if="isMultipleAirport">
															{{flight.departure.airportCode}}
														</span>
													</div>
													<div class="col-6 col-md-4 col-lg-8 position-relative cvs-line">
														<div class="c-line-schedule"></div>
													</div>
													<div class="col-3 col-md-4 col-lg-2 px-0">
														<span class="font-10 cli-right" v-if="isMultipleAirport">
															{{flight.arrival.airportCode}}
														</span>
													</div>
												</div>
												<div class="row mx-0">
													<div class="col-3 col-md-4 col-lg-2 px-0">
														<p class="text-time fw-bold my-0 tt-left">
															{{flight.departure.time}}
														</p>
													</div>
													<div class="col-6 col-md-4 col-lg-8 px-0 position-relative">
														<div class="row w-100 m-0 line-fligths-1">
															<!--<span class="f-01 font-10 px-1" v-if="isMultipleAirport">
																{{flight.departure.airportCode}}
															</span>-->
															<div class="col-12 text-center px-0">
																<span class="font-12 cf-duration px-1 cvs-duration">{{flight.flightDuration}}</span>
															</div>
															<!--<div class="col-12 c-line-schedule"></div>-->
														</div>
													</div>
													<div class="col-3 col-md-4 col-lg-2 px-0 line-fligths-2  position-relative">
														<!--<span class="f-01 font-10" v-if="isMultipleAirport">
															{{flight.arrival.airportCode}}
														</span>-->
														<p class="text-time fw-bold my-0 tt-right">{{flight.arrival.time}}</p>
														<span v-if="flight.flightDays > 0" class="ci-tooltip position-absolute ca-roundtrip">
															+{{flight.flightDays}} <span class='cit-day'> {{flight.flightDays > 1 ? 'dias' : 'dia'}}</span>
															<span class="tooltiptext tooltip-top">Llegas el {{ $filters.date(flight.arrival.date, 'ddd DD MMM YYYY') }}.</span>
														</span>
													</div>
												</div>
											</div>
											<div class="ps-lg-4 col-5 col-md-2 order-md-2 col-lg-3 order-4 order-md-3">
												<div id="onClickViewDetail-1" class="col-12 c-click-detail z-95"
													 @click.stop.prevent="getFlightDetails(group.departure.name, group.departure.image, flight, group, 'starting')">
													<div id="overlayDetail-1" class="if-overlay c-overlay d-none"></div>
													<div class="m-0 text-center mt-1 mt-md-2 pointer font-14 z-95 position-relative">
														<span class="f-r-semibold pr-lg-0 sp d-block cvs-scale">{{ getScales(flight.stops) }}</span>
													</div>
												</div>
											</div>
											<template v-if="luggages[indexe]['departure'][index]">
												<div class="col-2 col-lg-2 d-flex align-items-center order-3 z-95"
													 @click.stop.prevent="getFlightDetails(group.departure.name, group.departure.image, flight, group, 'starting', true)">
													<span :id="`hand${indexe}${index}`" @mouseover="handleMouseOver($event)" data-bs-toggle="tooltip" data-bs-placement="top" :title="luggages[indexe]['departure'][index]['extra'][2]['title']" :class="luggages[indexe]['departure'][index]['extra'][2]['class']"></span>
													<span :id="`checked${indexe}${index}`" @mouseover="handleMouseOver($event)" data-bs-toggle="tooltip" data-bs-placement="top" :title="luggages[indexe]['departure'][index]['extra'][1]['title']" :class="luggages[indexe]['departure'][index]['extra'][1]['class']"></span>
												</div>
											</template>
											<template v-else>
												<div class="col-2 d-flex align-items-center order-3 z-95">
													<div class="col-5 col-md-2">
														<div class="c-card is-loading p-0 m-0 w-100">
															<div class="pe-1 py-2 p-md-0 p-lg-1">
																<p class="m-0 me-md-1 me-lg-0 py-2"></p>
															</div>
														</div>
													</div>
													<div class="col-5 col-md-2">
														<div class="c-card is-loading p-0 m-0 w-100">
															<div class="pe-1 py-2 p-md-0 p-lg-1">
																<p class="m-0 ms-md-1 ms-lg-0 py-2"></p>
															</div>
														</div>
													</div>
												</div>
											</template>
											<div class="col-3 col-md-2 order-md-4 order-2">
												<div class="d-flex">
													<div class="radio py-md-2 ms-auto c-radio" :class="{'cr-economic': flight.cheap}">
														<input class="position-relative"
															   type="radio"
															   :name="`r-departure-${group.departure.code}`"
															   :id="`r-departure-${group.departure.code}-${index}`">
														<label class="font-18 position-relative z-95" :for="`r-departure-${group.departure.code}-${index}`"></label>
													</div>
												</div>
											</div>

										</div>
									</div>
								</template>
								<!-- end row -->
								<div v-if="configList[indexe]['departure']['configFooter']['active']"
									 class="row c-view-more pointer z-95"
									 :class="{'tab-footer-offers': configList[indexe]['departure']['configFooter']['economic']}"
									 @click="showRows(indexe, 'departure')">
									<div class="col-12 text-center py-2 border-top cursor-pointer">
										<span v-if="configList[indexe]['departure']['configFooter']['economic']" class="txt">{{__("flightList.seeMoreEconomicFligthsDeparture")}}</span>
										<span v-else class="txt">{{__("flightList.seeMoreFligthsDeparture")}}</span>
										<span class="icon icon-expand font-24"></span>
									</div>
								</div>
							</div>
						</div>
						<div class="col-12 px-md-0">
							<div class="col-12 bg-white position-sticky sticky-int z-96 pb-1">
								<h3 class="title-list position-relative mt-md-3 px-md-3">
									<span class="icon icon-plane-left font-20"></span>
									<span class="font-poppins-medium font-16 ms-2 me-1">{{__("messages.return_flight")}}</span>
									&nbsp;
									<span class="font-poppins font-16 date-mobile ms-1">
										{{ $filters.date(group.returning.departure.date, 'dddd, DD  MMMM  YYYY') }}
									</span>
								</h3>
							</div>
							<div class="mt-1 c-if-round mx-md-2 c-border-xs">
								<template v-for="(flight, index) in group.returning.flights">
									<div :id="group.departure.code+'Regreso'+index" v-show="configList[indexe]['returning']['rows'][index]['hiddenRows']"
										 @click.stop.prevent="getFlightRevalidate(flight, index, group, indexe, 'returning')"
										 class="c-row-line cursor-pointer"
										 :class="{'row-color-1' : index % 2 == 0, 'row-color-2' : index % 2 != 0 }">
										<div id="rowActive-1" class="row position-relative mx-0 pt-md-1 pb-0 pb-md-0 row-active overflow-hidden pb-1 pb-md-0 d-md-flex justify-content-md-between">
											<div class="col-9 col-md-6 col-lg-5 px-0 pe-2 order-1">

												<div class="row mx-0 cl-iatas">
													<div class="col-3 col-md-4 col-lg-2 px-0">
														<span class="font-10 cli-left" v-if="isMultipleAirport">
															{{flight.departure.airportCode}}
														</span>
													</div>
													<div class="col-6 col-md-4 col-lg-8 position-relative cvs-line">
														<div class="c-line-schedule"></div>
													</div>
													<div class="col-3 col-md-4 col-lg-2 px-0">
														<span class="font-10 cli-right" v-if="isMultipleAirport">
															{{flight.arrival.airportCode}}
														</span>
													</div>
												</div>

												<div class="row mx-0">
													<div class="col-3 col-md-4 col-lg-2 px-0">
														<p class="text-time fw-bold tt-left">
															{{flight.departure.time}}
														</p>
													</div>
													<div class="col-6 col-md-4 col-lg-8 position-relative px-1 px-md-0">
														<div class="row w-100 m-0 line-fligths-1">
															<!--<span class="f-01 font-10 px-1" v-if="isMultipleAirport"> {{flight.departure.airportCode}} </span>-->
															<div class="col-12 text-center px-0">
																<span class="font-12 cf-duration px-1 cvs-duration">{{flight.flightDuration}}</span>
															</div>
															<!--<div class="col-12 c-line-schedule"></div>-->
														</div>
													</div>
													<div class="col-3 col-md-4 col-lg-2 px-0 line-fligths-2 position-relative">
														<!--<span class="f-01 font-10" v-if="isMultipleAirport">{{flight.arrival.airportCode}}</span>-->
														<p class="text-time fw-bold my-0 tt-right">{{flight.arrival.time}}</p>
														<span v-if="flight.flightDays > 0" class="ci-tooltip position-absolute ca-roundtrip">
															+{{flight.flightDays}} <span class='cit-day'> {{flight.flightDays > 1 ? 'dias' : 'dia'}}</span>
															<span class="tooltiptext tooltip-top">Llegas el {{ $filters.date(flight.arrival.date, 'ddd DD MMM YYYY') }}.</span>
														</span>
													</div>
												</div>
											</div>
											<div class="col-5 col-md-2 col-lg-3 order-md-2 order-4 order-md-3">
												<div id="onClickViewDetail-1" class="col-12 px-1 c-click-detail z-95"
													 @click.stop.prevent="getFlightDetails(group.returning.name, group.returning.image, flight, group, 'returning')">
													<div id="overlayDetail-1" class="if-overlay c-overlay d-none"></div>
													<div class="m-0 text-center mt-1 mt-md-2 pointer font-14 z-95 position-relative">
														<span class="f-r-semibold pr-lg-0 sp d-block cvs-scale">{{ getScales(flight.stops) }}</span>
													</div>
												</div>
											</div>
											<template v-if="luggages[indexe]['returning'][index]">
												<div class="col-2 col-lg-2 d-flex align-items-center order-3 z-95"
													 @click.stop.prevent="getFlightDetails(group.returning.name, group.returning.image, flight, group, 'returning', true)">
													<span :id="`handR${indexe}${index}`" @mouseover="handleMouseOver($event)" data-bs-toggle="tooltip" data-bs-placement="top" :title="luggages[indexe]['returning'][index]['extra'][2]['title']" :class="luggages[indexe]['returning'][index]['extra'][2]['class']"></span>
													<span :id="`checkedR${indexe}${index}`" @mouseover="handleMouseOver($event)" data-bs-toggle="tooltip" data-bs-placement="top" :title="luggages[indexe]['returning'][index]['extra'][1]['title']" :class="luggages[indexe]['returning'][index]['extra'][1]['class']"></span>
												</div>
											</template>
											<template v-else>
												<div class="col-2 d-flex align-items-center order-3 z-95">
													<div class="col-5 col-md-2">
														<div class="c-card is-loading p-0 m-0 w-100">
															<div class="pe-1 py-2 p-md-0 p-lg-1">
																<p class="m-0 me-md-1 me-lg-0 py-2"></p>
															</div>
														</div>
													</div>
													<div class="col-5 col-md-2">
														<div class="c-card is-loading p-0 m-0 w-100">
															<div class="pe-1 py-2 p-md-0 p-lg-1">
																<p class="m-0 ms-md-1 ms-lg-0 py-2"></p>
															</div>
														</div>
													</div>
												</div>
											</template>
											<div class="col-3 col-md-2 order-md-4 order-2">
												<div class="d-flex">
													<div class="radio py-md-2 ms-auto c-radio" :class="{'cr-economic': flight.cheap}">
														<input class="position-relative"
															   type="radio"
															   :name="`r-returning-${group.departure.code}`"
															   :id="`r-returning-${group.departure.code}-${index}`">
														<label class="font-18 position-relative z-95" :for="`r-returning-${group.departure.code}-${index}`"></label>
													</div>
												</div>
											</div>

										</div>
									</div>
								</template>
								<div v-if="configList[indexe]['returning']['configFooter']['active']"
									 class="row c-view-more pointer z-95"
									 :class="{'tab-footer-offers': configList[indexe]['returning']['configFooter']['economic']}"
									 @click="showRows(indexe, 'returning')">
									<div class="col-12 text-center py-2 border-top cursor-pointer">
										<span v-if="configList[indexe]['returning']['configFooter']['economic']" class="txt">{{__("flightList.seeMoreEconomicFligthsReturning")}}</span>
										<span v-else class="txt">{{__("flightList.seeMoreFligthsReturning")}}</span>
										<span class="icon icon-expand font-24"></span>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="col-12 col-md-4 col-lg-3 sticky-bottom z-96">
					<div class="text-end w-100 mt-0 mt-lg-2 sticky-top">
						<div class="hide-lg pt-2"></div>
						<div class="row py-0 mb-2 mb-md-0">
							<div class="col-7 col-md-12 px-0 px-md-3">
								<div class="pt-2 mt-1 pt-md-0 mt-md-0">
									<span v-if="configList[indexe]['totalPriceMessage']" class="mb-0 f-p-medium c-txt-total">
										{{information.adults + information.kids > 1 ? __("messages.by_passenger") : __("messages.total")}}:
									</span>
									<span v-else class="mb-0 f-p-medium c-txt-total">{{__("messages.from")}}: </span>
									<span :id="group.departure.code+'Total'" class="mb-0 f-p-medium color-blue c-txt-rate">
										<CurrencyDisplay :amount="configList[indexe]['price']" :showCurrencyCode="false" />
									</span>
								</div>
								<div class="font-12">({{ __("flightList.fairesIncluded") }})</div>
							</div>
							<div class="col-5 col-md-12 mt-2 mt-md-3">
								<button @click="getUpsellList(indexe, group)" class="btn btn-primary d-block w-100 py-3 py-md-3 f-p-medium font-14">
									{{__("messages.select")}}
								</button>
							</div>
						</div>
						<!--cargando-->
						<div class="loading-component" :style="`display: ${configList[indexe]['loading']}`">
							<div class="lds-dual-ring"></div>
							<p class="text-uppercase font-16 f-r-semibold mt-3 mb-1 loading-text">Cargando tarifa...</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>
	<ModalUpsell v-if="getShowUpsell" />
	<ModalMessages :msg="msg" :btn='__("booker.ok")' :title='__("messages.choose_another_flight")' />
</template>

<script>
	import { storeToRefs } from 'pinia';
	import { useFlightStore } from '../../../stores/flight';
	import { useFlightUpsellStore } from '../../../stores/flightUpsell';
	import { useFlightDetailStore } from '../../../stores/flightDetail';
	import { useFlightFamilyFareStore } from '../../../stores/flightFamilyFare';
	import { useFlightRevalidateStore } from '../../../stores/flightRevalidate';
	import { useFlightMatrixStore } from '../../../stores/flightMatrix';
	import { List } from '../../../../utils/analytics/flightList.js'
	import { getDetail, getParamsDetailFlight, getFamilyFare, getParamsUpsell, getUpsell, getParamsRevalidate, getRevalidate } from '../../../services/ApiFlightFrontServices';
	import { Logger } from '../../../../utils/helpers/logger';
	import { useUserSelectionStore } from '../../../stores/user-selection';
	import jsonData from '../../../../../json/iatasGrouper.json';
	import CurrencyDisplay from '../../common/CurrencyDisplay.vue';

	export default {
		props: ['title', 'subtitle', 'urlWithoutAirline', 'airlineCode'],
		data() {
			return {
				configSite: window.__pt.settings.site.airlineConfiguration.international ?? [],
				skeletons: [1, 2, 3],
				paramsDetail: {},
				paramsRevalidate: {},
				configList: {},
				paramsUpsell: {},
				msg: "",
				iatasAgrouper: jsonData,
				configLuggage: {}

			}
		},

		setup() {
			const useFlightUpsell = useFlightUpsellStore();
			const storeFlight = useFlightStore()
			const flightDetailStore = useFlightDetailStore();
			const flightFamilyFareStore = useFlightFamilyFareStore();
			const flightRevalidateStore = useFlightRevalidateStore();
			const flightMatrixStore = useFlightMatrixStore();
			const userSelectionStore = useUserSelectionStore();

			const { getParams, getGroups, getFlightResponse, getProgressBar, getfirstCheapest } = storeToRefs(storeFlight); //get
			const { getFlightUpsell, getShowUpsell } = storeToRefs(useFlightUpsell);
			const { getTotalAmount } = storeToRefs(flightRevalidateStore);
			const { getShowDetail } = storeToRefs(flightDetailStore);
			const { getLoading } = storeToRefs(flightMatrixStore);
			const { getLuggage } = storeToRefs(flightFamilyFareStore);
			const { getFiltersAppliedArray, getFlagFilters } = storeToRefs(userSelectionStore);

			const { changeOpenCloseModalUpsell, setFlightUpsell, setFlightSelected, activateLoadingUpsell } = useFlightUpsell; //set/actions
			const { setFlightDetailResponse, activeModalDetail, setExtraData } = flightDetailStore;
			const { setFlightFamilyFareResponse, setIsLuggage } = flightFamilyFareStore;
			const { setFlightRevalidateResponse, setRevalidateStatus } = flightRevalidateStore;
			const { setFlagFilters } = userSelectionStore;

			return {
				changeOpenCloseModalUpsell,
				getParams, getGroups, getFlightResponse,
				setFlightDetailResponse, setFlightFamilyFareResponse,
				getFlightUpsell, setFlightUpsell,
				getShowUpsell, setFlightSelected, activateLoadingUpsell,
				setFlightRevalidateResponse, getTotalAmount, setRevalidateStatus,
				activeModalDetail, getShowDetail, getProgressBar, getLoading, getfirstCheapest,
				getFiltersAppliedArray, getFlagFilters, setFlagFilters, setExtraData, getLuggage,
				setIsLuggage
			}
		},


		mounted() {
			window.addEventListener('scroll', this.handleScroll);
		},
		beforeDestroy() {
			window.removeEventListener('scroll', this.handleScroll);
		},

		computed: {
			groups() {
				const views = ['departure', 'returning'];
				for (let grupo in this.getGroups) {
					this.configList[grupo] = {};
					this.configList[grupo]['price'] = this.getGroups[grupo].price;
					this.configList[grupo]['loading'] = 'none';
					this.configList[grupo]['totalPriceMessage'] = this.getGroups[grupo]['departure']['flights'].length == 1 && this.getGroups[grupo]['returning']['flights'].length == 1;
					for (let indexView in views) {
						let cheaspest = true;
						const view = views[indexView];
						this.configList[grupo][view] = {};
						this.configList[grupo][view]['configFooter'] = {};
						this.configList[grupo][view]['rows'] = {};

						this.configList[grupo][view]['configFooter']['economic'] = false;
						this.configList[grupo][view]['configFooter']['active'] = Object.keys(this.getGroups[grupo][view]['flights']).length > 8;
						let inCheaspest = 0;
						for (let index in this.getGroups[grupo][view]['flights']) {
							this.configList[grupo][view]['rows'][index] = {};
							this.configList[grupo][view]['rows'][index]['hiddenRows'] = index < 8;
							if (cheaspest && this.getGroups[grupo][view]['flights'][index]['cheap']) {
								inCheaspest = index;
								cheaspest = false;
							}
							if (index > 7 && this.getGroups[grupo][view]['flights'][index]['cheap']) {
								this.configList[grupo][view]['configFooter']['economic'] = true;
							}
						}
						inCheaspest = cheaspest ? 0 : inCheaspest;
						this.configList[grupo][view]['selectedViewInfo'] = {
							code: this.getGroups[grupo][view]['flights'][inCheaspest].airline.code,
							flightId: this.getGroups[grupo][view]['flights'][inCheaspest].id,
							fareId: this.getGroups[grupo][view]['flights'][inCheaspest].fares[0].fareId,
							fareKey: this.getGroups[grupo][view]['flights'][inCheaspest].fares[0].fareKey,
							amount: this.getGroups[grupo][view]['flights'][inCheaspest].fares[0].amount,
                            date: view == "returning" ? this.getGroups[grupo][view]['flights'][inCheaspest].departureTime : this.getGroups[grupo][view]['flights'][inCheaspest].arrivalTime,
							index: inCheaspest
						}
					}
				}
				return this.getGroups
			},
			luggages() {
				const views = ['departure', 'returning'];
				for (let grupo in this.getGroups) {
					this.configLuggage[grupo] = {};
					for (let indexView in views) {
						const view = views[indexView];
						this.configLuggage[grupo][view] = {};
						for (let index in this.getGroups[grupo][view].flights) {
							let code = '';
							for (let seg in this.getGroups[grupo][view].flights[index].segments) {
								code += this.getGroups[grupo][view].flights[index].segments[seg].departure.code + "-";
								code += this.getGroups[grupo][view].flights[index].segments[seg].arrival.code + "-";
								code += this.getGroups[grupo][view].flights[index].segments[seg].operatingAirline.code + "-";
								code += this.getGroups[grupo][view].flights[index].segments[seg].marketingAirline.code + "-";
							}
							code += this.getGroups[grupo][view].flights[index].fares[0].fareGroup;
							this.configLuggage[grupo][view][index] = (this.getLuggage || {})[code];
						}
					}
				}
				return this.configLuggage
			},
			information() {
				return this.getParams
			},
			isMultipleAirport() {
				const iataStarting = window.__pt.data.startingFromAirport;
				const iataReturning = window.__pt.data.returningFromAirport;
				return this.iatasAgrouper[iataStarting] != undefined && this.iatasAgrouper[iataStarting]
					|| this.iatasAgrouper[iataReturning] != undefined && this.iatasAgrouper[iataReturning];
			},
		},
		updated() {
			if (!this.getLoading && ((this.getProgressBar > 0 && this.getProgressBar < 100) || (this.getFiltersAppliedArray.length > 0 && !this.getFlagFilters) || (this.getFiltersAppliedArray.length == 0 && this.getFlagFilters))) {
				for (let view in this.getfirstCheapest) {
					for (let group in this.getfirstCheapest[view]) {
						for (let index in this.getfirstCheapest[view][group]) {
							if (this.getfirstCheapest[view][group][index]) {
								document.getElementById(`r-${view}-${group}-${index}`).checked = true;
							}
						}
					}
				}
				if (this.getFiltersAppliedArray.length > 0 && !this.getFlagFilters) {
					this.setFlagFilters(true);
				}
				if (this.getFiltersAppliedArray.length == 0 && this.getFlagFilters) {
					this.setFlagFilters(false);
				}
			}
		},
		methods: {
			handleMouseOver(event) {
				if (!window.__pt.settings.site.isMobileDevice()) {
					const span = document.getElementById(event.target.id);
					const tooltip = new bootstrap.Tooltip(span);
					tooltip.show();
				}
			},
			getScales(scales) {
				let scale = this.__(`messages.stops_0`);
				if (scales >= 1) {
					scale = scales == 1 ? `${scales} ${this.__("messages.stops_1")}` : `${scales} ${this.__("messages.stops_2")}`;
				}
				return scale;
			},
			showRows(indexGroup, view) {
				for (let clave in this.configList[indexGroup][view]['rows']) {
					if (!this.configList[indexGroup][view]['rows'][clave]['hiddenRows'] || clave > 7) {
						this.configList[indexGroup][view]['rows'][clave]['hiddenRows'] = !this.configList[indexGroup][view]['rows'][clave]['hiddenRows'];
					}
				}
				this.configList[indexGroup][view]['configFooter']['active'] = !this.configList[indexGroup][view]['configFooter']['active'];
			},
			async getFlightDetails(name, image, flight, group, type, luggage = false) {
				if (this.getShowDetail) return;
				this.activeModalDetail();
				const modalElement = document.getElementById('modalDetail');
				const modal = new bootstrap.Modal(modalElement);
				this.setExtraData({
					airlineLogoUri: image,
					airlineName: name,
					view: type == 'starting' ? this.__(`messages.departure_flight`) : this.__(`messages.return_flight`)
				});
				modal.show();
				this.paramsDetail = {
					token: group.quoteToken,
					flightId: flight.id,
					flightType: type,
					airlineLogoUri: image,
					airlineName: name,
					fareId: flight.fares[0].fareId,
					fareKey: flight.fares[0].fareKey,
				};
				let rq = getParamsDetailFlight(this.paramsDetail);

				let responseDetail = await getDetail(rq);
				let responseFamilyFare = await getFamilyFare(rq);

				this.setFlightDetailResponse(responseDetail);
				this.setFlightFamilyFareResponse(responseFamilyFare);
				List.modalDetail(group.departure.code, responseFamilyFare.familyFareName, type == 'starting' ? 'ida' : 'regreso', luggage);
				this.activeModalDetail();
				this.setIsLuggage(luggage);
			},
			async getFlightRevalidate(flight, indexFlight, group, indexGroup, view) {
				if (Object.keys(this.configList[indexGroup]['departure']['selectedViewInfo']).length > 0
					&& Object.keys(this.configList[indexGroup]['returning']['selectedViewInfo']).length > 0
					&& this.configList[indexGroup]['returning']['selectedViewInfo'].code == this.configList[indexGroup]['departure']['selectedViewInfo'].code) {
					const fechaLlegada = new Date(view == "returning" ? this.configList[indexGroup]['departure']['selectedViewInfo'].date : flight.arrivalTime);
					const fechaRegreso = new Date(view == "returning" ? flight.departureTime : this.configList[indexGroup]['returning']['selectedViewInfo'].date);
					if (fechaLlegada >= fechaRegreso) {
						document.getElementById(`r-${view}-${this.configList[indexGroup][view]['selectedViewInfo'].code}-${indexFlight}`).checked = false;
						document.getElementById(`r-${view}-${this.configList[indexGroup][view]['selectedViewInfo'].code}-${this.configList[indexGroup][view]['selectedViewInfo'].index}`).checked = true;
						this.msg = this.__("messages.date_validation_roundtrip");
						const modalMessages = document.getElementById('modalMessages');
						const modal = new bootstrap.Modal(modalMessages);
						modal.show();
						return false;
					}
					document.getElementById(`r-${view}-${this.configList[indexGroup][view]['selectedViewInfo'].code}-${indexFlight}`).checked = true;
					let paramsDataView = {
						code: flight.airline.code,
						flightId: flight.id,
						fareId: flight.fares[0].fareId,
						fareKey: flight.fares[0].fareKey,
						amount: flight.fares[0].amount,
						date: view == "returning" ? flight.departureTime : flight.arrivalTime,
						index: indexFlight
					};
					this.configList[indexGroup][view]['selectedViewInfo'] = paramsDataView;
					this.configList[indexGroup]['loading'] = 'flex';
					this.paramsRevalidate = {
						fareKey: this.configList[indexGroup]['departure']['selectedViewInfo'].fareKey + "|" + this.configList[indexGroup]['returning']['selectedViewInfo'].fareKey,
						outboundFlightId: this.configList[indexGroup]['departure']['selectedViewInfo'].flightId,
						OutputFareId: this.configList[indexGroup]['departure']['selectedViewInfo'].fareId,
						returnFlightId: this.configList[indexGroup]['returning']['selectedViewInfo'].flightId,
						ReturnFareId: this.configList[indexGroup]['returning']['selectedViewInfo'].fareId,
						token: group.quoteToken
					}
					const rq = getParamsRevalidate(this.paramsRevalidate);
					const responseRevalidate = await getRevalidate(rq);
					this.setFlightRevalidateResponse(responseRevalidate);
					const totalAmount = this.getTotalAmount > 0 ? this.getTotalAmount
						: this.configList[indexGroup]['departure']['selectedViewInfo'].amount + this.configList[indexGroup]['returning']['selectedViewInfo'].amount;
					this.configList[indexGroup]['price'] = totalAmount / (window.__pt.data.adults + window.__pt.data.kids);
					this.configList[indexGroup]['totalPriceMessage'] = true;
					this.configList[indexGroup]['loading'] = 'none';
				}
			},
			async getUpsellList(indexGroup, group) {
				const flights = {
					departureFlight: group.departure.flights[this.configList[indexGroup]['departure']['selectedViewInfo'].index],
					returningFlight: group.returning.flights[this.configList[indexGroup]['returning']['selectedViewInfo'].index],
					totalAmount: (this.configList[indexGroup]['departure']['selectedViewInfo'].amount + this.configList[indexGroup]['returning']['selectedViewInfo'].amount) / (window.__pt.data.adults + window.__pt.data.kids)
				};
				const quoteTaskID = [
					{ taskID: group.quoteTokenFQS },
					{ taskID: group.quoteTokenFQS + "-2" }
				];
				this.setFlightSelected(flights);
				this.setRevalidateStatus(false);
				this.paramsUpsell = {
					fareKey: this.configList[indexGroup]['departure']['selectedViewInfo'].fareKey + "|" + this.configList[indexGroup]['returning']['selectedViewInfo'].fareKey,
					outboundFlightId: this.configList[indexGroup]['departure']['selectedViewInfo'].flightId,
					OutputFareId: this.configList[indexGroup]['departure']['selectedViewInfo'].fareId,
					returnFlightId: this.configList[indexGroup]['returning']['selectedViewInfo'].flightId,
					ReturnFareId: this.configList[indexGroup]['returning']['selectedViewInfo'].fareId,
					token: group.quoteToken
				}
				this.setFlightUpsell({});
				this.activateLoadingUpsell();
				this.changeOpenCloseModalUpsell();
				const promises = new Promise(async (resolve, reject) => {
					const responses = {};
					const rqRevalidate = getParamsRevalidate(this.paramsUpsell);
					responses['revalidate'] = await getRevalidate(rqRevalidate);
					responses['revalidate']['isSelected'] = true;
					responses['revalidate']['id'] = this.paramsUpsell.fareKey;
					responses['revalidate']['taskID'] = quoteTaskID;
					responses['revalidate']['quoteTaskID'] = responses['revalidate']['quoteTaskID'] ? responses['revalidate']['quoteTaskID'] : group.quoteToken;
					responses['familyFare'] = {};
					if (responses['revalidate'] && !responses['revalidate'].error && !responses['revalidate'].error) {
						if (Object.keys(responses['revalidate'].fares).length > 0) {
							const fareGroup = responses['revalidate'].fares[0].fareGroup;
							let flightFareFamily = {};
							let flightType = '';
							if (flights.departureFlight.fares[0].fareGroup == fareGroup) {
								flightFareFamily = flights.departureFlight;
								flightType = 'starting';
							} else {
								flightFareFamily = flights.returningFlight;
								flightType = 'returning';
							}
							this.paramsDetail = {
								token: group.quoteToken,
								flightId: flightFareFamily.id,
								flightType: flightType,
								airlineLogoUri: group.departure.image,
								airlineName: group.departure.name,
								fareId: flightFareFamily.fares[0].fareId,
								fareKey: flightFareFamily.fares[0].fareKey,
							};
							const rqFamilyFare = getParamsDetailFlight(this.paramsDetail);
							responses['familyFare'] = await getFamilyFare(rqFamilyFare);
						}
						resolve(responses);
					}
					reject("Error revalidate");
				}).then(res => {
					this.setFlightRevalidateResponse(res['revalidate']);
					this.setFlightFamilyFareResponse(res['familyFare']);
				}).catch(err => {
					// Error handler when any requesst fails
					Logger.warn(err);
				}).finally(async () => {
					this.setRevalidateStatus(true);
				});
				await promises;

				let rq = getParamsUpsell(this.paramsUpsell);

				let responseUpsell = await getUpsell(rq);
				responseUpsell.quoteToken = group.quoteToken;
				responseUpsell.taskID = quoteTaskID;
				this.setFlightUpsell(responseUpsell);
			},
			firstAirline(value) {
				value = value.toUpperCase()
				return (this.configSite || []).sort((a, b) => ((a.airlineCode || []).find(code => code.toUpperCase() === value) || (a.airlineName).toUpperCase() === value) ? -1 : 1);
			},
			sortAirlines(airlines) {
				if (this.information.addFirst && this.information.addFirst !== 'home') {
					this.configSite = this.firstAirline(this.information.addFirst)
				}
				else if (this.information.landing && this.information.landing !== 'home') {
					this.configSite = this.firstAirline(this.information.landing)
				}

				return (this.configSite).flatMap(
					setting => airlines.filter(
						airline => (setting.airlineCode || []).find(
							code => airline.departure?.code?.toUpperCase() === code.toUpperCase())))
			},
			handleScroll() {
				const item = document.getElementsByClassName('sticky-int');
				const scrollTop = Math.round(window.scrollY);
				for (let i = 0; i < item.length; i++) {
					const rect = item[i].getBoundingClientRect();
					if ((scrollTop + 35) >= (rect.top + window.scrollY) && !item[i].classList.contains('shadow-sticky')) {
						item[i].classList.add('shadow-sticky');
					} else if (scrollTop + 40 < (rect.top + window.scrollY) && item[i].classList.contains('shadow-sticky')) {
						item[i].classList.remove('shadow-sticky');
					}
				}
			},
		},
		components: {
			CurrencyDisplay
		}
	}
</script>

<style lang="scss" scoped>
	.one-flight .c-view-more {
		border-radius: 0 0 9px 9px
	}

	@media (min-width:768px) and (max-width:1024px),(min-width:1025px) {
		.one-flight .c-view-more {
			margin-left: 0;
			margin-right: 0
		}
	}

	.one-flight .c-info-flight .bg-gray {
		margin: 0 1px 0 -14px
	}

	.one-flight .c-bar-info {
		margin-left: 0 !important;
		margin-right: 0 !important
	}

	.row-round {
		border-top-left-radius: 9px
	}

	@media (max-width:767px) {
		.row-round {
			border-radius: 9px 9px 0 0
		}
	}

	.cf-logo {
		border-radius: 9px 0 0 0;
		position: relative;
		z-index: 1
	}

		.cf-logo div {
			position: relative;
			top: 5px;
		}

		.cf-logo:before {
			content: ""
		}

	@media (max-width:767px) {
		.cf-logo {
			border-radius: 9px 0 0 0
		}
	}

	.c-view-more {
		border-radius: 0 0 9px 9px
	}

	@media (min-width:768px) and (max-width:1024px),(min-width:1025px) {
		.c-view-more {
			box-shadow: 0 0 10px #999;
			margin-left: -20px;
			margin-right: -20px;
			margin-top: -40px;
			position: relative;
			z-index: 0
		}
	}

	.c-box-flights .c-view-more {
		box-shadow: 0 0 10px #999;
		border-radius: 0 0 0 5px;
		margin-left: -15px;
		margin-right: -15px;
		margin-top: -20px;
		position: relative;
		z-index: 1
	}

	@media (max-width:767px) {
		.c-box-flights .c-view-more {
			border-bottom: 1px solid #eee !important
		}
	}

	.c-box-flights .c-view-more {
		background-color: #fff;
		z-index: 98;
	}

		.c-box-flights .c-view-more span {
			color: var(--text-link);
			font: var(--body-bold)
		}

		.c-box-flights .c-view-more .txt:hover {
			cursor: pointer;
			text-decoration: underline
		}

	@media (max-width:767px) {
		div.c-box-flights .c-view-more {
			box-shadow: 0 0 10px #ccc !important
		}
	}

	.c-box-flights .tab-footer-offers {
		border-bottom: 8px solid #ffff54 !important
	}

	.c-row-line .cr-economic {
		position: relative;
	}

		.c-row-line .cr-economic:before {
			background-color: #ffff54;
			content: "";
			position: absolute;
			left: -15px;
			right: -16px;
			top: -8px;
			bottom: 0;
			z-index: 0
		}

	@media (max-width:767px) {
		.c-row-line .cr-economic:before {
			bottom: -10px;
			left: 9px;
			top: -14px
		}
	}

	.loading-component {
		background-color: white;
		grid-gap: 1rem;
		opacity: 0.7;
		z-index: 999;
		justify-content: center;
		align-items: center;
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
	}

	@media (max-width: 900px) {
		.loading-component {
			flex-direction: column;
		}
	}

	.loading-text {
		color: black;
		opacity: 1 !important;
	}

	.lds-dual-ring {
		opacity: 1 !important;
	}

		.lds-dual-ring:after {
			content: " ";
			display: block;
			width: 40px;
			height: 40px;
			border-radius: 50%;
			border: 6px solid #037bba;
			border-color: #037bba transparent #037bba transparent;
			animation: lds-dual-ring 1.2s linear infinite;
		}

	@keyframes lds-dual-ring {
		0% {
			transform: rotate(0deg);
		}

		100% {
			transform: rotate(360deg);
		}
	}

	@supports (position:sticky) {
		.sticky-top {
			position: sticky;
			top: 0;
			z-index: 1020
		}
	}

	@supports (position: sticky) {
		.sticky-top {
			z-index: 9
		}
	}

	@media (max-width: 767px) {
		.c-box-flights {
			position: relative;
			width: 100%;
			padding-right: 15px;
			padding-left: 15px;
		}

		.sticky-bottom {
			position: sticky;
			bottom: 0;
			z-index: 9;
			background-color: #fff;
			box-shadow: 0px -5px 5px #ededed;
			border-radius: 0 0 5px 5px;
		}
	}

	.c-box-flights .sticky-bottom {
		z-index: 98;
	}

	.c-txt-total {
		font-size: 14px
	}

	@media (min-width:1025px) {
		.c-txt-total {
			font-size: 18px
		}
	}

	.c-txt-rate {
		font-size: 18px
	}

	@media (min-width:1025px) {
		.c-txt-rate {
			font-size: 22px
		}
	}

	.ci-tooltip {
		/*position: absolute;*/
		display: inline-block;
		color: #c9430a;
		font-size: 12px;
		z-index: 95;
		top: 4px;
		right: 0;
		margin-left: 4px;
		position: relative;
	}

	@media (max-width:767px) {
		.ci-tooltip {
			right: 5px;
		}
	}

	.ci-tooltip .tooltiptext {
		visibility: hidden;
		position: absolute;
		width: 150px;
		width: 190px;
		background-color: #fff;
		color: #333;
		text-align: center;
		border-radius: 6px;
		z-index: 1;
		opacity: 0;
		transition: opacity .6s;
		border: 1px solid #f1f1f1;
		padding: 1px 10px;
		box-shadow: 0 .125rem .25rem rgba(0,0,0,.075) !important
	}

	@media (max-width:767px) {
		.ci-tooltip .tooltiptext {
			display: none
		}
	}

	.ci-tooltip:hover .tooltiptext {
		visibility: visible;
		opacity: 1
	}

	.tooltip-top {
		top: -18px;
		left: -20px;
		margin-left: -72px
	}

	/* ajustes vuelos internacionales redondo */
	.chf-flight {
		background-color: #F2F3F5;
		border-radius: 9px;
		margin-right: -16px;
		margin-left: -16px;
		.img-airline

	{
		@media (min-width: 992px) {
			position: relative;
			top: -3px;
			width: 28px;
		}
	}

	}

	.z-95 {
		z-index: 95 !important;
	}

	.z-96 {
		z-index: 96 !important;
	}

	.z-97 {
		z-index: 97 !important;
	}

	.font-15 {
		font-size: 15px !important;
	}

	.c-if-round {
		.c-click-detail

	{
		.sp

	{
		top: 0;
	}

	}

	.c-row-line .cr-economic:before {
		bottom: -20px;

		@media (max-width: 767px) {
			bottom: -34px;
		}
	}

	.radio label {
		@media (max-width: 767px) {
			top: 12px;
			right: -9px;
		}
	}

	.ca-roundtrip {
		color: #828282;
		width: 45px;

		@media (max-width: 376px) {
			right: -43px;
		}

		@media (min-width: 377px) and (max-width: 430px) {
			right: -36px;
		}

		@media (min-width: 431px) and (max-width: 767px) {
			right: -25px;
		}

		@media (min-width: 768px) and (max-width: 991px) {
			right: -26px;
		}

		@media (min-width: 992px) and (max-width: 1279px) {
			right: -47px;
		}

		@media (min-width: 1280px) {
			right: -35px;
		}
	}

	.row-active:hover:before {
		z-index: 95;
	}

	.cf-duration {
		position: relative;
		top: 0;
		z-index: 1;
	}

	.row-color-1 {
		.cf-duration

	{
		background-color: #f5f5f5;
	}

	}

	.row-color-2 {
		.cf-duration

	{
		background-color: #ffffff;
	}

	}

	.line-fligths-1 .f-01 {
		@media (min-width: 992px) and (max-width: 1024px) {
			left: -9% !important;
			top: 6px;
		}
	}

	.line-fligths-2 .f-01 {
		@media (min-width: 992px) and (max-width: 1024px) {
			left: -23% !important;
			top: 6px;
		}
	}

	.line-fligths-1 .f-01 {
		left: -5px;

		@media (max-width: 359px) {
			left: -55px !important;
		}

		@media (min-width: 360px) and (max-width: 415px) {
			left: -58px !important;
		}

		@media (min-width: 416px) and (max-width: 429px) {
			left: -61px !important;
		}

		@media (min-width: 992px) and (max-width: 1024px) {
			left: -3% !important;
		}

		@media (min-width: 768px) and (max-width: 991px) {
			left: -91%;
		}
	}

	.be-xs {
		@media (max-width: 425px) {
			margin-left: 10px;
		}

		.icon

	{
		cursor: pointer;
		width: 16px;
		height: 16px;
		margin-right: 2px;
	}

	}

	.c-click-detail .sp {
		white-space: nowrap;

		@media (max-width: 425px) {
			position: relative;
			right: 5px;
		}

		@media (min-width: 768px) {
			position: relative;
			top: 2px;
		}
	}

	}

	.c-box-flights {
		.title-list

	{
		.date-mobile

	{
		top: 11px;
	}

	}

	.cfd-time {
		@media (min-width: 1280px) {
			padding-left: 0px !important;
		}
	}

	.icon-big-bag-out, .icon-carry-on-bag, .icon-none {
		color: #96949c !important;
	}

	.sticky-top {
		top: 100px;
	}

	.icon-big-bag {
		&:before

	{
		color: var(--text-link) !important;
		font-size: 16px !important;
		margin-right: 0px !important;
		margin-left: 0px !important;
		inset: 0px !important;
	}

	}
	}

	.chs-logo {
		@media (max-width: 767px) {
			background-color: #F2F3F5 !important;
			padding: 5px 0;
		}

		.chsl

	{
		@media (max-width: 767px) {
			margin: auto;
			display: table;
		}
	}

	}

	.sticky-int {
		top: 35px;

		@media (min-width: 768px) and (max-width: 990px) {
			top: 33px;
		}

		.title-list

	{
		.icon

	{
		position: relative;
		top: 2px;

		@media (max-width: 767px) {
			top: 0 !important;
		}
	}

	}
	}

	.chf-flight {
		@media (max-width: 767px) {
			z-index: 98 !important;
		}
	}

	.top-0.chf-flight {
		top: -1px !important;
	}

	.text-time {
		color: #3B3A40;
		font-size: 1.125rem;
		font-weight: 600 !important;
	}

	/* ajustes iatas */
	p.text-time {
		margin-bottom: 0 !important;
		margin-top: 0 !important;
	}

	.cl-iatas {
		height: 9px;
		line-height: .5;

		@media (max-width: 767px) {
			margin-top: 4px;
		}

		.cli-left

	{
		margin-left: 74px;

		@media (max-width: 991px) {
			margin-left: 16px;
		}

		@media (min-width: 992px) {
			position: relative;
			top: 4px;
		}
	}

	.cli-right {
		margin-left: -32px;
		/*@media (min-width: 768px) and (max-width: 991px) {
				margin-left: 32px;
			}*/
		@media (max-width: 767px) {
			margin-left: 23px;
		}

		@media (min-width: 768px) and (max-width: 991px) {
			margin-left: 32px;
		}

		@media (min-width: 992px) {
			position: relative;
			top: 4px;
		}
	}

	}

	.tt-left {
		margin-left: 15px;
	}

	.tt-right {
		@media (min-width: 768px) and (max-width: 991px) {
			margin-left: 8px;
		}
	}

	@media (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
		.c-line-schedule {
			border: initial;
			top: 19px;
		}
	}

	.c-line-schedule {
		left: 10px;
		position: absolute;
		top: 22px;
		right: 10px;
		width: initial;

		@media (min-width: 768px) and (max-width: 991px) {
			left: 0;
			right: 0;
		}

		@media (min-width: 992px) and (max-width: 1279px) {
			left: 28px;
		}

		@media (max-width: 991px) {
			border-bottom: 1px dashed #000 !important;
		}
	}

	.cvs-line, .cvs-duration {
		@media (min-width: 550px) and (max-width: 767px) {
			position: relative;
			left: -20px;
		}
	}

	.cvs-scale {
		@media (min-width: 550px) and (max-width: 767px) {
			position: relative;
			left: -25px;
		}

		@media (min-width: 430px) and (max-width: 549px) {
			position: relative;
			left: -4px;
		}
	}

	.shadow-sticky {
		box-shadow: 0 2px 3px #ccc;
		&:before

	{
		bottom: 0;
		content: "";
		position: absolute;
		top: 0;
		border: 2px solid #fff;
		left: -1px;
		;
	}

	&:after {
		bottom: 0;
		content: "";
		position: absolute;
		top: 0;
		border: 2px solid #fff;
		right: -2px;
		;
	}

	}

	.position-sticky {
		@media (max-width: 767px) {
			border-right: 1px solid #eee;
			border-top: 1px solid #eee;
		}
	}
</style>