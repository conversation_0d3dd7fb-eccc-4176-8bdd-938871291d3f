<template>
    <div id="loader-page" class="loading-page d-center" :class="{'d-none': !isLoadPage}">
        <div class="loader__logo my-5"></div>
    </div>
</template>

<script>
import { storeToRefs } from 'pinia';
import { useLoaderPageStore } from '../../stores/loader-page';

export default {
        setup() {
            const storeLoaderPageStore = useLoaderPageStore();
            const { isLoadPage } = storeToRefs(storeLoaderPageStore);
            return { isLoadPage }
        }
    }
</script>