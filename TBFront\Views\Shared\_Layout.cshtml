﻿@using Microsoft.Extensions.Options
@using TBFront.Mappers
@using TBFront.Models.Configuration
@using TBFront.Models.Login;
@using TBFront.Options
@using TBFront.Services
@using TBFront.Helpers
@using PT.Platform.B2C.User.Entities;
@using Microsoft.AspNetCore.Http
@using System.IO;
@using Microsoft.AspNetCore.Hosting;
@using TBFront.Interfaces

@inject IOptions<SettingsOptions> settingOptions;
@inject IHttpContextAccessor httpContextaccessor;
@inject StaticHelper staticHelper;
@inject ConfigService configMapper;
@inject ViewHelper viewHelper

@{
    var language = settingOptions.Value.Language;
    var isRobot = (bool)ViewData["IsRobot"];
    var isMobile = (bool)ViewData["IsMobile"];
    var resolution = ViewData["Resolution"] as SectionResolution;
    var user = ViewData["User"] as User;
    var GTM = ViewData["GTM"] ?? settingOptions.Value.GTM;
    var query = viewHelper.GetCurrentQueryStringCom(Context, true);
    var culture = ViewData["CultureData"] as Culture;
    var userLocation = ViewData["userLocation"] as UserLocation;
}

<!DOCTYPE html>
<html lang="@language">
<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">


    @await RenderSectionAsync("Preload", required: false)


    @await Html.PartialAsync("~/Views/Shared/Meta/_Icon.cshtml")

    @await RenderSectionAsync("Css", required: false)

    @await RenderSectionAsync("Meta", required: false)

    <meta http-equiv="content-language" content="@language" />
    <meta name="language" content="@language" />

    @if (!isRobot)
    {
        <script>
            (function (w, d, s, l, i) {
                w[l] = w[l] || []; w[l].push({
                    'gtm.start':
                        new Date().getTime(), event: 'gtm.js'
                }); var f = d.getElementsByTagName(s)[0],
                    j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : ''; j.async = true; j.src =
                        'https://www.googletagmanager.com/gtm.js?id=' + i + dl; f.parentNode.insertBefore(j, f);
            })(window, document, 'script', 'dataLayer', '@GTM');
        </script>

    }

</head>
<body class="cloak">

    @if (!isRobot)
    {
        <!-- Google Tag Manager (noscript) -->
        <noscript>
            <iframe src="https://www.googletagmanager.com/ns.html?id=@(GTM)"
                    height="0" width="0" style="display:none;visibility:hidden"></iframe>
        </noscript>
        <!-- End Google Tag Manager (noscript) -->
    }

    @if (user != null)
    {
        <form id="logout-form" action="@(settingOptions.Value.SiteUrl)/v1/api/logout" method="POST" style="display: none;">
            <input type="hidden" name="redirectTo" value="@query">
        </form>
    }

    <div id="app">
        @RenderBody()
    </div>

    <script type="text/javascript">
        
        window.__pt = window.__pt || {};
        window.__pt.ln = window.__pt.ln || {};
        window.__pt.settings = window.__pt.settings || {};
        window.__pt.settings.site = @Json.Serialize(await configMapper.GetAppConfiguration(isRobot, isMobile));
        window.__pt.settings.login = window.__pt.settings.login || {};
        window.__pt.settings.login.header = window.__pt.settings.login.header || {};
        window.__pt.settings.page = "@(ViewData["Page"] ?? "none")";
        window.__pt.settings.site.mobile = @Json.Serialize(isMobile);
        window.__pt.settings.rb = @Json.Serialize(isRobot);
        window.__pt.user = @Json.Serialize(user);
        window.__pt.cultureData = @Json.Serialize((ViewData["CultureData"]));
        window.__pt.exchange = @Json.Serialize((ViewData["Exchange"]));
        window.__pt.userLocation = @Json.Serialize(userLocation);
    </script>
    @await RenderSectionAsync("ScriptsPriority", required: false)
    <script src="@staticHelper.GetVersion($"/js/lang/{culture?.CultureCode}.js", settingOptions.Value.Assets)"></script>
    <script src="@staticHelper.GetVersion("/js/default.js", settingOptions.Value.Assets)"></script>

    @if (!isRobot)
    {
        <script src="https://cdn.jsdelivr.net/npm/algoliasearch@4.5.1/dist/algoliasearch-lite.umd.js"></script>
        <script>
            var ALGOLIA_INSIGHTS_SRC = "https://cdn.jsdelivr.net/npm/search-insights";

            !function (e, a, t, n, s, i, c) {
                e.AlgoliaAnalyticsObject = s, e[s] = e[s] || function () {
                    (e[s].queue = e[s].queue || []).push(arguments)
                }, i = a.createElement(t), c = a.getElementsByTagName(t)[0],
                    i.async = 1, i.src = n, c.parentNode.insertBefore(i, c)
            }(window, document, "script", ALGOLIA_INSIGHTS_SRC, "aa");
            aa('init', {
                appId: '@settingOptions.Value.AlgoliaId',
                apiKey: '@settingOptions.Value.AlgoliaKey',
            });
        </script>
        @if (user != null)
        {
            <script>
                aa('setAuthenticatedUserToken', "@(user.Id)");
            </script>
        }
        <script type="text/javascript">
            var _user_id = ''; // Set to the user's ID, username, or email address, or '' if not yet known.
            var _session_id = 'unique_session_id'; // Set to a unique session ID for the visitor's current browsing session.

            var _sift = window._sift = window._sift || [];
            _sift.push(['_setAccount', '@settingOptions.Value.ApiKeySift']);
            _sift.push(['_setUserId', _user_id]);
            _sift.push(['_setSessionId', '@httpContextaccessor.HttpContext.Items["session_id"]']);
            _sift.push(['_trackPageview']);

            (function () {
                function ls() {
                    var e = document.createElement('script');
                    e.src = 'https://cdn.sift.com/s.js';
                    document.body.appendChild(e);
                }
                if (window.attachEvent) {
                    window.attachEvent('onload', ls);
                } else {
                    window.addEventListener('load', ls, false);
                }
            })();
        </script>


        <script src="https://www.google.com/recaptcha/api.js?hl=@(culture?.CultureCode)" async defer></script>



    }

    @await RenderSectionAsync("AppendScripts", required: false)
    @await RenderSectionAsync("Scripts", required: false)

</body>
</html>