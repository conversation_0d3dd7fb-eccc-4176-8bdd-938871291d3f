﻿using TBFront.Models.Common;
using TBFront.Models.Flight.Quote;

namespace TBFront.Models
{
    public class CheckoutData
    {
        public Dictionary<string, TBFront.Models.Flight.Quote.Flight>? Flights { get; set; }
        public Dictionary<string, FareLeg>? Fares { get; set; }
        public Summary Summary { get; set; }
    }

    public class Upsell
    {
        public List<ContentItem>? Content { get; set; }
        public double Difference { get; set; }
        public string Id { get; set; }
        public bool IsSelected { get; set; }
        public string? Name { get; set; }
        public double Rate { get; set; }
        public bool HasSpecialDiscount { get; set; }
        public int? NegotiatedFareId { get; set; }
    }

    public class ContentItem
    {
        public string? Name { get; set; }
        public string? Description { get; set; }
        public int? Category { get; set; }
    }

    public class Summary
    {
        public FlightCheckout Origin { get; set; }
        public FlightCheckout? Destination { get; set; }
        public String? Token { get; set; }
        public String? FareKey { get; set; }
        public String? DepartureName { get; set; }
        public String? ArrivalName { get; set; }
        public String? QuoteToken { get; set; }
        public List<bool>? Multiple { get; set; }
        public List<TaskIDItem>? QuoteTaskID { get; set; }
    }

    public class Airline
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public string AirlineLogoUrl { get; set; }
    }



    public class FlightInfo
    {
        public string Date { get; set; }
        public string Time { get; set; }
        public string AirportCode { get; set; }
        public string Terminal { get; set; }
    }

    public class Segment // ya no se usa borrar depues
    {
        public string ArrivalFullName { get; set; }
        public string DepartureFullName { get; set; }
        public string ArrivalAirportCode { get; set; }
        public string DepartureAirportCode { get; set; }
        public List<FlightCheckout> Flights { get; set; }
        //public object FareGroups { get; set; } <--- PREGUNTAR
    }

    /*public class Fare
    {
        public int Priority { get; set; }
        public string FareGroup { get; set; }
        public string FareKey { get; set; }
        public double AverageAmount { get; set; }
        public double DisplayAmount { get; set; }
        public List<FareDetail> FareDetails { get; set; }
        public int FareId { get; set; }
    }

    public class FareDetail
    {
        public string DisplayText { get; set; }
        public double Amount { get; set; }
        public int Type { get; set; }

    }*/

    public class FlightCheckout
    {
        public int Id { get; set; }
        public string ArrivalFullName { get; set; }
        public string DepartureFullName { get; set; }
        public Airline Airline { get; set; }
        public int ItineraryId { get; set; }
        public List<string> FlightNumbers { get; set; } = new List<string>();
        public FlightInfo Arrival { get; set; }
        public FlightInfo Departure { get; set; }
        public int Stops { get; set; }
        public int Overnight { get; set; }
        public string FlightDuration { get; set; }
        public bool IsNightly { get; set; }
        public int DaysOfFlight { get; set; }
        public List<TBFront.Models.Common.Fare> Fares { get; set; }
    }

    public class TaskIDItem
    {
        public string? TaskID { get; set; }
    }

}
