﻿@using TBFront.Helpers
@inject ViewHelper viewHelper

@{
    var page = ViewData["page"];
}

<div class="modal fade" id="cLoader" ata-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-sm modal-loading">
        <div class="modal-content">
            <div class="modal-body pt-0">
                <h5 class="pt-4 text-center font-22">Tu vuelo esta casi listo</h5>
                <div class="ca-load position-relative">
                    <div class="icon-loader">
                        <div class="cn-loader"></div>
                    </div>
                </div>
                <p class="font-18 font-bold mt-2 mb-1">Detalles del vuelo:</p>
                <div class="d-flex justify-content-between">
                    <div class="cl-01 font-14 d-flex align-items-center">
                        <div class="pe-3"><strong>BOG - MDE </strong>14 may 20:23 14:25 - Tarifa XS</div>
                    </div>
                    <div class="cl-img d-flex align-items-center">
                        <img class="w-100" src="https://www.tiquetesbaratos.com/resources/images/logos/logo-avianca.svg" />
                    </div>
                </div>
                <hr class="my-2" />
                <div class="d-flex justify-content-between">
                    <div class="cl-01 font-14 d-flex align-items-center">
                        <div class="pe-3"><strong>BOG - MDE </strong>14 may 20:23 14:25 - Tarifa XS</div>
                    </div>
                    <div class="cl-img d-flex align-items-center">
                        <img class="w-100" src="https://www.tiquetesbaratos.com/resources/images/logos/logo-avianca.svg" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>