import { defineStore } from "pinia";
import { apiRequestService } from "../../utils/http";
import { useFlightFamilyFareStore } from './flightFamilyFare'
import { getOrderUpsell } from "../mappers/flightMapper"; 
import { ListMapper, LuggageMapper } from '../mappers/luggageMapper';

export const useFlightUpsellStore = defineStore({
    id: "flightUpsell",
    state: () => ({
        loading: false,
        flightUpsell: {},
        showUpsell: false,
        flightSelected: {}
        //groupSelected: 0,
        //groupsSeen: {},
    }),
    getters: {
        getLoading: (state) => {
            return state.loading;
        },
        getShowUpsell: (state) => {
            return state.showUpsell;
        },
        getRates: (state) => {
            return state.flightUpsell && state.flightUpsell.packageRates;
        },
        getGroupSelected: (state) => {
            return state.groupSelected;
        },
        getShowAgain: (state) => {
            return (groupIndex) => {
                return state.groupsSeen[groupIndex];
            };
        },
        getContainsUpsell: (state) => {
            return (
                state.flightUpsell ||
                (state.flightUpsell &&
                    state.flightUpsell.packageRates &&
                    state.flightUpsell.packageRates > 0)
            );
        },
        getFlightUpsell: (state) => {
            return state.flightUpsell;
        },
        getFlightSelected: (state) => {
            return state.flightSelected;
        },
        getFareKey: (state) => {
            // Verifica si existen los datos necesarios en el objeto
            if (
                state.flightSelected &&
                state.flightSelected.departureFlight &&
                state.flightSelected.departureFlight.fares &&
                state.flightSelected.departureFlight.fares.length > 0
            ) {
                // Obtiene el fareKey del primer elemento en el arreglo de fares
                return state.flightSelected.departureFlight.fares[0].fareKey;
            }
            // Retorna null si no se encuentra el fareKey
            return null;
        },
    },
    actions: {
        //async getFlightUpsellData(uriParamsToSearch) {
        //    this.loading = true;
        //    await apiRequestService(
        //        {
        //            method: "get",
        //            uri: "/upsell",
        //        },
        //        uriParamsToSearch
        //    ).then((data) => {
        //        this.loading = false;
        //        this.flightUpsell = data;
        //    }).catch(error => {
        //        this.loading = false;
        //        this.flightUpsell = {};
        //    });
        //    this.loading = false;
        //},
        changeOpenCloseModalUpsell() {
            this.showUpsell = !this.showUpsell;
            if (this.showUpsell) {
                document.body.classList.add("overflow-y-hidden");
            } else {
                document.body.classList.remove("overflow-y-hidden");
            }
        },
        //setGroupSelected(group) {
        //    this.groupSelected = group;
        //    this.groupsSeen[group] = true;
        //},
        setFlightUpsell(response) {
             try {
                const flightFamilyFareStore = useFlightFamilyFareStore();
                let content = flightFamilyFareStore.getFlightFamilyFare.familyFareContent;
                const arrayIds = content.map(item => item.category);

                for (const packageRate of response.packageRates) {
                    const listId = packageRate.content.map(item => item.category);
                    arrayIds.push(...listId)
                }

                const orderUpsell = arrayIds.filter((value, index, self) => {
                    return self.indexOf(value) === index;
                });

                const upsellOrdered = getOrderUpsell(response.packageRates, orderUpsell)
                response.packageRates = upsellOrdered;
                this.flightUpsell = response;
                this.loading = false;
            }catch(e){
                this.flightUpsell = []
                this.loading = false;
            }
        },
        setFlightSelected(response) {
            this.flightSelected = response;
        },
        activateLoadingUpsell() {
            this.loading = true;
        },
        getLuggageFamilyFare(group, familyFare = {}) {
            let family = {};
            if (Object.keys(familyFare).length) {
                family = {
                    name: familyFare.familyFareName,
                    content: familyFare.familyFareContent
                };
            } else {
                let flightUpsell = this.flightUpsell.packageRates;
                family = flightUpsell?.find(item => item.group.toUpperCase() == group.toUpperCase());
                if (family == undefined) {
                    family = flightUpsell?.split("|").flatMap(tramo => tramo.split("^").map(segmento => segmento.split("~")[2]).includes(group.toUpperCase()));
                }
            }
            return LuggageMapper.mapFamilyFare(family);
        },
    },
});