﻿@using Microsoft.Extensions.Options
@using TBFront.Models.Configuration
@using TBFront.Options

@inject IOptions<SettingsOptions> settingOptions

<link rel="icon" type="image/x-icon" href="@(settingOptions.Value.CloudCdn)/assets-tb/img/@(settingOptions.Value.SiteName)/favicon.ico">
<link rel="apple-touch-icon" sizes="180x180" href="@(settingOptions.Value.CloudCdn)/assets-tb/img/@(settingOptions.Value.SiteName)/apple-touch-icon.png">
<link rel="icon" type="image/png" sizes="32x32" href="@(settingOptions.Value.CloudCdn)/assets-tb/img/@(settingOptions.Value.SiteName)/favicon-32x32.png">
<link rel="icon" type="image/png" sizes="16x16" href="@(settingOptions.Value.CloudCdn)/assets-tb/img/@(settingOptions.Value.SiteName)/favicon-16x16.png">
<link rel="apple-touch-icon" sizes="57x57" href="@(settingOptions.Value.CloudCdn)/assets-tb/img/@(settingOptions.Value.SiteName)/apple-touch-icon-57x57.png" />
<link rel="apple-touch-icon" sizes="72x72" href="@(settingOptions.Value.CloudCdn)/assets-tb/img/@(settingOptions.Value.SiteName)/apple-touch-icon-72x72.png" />
<link rel="apple-touch-icon" sizes="76x76" href="@(settingOptions.Value.CloudCdn)/assets-tb/img/@(settingOptions.Value.SiteName)/apple-touch-icon-76x76.png" />
<link rel="apple-touch-icon" sizes="114x114" href="@(settingOptions.Value.CloudCdn)/assets-tb/img/@(settingOptions.Value.SiteName)/apple-touch-icon-114x114.png" />
<link rel="apple-touch-icon" sizes="120x120" href="@(settingOptions.Value.CloudCdn)/assets-tb/img/@(settingOptions.Value.SiteName)/apple-touch-icon-120x120.png" />
<link rel="apple-touch-icon" sizes="144x144" href="@(settingOptions.Value.CloudCdn)/assets-tb/img/@(settingOptions.Value.SiteName)/apple-touch-icon-144x144.png" />
<link rel="apple-touch-icon" sizes="152x152" href="@(settingOptions.Value.CloudCdn)/assets-tb/img/@(settingOptions.Value.SiteName)/apple-touch-icon-152x152.png" />
<link rel="apple-touch-icon" sizes="180x180" href="@(settingOptions.Value.CloudCdn)/assets-tb/img/@(settingOptions.Value.SiteName)/apple-touch-icon-180x180.png" />
<link rel="manifest" href="@(settingOptions.Value.SiteUrl)/assets-tb/img/@(settingOptions.Value.SiteName)/manifest.json" crossorigin="use-credentials">

<meta name="msapplication-TileColor" content="#da532c">
<meta name="theme-color" content="#ffffff">

