import { authenticationApi } from './instance';

export const authenticationApiRequestService = async () => {
    const response = await authenticationApi.post(
        window.__pt.settings.site.auth.uri + window.__pt.settings.site.auth.resource,
        {
            clientId: window.__pt.settings.site.auth.clientId,
            clientSecret: window.__pt.settings.site.auth.clientSecret,
            grantType: window.__pt.settings.site.auth.grantType,
            organization: window.__pt.settings.site.auth.organization
        }
    );

    return response;
};
