﻿using TBFront.Interfaces;
using TBFront.Infrastructure.HttpService.APIB2CService.Dtos;

namespace TBFront.Infrastructure.HttpService.APIB2CService
{
    public static class APIB2CServiceRegister
    {
        public static void AddAPIB2CDependencies(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddHttpClient<APIB2CService>("");

            services.AddSingleton(s => configuration.GetSection("HttpAPIFrontServiceConfiguration").Get<APIFrontConfiguration>());

            services.AddSingleton<IAPIB2CService, APIB2CService>();

        }
    }
}
