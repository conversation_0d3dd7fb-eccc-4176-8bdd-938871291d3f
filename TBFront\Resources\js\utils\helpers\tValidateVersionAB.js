/**
 * Validates the version of the application.
 *
 * @param {number} versionV - The version number to validate against.
 *
 * @returns {boolean} - True if the version matches the provided version number, false otherwise.
 */
export function tValidateVersionAB(versionV = 1){
    let version =  (window.__pt.experiment?.isExperiment) ? 2 : 1;
    if((new URLSearchParams(window.location.search)).get('version')){
        version = (new URLSearchParams(window.location.search)).get('version')
    }
    window.__pt.version_list = version;
    //version 1 es la que siempre ha existido y la version 2 sería la nueva version de 2 pasos
    return (version  == versionV);
}