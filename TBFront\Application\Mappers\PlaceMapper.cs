﻿using TBFront.Models.Places.Response;

namespace TBFront.Application.Mappers
{
    public class PlaceMapper
    {

        public static Places.Standard.Dtos.AirportsPlaceRequest Request(string codeOrigin, string codeDestination, string culture)
        {
            var codes = new List<string>();

            if (!string.IsNullOrEmpty(codeOrigin))
            {
                codes.Add(codeOrigin);
            }

            if (!string.IsNullOrEmpty(codeDestination))
            {
                codes.Add(codeDestination);
            }

            return new Places.Standard.Dtos.AirportsPlaceRequest
            {
                Types = new[] { 10, 11, 18 },
                Culture = culture,
                Codes = codes
            };
        }

        public static List<PlaceResponse> Map(List<Places.Standard.Dtos.Place> places)
        {
            if (places == null || !places.Any())
                return new List<PlaceResponse>();

            return places.Select(place => new PlaceResponse
            {
                Code = place.Code,
                DisplayHtml = place.DisplayText,
                DisplayText = place.DisplayText,
                Name = place.Name,
                Id = place.Id,
                Type = place.Type,
                IsActive = true,
                LocationInfo = new LocationInfo
                {
                    CountryA2 = place.LocationInfo.CountryA2,
                    CountryISO = place.LocationInfo.CountryISO,
                }
            }).ToList();
        }
    }
}
