<template>
    <div class="modal fade" data-bs-backdrop="static" id="requote-modal" tabindex="-1" aria-labelledby="RequoteModal" aria-modal="true" role="dialog">
        <div class="modal-dialog modal-dialog-centered modal-sm quote-modal-list">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title fs-5 ps-0" id="exampleModalLabel">{{__("messages.spent_some_time")}}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" @click="requotation()"></button>
                </div>
                <div class="modal-body d-flex flex-column">
                    <p class="largeNameFlight text-center">
                        <strong class="strong">
                            {{ pt.data.startingAirportPlace.cityName ?? '' }}
                        </strong>({{ pt.data.startingAirportPlace.airportCode ?? '' }})
                        - <strong class="strong">
                        {{ pt.data.returningAirportPlace.cityName ?? '' }}
                    </strong>({{  pt.data.returningAirportPlace.airportCode ?? '' }})
                    </p>
                    <span class="icon icon-alarm-clock d-block mx-auto font-50 mt-1 mb-4"></span>
                    <button type="button" class="btn btn-secondary py-3" data-bs-dismiss="modal" @click="requotation()">{{__("messages.update_search")}}</button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        data() {
            return {
                pt: window.__pt,
            }
        },
        methods: {
            requotation() {
                location.reload();
            }
        }
    }
</script>