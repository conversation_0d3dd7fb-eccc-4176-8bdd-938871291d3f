﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using TBFront.Helpers;
using TBFront.Interfaces;
using TBFront.Mappers;
using TBFront.Models.Configuration;
using TBFront.Models.Places.Request;
using TBFront.Models.Request;
using TBFront.Options;
using TBFront.Types;

namespace TBFront.Controllers
{
    public class CheckoutController : Controller
    {
        private readonly ILogger<CheckoutController> _logger;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly SettingsOptions _options;
        private readonly ViewHelper _helper;
        private readonly ICommonHandler _commonHandler;
        private readonly IAlternateHandler _alternateHandler;
        private readonly IUserHandler _userHandler;

        public CheckoutController(ILogger<CheckoutController> logger, IHttpContextAccessor httpContextAccessor, IOptions<SettingsOptions> options, ViewHelper helper, ICommonHandler commonHandler, IAlternateHandler alternateHandler, IUserHandler userHandler)
        {
            _logger = logger;
            _options = options.Value;
            _helper = helper;
            _httpContextAccessor = httpContextAccessor;
            _commonHandler = commonHandler;
            _alternateHandler = alternateHandler;
            _userHandler = userHandler;
        }

        [Route("/vuelos/checkout")]
        [Route("/{culture}/vuelos/checkout")]
        [Route("/flights/checkout")]
        [Route("/{culture}/flights/checkout")]
        [HttpGet]
        [HttpPost]
        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> Index(CheckoutQuoteRequest requestQuote, string culture)
        {
            var route = HomeMapper.Path(Request.Path.Value ?? "");
            var path = HomeMapper.GetPath(route);

            var name = HttpContext.Request.Cookies["session_id"];
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(60000));

            var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
            var meta = MetaMapper.Generic(_options, userSelection, _helper, new Models.ContentDeliveryNetwork.Seo.SeoResponse(), "vuelos/checkout");
            var alternates = await _alternateHandler.QueryAsync(new PlaceRequest { Culture = userSelection.Culture.CultureCode, Id = 0, Path = "flights/checkout", Route = "flights/checkout", Type = PageType.Generic }, cts.Token);
            var user = await _userHandler.QueryAsync(new Models.User.UserRequest() { }, cts.Token);
            meta.Title = $"{_helper.Localizer("meta-title-checkout")} - {meta.Title}";

            ViewData["Alternates"] = alternates;
            ViewData["Request"] = requestQuote;
            ViewData["ValidRequest"] = true;
            ViewData["Page"] = "CheckoutStepOne";
            ViewData["SessionId"] = name;
            ViewData["IpRemote"] = Request.HttpContext.Connection.RemoteIpAddress;
            ViewData["X-Forwarded-For"] = HttpContext.Request.Headers["X-Forwarded-For"].ToString();
            ViewData["CultureData"] = userSelection.Culture;
            ViewData["CurrencyData"] = userSelection.Currency;
            ViewData["Exchange"] = userSelection.ExchangeClient;
            ViewData["UserLocation"] = userSelection.Context.Location;
            ViewData["User"] = user;

            ViewData["MetaTag"] = meta;
            
            return View();
        }

        [Route("/vuelos/voucher")]
        [Route("/{culture}/vuelos/voucher")]
        [Route("/flights/voucher")]
        [Route("/{culture}/flights/voucher")]
        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> Confirmation(VoucherRequest requestVoucher)
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(60000));

            var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
            var meta = MetaMapper.Generic(_options, userSelection, _helper, new Models.ContentDeliveryNetwork.Seo.SeoResponse(), "vuelos/voucher");

            meta.Title = $"{_helper.Localizer("meta-title-voucher")} - {meta.Title}";
            var user = await _userHandler.QueryAsync(new Models.User.UserRequest() { }, cts.Token);
            var alternates = await _alternateHandler.QueryAsync(new PlaceRequest { Culture = userSelection.Culture.CultureCode, Id = 0, Path = "flights/voucher", Route = "flights/voucher", Type = PageType.Generic }, cts.Token);

            ViewData["Request"] = requestVoucher;
            ViewData["ValidRequest"] = requestVoucher.IsValid();
            ViewData["Page"] = "CheckoutStepThree";
            ViewData["MetaTag"] = meta;
            ViewData["Alternates"] = alternates;
            ViewData["CultureData"] = userSelection.Culture;
            ViewData["CurrencyData"] = userSelection.Currency;
            ViewData["Exchange"] = userSelection.ExchangeClient;
            ViewData["UserLocation"] = userSelection.Context.Location;
            ViewData["User"] = user;
            return View();
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public ActionResult ErrorPage(string errorMgs, int statusCode)
        {
            ViewData["ErrorMgs"] = errorMgs;
            _httpContextAccessor.HttpContext.Response.StatusCode = statusCode;
            return View("~/Views/Error/Index.cshtml");
        }
    }
}
