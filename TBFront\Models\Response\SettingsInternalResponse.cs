﻿using TBFront.Models.Configuration;
using TBFront.Options;

namespace TBFront.Models.Response
{
    public class SettingsInternalResponse
    {
        public QuoteConfiguration QuoteConfiguration { get; set; }
        public AirlineConfiguration AirlineConfiguration { get; set; }
        public FlightValidationStops FlightValidationStops { get; set; }
        public FormsConfiguration FormsConfiguration { get; set; }
        public List<AirlinesCheckIn> AirlinesCheckIn { get; set; }
        public List<Phones> Phones { get; set; }
        public List<string> EmailDomains { get; set; }
    }
}
