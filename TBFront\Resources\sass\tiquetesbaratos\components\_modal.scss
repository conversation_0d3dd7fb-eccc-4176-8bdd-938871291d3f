@import '../_variables';

.modal-detail-flight {
    .flight-selected__title {
        font-size: 14px;
    }
    .flight-selected__airbus{
        font-size: 12px;
    }
    .flight-general-desc {
        font-size: 14px;
    }
    .flight-general-alert {
        font-size: 12px;
    }

    .item-flight-logo {
        display: inline-block;
        float: left;
        margin-right: 5px;
    }

    .item-flight-information {
        display: inline-block;
        float: left;
        margin-right: 5px;        
    }

    ul.container_fly {
        margin: 0;
        padding: 0;
        list-style: none;
    }

    .container_fly {
        margin-top: 0.5rem;
        border-radius: 12px;
        position: relative;
    }

    li.container_fly_list {
        padding-bottom: 1.5rem;
        border-left: 1px solid #3E2F6A;
        position: relative;
        padding-left: 20px;
        margin-left: 10px;
    }

    li.container_fly_list:last-child {
        border: 0px;
        padding-bottom: 0;
    }

    li.container_fly_list:before {
        content: '';
        width: 11px;
        height: 11px;
        background: #3E2F6A;
        border: 1px solid #3E2F6A;
        border-radius: 50%;
        position: absolute;
        left: -5.5px;
        top: 0px;
    }
    
    .container_fly_detail{
        span {
            &:nth-child(1) {
              font-size: 12px;
              color: $color-gray-muted;
            }
        
            &:nth-child(2) {
              font-size: 18px;
            }
            &:nth-child(3) {
               font-size: 16px;
              }
            &:nth-child(4) {
                font-size: 14px;
            }
            &:nth-child(5) {
               font-size: 14px;
               color: $color-gray-muted;
              }
          }
    }

    .flight-scale{
        span {
            &:nth-child(1) {
              font-size: 16px;
            }
        
            &:nth-child(2) {
              font-size: 14px;
            }
            &:nth-child(3) {
               font-size: 14px;
              }
            
          }
    }

    .list-flight-details {
        margin-top: 0px;
        margin-bottom: 9.5px;
        padding-inline-start: 15px;
        list-style: disc;
    }

    .nav-tabs{
        --bs-nav-tabs-link-active-border-color: #fff #dee2e6 #fff;
        --bs-nav-tabs-border-radius: 0rem;
    }

    .nav-tabs .nav-link.active i {
        color: $color-primary;
    }

    .nav-link{
        color: $gray-700;
        background-color: #E4E4E7;
    }

    .alert-warning {
        --bs-alert-bg: rgba(254, 246, 231, 1);
        --bs-alert-border-color: rgba(254, 246, 231, 1);
    }

    .alert{
        --bs-alert-color: $grey-900;
    }

    .border-scale{
        border-left: 2px dashed rgba(227, 144, 3, 1);
    }

}




/*
@media (min-width: 992px) {

    .modal-lg,
    .modal-xl {
        --bs-modal-width: 600px;
    }
}*/

.modal-tb .modal-header {
    background: #037bba;
    background: linear-gradient(180deg, #037bba, #035aaf 79%);
    border-radius: 3px 3px 0 0;
}

.modal-tb .modal-header .close {
    text-shadow: none;
}

.modal-tb .modal-body .icon {
    display: table;
    margin: auto;
}