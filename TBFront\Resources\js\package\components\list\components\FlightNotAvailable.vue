<template>
    <div class="container py-4 c-widget-empty">
        <div class="container c-groups pb-5">
            <div class="row">
                <div class="col-12">
                    <div class="c-form p-4 m-4 border rounded">
                        <div class="row">
                            <div class="col-12">
                                <div class="py-2 d-sm-flex">
                                    <span class="icon icon-contact-support font-32 pr-2"></span>
                                    <h3 class="font-28 f-p-semibold">Vuelo de <b>{{pt.data.startingFromAirport}}</b> a <b>{{pt.data.returningFromAirport}}</b> no disponible.</h3>
                                </div>
                                <p>{{__("messages.flight_not_available")}}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import { List } from '../../../../utils/analytics/flightList.js'
    import { storeToRefs } from 'pinia';
    import { useUserSelectionStore } from '../../../stores/user-selection';
    export default {
        data() {
            return {
                pt: window.__pt,
            }
        },
        setup() {
            const userSelectionStore = useUserSelectionStore();
            const { getFiltersAppliedArray } = storeToRefs(userSelectionStore);

            return {
                getFiltersAppliedArray
            }
        },
        mounted() {
            if (this.getFiltersAppliedArray.length > 0) {
                List.filtersWithoutFlights(this.getFiltersAppliedArray);
            } else {
                List.flightNotAvailable();
            }
        }
    }
</script>
<style>
    .font-32 {
        font-size: 32px !important
    }
    .font-28 {
        font-size: 28px !important
    }
</style>