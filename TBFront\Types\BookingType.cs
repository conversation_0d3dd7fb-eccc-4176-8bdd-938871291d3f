﻿namespace TBFront.Types
{
    public static class BookingType
    {
        public const string BookNowPayLaterTag = "ReserveNowPayLater";
        public const string EarlyBooking = "EarlyBooking";
        public const string EarlyBookingInProgress = "EarlyBookingInProgress";
        public const string SourceHost = "SourceHost";
        public const string ForceConfirmation = "ForceConfirmation";
        public const string QuoteApi = "QuoteApi";
        public const string QuoteHash = "QuoteHash";
        public const int Adult = 0;
        public const int Children = 1;
        public const int Infant = 4;
        public const int Passengers = 1;
        public const int Taxes = 2;
        public const int ServiceCharge = 3;
        public const int AllCharges= 5;


    }
}
