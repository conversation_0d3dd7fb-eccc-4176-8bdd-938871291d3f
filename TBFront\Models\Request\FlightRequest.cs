﻿using TBFront.Models.FlightParams;

namespace TBFront.Models.Request
{
    public class FlightRequest
    {
        public int Adults { get; set; } = 1;
        public string AddFirst { get; set; } = "home";
        
        public string Landing { get; set; } = "home";
        public string Agekids { get; set; } = string.Empty;
        public string AirlineCode { get; set; } = string.Empty;
        public bool Callus { get; set; } = false;
        public string CheckIn { get; set; } = string.Empty;
        public string CheckOut { get; set; } = string.Empty;
        public int Kids { get; set; } = 0;
        public int Mode { get; set; } = 0;
        public int Page { get; set; } = 0;
        public string ReturningFromAirport { get; set; }
        public string ReturningFromDateTime { get; set; }
        public string StartingFromAirport { get; set; }
        public string StartingFromDateTime { get; set; }
        public DateTime StartingDate => GetDateFrom();
        public DateTime ReturningDate => GetDateTo();
        public FlightItem ReturningAirportPlace { get; set; } = new FlightItem();
        public FlightItem StartingAirportPlace { get; set; } = new FlightItem();
        public int TripMode { get; set; } = 1;
        public int DaysInAdvance { get; set; } = 0;
        public bool IsNational { get; set; }
        public bool IsRoundtrip => GetTripMode();
        public List<FlightParams.Pax> Paxes { get; set; } = new List<FlightParams.Pax>();
        public string Culture { get; set; } = string.Empty;
        public bool IsCanonical { get; set; } = false;
        public bool GetTripMode()
        {
            return Mode == 1 && TripMode == 1;
        }

        private DateTime GetDateFrom()
        {
            var date = DateTime.Now;

            if (DateTime.TryParse(StartingFromDateTime, out date))
            {
                return date;
            }

            return DateTime.Now;
        }

        private DateTime GetDateTo()
        {
            var date = DateTime.Now;

            if (DateTime.TryParse(ReturningFromDateTime, out date))
            {
                return date;
            }

            return DateTime.Now;
        }
    }


    public class FlightItem
    {
        public string Airport { get; set; }
        public string AirportCode { get; set; }
        public int AirportType { get; set; }
        public string City { get; set; }
        public string CityFullName { get; set; }
        public string CityName { get; set; }
        public string CityCountryName { get; set; }
        public string CountryISO { get; set; }

    }
}
