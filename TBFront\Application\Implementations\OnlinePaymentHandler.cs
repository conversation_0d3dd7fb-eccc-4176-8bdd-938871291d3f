﻿using Microsoft.Extensions.Options;
using TBFront.Application.Mappers;
using TBFront.Infrastructure.HttpService.FlightQuote;
using TBFront.Infrastructure.HttpService.PaymentGateway.Dtos;
using TBFront.Interfaces;
using TBFront.Models.Flight.Summary;
using TBFront.Models.Forms.Request;
using TBFront.Models.Forms.Response;
using TBFront.Models.HotelFacade.Response;
using TBFront.Models.PaymentGateway;
using TBFront.Options;
using TBFront.Services;
using TBFront.Types;

namespace TBFront.Application.Implementations
{
    public class OnlinePaymentHandler : IOnlinePaymentHandler
    {
        private readonly IItineraryHandler _itineraryService;
        private readonly HashService _hashTool;
        private readonly IPaymentGatewayService _paymentGatewayService;
        private readonly PaymentGatewayConfiguration _paymentGatewayconfiguration;
        private readonly SettingsOptions _options;
        private readonly ICacheService _cacheService;
        private readonly IAPIB2CService _APIB2CService;
        private readonly IFlightService _flightService;


        public OnlinePaymentHandler(
            IItineraryHandler itineraryService,
            IPaymentGatewayService paymentGatewayService,
            IOptions<SettingsOptions> options,
            ICacheService cacheService,
            IAPIB2CService APIB2CService,
            IFlightService flightService,
            PaymentGatewayConfiguration paymentGatewayconfiguration,
            HashService hashTool
        )
        {
            _itineraryService = itineraryService;
            _hashTool = hashTool;
            _paymentGatewayService = paymentGatewayService;
            _paymentGatewayconfiguration = paymentGatewayconfiguration;
            _options = options.Value;
            _flightService = flightService;
            _cacheService = cacheService;
            _APIB2CService = APIB2CService;
        }

        public async Task<OnlinePaymentResponse> QueryAsync(OnlinePaymentRequest request, CancellationToken ct)
        {
            var response = new OnlinePaymentResponse
            {
                Request = request,
                KeyValidation = Guid.NewGuid().ToString()
            };

            var itineraryResponse = await _itineraryService.QueryAsync(new Models.BookingItinerary.ItineraryRequest { Email = request.Email, Id = request.Code }, ct);


            if (itineraryResponse.Errors.Count > 0)
            {
                response.Status = "not-found";
                response.Message = itineraryResponse.Errors.FirstOrDefault().Message;
            }

            if ((string.IsNullOrEmpty(response.Status) && itineraryResponse.Data.TravelItinerary.TagsList.Contains("cancelled")) 
                || (string.IsNullOrEmpty(response.Status) && itineraryResponse.Data.TravelItinerary.BookingServices.Sum(b => b.ServiceCharge.ServiceAmountTotal) <= 0 ) )
            {
                var type = PayOnlineMapper.GetProductType(itineraryResponse.Data.TravelItinerary.BookingServices);
                response.Client = PayOnlineMapper.Client(itineraryResponse.Data.TravelItinerary, type);
                response.Status = "cancelled";
            }

            if (string.IsNullOrEmpty(response.Status))
            {
                var id = _hashTool.Encrypt(request.Code);
                var email = _hashTool.Encrypt(request.Email);
                var itinerary = itineraryResponse.Data.TravelItinerary;
                var keyCacheToken = $"PGA_Email:{request.Email}_Id:{request.Code}";
                var type = PayOnlineMapper.GetProductType(itinerary.BookingServices);
                var hotelContent = new ContentHotelResponse();
                var summaryResponse = new SummaryResponse();
                response.TravelItinerary = itinerary;

                var paymentgatewayResponse = await _cacheService.GetCache<PaymentGatewayResponse>(keyCacheToken, ct);

                if (paymentgatewayResponse is null)
                {
                    var paymentGatewayConfigurationRequest = PayOnlineMapper.GetRequestPaymentConfiguration(itinerary);
                    var paymentGatewayConfigurationResponse = await _paymentGatewayService.QueryAsync(paymentGatewayConfigurationRequest, ct);

                    if (type == ProductType.HotelsToken || type == ProductType.PackagesToken)
                    {
                        var hotelContentRequest = PayOnlineMapper.GetRequestHotelContent(itinerary.BookingServices, _options.Culture);
                        hotelContent = await _APIB2CService.QueryAsync(hotelContentRequest, ct);
                    }

                    if (type == ProductType.FlightsToken || type == ProductType.PackagesToken)
                    {
                        var summaryRequest = SummaryMapper.Request(request.Code, request.Email, response.TravelItinerary.ChannelId);
                        summaryResponse = await _flightService.QueryAsync(summaryRequest, ct);
                    }

                    var paymentgatewayRequest = PayOnlineMapper.Request(itinerary, hotelContent, summaryResponse, paymentGatewayConfigurationResponse, _options, _paymentGatewayconfiguration, request.SessionId, id, email, response.KeyValidation);
                    paymentgatewayResponse = await _paymentGatewayService.QueryAsync(paymentgatewayRequest, ct);
                    _cacheService.SetCache(keyCacheToken, paymentgatewayResponse);
                }


                response.Client = PayOnlineMapper.Client(itinerary, type);

                if (paymentgatewayResponse.IsSuccess)
                {
                    response.UrlRedirect = paymentgatewayResponse.RedirectUrl;
                    response.Status = "ok";
                }
                else
                {
                    response.Status = "error-pga";
                }

            }



            return response;
        }
    }
}
