import {BaseModel} from "./baseModel";
export class GenericEventModel extends BaseModel{
    constructor(args) {
        super()
        const defaults = {
            EventName: { required: true, default: "", type: "string" },
            EventCategory: { required: true, default: "", type: "string" },
            EventAction: { required: true, default: "", type: "string" },
            EventLabel: { required: true, default: "", type: "string" },
        };

        this.map(defaults, args)
    }
}