@using System.Text.RegularExpressions
@using TBFront.Helpers
@using Microsoft.Extensions.Options
@using TBFront.Models.Login
@using TBFront.Models.Meta.Alternate
@using TBFront.Options
@using TBFront.Models.Configuration
@using TBFront.Types
@using Newtonsoft.Json
@using PT.Platform.B2C.User.Entities;

@inject ViewHelper viewHelper
@inject IOptions<SettingsOptions> settingOptions
@inject IOptions<SiteOptions> siteOptions
@inject IOptions<CurrencyOptions> currencyOptions
@inject StaticHelper staticHelper;

@{
    var navs = (bool)ViewData["navs"];
    var login = (bool)ViewData["login"];
    // var tabs = siteOptions.Value.Tabs;
    var phones = siteOptions.Value.Phones;
    var resolution = ViewData["Resolution"] as SectionResolution;
    var mobile = resolution.Device == DeviceType.Mobile;
    var citysUtf8 = viewHelper.GetSpecialCitiesWords();
    var routeHome = ViewData["IsRoutMain"] ?? "";
    var isRobot = viewHelper.IsRobot();
    var user = ViewData["User"] as User;
    var page = ViewData["Page"];
    var culture = ViewData["CultureData"] as Culture;
    var alternates = ViewData["Alternates"] as AlternateMain;
    var currencyConfiguration = ViewData["currencyData"] as Currency;
    var queryString = Context.Request.QueryString.Value;
    var checkout = (bool)(ViewData["Checkout"] ?? false);
    var query = viewHelper.GetCurrentQueryStringCom(Context);
    var userLocation = ViewData["UserLocation"] as UserLocation;

    if (culture is null)
    {
        culture = new Culture();
    }

    if (userLocation is null)
    {
        userLocation = new UserLocation();
    }
}
<header id="header">
 <language-currency-modal></language-currency-modal>
	<!-------- HEADER -------->
	<nav class="container">
		<div class="header__top">
			<!---------------------- LOGO ---------------------->
			<a class="header__logo" href="@settingOptions.Value.SiteUrl" title="@settingOptions.Value.AppName">
				<img alt="@settingOptions.Value.AppName" src="https://img.cdnpth.com/media/assets-tb/1.7.33/img/logo-tiquetesbaratos.png" data-srcset="https://img.cdnpth.com/media/assets-tb/1.7.33/img/logo-tiquetesbaratos.png 1x" width="60" height="60" />
			</a>
            <!--------------- MAIN BUTTONS --------------->
			<div class="header__buttons d-none d-md-flex">
				<!--------------- LANGUAGE & CURRENCY --------------->
				@if (!checkout)
                {
                    <language-currency-button :culture='@Json.Serialize(culture)' :currency='@Json.Serialize(currencyConfiguration)' :mobile="@Json.Serialize(false)"></language-currency-button>
                }
                	@if (!checkout)
				{
                <!--------------- HELP --------------->
				<div class="position-relative">
					<button class="header__btn"  data-dropdown-toggle="helpDropdown" aria-expanded="false">
						@viewHelper.Localizer("ayuda")
					</button>
					<div class="header__dropdown" data-dropdown-menu id="helpDropdown">
						<menu class="dropdown__container" id="helpMenu" role="menu">
							<li role="presentation">
								<a class="dropdown__link" href="@(settingOptions.Value.SiteUrl)/@(culture.CultureCode)/vuelos/preguntas-frecuentes" target="_blank" rel="noopenner noreferrer" role="menuitem">
									@viewHelper.Localizer("faq")
								</a>
							</li>
							<li role="presentation">
								<button class="dropdown__link" data-bs-toggle="modal" data-bs-target="#staticBackdrop" role="menuitem">
									@viewHelper.Localizer("need_call")
								</button>
							</li>
							<li role="presentation">
								<a class="dropdown__link" href="@(settingOptions.Value.SiteUrl)/@(culture.CultureCode)/vuelos/escribenos" target="_blank" rel="noopenner noreferrer" role="menuitem">
									@viewHelper.Localizer("write_to_us")
								</a>
							</li>
							<li role="presentation">
								<a class="dropdown__link" href="@(settingOptions.Value.SiteUrl)/@(culture.CultureCode)/vuelos/grupos" target="_blank" rel="noopenner noreferrer" role="menuitem">
									@viewHelper.Localizer("trip_group")
								</a>
							</li>
						</menu>
					</div>
				</div>
				}
                         		<!--------------- PHONES --------------->
				<div class="position-relative" >
					<button class="header__btn" data-dropdown-toggle="phoneDropdown" aria-expanded="false">
						<i class="icon icon-phone" style="width: 18px;"></i>
						<span>
                            <span class="d-none d-lg-inline">@viewHelper.Localizer("to_reservate")</span>&nbsp;
                            <strong>@siteOptions.Value.StaticPhoneNumbers.PrimaryPhoneFormat</strong>
                        </span>
					</button>
					<div class="header__dropdown" data-dropdown-menu id="phoneDropdown" >
						<menu class="dropdown__container dropdown__container--phones" id="phoneMenu" role="menu">
						 @foreach (var contact in phones)
							{
								<li role="presentation">
									<a class="dropdown__link justify-content-between" href="tel:@(contact.Phone.Replace(" ", ""))" role="menuitem">
										<strong> @(citysUtf8.ContainsKey(contact.City) ? citysUtf8[contact.City] : contact.City) </strong>
										@(contact.Phone)
									</a>
								</li>
							}
							<li class="phones__halfwidth" role="presentation">
								<a class="dropdown__link" target="_blank" rel="noopener noreferrer" href="https://wa.me/573104915803" ng-click="vm.sendContentHeader('Whatsapp')" role="menuitem">
									<i class="icon icon-whatsapp d-flex"></i>
									Whatsapp
								</a>
							</li>
							<li class="phones__halfwidth" role="presentation">
								<a class="dropdown__link" target="_blank" rel="noopener noreferrer" href="http://m.me/128565927176678" ng-click="vm.sendContentHeader('Messenger')" role="menuitem">
									<i class="icon icon-messenger d-flex"></i>
									Messenger
								</a>
							</li>
							<li class="phones__fullwidth" role="presentation">
								<button class="phones__contactAgentBtn" data-bs-toggle="modal" data-bs-target="#staticBackdrop" role="menuitem">
									<i class="icon icon-support-agent"></i>
									@viewHelper.Localizer("need_call")
								</button>
							</li>
						</menu>
					</div>
				</div>
                	@if (!checkout){
                <!----------- LOGIN ----------->
                <div class="position-relative">
                    <button class="header__btn header__btn--login"  data-dropdown-toggle="loginDropdown" aria-expanded="false">
                        @if (user == null){
                            <span>@viewHelper.Localizer("session_login_session")</span>
                        }
                        else{
                            <div class="logged__avatar" aria-hidden="true">
                                <span>@(string.IsNullOrEmpty(user.UserProfile.Image) ? (!string.IsNullOrEmpty(user.UserProfile.Name) ? user.UserProfile.Name[0] : user.UserProfile.ContactEmail[0]) : "")</span>
                            </div>
                            <span class="d-none d-lg-block">@((user.UserProfile.Name?.Split(' ')[0] ?? user.UserProfile.ContactEmail))</span>
                        }
                        <i class="icon icon-menu"></i>
                    </button>

                    <div class="header__dropdown" id="loginDropdown" data-dropdown-menu>
                        <menu class="dropdown__container" role="menu" id="loginPopup">
                           @if (user == null){
                                <li class="dropdown__item dropdown__item--login" >
                                    <p class="mb-12">@viewHelper.Localizer("login_and_get") <strong>@viewHelper.Localizer("10_percent_dsc")</strong> @viewHelper.Localizer("on_your_next_trip")</p>
                                    <a class="link" href="@($"/{culture.CultureCode}/login?redirectTo={query}")">
                                        @viewHelper.Localizer("start_session_or_create")
                                        <i class="icon icon-chevron-right"></i>
                                    </a>
                                </li>
                            }
                      	        <li role="presentation">
									<a class="dropdown__link" href="@($"/{culture.CultureCode}/{viewHelper.Localizer("favorites").ToLower()}")" ng-click="vm.sendContentHeader('@viewHelper.Localizer("favorites")')">
										<i class="icon icon-fav"></i>
										@viewHelper.Localizer("favorites")
									</a>
								</li>
								<li role="presentation">
									<a class="dropdown__link"
									   ng-href="{{ vm.sessionData.isLogin ? '/' + '@culture.CultureCode' + '/user/reservations' : '@settingOptions.Value.SiteUrl' + '/' + '@culture.CultureCode' + '/vuelos/consultar-reservas' }}"
									   ng-click="vm.sendContentHeader('@viewHelper.Localizer("get_booking")')" role="menuitem">
										<i class="icon icon-suitecase"></i>
										@viewHelper.Localizer("my_trips")
									</a>
								</li>
								<li role="presentation">
									<a class="dropdown__link" href="@(settingOptions.Value.SiteUrl)/@(culture.CultureCode)/vuelos/check-in" role="menuitem">
										<i class="icon-tickets-airline"></i>
										@viewHelper.Localizer("web_check_in")
									</a>
								</li>
								<li role="presentation">
									<a class="dropdown__link" href="@(settingOptions.Value.SiteUrl)/@(culture.CultureCode)/vuelos/pago-en-linea" role="menuitem">
										<i class="icon-credit-card1"></i>
										@viewHelper.Localizer("payment_online")
									</a>
								</li>
								<li role="presentation">
									<a class="dropdown__link" href="@(settingOptions.Value.SiteUrl)/@(culture.CultureCode)/vuelos/consultar-reservas" role="menuitem">
										<i class="icon-receipt"></i>
										@viewHelper.Localizer("header_tb_fact")
									</a>
								</li>
                            @if (user != null){
                                <li class="dropdown__item" role="presentation">
                                    <button class="dropdown__link" onclick="event.preventDefault(); document.getElementById('logout-form').submit();" title="@viewHelper.Localizer("log_out")" role="menuitem">
                                        <i class="icon-right-from-bracket"></i>
                                        @viewHelper.Localizer("log_out")
                                    </button>
                                </li>
                            }
                        </menu>
                    </div>
                </div>
				}
			</div>
               <div class="d-flex g-16 align-items-center d-md-none">
			 	@if (!checkout){
                <!--------------- MENU MOBILE --------------->
                <div class="header__menu">
                   <input type="checkbox" id="menuToggle" class="menu_toggle" aria-label="menu" aria-expanded="false" hidden>
                    <label class="menu__btn menu__btn--user menu_btn @(user != null ? "menu__btn--logged" : "")" for="menuToggle" role="button" aria-label="@viewHelper.Localizer("trips_account")">
                        @if (user == null){
                            <i class="icon icon-person-black"></i>
                        }
                        else{
                            <div class="logged__avatar" aria-hidden="true">
                                <span>@(string.IsNullOrEmpty(user.UserProfile.Image) ? (!string.IsNullOrEmpty(user.UserProfile.Name) ? user.UserProfile.Name[0] : user.UserProfile.ContactEmail[0]) : "")</span>
                            </div>
                        }
                    </label>

                    <div class="menu__window menu__window--right" role="dialog" aria-label="menu" id="menuWindow">
                        <div class="menu__session @(user != null ? "menu__session--logged" : "")" >
                            <h2 class="session__title">
                                @if (user == null){
                                    <span>@viewHelper.Localizer("email_welcome_user", settingOptions.Value.AppName)</span>
                                }
                                @if (user != null){
                                    <div class="logged__avatar">@(string.IsNullOrEmpty(user.UserProfile.Image) ? (!string.IsNullOrEmpty(user.UserProfile.Name) ? user.UserProfile.Name[0] : user.UserProfile.ContactEmail[0]) : "")</div>
                                   <span>¡@viewHelper.Localizer("hi"), @((user.UserProfile.Name?.Split(' ')[0] ?? user.UserProfile.ContactEmail))!</span>
                                }
                            </h2>
                            <button class="menu__closeBtn menu__close" aria-label="close">
                                <i class="icon icon-close"></i>
                            </button>
                            @if (user == null){
                                <p class="mb-12">@viewHelper.Localizer("login_and_get") <strong>@viewHelper.Localizer("10_percent_dsc")</strong> @viewHelper.Localizer("on_your_next_trip")</p>
                           
                                <a class="link" href="@($"/{culture.CultureCode}/login?redirectTo={query}")">
                                    @viewHelper.Localizer("start_session_or_create")
                                    <i class="icon icon-chevron-right"></i>
                                </a>
                             }
                        </div>

                        <menu class="menu__navigation">
                            @if (user != null){
                                <li>
                                    <a href="/@(culture.CultureCode)/user" >
                                        <i class="icon icon-person-black"></i>
                                        @viewHelper.Localizer("account")
                                    </a>
                                </li>              
                            }
                            <li>
                                <a href="@($"/{culture.CultureCode}/favorites")">
                                    <i class="icon icon-fav"></i>
                                    @viewHelper.Localizer("favorites")
                                </a>
                            </li>
                            <li>
                                <a href="@(user != null ? $"/{culture.CultureCode}/user/reservations" : viewHelper.Localizer($"get_booking_{settingOptions.Value.SiteName}", culture.CultureCode))">
                                    <i class="icon icon-suitecase"></i>
                                    @viewHelper.Localizer("my_trips")
                                </a>
                            </li>
                            <li>
								<a href="@(settingOptions.Value.SiteUrl)/@(culture.CultureCode)/vuelos/check-in" >
									<i class="icon-tickets-airline"></i>
									@viewHelper.Localizer("web_check_in")
								</a>
							</li>
							<li>
								<a href="@(settingOptions.Value.SiteUrl)/@(culture.CultureCode)/vuelos/pago-en-linea">
									<i class="icon-credit-card1"></i>
									@viewHelper.Localizer("payment_online")
								</a>
							</li>
                             @if (user != null){
                                <li>
                                    <button class="session__link" onclick="event.preventDefault(); document.getElementById('logout-form').submit();"  role="button">
                                        <i class="icon-right-from-bracket"></i>
                                        @viewHelper.Localizer("log_out")
                                    </button>
                                </li>
                            }
                        </menu>
                    </div>
                </div>
				}
                   <!--------------- MENU MOBILE --------------->
				<div class="header__menu d-md-none">
			        <input type="checkbox" id="menuToggleMain" class="menu_toggle" aria-label="menu" aria-expanded="false" hidden>
                    <label class="menu__btn menu__btn--user menu_btn" for="menuToggleMain" role="button" aria-label="menu">
                          <i class="icon icon-menu" style="width: 24px;"></i>
                    </label>
				    <div class="menu__window menu__window--right" role="dialog" aria-label="menu">
						   <div class="d-flex justify-content-end w-100">
                            <button class="menu__closeBtn menu__close"  aria-label="close">
                                <i class="icon icon-close"></i>
                            </button>
                        </div>

						<menu class="menu__navigation">
							@if (!checkout){
						   <li>
                                <language-currency-button :culture='@Json.Serialize(culture)' :currency='@Json.Serialize(currencyConfiguration)' :mobile="@Json.Serialize(true)"></language-currency-button>
                            </li>
							<div class="px-3">
								<hr>
							</div>
							
						    @if (navs   && culture is not null)
                            {
                                @foreach (var tab in culture.Tabs)
                                {
                                    <li role="presentation">
                                        @if (routeHome.ToString() == tab.Url)
                                        {
                                            <a class="tab @("flight" == tab.Name ? "current" : "")" @if (tab.IsDisney())
                                            {
                                                @Html.Raw("target='_blank' rel='noopenner noreferrer'")
                                            } href="@tab.Url" role="menuitem">
                                                <i class="@tab.Class" style="width: 20px;"></i>
                                                @viewHelper.Localizer(tab.Title)
                                            </a>
                                        }
                                        else
                                        {
                                            <a class="tab @("flight" == tab.Name ? "current" : "")" @if (tab.IsDisney())
                                            {
                                                @Html.Raw("target='_blank' rel='noopenner noreferrer'")
                                            } href="@tab.Url" role="menuitem">
                                                <i class="@tab.Class" style="width: 20px;"></i>
                                                @viewHelper.Localizer(tab.Title)
                                            </a>
                                        }
                                    </li>
                                }
                            }


							<div class="px-3">
								<hr>
							</div>

							<li>
								<button class="w-100 d-flex align-items-center g-8" data-dropdown-toggle="helpMenumobile"  aria-expanded="false" aria-haspopup="true" type="button" aria-controls="helpMobile">
									<i class="icon icon-help"></i>
									<span class="flex-grow-1 d-flex align-items-center justify-content-between">
										@viewHelper.Localizer("ayuda")
										<i class="icon icon-chevron-right font-24"></i>
									</span>
								</button>
							</li>

							<div class="px-3">
								<hr>
							</div>
							}
							<li>
								<button class="w-100 d-flex align-items-center g-8"  data-dropdown-toggle="phoneMenumobile" aria-expanded="false" aria-haspopup="true" type="button" aria-controls="callAgent">
									<i class="icon icon-phone"></i>
									<span class="flex-grow-1 d-flex align-items-center justify-content-between">
										@viewHelper.Localizer("to_reservate")
										<i class="icon icon-chevron-right font-24"></i>
									</span>
								</button>
							</li>
							<li>
								<a target="_blank" rel="noopener noreferrer" href="https://wa.me/573104915803" ng-click="vm.sendContentHeader('Whatsapp')" role="menuitem">
									<i class="icon icon-whatsapp"></i>
									Whatsapp
								</a>
							</li>
							<li>
								<a target="_blank" rel="noopener noreferrer" href="http://m.me/128565927176678">
									<i class="icon icon-messenger"></i>
									Messenger
								</a>
							</li>
						</menu>
					</div>
					<!----- MODAL HELP ----->
					<div class="menu__window menu__window--right menu__window--modal"  id="helpMenumobile">
						<div class="modal__container">
							<button class="modal__header" data-dropdown-close>
								<h3>@viewHelper.Localizer("ayuda")</h3>
								<i class="icon icon-chevron-left"></i>
							</button>
							<menu class="modal__menu flex-grow-1">
								<li>
									<a class="menu__link" href="@(settingOptions.Value.SiteUrl)/@(culture.CultureCode)/vuelos/preguntas-frecuentes" target="_blank" rel="noopenner noreferrer">
										@viewHelper.Localizer("faq")
									</a>
								</li>
								<li>
									<button class="menu__link" data-bs-toggle="modal" data-bs-target="#staticBackdrop" aria-haspopup="true" type="button">
										@viewHelper.Localizer("need_call")
									</button>
								</li>
								<li>
									<a class="menu__link" href="@(settingOptions.Value.SiteUrl)/@(culture.CultureCode)/vuelos/escribenos" target="_blank" rel="noopenner noreferrer" role="menuitem">
										@viewHelper.Localizer("write_to_us")
									</a>
								</li>
								<li>
									<a class="menu__link" href="@(settingOptions.Value.SiteUrl)/@(culture.CultureCode)/vuelos/grupos" target="_blank" rel="noopenner noreferrer">
										@viewHelper.Localizer("trip_group")
									</a>
								</li>
							</menu>
						</div>
					</div>
					<!----- MODAL PHONES ----->
					<div class="menu__window menu__window--right menu__window--modal" id="phoneMenumobile" role="dialog" id="callAgent">
						<div class="modal__container">
							<button class="modal__header" data-dropdown-close>
								<h3>@viewHelper.Localizer("call_advisor")</h3>
								<i class="icon icon-chevron-left"></i>
							</button>
							<menu class="modal__menu">
							 @foreach (var phone in phones)
								{
									<li>
										<a class="menu__link" href="tel:@(phone.Phone.Replace(" ", ""))">
											<strong> @(citysUtf8.ContainsKey(phone.City) ? citysUtf8[phone.City] : phone.City) </strong>
											<span>@(phone.Phone)</span>
										</a>
									</li>
								}
								<li>
									<button class="menu__callBtn" data-bs-toggle="modal" data-bs-target="#staticBackdrop" aria-haspopup="true" type="button">
										<i class="icon icon-support-agent d-flex"></i>
										<u>@viewHelper.Localizer("need_call")</u>
									</button>
								</li>
							</menu>
						</div>
					</div>
				</div>
	
            </div>
        </div>
       @if (navs)
        {
        <div class="header__bottom d-none d-md-flex">
			<menu class="bottom__container" role="menubar">
                @foreach (var tab in culture.Tabs)
                {
                    <li class="bottom__tab @(!tab.IsDisney() && tab.Active ? "" : "")" role="presentation">
                        <a class=" @(!tab.IsDisney() && tab.Active ? "current" : "") " href="@tab.Url"
                        @if (tab.IsDisney())
                        {
                            @Html.Raw("target='_blank'")
                        }>
                        <i class="@tab.Class" style="width: 18px;"></i>
                  		@viewHelper.Localizer(tab.Title)
                        </a>
                    </li>
                }
			</menu>
		</div>
        }
	</nav>
</header>
@section Scripts {
    <script>
        window.__pt.cultureData = @Json.Serialize((ViewData["CultureData"]));
        window.__pt.exchange = @Json.Serialize((ViewData["Exchange"]));
    </script>

    <script src="@staticHelper.GetVersion("/js/vue/app.js", settingOptions.Value.Assets)"></script>
}

@* modal calls *@
<div class="modal fade" id="staticBackdrop" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <need-a-call></need-a-call>
        </div>
    </div>
</div>