﻿using System.Text.Json.Serialization;

namespace TBFront.Models.Configuration
{
    public class LoginConfiguration
    {

        public List<ProvidersLogin> Providers { get; set; }
        public FirebaseConfiguration FirebaseSettings { get; set; }
        public MainLogin Main { get; set; }


        public LoginConfiguration()
        {
            this.Providers = new List<ProvidersLogin>();
            this.FirebaseSettings = new FirebaseConfiguration();
            this.Main = new MainLogin();
        }
    }


    public class MainLogin
    {
        [JsonPropertyName("forgot")]
        public bool Forgot { get; set; }

        [JsonPropertyName("active")]
        public bool Active { get; set; }

        [JsonPropertyName("autoOpen")]
        public bool AutoOpen { get; set; }

        [JsonPropertyName("timeOpen")]
        public int TimeOpen { get; set; }

    }

    public class ProvidersLogin
    {

        [JsonPropertyName("code")]
        public string? Code { get; set; }

        [JsonPropertyName("active")]
        public bool Active { get; set; }

        [JsonPropertyName("name")]
        public string? Name { get; set; }

        [JsonPropertyName("icon")]
        public string? Icon { get; set; }

    }

    public class FirebaseConfiguration
    {
        [JsonPropertyName("apiKey")]
        public string? ApiKey { get; set; }

        [JsonPropertyName("authDomain")]
        public string? AuthDomain { get; set; }

        [JsonPropertyName("databaseURL")]
        public string? DatabaseURL { get; set; }

        [JsonPropertyName("projectId")]
        public string? ProjectId { get; set; }

        [JsonPropertyName("storageBucket")]
        public string? StorageBucket { get; set; }

        [JsonPropertyName("messagingSenderId")]
        public string? MessagingSenderId { get; set; }

        [JsonPropertyName("appId")]
        public string? AppId { get; set; }

        [JsonPropertyName("measurementId")]
        public string? MeasurementId { get; set; }

    }

}
