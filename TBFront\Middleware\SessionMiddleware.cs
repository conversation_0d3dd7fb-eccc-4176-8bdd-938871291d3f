﻿
namespace TBFront.Middleware
{
    public class SessionIdMiddleware
    {
        private readonly RequestDelegate _next;

        public SessionIdMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task Invoke(HttpContext context)
        {

            var sessionId = context.Request.Cookies["session_id"];
            if (string.IsNullOrEmpty(sessionId))
            {
                sessionId = Guid.NewGuid().ToString();
                var cookieOptions = new CookieOptions()
                {
                    Secure = true, // Esto asegura que la cookie solo se envíe a través de HTTPS
                    HttpOnly = true, // Esto asegura que la cookie no sea accesible a través de scripts del lado del cliente
                    Path = "/",
                    Expires = DateTimeOffset.UtcNow.AddDays(365),
                };
                context.Response.Cookies.Append("session_id", sessionId, cookieOptions);
            }

            if (!context.Items.TryGetValue("session_id", out var sessionContext))
            {
                context.Items.Add("session_id", sessionId);
            }


            await _next(context);

        }
    }
}
