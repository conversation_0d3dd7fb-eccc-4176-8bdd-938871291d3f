﻿using Amazon.Runtime.Internal.Endpoints.StandardLibrary;
using System.Text.RegularExpressions;
using TBFront.Types;

namespace TBFront.Options
{
    public class SiteOptions
    {

        public string? Code { get; set; }
        public string? Favorites { get; set; }
        public List<TabsOptions> Tabs { get; set; }
        public List<Phones> Phones { get; set; }
        public List<SiteConfig>? Configuration { get; set; }
        public StaticPhoneNumbers StaticPhoneNumbers { get; set; }
        public TabsHomePVF TabsHomeSeo { get; set; }
        public List<string>? InfoPages { get; set; }
        public string? quienesSomos { get; set; }
        public string terminosCondiciones { get; set; }
        public string acercaDeAnato { get; set; }
        public string politicaDePrivacidad { get; set; }
        public string compraSegura { get; set; }
        public string contraLaPornografiaInfantil { get; set; }
        public string leyRetracto { get; set; }
        public List<AirlinesCheckIn> airlinesCheckIn { get; set; }
        public string algoliaFlightIndex { get; set; }

    }


    public class TabsOptions
    {
        public string? Name { get; set; }
        public string Title { get; set; }
        public string Url { get; set; }
        public bool Open { get; set; }
        public bool Active { get; set; }
        public string Class { get; set; }
        public List<string> ActivePaths { get; set; } = new List<string>();
        public bool IsDisney()
        {
            return this.Name == ProductType.Disney;
        }
    }

    public class Phones
    {
        public string? Phone { get; set; }
        public string? City { get; set; }
    }
    public class AirlinesCheckIn
    {
        public string? name { get; set; }
        public string? img { get; set; }
        public Dictionary<string, string>? url { get; set; } = new ();
    }
    public class StaticPhoneNumbers
    {
        public string? PrimaryPhone { get; set; }
        public string? PrimaryPhoneFormat { get; set; }
        public string? SecondaryPhone { get; set; }
        public string? SecondaryPhoneFotmat { get; set; }

        public string? RestOfWorld { get; set; }
        public string? RestOfWorldFormat { get; set; }

        public string? UsaPhone { get; set; }
        public string? UsaPhoneFormat { get; set; }
    }
    public class TabsSeo
    {
        public TabsSeo()
        {
            Info = new List<InfoTabs>();
        }
        public string? Name { get; set; }
        public string Title { get; set; }
        public string Url { get; set; }
        public bool Active { get; set; }
        public List<InfoTabs> Info { get; set; }
    }
    public class InfoTabs
    {
        public string? Name { get; set; }
        public string Title { get; set; }
        public string Url { get; set; }
        public string Photo { get; set; }
        public string Description { get; set; }
    }
    public class TabsAnswers
    {
        public TabsAnswers()
        {
            Answers = new List<InfoTabs>();
            TabsGeneral = new List<TabsSeo>();
            Title = "";
        }
        public string Title { get; set; }
        public List<InfoTabs> Answers { get; set; }
        public List<TabsSeo> TabsGeneral { get; set; }
    }
    public class TabsHomePVF
    {
        public TabsHomePVF()
        {
            Hotels = new TabsAnswers();
            Flights = new TabsAnswers();
            Packages = new TabsAnswers();
            Home = new TabsAnswers();
            HomeAbout = new TabsAnswers();
        }
        public TabsAnswers Hotels { get; set; }
        public TabsAnswers Flights { get; set; }
        public TabsAnswers Packages { get; set; }
        public TabsAnswers Home { get; set; }
        public TabsAnswers HomeAbout { get; set; }
    }

    public class SiteConfig
    {
        public string Id { get; set; } = string.Empty;
        public List<ChannelOptions> Channel { get; set; }
        public string Language { get; set; } = string.Empty;
        public string Culture { get; set; } = string.Empty;
        public string LanguageCode { get; set; } = string.Empty;
        public string Currency { get; set; } = string.Empty;
        public int SiteId { get; set; }
        public int OrganizationId { get; set; }
    }

    public class ChannelOptions
    {
        public int Code { get; set; }
        public int ChannelId { get; set; }
        public string? Device { get; set; }
        public int ChannelGroupId { get; set; }
        public string Source { get; set; } = string.Empty;
        public string Country { get; set; } = string.Empty;
        public ChannelDevice? Mobile { get; set; } = new ChannelDevice();
        public ChannelDevice? Desktop { get; set; } = new ChannelDevice();
    }
    public class ChannelConfiguration
    {
        public string Id { get; set; } = string.Empty;
        public List<string> Countries { get; set; } = new List<string>();
        public string Currency { get; set; } = string.Empty;
        public string Domain { get; set; } = string.Empty;
        public string Site { get; set; } = string.Empty;
        public List<ChannelOptions> ChannelConfig { get; set; } = new List<ChannelOptions>();
    }
    public class ChannelDevice
    {
        public int ChannelId { get; set; } = 0;
        public int ChannelGroupId { get; set; } = 0;
    }
}