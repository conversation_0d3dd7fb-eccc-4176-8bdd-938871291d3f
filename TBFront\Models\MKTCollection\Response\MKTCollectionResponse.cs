﻿using System.Text.Json.Serialization;
using TBFront.Models.Collection;

namespace TBFront.Models.MKTCollection.Response
{

    public class MKTCollectionResponse
    {
        [JsonPropertyName("status")]
        public bool Status { get; set; }

        [JsonPropertyName("data")]
        public CollectionSchemaResponse Data { get; set; } = new CollectionSchemaResponse();

    }

    public class CollectionSchemaResponse
    {
        [JsonPropertyName("domain")]
        public string Domain { get; set; } = string.Empty;

        [JsonPropertyName("profileid")]
        public string Profileid { get; set; } = string.Empty;

        [JsonPropertyName("date")]
        public DateTime Date { get; set; }

        [JsonPropertyName("title")]
        public string Title { get; set; } = string.Empty;

        [JsonPropertyName("sections")]
        public IEnumerable<Section> Sections { get; set; } = Enumerable.Empty<Section>();
        [JsonPropertyName("data")]
        public CollectionResponse? Data { get; set; } = new CollectionResponse();
    }



    public class Section
    {
        [JsonPropertyName("title")]
        public string Title { get; set; } = string.Empty;

        [JsonPropertyName("description")]
        public string Description { get; set; } = string.Empty;

        [JsonPropertyName("order")]
        public int Order { get; set; }

        [JsonPropertyName("active")]
        public bool Active { get; set; }

        [JsonPropertyName("cardtype")]
        public string CardType { get; set; } = string.Empty;

        [JsonPropertyName("cards")]
        public IEnumerable<Card> Cards { get; set; } = Enumerable.Empty<Card>();

    }

    public class Card
    {
        [JsonPropertyName("imageurl")] 
        public string ImageUrl { get; set; } = string.Empty;

        [JsonPropertyName("url")]
        public string Url { get; set; } = string.Empty;    

        [JsonPropertyName("promotiontype")]
        public string PromotionType { get; set; } = string.Empty;

        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        [JsonPropertyName("notes")]
        public string Notes { get; set; } = string.Empty;

        [JsonPropertyName("active")]
        public bool Active { get; set; }

        [JsonPropertyName("order")]
        public int Order { get; set; }

        [JsonPropertyName("type")]
        public string Type { get; set; } = string.Empty;

        [JsonPropertyName("destination")]
        public string Destination { get; set; } = string.Empty;

        [JsonPropertyName("pricenote")]
        public string PriceNote { get; set; } = string.Empty;

        [JsonPropertyName("price")]
        public string Price { get; set; } = string.Empty;

    }
}
