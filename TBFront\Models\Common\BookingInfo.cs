﻿using System.Text.Json.Serialization;

namespace TBFront.Models.Common
{
    public class BookingInfo
    {
        [JsonPropertyName("ID")]
        public int Id { get; set; }
        public string? JourneySellKey { get; set; }
        public string? FareSellKey { get; set; }
        public string? RuleNumber { get; set; }
        public int SeatsRemaining { get; set; }
        public string? ProviderCabin { get; set; }

    }
}
