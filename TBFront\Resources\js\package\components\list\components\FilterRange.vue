<template>
	<h2 class="accordion-header" id="panelsStayOpen-headingO-mealplans">
		<button class="accordion-button d-block button-accordion" type="button" data-bs-toggle="collapse"
				data-bs-target="#panelsStayOpen-collapseOn-mealplans" aria-expanded="true"
				aria-controls="`panelsStayOpen-collapseOn-${filterElement.uri}`" style="background-color: white;">
			<span class="me-2 text-dark">{{ __('messages.filter_by_price') }}</span>
		</button>
	</h2>
	<div class="accordion-collapse collapse show" id="panelsStayOpen-collapseOn-{{filterElement.uri}}"
		 aria-labelledby="panelsStayOpen-headingO-{{filterElement.uri}}">
		<div class="accordion-body">
			<div class="d-flex flex-wrap">
				<div class="col-6 text-left ps-0">
					<p class="label-input-abs m-0 min_text-12">
						{{ __('messages.min') }}
					</p>
					<span class="label-input-abs  min_text-12">
						{{$filters.thousandCurrencyFormat(getCustomPricesFilters.min)}}
					</span>
				</div>
				<div class="col-6 text-end pe-0">
					<p class="label-input-abs m-0 min_text-12 ">
						{{ __('messages.max') }}
					</p>
					<span class="label-input-abs min_text-12">
						{{$filters.thousandCurrencyFormat(getCustomPricesFilters.max)}}
					</span>
				</div>
				<div class="col-12 mb-3" style="padding-left: 12px; padding-right: 20px;">
					<input id="prices" type="text" class="form-control" data-slider-step="1" data-slider-tooltip="hide" />
				</div>
				<div class="d-flex pt-3">
					<div class="position-relative">
						<span class="position-absolute label-input-abs">{{ __('messages.from_range') }}</span>
						<span class="position-absolute dolar-input-abs">$</span>
						<input class="form-control min_slider_box p-3 min_text-14"
							   oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1').replace(/^0[^.]/, '0');"
							   type="text" :placeholder="__('messages.min')">
					</div>
					<div class="p-2">-</div>
					<div class="position-relative">
						<span class="position-absolute label-input-abs">{{ __('messages.until_range') }}</span>
						<span class="position-absolute dolar-input-abs">$</span>
						<input class="form-control max_slider_box p-3 min_text-14"
							   oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1').replace(/^0[^.]/, '0');"
							   type="text" :placeholder="__('messages.min')">
					</div>
				</div>
				<div class="w-100 text-end mt-3">
					<button type="submit" class="btn btn-search btn-apply pl-4 pr-4" @click="applyPriceFilter()">
						{{ __('messages.apply_range') }}
					</button>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
	import { storeToRefs } from 'pinia';
	import { usePackageListStore } from '../../../stores/package-list';
	export default {
		data() {
			return {
				config: window.__pt.settings.site,
				slide_price: null,
				paramsFilter: {},
				pricesSelected: [],
				filterSelection: {
					min: 0,
					max: 0
				}
			};
		},
		setup() {
			const storePackageListStore = usePackageListStore();
			const { setPriceFilterApplied } = storePackageListStore;
			const { getCustomPricesFilters } = storeToRefs(storePackageListStore);
			return { getCustomPricesFilters, setPriceFilterApplied };
		},
		mounted() {
			this.initializeSliders(this.getCustomPricesFilters)
		},
		methods: {
			initializeSliders(filterPrice) {

				let minValue = this.paramsFilter.filterPriceMin || filterPrice.min;
				let maxValue = this.paramsFilter.filterPriceMax || filterPrice.max;

				if (this.pricesSelected.length === 0 && filterPrice.min && filterPrice.max) {
					minValue = filterPrice.min;
					maxValue = filterPrice.max;
				}

				let options = {
					min: filterPrice.min,
					max: filterPrice.max,
					value: [minValue, maxValue],
					//tooltip: 'always'
				};

				let prices = document.getElementById("prices");
				//let prices_map = document.getElementById("prices_map");
				if (prices) {

					if (this.slide_price) {
						this.slide_price.destroy();
					}

					this.slide_price = new Slider("#prices", options);
					this.slide_price.on("slide", this.onChangeRangePrice);
					this.slide_price.on("slideStop", this.onChangeWithReloadRangePrice);
				}

				/* if (prices_map) {

					if (this.slide_price_map) {
						this.slide_price_map.destroy();
					}

					this.slide_price_map = new Slider("#prices_map", options);
					this.slide_price_map.on("slide", this.onChangeRangePrice);
					this.slide_price_map.on("slideStop", this.onChangeWithReloadRangePrice);
				} */

				if (!this.paramsFilter.filterPriceMin && !this.paramsFilter.filterPriceMax) {
					this.filterSelection.min = minValue;
					this.filterSelection.max = maxValue;
				}

				if (this.pricesSelected.length && (this.filterSelection.max <= filterPrice.max)) {
					this.setBoxRangeValue(this.filterSelection.min, this.filterSelection.max);
				} else {
					this.setBoxRangeValue(filterPrice.min, filterPrice.max);
				}

				if (this.filterSelection.min < filterPrice.min || this.filterSelection.max > filterPrice.max) {
					this.paramsFilter.filterPriceMin = undefined;
					this.paramsFilter.filterPriceMax = undefined;
					this.filterSelection.min = filterPrice.min;
					this.filterSelection.max = filterPrice.max;
				}

				document.querySelector('.min_slider_box').addEventListener('keyup', (e) => { this.onKeyupSliderBox(e, 'min') });
				document.querySelector('.max_slider_box').addEventListener('keyup', (e) => { this.onKeyupSliderBox(e, 'max') });
				document.querySelector('.min_slider_box').addEventListener('change', (e) => { this.onChangeSliderBox(e, 'min') });
				document.querySelector('.max_slider_box').addEventListener('change', (e) => { this.onChangeSliderBox(e, 'max') });

			},
			onKeyupSliderBox(e, prop) {
				let strnum = e.target.value + "".replaceAll(",", "");
				if (strnum.includes(",")) {
					return;
				}

				let valor = Math.abs(parseInt(strnum));

				if (prop === 'min' && valor < this.filterSelection.min) {
					return;
				}

				if (prop === 'max' && valor + 1 < this.filterSelection.min) {
					return;
				}

				this.filterSelection[prop] = isNaN(valor) ? this.getCustomPricesFilters[prop] : valor;
				this.slide_price.setValue([this.filterSelection.min, this.filterSelection.max], true, true);


				if (window.screen.width < 920) {
					//vm.onSubmitFilter();
				}
			},
			onChangeSliderBox() {
				this.setBoxRangeValue(this.filterSelection.min, this.filterSelection.max);
			},
			setBoxRangeValue(min, max) {
				const thousandSeparator = window.__pt.settings.site.currency == "COP" ? "." : ",";
				document.querySelector('.min_slider_box').value = (Math.round(min) + "").replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, thousandSeparator);
				document.querySelector('.max_slider_box').value = (Math.round(max) + "").replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, thousandSeparator);
			},
			onChangeRangePrice(values) {

				var min = values[0] || 0;
				var max = values[1] || 0;
				this.filterSelection.min = min;
				this.filterSelection.max = max;
				this.setBoxRangeValue(min, max);
			},
			onChangeWithReloadRangePrice(value) {
				this.onChangeRangePrice(value);

				if (this.slide_price) {
					this.slide_price.setValue([this.filterSelection.min, this.filterSelection.max], true, true);
				}
				//onReloadHotels();
				//vm.onSubmitFilter();

				//setFilterPhillRange();
				if (window.screen.width < 920) {
					//vm.onSubmitFilter();
				}
			},
			applyPriceFilter() {
				this.setPriceFilterApplied(this.filterSelection);
			},
			resetFilter() {
				this.setBoxRangeValue(this.getCustomPricesFilters.min, this.getCustomPricesFilters.max);
				this.slide_price.setValue([this.getCustomPricesFilters.min, this.getCustomPricesFilters.max], true, true);
				this.applyPriceFilter();
			}
		}
	}
</script>