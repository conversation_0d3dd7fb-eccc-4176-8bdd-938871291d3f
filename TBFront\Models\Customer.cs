﻿using Newtonsoft.Json;
using System.Text.Json.Serialization;

namespace TBFront.Models
{
    public class Customer
    {
        
        [JsonPropertyName("dial_code")]
        public string? DialCode { get; set; }

        [JsonPropertyName("email")]
        public string? Email { get; set; }

        [JsonPropertyName("email_confirmation")]
        public string? EmailConfirmation { get; set; }

        public string? Phone { get; set; }

        [JsonPropertyName("ip_client")]
        public string? IpClient { get; set; }

        [JsonPropertyName("policy_terms")]
        public bool PolicyTerms { get; set; }

        [JsonPropertyName("accept_transfer")]
        public bool AcceptTransfer { get; set; }

        [JsonPropertyName("user_key")]
        public string? UserKey { get; set; }
        public List<PassengersBooking>? PassengersBooking { get; set; }
    }

    public class PassengersBooking {
        public List<Passengers>? Passengers { get; set; }
    }
    public class Passengers
    {
        [Json<PERSON>ropertyName("firstname")]
        public string? Firstname { get; set; }

        [JsonPropertyName("lastname")]
        public string? Lastname { get; set; }

        [JsonPropertyName("type")]
        public string? Type { get; set; }

        public int? Age { get; set; }

        [JsonPropertyName("prefix")]
        public string? Prefix { get; set; }

        public bool? IsChild { get; set; }

        public int? Gender { get; set; }

        public string? Nationality { get; set; }

        public string? Birthday { get; set; }

        [JsonPropertyName("identityDocument")]
        public string? IdentityDocument { get; set; }


        [JsonPropertyName("day_selected")]
        public string? Day { get; set; }

        [JsonPropertyName("year_selected")]
        public string? Year { get; set; }

        [JsonPropertyName("month_selected")]
        public string? Month { get; set; }
    }
}
