﻿import { Logger } from '../../utils/helpers/logger';
import { apiRequestService } from '../../utils/http';

const config = window.__pt.settings.site;
const culture = window.__pt.cultureData;

export const getParamsFlight = (request, extraParams = {}) => {
    let paxes = request.paxes[0] || { children: [] };
    let params = {
        mode: request.mode,
        tripMode: request.tripMode,
        startingFromAirport: request.startingFromAirport,
        returningFromAirport: request.returningFromAirport,
        startingFromDateTime: request.startingFromDateTime,
        returningFromDateTime: request.returningFromDateTime,
        adults: request.adults,
        kids: request.kids,
        agekids: paxes.children.join(","),
        site: config.apiFlights.siteConfig,
        quoteList: true,
        quoteFlight: true,
        cacheTimeout: config.timeToShowRequoteModal,
        culture: culture.internalCultureCode,
        ...extraParams
    };

    if (request.airlineCode) {
        params.carrierCode = request.airlineCode;
    }
    
    return params;
};


export const getList = async (params) => {
    let response = null;

    const resource = {
        method: 'get',
        uri: `${config.apiFlights.domain}${config.apiFlights.path}${config.apiFlights.pathSearch}`
    };

    try {
        response = await apiRequestService(resource, params);
    } catch (e) {
        Logger.error(e.message);
    }

    return response;
};

export const getReturningList = async (params) => {
    let response = null;

    const resource = {
        method: 'get',
        uri: `${config.apiFlights.domain}${config.apiFlights.path}${config.apiFlights.pathSearch}/returning`
    };

    try {
        response = await apiRequestService(resource, params);
    } catch (e) {
        Logger.error(e.message);
    }

    return response;
};

export const getFilteredList = async (params) => {
    let response = null;
    const resource = {
        method: 'get',
        uri: `${config.apiFlights.domain}${config.apiFlights.path}${config.apiFlights.pathFilterSearch}`
    };

    try {
        response = await apiRequestService(resource, params);
    } catch (e) {
        Logger.error(e.message);
    }

    return response;
};

export const getParamsDetailFlight = (request) => {
    let params = {
        ...request,
        flightItineraryId: undefined,
        site: config.apiFlights.siteConfig,
        culture: culture.internalCultureCode,
    };
    return params;
};

export const getDetail = async (params) => {
    let response = {};

    const resource = {
        method: 'get',
        uri: `${config.apiFlights.domain}${config.apiFlights.path}${config.apiFlights.pathFlightDetail}`
    };

    try {
        response = await apiRequestService(resource, params);
    } catch (e) { }

    return response;
};

export const getFamilyFare = async (params) => {
    let response = {};

    const resource = {
        method: 'get',
        uri: `${config.apiFlights.domain}${config.apiFlights.path}${config.apiFlights.pathFlightFamilyFare}`
    };

    try {
        response = await apiRequestService(resource, params);
    } catch (e) { }

    return response;
};

export const getMatrix = async (params) => {
    let response = {};

    const resource = {
        method: 'get',
        uri: `${config.apiFlights.domain}${config.apiFlights.path}${config.apiFlights.pathMatrix}`,
        culture: culture.internalCultureCode
    };

    try {
        response = await apiRequestService(resource, params);
    } catch (e) { }

    return response;
};

export const getParamsUpsell = (request) => {
    let params = {
        ...request,
        site: config.apiFlights.siteConfig,
        culture: culture.internalCultureCode
    };
    return params;
};

export const getUpsell = async (params) => {
    let response = {};

    const resource = {
        method: 'get',
        uri: `${config.apiFlights.domain}${config.apiFlights.path}${config.apiFlights.pathUpsell}`
    };

    try {
        response = await apiRequestService(resource, params);
    } catch (e) { }

    return response;
};

export const getParamsRevalidate = (request) => {
    let params = {
        ...request,
        site: config.apiFlights.siteConfig,
        culture: culture.internalCultureCode
    };
    return params;
};

export const getRevalidate = async (params) => {
    let response = {};

    const resource = {
        method: 'get',
        uri: `${config.apiFlights.domain}${config.apiFlights.path}${config.apiFlights.pathRate}`
    };

    try {
        response = await apiRequestService(resource, params);
    } catch (e) { }

    return response;
};

export const getParamsReturningFlight = ({departureToken,flightQuoteId }) => {
    if(!departureToken){
        console.error(`It is invalid departureToken with ${departureToken}`)
        return {}
    }
    if(!flightQuoteId){
        console.error(`It is invalid departureToken with ${flightQuoteId}`)


        return {}
    }
    return {departureToken, flightQuoteId };
};

export const getLuggage = async (params) => {
    let response = {};

    const resource = {
        method: 'get',
        uri: `${config.apiFlights.domain}${config.apiFlights.path}${config.apiFlights.pathLuggage}`
    };

    try {
        response = await apiRequestService(resource, params);
    } catch (e) { }

    return response;
};
