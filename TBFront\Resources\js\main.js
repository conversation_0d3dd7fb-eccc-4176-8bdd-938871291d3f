﻿class Site {

    constructor() {
        //this.onInit();
    }

    initializeDatalayer() {
        const culture = window.__pt.cultureData;
        const userLocation = window.__pt.userLocation;
        const exchange = window.__pt.exchange;

        if (window.dataLayer) {
            let locationCountry = userLocation?.country.toUpperCase() ?? culture.country.toUpperCase();
            let userCountry = userLocation.userCountry || "";

            window.dataLayer.push({
                Country: culture.country,
                CultureCode: culture.cultureCode,
                CultureExternal: culture.cultureExternal,
                InternalCultureCode: culture.internalCultureCode,
                UserCultureCode: `${culture.cultureCode}-${userCountry.toLowerCase()}`,
                Language: culture.language,
                SiteCode: culture.siteCode,
                SiteName: culture.siteName,
                UserLocation: locationCountry,
                CurrencyBase: exchange.base,
                CurrencyDisplay: exchange.currency
            });
        }
        		document.addEventListener('DOMContentLoaded', function () {
			// Seleccionar todos los botones y menús
			const menuButtons = document.querySelectorAll('.menu_btn');

			menuButtons.forEach(function (menuBtn) {
				const menuWindow = menuBtn.nextElementSibling;

				if (!menuWindow || !menuWindow.classList.contains('menu__window')) return;

				// Busca el botón de cerrar DENTRO del menú
				const closeBtn = menuWindow.querySelector('.menu__close');

				// Evento para el botón principal (tu código original)
				menuBtn.addEventListener('click', function () {
					const isExpanded = this.getAttribute('aria-expanded') === 'true';
					this.setAttribute('aria-expanded', !isExpanded);
					menuWindow.classList.toggle('show');
				});

				// Nuevo evento para el botón de cerrar
				if (closeBtn) {
					closeBtn.addEventListener('click', function () {
						menuBtn.setAttribute('aria-expanded', 'false');
						menuWindow.classList.remove('show');
					});
				}
			});
		});
	document.addEventListener('DOMContentLoaded', function () {
    // Configuración para todos los dropdowns
    const dropdownTriggers = document.querySelectorAll('[data-dropdown-toggle]');

    dropdownTriggers.forEach(trigger => {
        const dropdownId = trigger.getAttribute('data-dropdown-toggle');
        const dropdownMenu = document.getElementById(dropdownId);

        if (!dropdownMenu) return;

        // Evento para abrir/cerrar desde el botón principal
        trigger.addEventListener('click', function (e) {
            e.stopPropagation();
            toggleDropdown(this, dropdownMenu);
        });

        // Cerrar desde elementos dentro del menú
        dropdownMenu.querySelectorAll('[data-dropdown-close]').forEach(closeBtn => {
            closeBtn.addEventListener('click', function (e) {
                e.preventDefault();
                closeDropdown(trigger, dropdownMenu);
            });
        });
    });

    // Cerrar dropdowns al hacer clic fuera de ellos
    document.addEventListener('click', function (e) {
        const openDropdowns = document.querySelectorAll('.show[data-dropdown-menu]');
        openDropdowns.forEach(menu => {
            const dropdownId = menu.id;
            const trigger = document.querySelector(`[data-dropdown-toggle="${dropdownId}"]`);
            
            // Verificar si el clic fue fuera del menú y del trigger
            if (!menu.contains(e.target) && (!trigger || !trigger.contains(e.target))) {
                closeDropdown(trigger, menu);
            }
        });
    });

    // Funciones helpers
    function toggleDropdown(trigger, menu) {
        const isExpanded = trigger.getAttribute('aria-expanded') === 'true';
        isExpanded ? closeDropdown(trigger, menu) : openDropdown(trigger, menu);
    }

    function openDropdown(trigger, menu) {
        // Cierra otros dropdowns abiertos primero
        document.querySelectorAll('.show[data-dropdown-menu]').forEach(openMenu => {
            if (openMenu !== menu) {
                const otherTrigger = document.querySelector(`[data-dropdown-toggle="${openMenu.id}"]`);
                if (otherTrigger) closeDropdown(otherTrigger, openMenu);
            }
        });

        trigger.setAttribute('aria-expanded', 'true');
        trigger.classList.add('active');
        menu.classList.add('show');
    }

    function closeDropdown(trigger, menu) {
        if (!trigger || !menu) return;
        trigger.setAttribute('aria-expanded', 'false');
        trigger.classList.remove('active');
        menu.classList.remove('show');
    }
});
    }

    onInit() {
        this.initializeDatalayer();
        window.__pt.settings.site.isMobile = this.isMobile;
        window.__pt.settings.site.isMobileLandScape = this.isMobileLandScape;
        window.__pt.settings.site.openModal = this.openModal;
        window.__pt.settings.site.openModalCalls = this.openModalCalls;
        window.__pt.settings.site.openOffCanva = this.openOffCanva;
        window.__pt.settings.site.openMenuHeader = this.openMenuHeader;
        window.__pt.settings.site.closeMenuHeader = this.closeMenuHeader;
        window.__pt.settings.site.isMobileDevice = this.isMobileDevice;
        window.__pt.settings.site.isMobileAndTablet = this.isMobileAndTablet;
        this.lazyload();
        this.header();
    }

    isMobile() {
        return window.matchMedia("only screen and (max-width: 760px)").matches
    }

    isMobileDevice() {
        return window.matchMedia("only screen and (max-width: 1024px)").matches
    }

    isMobileAndTablet() {
        return window.matchMedia("only screen and (max-width: 1023px)").matches
    }

    isMobileLandScape() {
        return window.matchMedia("only screen and (max-height: 575.98px) and (orientation: landscape)").matches
    }

    lazyload() {
        document.addEventListener("DOMContentLoaded", function () {
            var lazyImages = [].slice.call(document.querySelectorAll(".lazy"));
            if ("IntersectionObserver" in window) {
                var lazyImageObserver = new IntersectionObserver(function (entries, observer) {
                    entries.forEach(function (entry) {
                        if (entry.isIntersecting) {
                            var lazyImage = entry.target;
                            if (lazyImage.dataset.imgbg) {
                                lazyImage.style.cssText = "background-image: url(" + lazyImage.dataset.imgbg + ")";
                            } else {
                                lazyImage.src = lazyImage.dataset.src;
                                lazyImage.srcset = lazyImage.dataset.srcset;
                            }

                            lazyImage.classList.remove("lazy");
                            lazyImageObserver.unobserve(lazyImage);
                        }
                    });
                });

                lazyImages.forEach(function (lazyImage) {
                    lazyImageObserver.observe(lazyImage);
                });
            }
        });

    }

    openModal() {
        var bsOffcanvas = document.getElementById('idMenuMobile');
        bsOffcanvas.classList.add('hide-xs');
        const modalElement = document.getElementById('staticBackdrop');
        const modalStaticBackDrop = new bootstrap.Modal(modalElement);
        modalStaticBackDrop.show();
    }

    openModalCalls() {
        var bsOffcanvas = bootstrap.Offcanvas.getInstance('#offcanvaCalls');
        bsOffcanvas.hide();

        var modal = new bootstrap.Modal(document.getElementById('staticBackdrop'));
        modal.show();
    }

    openOffCanva() {
        const bsOffcanvas = new bootstrap.Offcanvas('#offcanvaMobile');
        bsOffcanvas.show();
    }

    header() {
        document.addEventListener("DOMContentLoaded", function() {
            actionItems(document.getElementById('subMenu'));
            actionItems(document.getElementById('idMyfavorites'), true);
            const menu = document.querySelectorAll('.tabs-header');
            let active = '';
            menu.forEach(element => {
                const dropdown = element.querySelector('.ch-dropdown');
                element.addEventListener('click', (event) => {
                    let hliActive = element.classList.contains('hli-active');
                    closeDropDown();
                    if (element.id != active || !hliActive) {
                        active = element.id;
                        element.classList.add("hli-active");
                        dropdown.classList.remove("d-none");
                    }
                    event.stopPropagation();
                });
            });

            document.addEventListener('click', () => {
                closeDropDown();
            });

            function closeDropDown() {
                const menu = document.querySelectorAll('.tabs-header');
                menu.forEach(element => {
                    element.classList.remove("hli-active");
                    const dropdown = element.querySelector('.ch-dropdown');
                    dropdown.classList.add("d-none");
                });
            }

            const idMenuMobile = document.getElementById('idMenuMobile');
            actionItems(idMenuMobile);
            
            function actionItems(id, special = false) {
                if (id != null) {
                    const listItems = id.querySelectorAll('a');
                    listItems.forEach(link => {
                        let li = link.parentElement;
                        if (special) {
                            li = li.parentElement;
                        }
                        if (link.href) {
                            li.addEventListener('click', () => {
                                if (link.target === '_blank') {
                                    window.open(link.href, '_blank');
                                } else {
                                    window.location.href = link.href;
                                }
                            });
                        }
                    });
                }
            }
            //Scroll header mobile
            const scrollRight = document.getElementById('scrollRightHeader');
            const scrollContainer = document.getElementById('subMenu');
            if (scrollContainer != null) {
                scrollContainer.addEventListener('scroll', function() {
                    const anchoScroll = (scrollContainer.scrollWidth - scrollContainer.clientWidth) - 1;
                    let scrollPosition = Math.ceil(scrollContainer.scrollLeft);
                    if (scrollPosition >= anchoScroll) {
                        scrollRight.classList.remove('ccr-blur');
                    } else {
                        scrollRight.classList.add('ccr-blur');
                    }
                });
            }
        });
    }

    openMenuHeader() {
        var menuMobile = document.getElementById("idMenuMobile");
        menuMobile.classList.remove("hide-xs");
    }

    closeMenuHeader() {
        var menuMobile = document.getElementById("idMenuMobile");
        menuMobile.classList.add("hide-xs");
    }
}

export const site = new Site();