﻿using TBFront.Models.Configuration;

namespace TBFront.Options
{
    public class SettingsOptions
    {
        public string AppName { get; set; }
        public string? SiteName { get; set; }
        public string? Code { get; set; }
        public bool Production { get; set; }
        public int Version { get; set; }
        public string? Sufix { get; set; }
        public string Assets { get; set; }
        public string SiteUrl { get; set; }
        public string? Site { get; set; }
        public string? CheckoutUrl { get; set; }
        public string? ListUrl { get; set; }
        public string? EmailCheckouttUrl { get; set; }
        public string? QuoteCheckoutStepOne { get; set; }
        public int RangeTotalAmount { get; set; }
        public int RangeTotalAmountCreateBooking { get; set; }
        public string? RevalidateTimeOut { get; set; }
        public string? VoucherCheckout { get; set; }
        public string? RevalidateCheckout { get; set; }
        public string? BookingCheckout { get; set; }
        public string? Domain { get; set; }
        public string? CloudCdn { get; set; }
        public int Organization { get; set; }
        public int OrganizationId { get; set; }
        public int OrganizationContent { get; set; }

        public int Property { get; set; }
        public int Channel { get; set; }
        public string Language { get; set; }
        public string Culture { get; set; }
        public string? CultureApp { get; set; }
        public List<string> Cultures { get; set; }
        public string? CulturesAllowed { get; set; }
        public string? Country { get; set; }
        public string CountryISO { get; set; }
        public string Currency { get; set; }
        public string? CurrencySymbol { get; set; }
        public string? CurrencyCodeName { get; set; }
        public string GTM { get; set; }
        public bool Login { get; set; }
        public string? GoogleMapsApiKey { get; set; }
        public bool ShowConsoleLogs { get; set; }
        public bool ShowServerLogs { get; set; }
        public string? RecaptchaKey { get; set; }
        public string? AlgoliaId { get; set; }
        public string? AlgoliaKey { get; set; }
        public string? FingerPrintkey { get; set; }
        public string? FingerPrintURL { get; set; }
        public bool FingerPrintkeyEnableSdk { get; set; }
        public string? ApiKeySift { get; set; }
        public int TimeToShowRequoteModal { get; set; }
        public string RedirectToPath { get; set; }
        public string? HashKey { get; set; }
        public string CheckoutHashKey { get; set; }
        public string UrlBookingService { get; set; }
        public string UrlAuthToken { get; set; }
        public string ClientId { get; set; }
        public string ClientSecret { get; set; }
        public string? MailUrl { get; set; }
        public bool MailIsEnable { get; set; }
        public int MailIsLanguage { get; set; }
        public int Retry { get; set; }
        public int RetryTimeOut { get; set; }
        public int AffiliateId { get; set; }
        public int AffiliateSiteId { get; set; }
        public string ContactPhone { get; set; }
        public ContactMeConfiguration ContactMeConfiguration { get; set; }
        public List<ChannelConfig> ChannelConfig { get; set; }
        public BookerConfig Booker { get; set; }
        public ApiFlights ApiFlights { get; set; }
        public ApiB2C ApiB2C { get; set; }
        public SEOSettings SEOSettings { get; set; }
        public ImageResolutionConfiguration? ImageResolutions { get; set; }
        public QuoteConfiguration QuoteConfiguration { get; set; }

        public AirlineConfiguration AirlineConfiguration { get; set; }
        
        public FlightValidationStops FlightValidationStops { get; set; }
        public FormsConfiguration FormsConfiguration { get; set; }
        /** Login **/
        public string AccountKeyPrivate { get; set; }
        public string? AwsAccessKeyId { get; set; }
        public string? AwsSecretAccessKey { get; set; }
        public string? AwsRegion { get; set; }
        public string? AwsS3BucketName { get; set; }
        public string? DynamoAwsAccesKeyId { get; set; }
        public string? DynamoSecretAccessKey { get; set; }
        public string? DynamoAwsAccesKeyArn { get; set; }
        public string? ApiKeySendGrid { get; set; }
        public string? ContactEmailFrom { get; set; }
        public string? ContactEmailFromName { get; set; }
        public string? GroupsEmailFrom { get; set; }
        public string? GroupsEmailFromName { get; set; }

		public string? SecrectKeyRecatcha { get; set; }
        public string? FirebaseConfigNet { get; set; }
        public int HoursDepositLimit { get; set; }
        public List<int> BreakdownList { get; set; }
        public int RetryCheckout { get; set; }
        public List<string> RetryCheckoutAllowed { get; set; } = new List<string>();
        public List<IataCodes> IataCodes { get; set; }
        public List<ChannelConfiguration> ChannelConfiguration { get; set; }
        public string CountryChannelDefault { get; set; }
        public List<string> CurrencyExceptions { get; set; } = [];
        public InternalAPI InternalAPI { get; set; }
        public List<string> EmailDomains { get; set; }
        public string AlgoliaFlightIndex { get; set; }

    }
    public class IataCodes { 
        public string Iata { get; set; }
        public int Engine { get; set; }
    }


    public class ApiFlights
    {

        public string? Domain { get; set; }
        public string? Path { get; set; }
        public string? PathSearch { get; set; }
        public string? PathMatrix { get; set; }
        public string? PathPromotions { get; set; }
        public int PaxConfig { get; set; }
        public string? PathFilterSearch { get; set; }
        public string? PathFlightDetail { get; set; }
        public string? PathFlightFamilyFare { get; set; }
        public string? PathRate { get; set; }
        public string? PathUpsell { get; set; }
        public string? PathCheckout { get; set; }
        public string SiteConfig { get; set; }
        public string? PathLuggage { get; set; }

    }

    public class BookerConfig
    {
        public AdultsConfig Adults { get; set; }
        public ChildrenConfig Kids { get; set; }
        public ChildrenAgesConfig KidsAge { get; set; }
        public string? ServiceUrl { get; set; }
        public string? ServicePath { get; set; }
        public string? AlgoliaSiteName { get; set; }
        public int AutocompleteItems { get; set; }
        public string? PlaceTypes { get; set; }
        public string? HistoryStorageKey { get; set; }
        public string? HistoryResearchStorageKey { get; set; }
    }

    public class AdultsConfig
    {
        public int Min { get; set; }
        public int Max { get; set; }
        public int Default { get; set; }
        public int DefaultRoomAdded { get; set; }
    }

    public class ChildrenConfig
    {
        public int Min { get; set; }
        public int Max { get; set; }
        public int Default { get; set; }
        public int DefaultRoomAdded { get; set; }
    }

    public class ChildrenAgesConfig
    {
        public int Min { get; set; }
        public int Max { get; set; }
    }

    public class ApiB2C
    {

        public string Uri { get; set; }
        public string PathPaymentOptions { get; set; }
        public string PathReviews { get; set; }
        public string PathFilters { get; set; }
        public string PathGallery { get; set; }
    }

    public class SEOSettings
    {
        public string PickupCode { get; set; }
        public int PickupId { get; set; }
        public string PickupCodeAlternative { get; set; }
        public int PickupIdAlternative { get; set; }
    }

    public class ApiPromotions
    {

        public string? Domain { get; set; }
        public string SiteConfig { get; set; }
    }

    public class FormsConfiguration
    {
        public string PathContact { get; set; }
        public string PathGroups { get; set; }
        public string PathCall { get; set; }
    }
    public class InternalAPI
    {
        public string CurrencyChangePath { get; set; } = string.Empty;
    }
}