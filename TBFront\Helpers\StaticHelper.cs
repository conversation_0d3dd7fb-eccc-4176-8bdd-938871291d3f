﻿using Microsoft.Extensions.Options;
using TBFront.Options;

namespace TBFront.Helpers
{
    public class StaticHelper
    {
        private string _version = "";
        private readonly SettingsOptions _options;
        public StaticHelper(IOptions<SettingsOptions> options)
        {
            _options = options.Value;
            SetVersion();
        }

        public string GetVersion(string name = "", string? path = "/", bool hasVersion = true)
        {
            if (hasVersion)
            {
                return $"{_options.CloudCdn}{path}{name}?v={_version}";

            }
            return $"{_options.CloudCdn}{path}{name}";
        }


        private void SetVersion()
        {
            _version = Guid.NewGuid().ToString();
        }

    }
}
