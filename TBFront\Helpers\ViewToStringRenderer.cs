﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.Razor;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
namespace TBFront.Helpers
{
    public class ViewToStringRenderer
    {
        private readonly IServiceProvider _serviceProvider;

        public ViewToStringRenderer(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public async Task<string> RenderViewToStringAsync<TModel>(string viewName, TModel model)
        {
            var httpContext = new DefaultHttpContext { RequestServices = _serviceProvider };
            var actionContext = new ActionContext(httpContext, new Microsoft.AspNetCore.Routing.RouteData(), new ControllerActionDescriptor());

            var viewEngine = _serviceProvider.GetRequiredService<IRazorViewEngine>();

            // Verifica si la vista existe
            var viewExists = ViewExists(viewEngine, actionContext, viewName);

            if (!viewExists)
            {
                throw new ArgumentNullException($"La vista '{viewName}' no existe.");
            }

            var view = viewEngine.GetView(executingFilePath: null, viewPath: viewName, isMainPage: true);

            using (var writer = new StringWriter())
            {
                var viewContext = new ViewContext(actionContext, view.View, new ViewDataDictionary<TModel>(new EmptyModelMetadataProvider(), new ModelStateDictionary())
                {
                    Model = model // Establece el modelo para la vista
                }, new TempDataDictionary(actionContext.HttpContext, _serviceProvider.GetRequiredService<ITempDataProvider>()), writer, new HtmlHelperOptions());

                await view.View.RenderAsync(viewContext);

                return writer.ToString();
            }
        }

        private bool ViewExists(IRazorViewEngine viewEngine, ActionContext actionContext, string viewName)
        {
            var getViewResult = viewEngine.GetView(executingFilePath: null, viewPath: viewName, isMainPage: true);
            return getViewResult.Success;
        }
    }
}
