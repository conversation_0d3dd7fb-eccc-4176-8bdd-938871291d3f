﻿using TBFront.Models.Login;
using TBFront.Models.Request;

namespace TBFront.Application.Mappers
{
    public class MailMapper
    {
        public static EmailRequest ConfirmationRequest(string id, CheckoutBookingRequest request)
        {

            var passenger = request.Customer.PassengersBooking.First().Passengers
                .FirstOrDefault();
            
            return new EmailRequest()
            {
                Subject = $"Tu reservación | Localizador {id}",
                BCCs = new List<string>(),
                NameView = "Booking/CreateBooking.cshtml",
                Name = $"{passenger.Firstname} {passenger.Lastname}",
                ToEmail = request.Customer.Email,
            };
        }




    }
}
