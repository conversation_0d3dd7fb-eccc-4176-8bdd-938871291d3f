﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using TBFront.Interfaces;
using TBFront.Models.ContentDeliveryNetwork.Exchange;
using TBFront.Options;

namespace TBFront.Controllers
{
    [ResponseCache(NoStore = true, Location = ResponseCacheLocation.None)]
    public class BaseController : Controller
    {
        private readonly ICommonHandler _commonHandler;
        public BaseController(ICommonHandler commonHandler)
        {
            _commonHandler = commonHandler;
        }
        public async override void OnActionExecuting(ActionExecutingContext context)
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));
            var cultureCode = HttpContext.Items["culture"]?.ToString();
            var currencyCode = HttpContext.Items["currency"]?.ToString();
            var countryCode = HttpContext.Items["countryCode"]?.ToString();
            var channel = await _commonHandler.QueryAsync(new ChannelConfiguration { Id = countryCode ?? "" }, cts.Token);
            var culture = await _commonHandler.QueryAsync(new Culture { CultureCode = cultureCode ?? "" }, cts.Token);
            var currency = await _commonHandler.QueryAsync(new Currency { CurrencyCode = currencyCode ?? "" }, cts.Token);
            var exchangeTask = await _commonHandler.QueryAsync(new ExchangeRequest { CurrencyBase = channel.Currency, Currency = currencyCode ?? "" }, cts.Token);
            culture.Currency = currency.CurrencyCode;
            culture.CurrencySymbol = currency.CurrencyCodeSymbol;
            culture.CurrencyCodeName = currency.CurrencyCodeName;
            currency.DecimalDigits = currency.DecimalDigits;
            ViewData["cultureData"] = culture;
            ViewData["currencyData"] = currency;
            ViewData["exchange"] = exchangeTask;
            base.OnActionExecuting(context);

        }
    }
}