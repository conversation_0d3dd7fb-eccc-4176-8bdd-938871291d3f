﻿namespace TBFront.Models.PaymentGateway.Dtos
{
    public class PaymentConfigInfoRequest
    {
        public string? Currency { get; set; }
        public decimal Amount { get; set; }

        public List<int> ExternalProviders { get; set; }

        public int BookingChannelId { get; set; }

        public int Product { get; set; }

        public int ChannelGroupId { get; set; }

        public int AffiliateId { get; set; }

        public int AffiliateSiteId { get; set; }

        public int FlightEngine { get; set; }

        public string? AirlineIATACode { get; set; }

        public int PaymentGatewayApp { get; set; }

        public bool ProviderCollect { get; set; }

    }
}
