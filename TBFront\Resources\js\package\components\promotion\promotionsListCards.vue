<template>
    <div>
        <div v-if="promotions != null">
            <section v-if="promotions.national.length > 0" class="container pt-4 c-slide-items">
                <div class="col-12">
                    <h3 v-if="typee === 'origin'" class="text-center font-30">
                        <span class="d-block d-md-inline">{{ __('promotions.salesDestinations') }}</span>
                        <span class="color-yellow mx-2">{{__('promotions.nationals')}}</span>
                        <span class="cs-img">
                            <img width="37" height="28" v-lazy="`/assets-tb/img/promociones/img-co.png`" />
                        </span>
                    </h3>
                    <h3 v-else>
                        <span class="font-poppins-semibold"> {{ __('promotions.destinationTitleList')}} </span>
                    </h3>
                </div>
                <div class="my-4">
                    <ul class="list-1 slider-content list-unstyled" v-if="!mobile">
                        <li v-for="(item, index) in promotions.national" class="col-md-6 col-lg-3 px-4" :class="{'active': index === 0}">
                            <a class="card-promotions" :href="getUrlToPromotions(item, 'oneWay')">
                                <figure @click="quoteFlight(item)" class="overflow-hidden cursor-pointer">
                                    <img onerror="this.src='https://www.cvent-assets.com/brand-page-guestside-site/assets/images/venue-card-placeholder.png';" class="w-100" v-lazy="`https://www.tiquetesbaratos.com/assets/recent-search-imgs/iatas/${item.code}.jpg`" :alt="`background-${item.name}`" />
                                    <div class="px-3 border-bottom">
                                        <h5 class="py-3 mb-0 font-18">{{ item.name }}</h5>
                                    </div>
                                    <div class="p-3 text-end">
                                        <p class="mb-0 font-16">Desde</p>
                                        <p class="mb-0 font-20 color-blue font-roboto-medium">
                                            <CurrencyDisplay :amount="item.price" :showCurrencyCode="true" :reduceIsoFont="true"/>
                                        </p>
                                        <p class="mb-0 font-16">por trayecto</p>
                                    </div>
                                </figure>
                            </a>
                        </li>

                    </ul>
                    <carousel v-else :items-to-show="1.5" :autoplay="2000" :wrap-around="true">
                        <slide v-for="(item, index) in promotions.national" :key="index">
                            <div class="carousel__item">
                                <a class="card-promotions" :href="getUrlToPromotions(item, 'oneWay')">
                                    <figure @click="quoteFlight(item)" class="overflow-hidden cursor-pointer">
                                        <img onerror="this.src='https://www.cvent-assets.com/brand-page-guestside-site/assets/images/venue-card-placeholder.png';" width="200" v-lazy="`https://www.tiquetesbaratos.com/assets/recent-search-imgs/iatas/${item.code}.jpg`" :alt="`background-${item.name}`" />
                                        <div class="px-3 border-bottom">
                                            <h5 class="py-3 mb-0 font-18">{{ item.name }}</h5>
                                        </div>
                                        <div class="p-3 text-end">
                                            <p class="mb-0 font-16">Desde</p>
                                            <p class="mb-0 font-20 color-blue font-roboto-medium">
                                                <CurrencyDisplay :amount="item.price" :showCurrencyCode="true" :reduceIsoFont="true"/>
                                            </p>
                                            <p class="mb-0 font-16">por trayecto</p>
                                        </div>
                                    </figure>
                                </a>
                            </div>
                        </slide>
                    </carousel>
                </div>
            </section>
            <section v-if="promotions.national.length === 0" class="container pt-4 c-slide-items">
                <p class="text-center">{{ typee === 'destination' ? __('promotions.noDestinationPromotions') : __('promotions.noOriginsPromotions') }}</p>
            </section>

            <section v-if="promotions.international.length > 0" class="container pt-4 c-slide-items">
                <div class="col-12">
                    <h3 v-if="typee === 'origin'" class="text-center font-30">
                        <span class="d-block d-md-inline">{{  __('promotions.salesDestinations') }}</span>
                        <span class="color-blue mx-2">{{__('promotions.internationals')}}</span>
                        <span class="cs-img">
                            <img width="auto" height="28" v-lazy="`/assets-tb/img/promociones/img-inter.png`" />
                        </span>
                    </h3>

                    <h3 v-else>
                        <span class="font-poppins-semibold"> {{ __('promotions.destinationTitleList')}} </span>
                    </h3>
                </div>
                <div class="my-4">
                    <ul class="list-1 slider-content list-unstyled" v-if="!mobile">
                        <li v-for="(item, index) in promotions.international" class="col-md-6 col-lg-3 px-4" :class="{'active': index === 0}">
                            <a class="card-promotions" :href="getUrlToPromotions(item, 'roundTrip')">
                                <figure @click="quoteFlight(item)" class="overflow-hidden cursor-pointer">
                                    <img onerror="this.src='https://www.cvent-assets.com/brand-page-guestside-site/assets/images/venue-card-placeholder.png';" class="w-100" v-lazy="`https://www.tiquetesbaratos.com/assets/recent-search-imgs/iatas/${item.code}.jpg`" :alt="`background-${item.name}`" />
                                    <div class="px-3 border-bottom">
                                        <h5 class="py-3 mb-0 font-18">{{ item.name }}</h5>
                                    </div>
                                    <div class="p-3 text-end">
                                        <p class="mb-0 font-16">Desde</p>
                                        <p class="mb-0 font-20 color-blue font-roboto-medium">
                                            <CurrencyDisplay :amount="item.price" :showCurrencyCode="true" :reduceIsoFont="true"/>
                                        </p>
                                        <p class="mb-0 font-16">ida y regreso</p>
                                    </div>
                                </figure>
                            </a>
                        </li>

                    </ul>

                    <carousel v-else :items-to-show="1.5" :autoplay="2000" :wrap-around="true">
                        <slide v-for="(item, index) in promotions.international" :key="index">
                            <div class="carousel__item">
                                <a class="card-promotions" :href="getUrlToPromotions(item, 'roundTrip')">
                                    <figure @click="quoteFlight(item)" class="overflow-hidden cursor-pointer">
                                        <img onerror="this.src='https://www.cvent-assets.com/brand-page-guestside-site/assets/images/venue-card-placeholder.png';" width="200" v-lazy="`https://www.tiquetesbaratos.com/assets/recent-search-imgs/iatas/${item.code}.jpg`" :alt="`background-${item.name}`" />
                                        <div class="px-3 border-bottom">
                                            <h5 class="py-3 mb-0 font-18">{{ item.name }}</h5>
                                        </div>
                                        <div class="p-3 text-end">
                                            <p class="mb-0 font-16">Desde</p>
                                            <p class="mb-0 font-20 color-blue font-roboto-medium">
                                                <CurrencyDisplay :amount="item.price" :showCurrencyCode="true" :reduceIsoFont="true" />
                                            </p>
                                            <p class="mb-0 font-16">ida y regreso</p>
                                        </div>
                                    </figure>
                                </a>
                            </div>
                        </slide>
                    </carousel>
                </div>
            </section>
           
        </div>

        <div v-else class="container py-4 c-widget-empty">
            <div class="container c-groups pb-5">
                <div class="row">
                    <div class="col-12">
                        <div class="c-form p-4 m-4 border rounded">
                            <div class="row">
                                <div class="col-12">
                                    <div class="py-2 d-sm-flex">
                                        <span class="icon icon-contact-support font-32 pr-2"></span>
                                    </div>
                                    <p>{{__("messages.promotions_not_avaible")}}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import 'vue3-carousel/dist/carousel.css'
    import { storeToRefs } from 'pinia';
    import { __ } from '../../../utils/helpers/translate';
    import { Carousel, Slide } from 'vue3-carousel'
    import { usePromotionStore } from '../../stores/promotion';
    import { getUrlWithQueryString } from "../../../utils/helpers/queryString";
    import { hidenCloak } from '../../../utils/helpers/animates';
    import { Logger } from '../../../utils/helpers/logger';
    import CurrencyDisplay from '../common/CurrencyDisplay.vue';

    const cultureData = window.__pt.cultureData;

    export default {
        data() {
            return {
                skeletons: [1, 2, 3],
                loading: true,
            }
        },
        components: {
            Carousel,
            Slide,
            CurrencyDisplay,
        },
        props: {
            typee: { type: String, default: "" },
            mobile: { type: Boolean, default: false },
        },
        setup() {
            const usePromotion = usePromotionStore();
            const { setSelected, setDestinationObjectSelected } = usePromotion;
            const { getPromotions, getOriginSelected, getSelected } = storeToRefs(usePromotion);
            return { getPromotions, getOriginSelected, setSelected, setDestinationObjectSelected, getSelected }
        },
        mounted() {
            setTimeout(() => {
                this.loading = false;
            }, "3000");
        },
        computed: {
            promotions() {
                const indexDestination = this.getPromotions.findIndex((promotion) => promotion.code.toLowerCase() === this.getOriginSelected.toLowerCase());
                if (indexDestination >= 0) {
                    const objectSelected = {
                        name: this.getPromotions[indexDestination].name,
                        code: this.getPromotions[indexDestination].code,
                    }
                    if (this.typee === "destination") {
                        this.setDestinationObjectSelected(objectSelected);
                    } else {
                        this.setSelected(objectSelected);
                    }
                    return this.getPromotions[indexDestination];
                } else {
                    return null;
                }
            },
        },
        mounted() {
            hidenCloak();
        },
        methods: {
            getUrlToPromotions(item, type) {
                try {
                    let origin, destination = "";
                    const dateStarting = this.$filters.date(item.date, "YYYY-MM-DD");
                    const dateReturning = this.$filters.date(item.returnDate, "YYYY-MM-DD");
                    if (this.typee === "origin") {
                        origin = `${this.getSelected.name}|${this.getSelected.code}`;
                        destination = `${item.name}|${item.code}`;
                    } else {
                        destination = `${this.getSelected.name}|${this.getSelected.code}`;
                        origin = `${item.name}|${item.code}`;
                    }

                    const queryString = {
                        option: "com_sabrecalendar2",
                        view: "calendar",
                        transporte: "unavia",
                        origen: origin,
                        destino: destination,
                        from: dateStarting,
                        to: dateReturning,
                        adultos: 1,
                        ninos: 0,
                        tipotarifa: type,
                        hotelsUri: item.hotelsUri,
                        campaingToken: item.campaignToken,

                    }
                    const url = getUrlWithQueryString(queryString);
                    return `/${cultureData.cultureCode}/vuelos/calendar/${url}`;

                } catch (err) {
                    Logger.error(err);
                }
            }
        }
    }
</script>

<style lang="scss" scoped>

    .card-promotions {
        color: unset;
        text-decoration: none;
    }

    figure {
        img {
            min-height: 181px;
        }
    }
</style>