import {defineStore} from 'pinia'
import {ListMapper} from '../mappers/listMapper';
import { sleep } from '../../utils/helpers';

export const useFlightStore = defineStore({
	id: "flight",
	state: () => ({
		loading: false,
		params: window.__pt.data,
		groups: [],
		flightsResponse: [],
		progressBar: 0,
		isloadFlightResponses: false
	}),

	getters: {
		getLoading: (state) => {
			return state.loading;
		},
		getGroups: (state) => {
			return state.groups;
		},
		getFlightResponse: (state) => {
			return state.flightsResponse;
		},
		getParams: (state) => {
			return state.params;
		},
		getProgressBar: (state) => {
			return state.progressBar;
		},
		getAllQuoteTokens: (state) => {
			const tokens = [];
			state.flightsResponse.forEach(fr => tokens.push(fr.quoteToken));
			return tokens;
		},
		getStartQuoteTokens: (state) => {
			const tokens = [];
			for (const flight in state.flightsResponse) {
				for (const airline in state.flightsResponse[flight].departure?.flightList) {	
					if (!(tokens.indexOf(state.flightsResponse[flight].departure?.flightList[airline].token) !== -1)) {		
						tokens.push(state.flightsResponse[flight].departure?.flightList[airline].token);
					}
				}
			}
			return tokens;
		},
		getReturnQuoteTokens: (state) => {
			const tokens = [];
			for (const flight in state.flightsResponse) {
				for (const airline in state.flightsResponse[flight].returning?.flightList) {	
					if (!(tokens.indexOf(state.flightsResponse[flight].returning?.flightList[airline].token) !== -1)) {		
						tokens.push(state.flightsResponse[flight].returning?.flightList[airline].token);
					}
				}
			}
			return tokens;
		},
		getreturningQuoteToken: (state) => {
			const tokens = [];
			state.flightsResponse.forEach(fr => tokens.push(fr.returningQuoteToken));
			return tokens;
		},
		getTotalGroup: (state) => {
			return (isStarting, code) => {
				let flight = null;
				for (let i = 0; i < state.groups.length; i++) {
					let group = state.groups[i];

					if (isStarting) {
						if (group && group.departure && group.departure.code === code) {
							for (let j = 0; j < group.departure.flights.length; j++) {
								if (group.departure.flights[j].cheap) {
									flight = group.departure.flights[j];
									break;
								}
							}
						}
					} else {
						if (group && group.returning && group.returning.code === code) {
							for (let k = 0; k < group.returning.flights.length; k++) {
								if (group.returning.flights[k].cheap) {
									flight = group.returning.flights[k];
									break;
								}
							}
						}
					}

					if (flight) {
						break;
					}
				}
				const paxes = window.__pt.data.adults + window.__pt.data.kids;
				return flight?.fares[0]?.amount / paxes;
			}
		},
		getfirstCheapest: (state) => {
			let firstCheapest = {
				departure: {},
				returning: {}
			};
			const views = ['departure', 'returning'];
			for (let grupo in state.groups) {
				const code = state.groups[grupo]['departure']?.code;
				for (let indexView in views) {
					let cheaspest = true;
					const view = views[indexView];
					firstCheapest[view][code] = {};
					let inCheaspest = 0;
					for (let index in state.groups[grupo][view]['flights']) {
						firstCheapest[view][code][index] = false;
						if (cheaspest && state.groups[grupo][view]['flights'][index]['cheap']) {
							inCheaspest = index;
							cheaspest = false;
						}
					}
					inCheaspest = cheaspest ? 0 : inCheaspest;
					firstCheapest[view][code][inCheaspest] = true;
				}
			}
			return firstCheapest;
		},
		getIsLoadFlightResponses: (state) => {
			return state.isloadFlightResponses;
		},
	},

	actions: {
		setLoading(value= false) {
			this.firstLoad = value
			this.loading = value
		},
		
		removeElementResponse(code){
			this.flightsResponse = this.flightsResponse.filter(item => !(item.airlines ?? []).find(iCode => iCode === code));
			this.groups = ListMapper.map(this.flightsResponse);
		},
		getElementResponse(code){
			return this.flightsResponse.find(item => (item.airlines ?? []).find(iCode => iCode === code)) 
		},

		setFlightResponse(response) {
			if (response && response.quoteToken) {
				this.flightsResponse.push(response);
				this.groups = ListMapper.map(this.flightsResponse);
			}
		},
		async setFlightResponses(responses) {
			try {
				this.isloadFlightResponses = true;
				this.resetFlightResponse()
				await sleep(50)
				this.flightsResponse = responses;
				this.groups = ListMapper.map(responses);
				this.isloadFlightResponses = false;
			} catch (error) {
				this.resetFlightResponse()
				this.isloadFlightResponses = false;
			}

		},
		resetFlightResponse() {
			this.flightsResponse = [];
			this.groups = [];
		},

		setProgressBar(response) {
			this.progressBar = response
		},

	},
});
