<template>
    <div class="item-flight-leg pt-md-2">
        <img :src="props.imageAirline" alt="Airline logo" width="30" height="30" class="item-flight-logo"
            v-if="!imgErrors[props.imageAirline]" @error="handleImgError(props.imageAirline)">
        <div class="item-flight-information">
            <span v-if="type=='starting'" class="flight-leg-title">{{__('messages.flight_leg_title_starting')}}</span>
            <span v-if="type=='returning'" class="flight-leg-title">{{__('messages.flight_leg_title_returning')}}</span>
            <span class="flight-leg-route">
                <strong class="route">{{ props.departureAirportCode }} - {{ props.arrivalAirportCode }} </strong>
                <br>
                <span class="flight-dates"> {{ $filters.date(props.dateFlight, 'ddd DD MMM') }} </span>
                <span class="ms-1" v-if="props.flightDays != 0">
                    <template v-if="props.flightDays > 1">
                        {{ __("multiticket.days", [props.flightDays]) }}
                    </template>
                    <template v-else>
                        {{ __("multiticket.day", [props.flightDays]) }}
                    </template>
                </span>
            </span>
            <small class="flight-leg-time"><span class="redeye">{{ props.departureTime }} - {{ props.arrivalTime
                    }}</span> - {{stops()}} </small>
        </div>
    </div>
</template>

<script setup>
    import { ref } from 'vue';
    import { __ } from '../../../utils/helpers/translate';
    const props = defineProps({
            type:"",
            departureAirportCode:"",
            arrivalAirportCode:"",
            arrivalTime:"",
            dateFlight:"",
            departureTime:"",
            stops:0,
            daysOfFlight:0,
            imageAirline:"",
            flightDays: 0
        })

    const imgErrors = ref({});
    const stops = () => {
        let scale = __("messages.stops_0");
        if (props.stops >= 1) {
            scale = props.stops == 1 ? props.stops + " " + __("messages.stops_1") : props.stops + " " + __("messages.stops_2");
        }
        return scale;
    }
    const handleImgError = (img) => {
        imgErrors.value[img] = true;
    };
</script>

<style lang="scss" scoped>

    .item-flight-leg {
        padding: 0rem 1rem 0px 0px;
    }

    .package-item-flight-content .item-flight-leg {
        padding: 0.5rem 1rem
    }

    #ptw-content {
        padding: 4px !important
    }

    .item-flight-logo, .item-flight-information {
        display: inline-block;
        float: left;
        margin-right: 5px;
    }

    .item-flight-leg, .item-flight-action {
        display: table-cell;
        vertical-align: middle;
    }

    .item-flight-leg, .item-flight-action {
        margin-bottom: 0.75em;
    }

    .flight-leg-title, .item-flight-action a {
        font-weight: bold;
    }

    .flight-leg-title, .flight-leg-route, .flight-leg-time {
        display: block;
    }

    .c-detail-flight-rate {
        .item-flight-information {
            * {
                font-size: 12px;
            }
        }    
    }
</style>