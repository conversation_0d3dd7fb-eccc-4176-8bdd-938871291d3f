﻿using Microsoft.Extensions.Options;
using TBFront.Models.Request;
using TBFront.Models.Response;
using TBFront.Options;
using System.Text.Json;
using TBFront.Models.Login;
using TBFront.Infrastructure.MailService.SendGrid;

namespace TBFront.Application.Implementations
{
    public class MailHandler
    {
        private MailService _mailService;
        private readonly SettingsOptions _options;
        private IHttpContextAccessor _httpContextAccessor;
        public MailHandler(MailService mailService, IOptions<SettingsOptions> options, IHttpContextAccessor httpContextAccessor)
        {
            _mailService = mailService;
            _options = options.Value;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task<MailResponse> SendMail(VoucherResponse voucherResponse, string bookingIdEncryp)
        {


            if (!_options.MailIsEnable || emailWasSend(bookingIdEncryp))
            {
                return new MailResponse { Message = "", IdMail = 0, SendSuccesful = false };
            }

            // Preguntar si hay más plantillas y la ruta
            var mailType = "/api/services/SendBookingInProcessEmail";

            var mailParams = new MailRequest
            {
                BookingId = voucherResponse.BookingId,
                ChannelId = voucherResponse.ChannelId,
                CustomerEmail = voucherResponse.CustomerEmail,
                CustomerName = $"{voucherResponse.CustomerFirstName} {voucherResponse.CustomerLastName}",
                Language = _options.MailIsLanguage,
                MailType = mailType
            };

            var request = _mailService.GetParams(mailParams);

            var responseMail = await _mailService.QueryAsync(request, mailType);

            return responseMail;

        }

        public async Task<MailResponse> SendBookNowPayLaterMail(int bookingId)
        {

            if (!_options.MailIsEnable)
            {
                return new MailResponse { Message = "", IdMail = 0, SendSuccesful = false };
            }

            var mailType = "/api/services/SendNewBookingEmail";

            var request = _mailService.GetParamsBookNow(bookingId);

            var responseMail = await _mailService.QueryAsync(request, mailType);

            return responseMail;

        }

        public bool emailWasSend(string bookingIdEncryp)
        {
            var getCookie = _httpContextAccessor.HttpContext.Request.Cookies["BookingMail"];
            List<string> emptyList = new List<string>();

            var listBooking = getCookie != null ? JsonSerializer.Deserialize<List<string>>(getCookie) : emptyList;

            if (!listBooking.Contains(bookingIdEncryp))
            {
                listBooking.Add(bookingIdEncryp);
                var jsonString = JsonSerializer.Serialize(listBooking);
                _httpContextAccessor.HttpContext.Response.Cookies.Append("BookingMail", jsonString);
                return false;
            }

            return true;
        }

        public async Task<ApiResponse<bool>> SendMailFromSendGrid(EmailRequest emailRequest, object modelData)
        {

            var responseMail = await _mailService.SendMail<bool>(emailRequest, modelData);
            return responseMail;
        }
    }
}
