export const getOrderUpsell = (upsell, orderItemsUpsell = []) => {

	for (let item of upsell) {
		if (item.content && item.content.length > 0) {
			item.content.sort((a, b) => {
				const categoriaA = a.category;
				const categoriaB = b.category;

				const indiceA = orderItemsUpsell.indexOf(categoriaA);
				const indiceB = orderItemsUpsell.indexOf(categoriaB);

				if (indiceA === -1) return 1;
				if (indiceB === -1) return -1;

				return indiceA - indiceB;
			});
		}
	}
	return upsell;
};