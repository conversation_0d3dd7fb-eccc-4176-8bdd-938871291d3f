.cb-flights {
  box-shadow: -2px 2px 10px #999;
  bottom: 0;
  position: sticky;
  z-index: 999
}

.bar-line {
  width: 100%;
  background-color: rgba(33, 150, 243, .15);
  position: absolute;
  left: -100%;
  animation: bar-line-frame 2s linear forwards;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  &.first-click{
    z-index: 1000;
    background-color: rgba(33, 150, 243, 1) !important;
    animation: bar-line-frame 1s linear forwards !important;

  }
}

@keyframes bar-line-frame {
  0% {
    left: -100%;
    width: 100%
  }

  50% {
    left: 0;
    width: 100%
  }

  to {
    left: 100%;
    width: 0
  }
}

.bar-line-bottom:before {
  content: "";
  background-color: var(--bg-primary);
  bottom: 0;
  height: 10px;
  left: 0;
  position: absolute;
  width: 0;
  animation: bar-line-bottom-frame .98s ease-in-out forwards
}

@keyframes bar-line-bottom-frame {
  0% {
    width: 0;
    right: 100%
  }

  to {
    width: 100%;
    right: 0
  }
}

.fade-in {
  animation: fadeInAnimation 0.2s;
}
.opacity-0{
  display: none;
}
@keyframes fadeInAnimation {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes fadeInAnimation_05 {
  from {
    opacity: 0;
  }
  to {
    opacity: 0.5;
  }
}
.fade-out {
  animation: fadeOutAnimation 0.5s;
}

@keyframes fadeOutAnimation {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
.modal{
  &.show{
    animation: fadeInAnimation 0.5s;
  }
}
.modal-backdrop{
  &.show{
    animation: fadeInAnimation_05 0.5s;
  }
}

.line-flights{
  .bb-white {
    @media (min-width: 768px) and (max-width: 1024px), (min-width: 1025px) {
      display: block !important;
    }
  }
  @media (max-width: 991px){
    border: 1px solid #c4c4c4;
    border-radius: 9px;
    box-shadow: 0 .125rem .25rem rgba(0,0,0,.075)!important;
    overflow: hidden;
  }
}

@media (min-width: 991px){
  .cf-layer {
    border: 2px solid #dee2e6;
    padding: 5px 20px 0;
    overflow: hidden;
  }
}
@media (max-width: 991px){
  .cf-layer {
    border: 0;
    padding: 5px 20px 0;
    overflow: auto;
  }
}
.c-multiticket{
  @media (max-width: 991px){
    .c-flights-list .c-bar-info span {
      display: inline !important;
    }
  }

}
@media (max-width: 991px){
  .c-round-flight .c-title-go {
    display: block !important;
    //padding-right: 15px;
    //padding-left: 15px;

  }
  .title-list{
    //padding-right: 15px;
    //padding-left: 15px;
  }
  .c-info-flight-xs {
    background-color: #f2f2f2 !important;
    border-radius: 10px !important;
  }
}
.flight-info-connections {
  padding-right: 30px !important;
  @media (min-width: 1025px) {
    padding-right: 30px !important;
  }

}

.c-info-flight[data-estado="activo"] {
  .shadow {
    box-shadow: none !important;
  }


}
/* Ajustes multiticket */
.c-multiticket {
  .c-sticky {
    top: -3px !important;
  }

  .header-style-list-flights {
    border-radius: 0 5px 0 0 !important;
    position: relative !important;
    top: 0px !important;
    overflow: hidden !important;
    @media (min-width: 768px) {
      right: 1px;
    }
    @media ($tablet) {
      right: 0;
    }       
  }

  .cf-logo {
    div {
      top: 2px !important;
    }
  }

  .cf-logo.cfl-c {
    div {
      top: 0 !important;
    }
  }

  .ctl-line {
    border: 1px solid;
    border-radius: 50px;
  }

  .c-early-dates {
    .row {
      @media (max-width: 767px) {
        margin-right: -30px !important;
        margin-left: -30px !important;
      }

      .custom-control {
        .custom-control-label {
          .c-text {
            font-size: 14px !important;
          }
        }
      }

    }
  }

  .line-flights {
    .rounded-left.line-bottom {
      @media (max-width: 767px) {
        padding: 0 15px !important;
      }
    }

  }

  .modal-family-information {
    .modal-content {
      @media (max-width: 767px) {
        background-color: transparent !important;
      }
    }
  }

  .line-flights {
    .c-line-schedule {
      .icon {
        @media (max-width: 767px) {
          left: 0;
          font-size: 18px !important;
          top: -6px !important;
          right: 0;
        }
        @media (min-width: 768px) and (max-width: 991px) {
          left: 0;
          right: 0;
        }
        @media (max-width: 400px) {
          left: 6px;
        }
        @media (max-width: 390px) {
          left: 8px;
        }
        @media (max-width: 380px) {
          left: 10px;
        }
        @media (max-width: 370px) {
          left: 12px;
        }
      }
    }
  }

  .c-line-schedule {
    @media (min-width: 992px) and (max-width: 1024px) {
      top: 19px !important;
    }
  }

  .cf-layer {
    @media (max-width: 767px) {
      overflow-x: hidden !important;
    }
  }

  .c-view-more {
    div {
      @media (max-width: 767px) {
        padding-left: 0 !important;
        padding-right: 0 !important;
      }
    }
  }

  .tbl-mobile {
    @media (max-width: 767px) {
      display: none;
    }
  }

  .c-int-matrix {
    .tbl-all {
      thead {
        @media (max-width: 767px) {
          display: revert !important;
        }
      }
    }
  }


  .c-matrix {
    @media (max-width: 767px) {
      padding-left: 45px !important;
    }
  }

  .table-dates {
    td {
      @media (max-width: 767px) {
        display: table-cell;;
      }
    }
  }

  .cc-header-flights {
    #mobileInfo {
      @media (max-width: 767px) {
        display: table-cell;;
      }
    }
  }

  .table {
    .m-tooltip-original {
      padding: 15px 25px !important;
      @media (min-width: 1025px) {
        padding: 15px !important;
      }

      .btn {
        color: #fff !important;
      }
    }
  }

  .cf-layer {
    @media (max-width: 767px) {
      margin-left: -14px !important;
      margin-right: -14px !important;
    }
  }

  .cbi-container {
    @media (max-width: 767px) {
      padding-left: 5px !important;
      padding-right: 5px !important;
    }
  }

  .c-btns-mobile {
    @media (min-width: 1025px), (min-width: 768px) and (max-width: 1024px) {
      padding-left: 0;
    }
  }

  .m-tooltip-original {
    @media (max-width: 767px) {
      left: -43px;
      top: 69px;
    }
    @media (min-width: 992px) and (max-width: 1279px) {
      left: -48px;
      top: 44px;
    }
    @media (min-width: 1280px) {
      left: -48px;
      top: 44px;
    }
  }
}

.c-multiticket{
  @media (max-width: 767px){
    .round-flight .c-scroll-vertical {
      left: -77px;
    }
    .c-scroll-horizontal{
      padding-right: 16px;
    }
  }
  .c-fs span, .c-fs strong {
    color: #333132;
  }
}
.c-all {
  * {
    font-size: 15px;
  }
}

.logo-JA {
  background-image: url(/assets-tb/img/tiquetesbaratos/logos/logo-jetsmart.png);
  background-size: contain;
  background-position: 50%;
  background-repeat: no-repeat;
  height: 29px;
  margin: auto;
  width: 90px;
}
.c-JA{
  border-color:#366086!important
}
.c-JA .header-color{
  background-color:#366086!important;
    &:before {
      background-color: #366086;
      content: "";
      position: absolute;
      left: -1px;
      top: 0;
      right: 0;
      width: 2px;
      bottom: 0;
  } 
}
.c-JA .line-bottom{
   border-bottom:1px solid rgba(54,96,134,.2);
 }
.c-JA .bg-1{
  background-color:rgba(54,96,134,.05);
}
.c-JA .bg-2{
  background-color:rgba(54,96,134,.1);
}
.c-JA .bg-3{
   background-color:rgba(54,96,134,.2);
}.c-JA .bg-4{
   background-color:rgba(54, 96, 134, .20);
 }

//wingo

.c-P5 {
  border-color: #63c !important
}

.c-P5 .header-color {
  background-color: #63c !important;
    &:before {
      background-color: #63c;
      content: "";
      position: absolute;
      left: -1px;
      top: 0;
      right: 0;
      width: 2px;
      bottom: 0;
  } 
}

.c-P5 .line-bottom {
  border-bottom: 1px solid rgba(42,0,136,.1)
}

.c-P5 .bg-1 {
  background-color: rgba(42,0,136,.01)
}

.c-P5 .bg-2 {
  background-color: rgba(42,0,136,.03)
}

.c-P5 .bg-3 {
  background-color: rgba(42,0,136,.05)
}

.c-P5 .bg-4 {
  background-color: rgba(42,0,136,.1)
}

.c-P5 .bg-5 {
  background-color: rgba(42,0,136,.15)
}

.c-P5 .bg-6 {
  background-color: rgba(42,0,136,.2)
}

.c-P5 .ctl-line {
  border-color: #63c !important
}

.c-P5 .ctl-line * {
  color: #63c !important
}

.logo-P5 {
  background-image: url(/assets-tb/img/tiquetesbaratos/logos/logo-wingo.jpg);
  background-size: contain;
  background-position: 50%;
  background-repeat: no-repeat;
  height: 29px;
  margin: auto;
  width: 90px;
}
.logo-9R {
  background-image: url(/assets-tb/img/tiquetesbaratos/logos/logo-satena.png);
  background-size: contain;
  background-position: 50%;
  background-repeat: no-repeat;
  height: 29px;
  margin: auto;
  width: 90px;
}
.logo-VE {
  background-image: url(/assets-tb/img/tiquetesbaratos/logos/logo-clic.png);
  background-size: contain;
  background-position: 50%;
  background-repeat: no-repeat;
  height: 29px;
  margin: auto;
  width: 90px;
}


/**satena **/
.c-9R {
  border-color: #366086 !important;
  .header-color {
    background-color: #366086 !important
  }
  .line-bottom {
    border-bottom: 1px solid rgba(54,96,134,.15)
  }
  .bg-1 {
    background-color: rgba(54,96,134,.05)
  }
  .bg-2 {
    background-color: rgba(54,96,134,.1)
  }

  .bg-3 {
    background-color: rgba(54,96,134,.15)
  }

  .bg-4 {
    background-color: rgba(54,96,134,.2)
  }
  
  .bg-5 {
    background-color: rgba(54,96,134,.30)
  }
  .ctl-line {
    border-color: #366086 !important
  } 
  
  .ctl-line * {
    color: #366086 !important
  }
}

/**clic air**/
.c-VE {
  border-color: #366086 !important;
  .header-color {
    background-color: #366086 !important
  }
  .line-bottom {
    border-bottom: 1px solid rgba(254,118,17,.3)
  }
  .bg-1 {
    background-color: rgba(254,118,17,.05)
  }
  .bg-2 {
    background-color: rgba(254,118,17,.1)
  }
  .bg-3 {
    background-color: rgba(254,118,17,.15)
  }
  .bg-4 {
    background-color: rgba(254,118,17,.2)
  }
  .bg-5 {
    background-color: rgba(254,118,17,.30)
  }
  .ctl-line {
    border-color: #366086 !important
  }
  .ctl-line * {
    color: #366086 !important
  }
}
.c-step {
  background-color: $green;
  border-radius: 100px;  
  padding: 1px 7px;
  font-size: 12px;
  font-weight: 600;
}
.color-gray-650 {
  color: #94979C;
}
.c-step-gray {
  background-color: #94979C;
  color: #fff;
}
.c-step-green {
  background-color: $green;
  color: #fff;
}