using ProtoBuf;
using System.Text.Json.Serialization;

namespace TBFront.Models.Blacklist.Response
{
    [ProtoContract]
    public class BlacklistResponse
    {
        [ProtoMember(1)]
        [JsonPropertyName("blackListFingerPrints")]
        public List<string> BlackListFingerPrints { get; set; } = new List<string>();

        [ProtoMember(2)]
        [JsonPropertyName("blackListEmails")]
        public List<string> BlackListEmails { get; set; } = new List<string>();

        [ProtoMember(3)]
        [JsonPropertyName("blackListEmailDomains")]
        public List<string> BlackListEmailDomains { get; set; } = new List<string>();

        
    }
}
