<template>
    <div class="container table-selected-container d-none d-md-block">
        <div class="row">
            <div class="col-12 px-md-0">

                <div class="accordion flight-selected-collapse mt-3" id="accordion-flight-selected">
                    <div class="accordion-item row-color-1">
                        <h2 class="accordion-header" id="heading-item">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                data-bs-target="#collapseFlight" aria-expanded="false" aria-controls="collapseFlight"
                                @click="toggleAccordion">
                                <span class="text-toggle-accordion h4 mb-0">
                                    {{ __('messages.flight_recommended') }} &nbsp;
                                </span>
                                <span class="text-toggle fw-bold">
                                    <template v-if="isOpen">
                                        {{ __('messages.hide') }}
                                    </template>
                                    <template v-else>
                                        {{ __('messages.show') }}
                                    </template>
                                </span>
                            </button>
                        </h2>
                        <div id="collapseFlight" class="accordion-collapse collapse show" aria-labelledby="heading-item"
                            data-bs-parent="#accordion-flight-selected">
                            <section id="InternationalListContainer" class="container pb-2 pb-md-3 px-2 px-md-3">
                                <div style="background-color: white; border-radius: 10px;">
                                    <div class="container ">
                                        <div class="row row-item">
                                            <div class="col-7 col-md-12 px-2 px-md-3">
                                                <div class="row">

                                                    <div class="col-12 col-lg-2 align-content-center text-ellipsis">
                                                        <span class="">
                                                            <img width="27" loading="lazy"
                                                                :src="setImgAirline(dataFlight.airline.code)">
                                                            <span class="airline-title ms-2">{{ dataFlight.airline.name
                                                                }}</span>

                                                        </span>
                                                    </div>
                                                    <div
                                                        class="col-12 col-md-4 col-lg-3 align-content-center px-md-2 position-relative">
                                                        <div class="dashed-line-container">
                                                            <span class="d-grid">
                                                                <span class="d-lg-none font-14 color-gray-300">{{
                                                                    dataFlight.departure.airportCode }}</span>
                                                                <span class="dlc-time">{{ dataFlight.departure.time
                                                                    }}</span>
                                                            </span>
                                                            <div class="dashed-line-wrapper">
                                                                <div class="dashed-line"></div>
                                                                <span
                                                                    class="dashed-line-text-departure d-none d-lg-block">{{
                                                                    dataFlight.departure.airportCode }}</span>
                                                            </div>
                                                            <span class="dlc-duration">{{ dataFlight.flightDuration
                                                                }}</span>
                                                            <span class="icon icon-plane-right dlc-icon d-none mx-auto"></span>
                                                            <div class="dashed-line-wrapper">
                                                                <div class="dashed-line"></div>
                                                                <span
                                                                    class="dashed-line-text-arrival d-none d-lg-block">{{
                                                                    dataFlight.arrival.airportCode }}</span>
                                                            </div>
                                                            <span class="d-grid">
                                                                <span
                                                                    class="d-lg-none font-14 color-gray-300 text-end">{{
                                                                    dataFlight.arrival.airportCode }}
                                                                    <span class="exponent"
                                                                        v-if="dataFlight.flightDays > 0">+{{
                                                                        dataFlight.flightDays }}</span>
                                                                </span>
                                                                <span class="dlc-time">{{ dataFlight.arrival.time
                                                                    }}</span>
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div class="col col-lg-3 align-content-center px-md-2">
                                                        <div class="info-line-container">
                                                            <span class="ilc-days d-md-none d-lg-block"
                                                                :class="{ 'invisible': dataFlight.flightDays == 0 }" data-bs-toggle="tooltip" data-bs-placement="top" 
                            :data-bs-title="`${__('flightList.youArriveOn')} ${$filters.date(dataFlight.arrival.date, 'ddd DD MMM YYYY')}.`" @mouseover="handleMouseOver($event)"
                            :id="`flightDays${idxGroup}${indexFlight}`">
                                                                <template v-if="dataFlight.flightDays > 1">
                                                                    {{__("multiticket.days", [dataFlight.flightDays])}}
                                                                </template>
                                                                <template v-else>
                                                                    {{__("multiticket.day", [dataFlight.flightDays])}}
                                                                </template>
                                                            </span>

                                                            <div style="flex-grow: 1;" class="d-none d-lg-block"></div>
                                                            <span class="ilc-scales cursor-pointer"
                                                                @click.stop.prevent="getFlightDetails(dataFlightGroup.departure.name, dataFlightGroup.departure.image, dataFlight, dataFlightGroup, 'starting')">
                                                                <template
                                                                    v-if="dataFlight.stops == 0">{{__("multiticket.direct")}}</template>
                                                                <template
                                                                    v-else-if="dataFlight.stops == 1">
                                                                    {{ dataFlight.stops }} {{__("multiticket.stop")}}</template>
                                                                <template v-else>{{ dataFlight.stops }}
                                                                    {{__("multiticket.stops")}} </template>
                                                            </span>
                                                            <div style="flex-grow: 1;" class="d-none d-lg-block"></div>
                                                            <span class="ilc-bags cursor-pointer" v-if="dataFlightLuggages"
                                                                @click.stop.prevent="getFlightDetails(dataFlightGroup.departure.name, dataFlightGroup.departure.image, dataFlight, dataFlightGroup, 'starting', true)">
                                                                <span :id="`hand-selected`"
                                                                    @mouseover="handleMouseOver($event)"
                                                                    data-bs-toggle="tooltip" data-bs-placement="top"
                                                                    :title="dataFlightLuggages['extra'][2]['title']"
                                                                    :class="dataFlightLuggages['extra'][2]['class']"></span>

                                                                <span :id="`checked-selected`"
                                                                    @mouseover="handleMouseOver($event)"
                                                                    data-bs-toggle="tooltip" data-bs-placement="top"
                                                                    :title="dataFlightLuggages['extra'][1]['title']"
                                                                    :class="dataFlightLuggages['extra'][1]['class']"></span>
                                                            </span>
                                                            <span class="ilc-bags" v-else>
                                                                <span class="px-2 py-1 me-1 is-loading"></span>
                                                                <span class="px-2 py-1 is-loading"></span>
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div
                                                        class="col col-md-auto col-lg-2 d-none d-md-block text-end px-md-1">
                                                        <div class="mgs-price">
                                                            <span>{{__("multiticket.price_per_pax")}}</span>
                                                            <span>
                                                                <CurrencyDisplay :amount="getAmountPerPerson(totalAmountDisplay)" :showCurrencyCode="false" />
                                                            </span>
                                                            <span>{{__("multiticket.tax_include")}}</span>
                                                        </div>
                                                    </div>
                                                    <div @click="changeFlight()"
                                                        class="col col-lg-2 d-none d-md-block text-center scales-list align-content-center px-md-1 cursor-pointer btn-change">
                                                        {{__("multiticket.change_flight")}}
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-5 ps-0 pe-2 d-md-none text-end align-content-center">
                                                <div class="mgs-price">
                                                    <span>{{__("multiticket.price_per_pax")}}</span>
                                                    <span>
                                                        <CurrencyDisplay :amount="getAmountPerPerson(totalAmountDisplay)" :showCurrencyCode="false" />
                                                    </span>
                                                    <span>{{__("multiticket.tax_include")}}</span>
                                                </div>
                                            </div>
                                            <div class="col-12 my-2 ps-0 pe-2 d-md-none text-end scales-list cursor-pointer btn-change"
                                                @click="changeFlight()">
                                                {{__("multiticket.change_flight")}}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </section>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="container table-selected-container d-md-none">
		<div class="row">
			<div class="col-12"></div>
			<div class="container">
				<div class="row">
					<div class="col">
						<div class="bg-selected py-2 px-1">
							<div class="col-12 mb-1 px-1">
								<span class="f-bold me-2">{{__("multiticket.selected_departure_flight")}} </span>
								<a class="a-link-1 ml-2 f-light" id="relink" @click="changeFlight()">{{__("multiticket.change_flight")}}</a>
							</div>
							<div class="col-12 font-14 px-1 d-flex align-items-center">
								<span id="familyMobile" class="pointer mr-025 text-ellipsis" style="max-width: 40px;">{{dataFlightLuggages.name}}</span>
								<span class="icon icon-info font-22 pointer" id="modalFamilyMobile"  @click.stop.prevent="getFlightDetails(dataFlightGroup.departure.name, dataFlightGroup.departure.image, dataFlight, dataFlightGroup, 'starting', true)"></span>
								<span class="px-1 pipe mr-025 ml-025" id="mobileSeparador">|</span>
								<span class="a-link-1 pointer" id="escalaMobile" data-detailrow="detalleMobileIda" v-if="dataFlight.stops == 0" @click.stop.prevent="getFlightDetails(dataFlightGroup.departure.name, dataFlightGroup.departure.image, dataFlight, dataFlightGroup, 'starting')">{{__("multiticket.direct")}} </span>
                                <span class="a-link-1 pointer" id="escalaMobile" data-detailrow="detalleMobileIda" v-else-if="dataFlight.stops == 1" @click.stop.prevent="getFlightDetails(dataFlightGroup.departure.name, dataFlightGroup.departure.image, dataFlight, dataFlightGroup, 'starting')">{{ dataFlight.stops }}{{__("multiticket.stop")}} </span>
                                <span class="a-link-1 pointer" id="escalaMobile" data-detailrow="detalleMobileIda" v-else @click.stop.prevent="getFlightDetails(dataFlightGroup.departure.name, dataFlightGroup.departure.image, dataFlight, dataFlightGroup, 'starting')">{{ dataFlight.stops }}{{__("multiticket.stops")}} </span>
								<span class="pl-1" id="horarioMobileIda"> {{ dataFlight.departure.time }} </span>
								<span class="icon icon-plane-right font-12 mr-05 ml-05"></span>
								<span class="pr-1" id="horarioMobileLlegada">{{ dataFlight.arrival.time }}</span>
								<span class="px-1 pipe">|</span>
								<span id="precioMobile">
                                    <CurrencyDisplay :amount="getAmountPerPerson(totalAmountDisplay)" :showCurrencyCode="false" />
                                </span>
                            </div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>


<script setup>

import { storeToRefs } from 'pinia';
import { onMounted, ref, computed } from 'vue';
import { getDetail, getParamsDetailFlight, getFamilyFare, getParamsUpsell, getUpsell, getParamsRevalidate, getRevalidate } from '../../services/ApiFlightFrontServices';
import { useMultiTicketStore } from '../../stores/multiTicket';
import { useFlightDetailStore } from '../../stores/flightDetail';
import { useFlightFamilyFareStore } from '../../stores/flightFamilyFare';
import { sleep } from '../../../utils/helpers';
import { useFlightStore } from '../../stores/flight';
import { getMatrix, getFilteredList } from '../../services/ApiFlightFrontServices';
import { useFlightMatrixStore  } from '../../stores/flightMatrix';
import { usePromotionStore } from '../../stores/promotion';
import { useUserSelectionStore } from '../../stores/user-selection';
import { List } from '../../../utils/analytics/flightList.js'
import CurrencyDisplay from '../common/CurrencyDisplay.vue';

const storeUserSelection = useUserSelectionStore();
const storeFlight = useFlightStore();
const useMultiTicket = useMultiTicketStore();
const flightDetailStore = useFlightDetailStore();
const flightFamilyFareStore = useFlightFamilyFareStore();
const storeFlightMatrix = useFlightMatrixStore();
const promotionStore = usePromotionStore();

const { getStartQuoteTokens, getAllQuoteTokens, getreturningQuoteToken } = storeToRefs(storeFlight);
const { setFlightResponses, resetFlightResponse } = storeFlight;
const { getCheckOutDataStepOne } = storeToRefs(useMultiTicket);
const { getShowDetail } = storeToRefs(flightDetailStore);
const { setFlightDetailResponse, activeModalDetail, setExtraData } = flightDetailStore;
const { setFlightFamilyFareResponse, setIsLuggage } = flightFamilyFareStore;
const { setIsStepTwo, setStopsFilters, setAirlinesFilters} = useMultiTicket;
const { setFlightMatrix } = storeFlightMatrix;
const { getTripMode } = storeToRefs(promotionStore);
const { changeFilters } = storeUserSelection;

const dataFlight = computed(() => {
    return getCheckOutDataStepOne.value.departureFlight;
});

const dataFlightGroup = computed(() => {;
    return getCheckOutDataStepOne.value.groupFlight;
});

const dataFlightLuggages = computed(() => {;
    return getCheckOutDataStepOne.value.luggages;
});

const totalAmountDisplay = computed(() => {;
    return getCheckOutDataStepOne.value.totalAmount;
});

const isOpen = ref(true);

const toggleAccordion = () => {
    isOpen.value = !isOpen.value;
};

const changeFlight = async () => {
    const modal = new bootstrap.Modal(document.getElementById('LoaderFullPage'), null);
    modal.show();
    await sleep(1000)

    const params = {
                        token: getAllQuoteTokens.value.join(','),//(!getIsStepTwo.value ? getStartQuoteTokens.join(',') : getReturnQuoteTokens.join(',')),
                        filterApplied: '',
                        site: window.__pt.settings.site.apiFlights.siteConfig,
                        tripMode : getTripMode.value,
                        simpleFlightQuotes : true,
                        step : true,
                        roundTripToken: getreturningQuoteToken.value.join(',')
                    };                

    params.allQuotes = !window.__pt.data.isNational;
    const response = await getFilteredList(params);
    resetFlightResponse();
    await sleep(50)
    changeFilters([]);
    await setFlightResponses(response.response);
    setIsStepTwo(false);
    await setMatrix();
    setAirlinesFilters([]); //-> reiniciamos filtros
    setStopsFilters('');
    await sleep(1000)
    modal.hide();
}
const setMatrix = async () => {
	if (getStartQuoteTokens.value && getStartQuoteTokens.value.length) {
		const response = await getMatrix({ token: getStartQuoteTokens.value.join(','), step: true });//
		setFlightMatrix(response);
	}
}
const resetFilterData = async () => {
    setIsStepTwo(false);
    setFlightMatrix([]);
}
const flightsToken = computed(() => {   
    return dataFlightGroup.value.departure.token;
});

const quoteToken = computed(() => {   
    return dataFlightGroup.value.quoteToken;
});

const getFlightDetails = async (name, image, flight, group, type, luggage = false) => {
    if (getShowDetail.value) return;
    activeModalDetail();
    const modalElement = document.getElementById('modalDetail');
    const modal = new bootstrap.Modal(modalElement);
    setExtraData({
        airlineLogoUri: image,
        airlineName: name
    });
    modal.show();
    const tramos = flight.fares[0].fareKey.split("|");
    const familias = tramos[0]
        .split("^")
        .map(segmento => segmento.split("~")[2]);
    let paramsDetail = {
        
        token: flightsToken.value,
        flightId: flight.id,
        flightType: type,
        airlineLogoUri: image,
        airlineName: name,
        fareId: flight.fares[0].fareId,
        fareKey: flight.fares[0].fareKey,
        familyFare: familias.join(","),
    };
    if (quoteToken.value.split(",")?.length > 1) {
        paramsDetail.oneWayToken = quoteToken.value;
        paramsDetail.multiple = flight.fares[0].multiple
        paramsDetail.stepView = "starting";
    }
    let rq = getParamsDetailFlight(paramsDetail);

    let responseDetail = await getDetail(rq);
    let responseFamilyFare = await getFamilyFare(rq);
    setFlightDetailResponse(responseDetail);
    setFlightFamilyFareResponse(responseFamilyFare);
    List.modalDetail(group.departure.code, responseFamilyFare.familyFareName, 'ida', luggage);
    activeModalDetail();
    setIsLuggage(luggage);
}

const handleMouseOver = (event) => {
    if (!window.__pt.settings.site.isMobileDevice()) {
        const span = document.getElementById(event.target.id);
        const tooltip = new bootstrap.Tooltip(span);
        tooltip.show();
    }
}
const getAmountPerPerson = (amount) => {
    const paxes = window.__pt.data.adults + window.__pt.data.kids;
    return amount / paxes;
}

const setImgAirline = (airlineCode) => {
    return `https://img.cdnpth.com/media/images/airlines-logos/${airlineCode}.svg`
}
</script>