<template>
    <div v-if="isShow" class="row">
        <div class="col-12 col-lg-10 offset-lg-1 p-0">
            <div class="row c-newsletter px-2 py-4 bg-white mx-0 shadow border">
                <div class="col-12 col-lg-12 pl-lg-4">
                    <h3 class="font-poppins-medium mt-lg-3 mb-lg-2 font-22 mb-3">{{ __("newsletter.title") }}</h3>
                    <p class="mb-0 color-black-light">{{ __("newsletter.subtitle") }}</p>
                </div>
                <div class="col-12 col-lg-12 pr-lg-4">
                    <div class=" pt-4 content-form-newsletter">
                        <div class="col pr-md-0 pr-lg-2 col pt-md-0 mb-md-3 mb-lg-2 pt-lg-0">
                            <div class="form-email">
                                <input type="text" v-model="emailUser" class="p-3 w-100 border rounded " :placeholder="__('newsletter.email')" />
                                <small v-if="showError" class="text-danger">{{ __("newsletter.validatorMessage") }}</small>
                            </div>
                            
                        </div>
                        <div class="col pr-md-0 pr-lg-2 pb-md-2">
                            <div>
                                <div class="cap">
                                    <div class="g-recaptcha-grupos">
                                        <div class="g-recaptcha"  id="recaptcha-newsletter-form" :data-sitekey="config.recaptchaKey"></div>
                                    </div>
                                    <p class="invalid-feedback text-left d-block mb-0" v-if="recaptchaToken === '' && submitCount > 0">
                                        {{__('errors.recaptcha_error')}}
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col mt-md-0 ps-md-1 h-100">
                            <div class="d-flex align-items-center h-100">
                                <button @click="sendEmailNewsletter()" class="vtb btn-primary w-100 py-4 px-4">{{ __("newsletter.suscribe") }}</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import { __ } from '../../../utils/helpers/translate';
    import { apiRequestService } from '../../../utils/http/service';
    import { isEmail } from '../../../utils/helpers/validator';
    import { getCookie } from '../../../utils/helpers/cookies';
    import { Generic } from '../../../utils/analytics/generics.js'
import { Logger } from '../../../utils/helpers/logger';
    import {getWgetIdCaptcha} from "../../../utils/helpers/recaptcha";

    export default {
        data() {
            return {
                config: window.__pt.settings.site || {},
                emailUser: "",
                showError: false,
                errorCaptcha: false,
                recaptchaToken: null,
                submitCount: 0
            }
        },
        props: {
            isShow: { type: Boolean, default: false },
        },
        methods: {
            __,
            async sendEmailNewsletter() {
                try {
                    this.recaptchaToken = this.validateCaptcha()
                    this.submitCount += 1
                    if (isEmail(this.emailUser) && this.recaptchaToken) {
                        const data = {
                            "apiKey": "ayyo9d",
                            "userKey": getCookie("__ca__chat"),
                            "email": this.emailUser,
                            "domain": location.host,
                            "timezone": new Date().getTimezoneOffset(),
                            "resolution": `${window.screen.width}x${window.screen.height}`,
                            "url": location.href,
                            "referrer": document.referrer || "",
                            "widget_ready": true
                        };
                        Generic.newsLetter(this.emailUser);
                        const response = await apiRequestService({ method: "post", fullUri: "https://tiquetesbaratos.user.com/api/v2/user-chatping/" }, null, data, "");
                        this.showError = false;
                        this.emailUser = "";
                        this.resetCaptcha()
                    } else {
                        this.showError = true;
                    }
                } catch (err) {
                    Logger.error(err);
                }

            },
            validateCaptcha(){
                const wgetId =  getWgetIdCaptcha("#recaptcha-newsletter-form")
                const recaptchaRes = grecaptcha.getResponse(wgetId);
                return recaptchaRes;
            },
            resetCaptcha(){
                const wgetId = this.wgetIdCaptcha()
                grecaptcha.reset(wgetId)
            }
        },
    }
</script>
<style lang="scss" scoped>
    .btn-primary {
        background: #2196f3;
        border: none;
        border-radius: 4px;
        color: #fff !important;
        transition: all .2s linear;
    }
    .cap {
      width: 100%;
      //align-items: center;
      //margin-bottom: 10px;
      @media screen and (max-width: 768px) {
        margin-bottom: 1rem;
        //justify-content: start !important;
      }
    }

    .g-recaptcha {
      width: 100% !important;
    }
    @media only screen and ( max-width: 768px) {
      .g-recaptcha {
        transform:scale(1.1);
        transform-origin:0 0;
      }
    }
    @media only screen and ( max-width: 767px) {
      .form-email {margin-bottom: 0.8rem;}
    }
    @media only screen and (max-width: 410px) {
      .cap{
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
      }
      .g-recaptcha {
        transform:scale(1);
        transform-origin:0 0;
      }
    }
    @media only screen and (max-width: 365px) {
      .g-recaptcha {
        transform:scale(0.95);
        transform-origin:0 0;
      }
    }
    @media only screen and (max-width: 364px) {
      .g-recaptcha {
        transform:scale(0.85);
        transform-origin:0 0;
      }
    }
    .form-email{
      input{
        min-height: 77px;
      }
    }
    .space{
      margin-top: 0.5rem;
    }
    
    .content-form-newsletter{
      grid-gap: 0.5rem;
      justify-content: space-between;
      display: flex;
      flex-wrap: wrap;
      .col {
        flex: 1;
      }
      @media (max-width: 1275px) {
        .col:last-child {
          flex: 100%;
        }
      }
      @media only screen and ( max-width: 767px){
        .col {
          flex: 100%;
        }
      }
    }

</style>