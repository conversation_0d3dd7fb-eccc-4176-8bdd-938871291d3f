﻿using TBFront.Models.Flight.Quote;
using TBFront.Models.Request;
using TBFront.Models.Common;
using TBFront.Options;

namespace TBFront.Application.Mappers
{
    public class FlightQuoteMapper
    {

        public static FlightQuoteResponse Map(FlightQuoteResponse response)
        {
            return response;
        }
        public static FlightQuoteRequest MapFlightListRequest(FlightListRequest request, SettingsOptions _options)
        {

            var passengers = FlightQuoteMapper.Passengers(request.Adults, request.Kids, request.Agekids);
            var points = FlightQuoteMapper.Points(request.TripMode, request.StartingFromAirport, request.ReturningFromAirport, request.StartingFromDateTime, request.ReturningFromDateTime);


            return new FlightQuoteRequest
            {
                Context = new Context()
                {
                    ChannelID = _options.Channel,
                    //ClientID = 912,
                    ClientID = 4,
                    OrganizationID = _options.Organization,
                },
                Currency = _options.Currency,
                TripMode = request.TripMode,
                IsPackage = false,
                FareMode = request.Mode,
                Passengers = passengers,
                Points = points
            };
        }

        public static FlightQuoteRequest MapFlightListRequest(FlightRevalidateRequest request, SettingsOptions _options)
        {

            var passengers = FlightQuoteMapper.Passengers(request.Adults, request.Kids, request.Agekids);
            var points = FlightQuoteMapper.Points(request.TripMode, request.StartingFromAirport, request.ReturningFromAirport, request.StartingFromDateTime, request.ReturningFromDateTime);


            return new FlightQuoteRequest
            {
                Context = new Context()
                {
                    ChannelID = _options.Channel,
                    //ClientID = 912,
                    ClientID = 1,
                    OrganizationID = _options.Organization,
                },
                Currency = _options.Currency,
                TripMode = request.TripMode,
                IsPackage = false,
                FareMode = request.Mode,
                Passengers = passengers,
                Points = points
            };
        }

        public static string Key(int mode, string startingFromAirport, string returningFromAirport, string startingFromDateTime, string returningFromDateTime, int adults, string children)
        {
            return $"quote_{mode}_CODE:{startingFromAirport}_{returningFromAirport}_DATES:{startingFromDateTime}_{returningFromDateTime}_P:A{adults}_C{children}";
        }


        private static List<Passenger> Passengers(int adults, int kids, string ageKids)
        {
            var passengers = new List<Passenger>();

            if (adults > 0)
            {
                passengers.Add(new Passenger()
                {
                    Type = 0,
                    Quantity = adults,
                    Age = 0
                });
            }

            if (kids > 0)
            {
                foreach (string kid in ageKids.Split(","))
                {
                    int kidAge = Convert.ToInt32(kid);
                    passengers.Add(new Passenger()
                    {
                        Type = kidAge >= 2 ? 1 : 4,
                        Quantity = 1,
                        Age = kidAge
                    });
                }
            }

            return passengers;
        }


        private static List<Point> Points(int tripMode, string StartingFromAirport, string ReturningFromAirport, string StartingFromDateTime, string ReturningFromDateTime)
        {
            var points = new List<Point>();

            if (tripMode == 1)
            {
                points.Add(new Point()
                {
                    Origin = new PointItem()
                    {
                        Code = StartingFromAirport
                    },
                    Destination = new PointItem()
                    {
                        Code = ReturningFromAirport
                    },
                    DepartureDate = DateTime.Parse(StartingFromDateTime).ToUniversalTime(),
                });

                points.Add(new Point()
                {
                    Origin = new PointItem()
                    {
                        Code = ReturningFromAirport
                    },
                    Destination = new PointItem()
                    {
                        Code = StartingFromAirport
                    },
                    DepartureDate = DateTime.Parse(ReturningFromDateTime).ToUniversalTime(),
                });
            }
            else
            {
                points.Add(new Point()
                {
                    Origin = new PointItem()
                    {
                        Code = StartingFromAirport
                    },
                    Destination = new PointItem()
                    {
                        Code = ReturningFromAirport
                    },
                    DepartureDate = DateTime.Parse(StartingFromDateTime).ToUniversalTime(),
                });
            }

            return points;
        }
    }
}
