/*--------------------------------------- HELPERS ---------------------------------------*/

// FONT SIZES ----------------------------------------------
.font-12 { font-size: 0.75rem !important; line-height: 1.5em;}
.font-14 { font-size: 0.875rem !important; line-height: 1.5em;}
.font-16 { font-size: 1rem !important; line-height: 1.5em;}
.font-18 { font-size: 1.125rem !important; line-height: 1.5em;}
.font-20 { font-size: 1.25rem !important; line-height: 1.5em;}
.font-24 { font-size: 1.5rem !important; line-height: 1.5em;}
.font-26 { font-size: 1.625rem !important; line-height: 1.5em;}
.font-28 { font-size: 1.75rem !important; line-height: 1.5em;}
.font-32 { font-size: 2rem !important; line-height: 1.5em;}
.font-40 { font-size: 2.5rem !important; line-height: 1.5em;}

@media (min-width: 768px) {
    .font-md-12 { font-size: 0.75rem !important; }
    .font-md-14 { font-size: 0.875rem !important; }
    .font-md-16 { font-size: 1rem !important; }
    .font-md-18 { font-size: 1.125rem !important; }
    .font-md-20 { font-size: 1.25rem !important; }
    .font-md-24 { font-size: 1.5rem !important; }
    .font-md-26 { font-size: 1.625rem !important; }
    .font-md-28 { font-size: 1.75rem !important; }
    .font-md-32 { font-size: 2rem !important; }
    .font-md-40 { font-size: 2.5rem !important; }
}
@media (min-width: 992px) {
    .font-lg-12 { font-size: 0.75rem !important; }
    .font-lg-14 { font-size: 0.875rem !important; }
    .font-lg-16 { font-size: 1rem !important; }
    .font-lg-18 { font-size: 1.125rem !important; }
    .font-lg-20 { font-size: 1.25rem !important; }
    .font-lg-24 { font-size: 1.5rem !important; }
    .font-lg-26 { font-size: 1.625rem !important; }
    .font-lg-28 { font-size: 1.75rem !important; }
    .font-lg-32 { font-size: 2rem !important; }
    .font-lg-40 { font-size: 2.5rem !important; }
}
@media (min-width: 1280px) {
    .font-xl-12 { font-size: 0.75rem !important; }
    .font-xl-14 { font-size: 0.875rem !important; }
    .font-xl-16 { font-size: 1rem !important; }
    .font-xl-18 { font-size: 1.125rem !important; }
    .font-xl-20 { font-size: 1.25rem !important; }
    .font-xl-24 { font-size: 1.5rem !important; }
    .font-xl-26 { font-size: 1.625rem !important; }
    .font-xl-28 { font-size: 1.75rem !important; }
    .font-xl-32 { font-size: 2rem !important; }
    .font-xl-40 { font-size: 2.5rem !important; }
}

// FONT WEIGHTS ----------------------------------------------
.font-400 { font-weight: 400 !important; }
.font-500 { font-weight: 500 !important; }
.font-600 { font-weight: 600 !important; }
.font-700 { font-weight: 700 !important; }

// FONT COLORS ----------------------------------------------
.font-primary { color: var(--text-primary) !important; }
.font-primary-strong { color: var(--text-primary-strong) !important; }
.font-secondary { color: var(--text-secondary) !important; }
.font-secondary-strong { color: var(--text-secondary-strong) !important; }

.font-main { color: var(--text-main) !important; }
.font-strong { color: var(--text-strong) !important; }
.font-subtle { color: var(--text-subtle) !important; }
.font-disabled { color: var(--text-disabled) !important; }
.font-oncolor { color: var(--text-oncolor) !important; }

.font-green { color: var(--text-success) !important; }
.font-green-strong { color: var(--text-success-strong) !important; }
.font-red { color: var(--text-error) !important; }
.font-red-strong { color: var(--text-error-strong) !important; }
.font-yellow { color: var(--yellow-500) !important; }
.font-yellow-strong { color: var(--text-warning) !important; }
.font-blue { color: var(--text-info) !important; }
.font-blue-strong { color: var(--text-info-strong) !important; }

.font-nav { color: var(--color-nav) !important; text-decoration: none; cursor: pointer; transition: color 150ms ease-out; }
.font-nav:hover { color: var(--color-nav-hover) !important; text-decoration: none;}

// TEXT TRANSFORM ----------------------------------------------
.text-uppercase { text-transform: uppercase; }
.text-lowercase { text-transform: lowercase; }
.text-tachado { text-decoration: line-through; }

// MARGINS ----------------------------------------------
.m-base { margin: var(--space) !important; }
.m-8 { margin: calc(var(--space) * 2) !important; }
.m-12 { margin: calc(var(--space) * 3) !important; }
.m-16 { margin: calc(var(--space) * 4) !important; }
.m-20 { margin: calc(var(--space) * 5) !important; }
.m-24 { margin: calc(var(--space) * 6) !important; }
.m-28 { margin: calc(var(--space) * 7) !important; }
.m-32 { margin: calc(var(--space) * 8) !important; }
.m-36 { margin: calc(var(--space) * 9) !important; }
.m-40 { margin: calc(var(--space) * 10) !important; }

.my-base, .mt-base { margin-top: var(--space) !important; }
.my-8, .mt-8 { margin-top: calc(var(--space) * 2) !important; }
.my-12, .mt-12 { margin-top: calc(var(--space) * 3) !important; }
.my-16, .mt-16 { margin-top: calc(var(--space) * 4) !important; }
.my-20, .mt-20 { margin-top: calc(var(--space) * 5) !important; }
.my-24, .mt-24 { margin-top: calc(var(--space) * 6) !important; }
.my-28, .mt-28 { margin-top: calc(var(--space) * 7) !important; }
.my-32, .mt-32 { margin-top: calc(var(--space) * 8) !important; }
.my-36, .mt-36 { margin-top: calc(var(--space) * 9) !important; }
.my-40, .mt-40 { margin-top: calc(var(--space) * 10) !important; }

.my-base, .mb-base { margin-bottom: var(--space) !important; }
.my-8, .mb-8 { margin-bottom: calc(var(--space) * 2) !important; }
.my-12, .mb-12 { margin-bottom: calc(var(--space) * 3) !important; }
.my-16, .mb-16 { margin-bottom: calc(var(--space) * 4) !important; }
.my-20, .mb-20 { margin-bottom: calc(var(--space) * 5) !important; }
.my-24, .mb-24 { margin-bottom: calc(var(--space) * 6) !important; }
.my-28, .mb-28 { margin-bottom: calc(var(--space) * 7) !important; }
.my-32, .mb-32 { margin-bottom: calc(var(--space) * 8) !important; }
.my-36, .mb-36 { margin-bottom: calc(var(--space) * 9) !important; }
.my-40, .mb-40 { margin-bottom: calc(var(--space) * 10) !important; }

.mx-base, .ml-base { margin-left: var(--space) !important; }
.mx-8, .ml-8 { margin-left: calc(var(--space) * 2) !important; }
.mx-12, .ml-12 { margin-left: calc(var(--space) * 3) !important; }
.mx-16, .ml-16 { margin-left: calc(var(--space) * 4) !important; }
.mx-20, .ml-20 { margin-left: calc(var(--space) * 5) !important; }
.mx-24, .ml-24 { margin-left: calc(var(--space) * 6) !important; }
.mx-28, .ml-28 { margin-left: calc(var(--space) * 7) !important; }
.mx-32, .ml-32 { margin-left: calc(var(--space) * 8) !important; }
.mx-36, .ml-36 { margin-left: calc(var(--space) * 9) !important; }
.mx-40, .ml-40 { margin-left: calc(var(--space) * 10) !important; }

.mx-base, .mr-base { margin-right: var(--space) !important; }
.mx-8, .mr-8 { margin-right: calc(var(--space) * 2) !important; }
.mx-12, .mr-12 { margin-right: calc(var(--space) * 3) !important; }
.mx-16, .mr-16 { margin-right: calc(var(--space) * 4) !important; }
.mx-20, .mr-20 { margin-right: calc(var(--space) * 5) !important; }
.mx-24, .mr-24 { margin-right: calc(var(--space) * 6) !important; }
.mx-28, .mr-28 { margin-right: calc(var(--space) * 7) !important; }
.mx-32, .mr-32 { margin-right: calc(var(--space) * 8) !important; }
.mx-36, .mr-36 { margin-right: calc(var(--space) * 9) !important; }
.mx-40, .mr-40 { margin-right: calc(var(--space) * 10) !important; }

// PADDINGS ----------------------------------------------
.p-base { padding: var(--space) !important; }
.p-8 { padding: calc(var(--space) * 2) !important; }
.p-12 { padding: calc(var(--space) * 3) !important; }
.p-16 { padding: calc(var(--space) * 4) !important; }
.p-20 { padding: calc(var(--space) * 5) !important; }
.p-24 { padding: calc(var(--space) * 6) !important; }
.p-28 { padding: calc(var(--space) * 7) !important; }
.p-32 { padding: calc(var(--space) * 8) !important; }
.p-36 { padding: calc(var(--space) * 9) !important; }
.p-40 { padding: calc(var(--space) * 10) !important; }

.py-base, .pt-base { padding-top: var(--space) !important; }
.py-8, .pt-8 { padding-top: calc(var(--space) * 2) !important; }
.py-12, .pt-12 { padding-top: calc(var(--space) * 3) !important; }
.py-16, .pt-16 { padding-top: calc(var(--space) * 4) !important; }
.py-20, .pt-20 { padding-top: calc(var(--space) * 5) !important; }
.py-24, .pt-24 { padding-top: calc(var(--space) * 6) !important; }
.py-28, .pt-28 { padding-top: calc(var(--space) * 7) !important; }
.py-32, .pt-32 { padding-top: calc(var(--space) * 8) !important; }
.py-36, .pt-36 { padding-top: calc(var(--space) * 9) !important; }
.py-40, .pt-40 { padding-top: calc(var(--space) * 10) !important; }

.py-base, .pb-base { padding-bottom: var(--space) !important; }
.py-8, .pb-8 { padding-bottom: calc(var(--space) * 2) !important; }
.py-12, .pb-12 { padding-bottom: calc(var(--space) * 3) !important; }
.py-16, .pb-16 { padding-bottom: calc(var(--space) * 4) !important; }
.py-20, .pb-20 { padding-bottom: calc(var(--space) * 5) !important; }
.py-24, .pb-24 { padding-bottom: calc(var(--space) * 6) !important; }
.py-28, .pb-28 { padding-bottom: calc(var(--space) * 7) !important; }
.py-32, .pb-32 { padding-bottom: calc(var(--space) * 8) !important; }
.py-36, .pb-36 { padding-bottom: calc(var(--space) * 9) !important; }
.py-40, .pb-40 { padding-bottom: calc(var(--space) * 10) !important; }

.px-base, .pl-base { padding-left: var(--space) !important; }
.px-8, .pl-8 { padding-left: calc(var(--space) * 2) !important; }
.px-12, .pl-12 { padding-left: calc(var(--space) * 3) !important; }
.px-16, .pl-16 { padding-left: calc(var(--space) * 4) !important; }
.px-20, .pl-20 { padding-left: calc(var(--space) * 5) !important; }
.px-24, .pl-24 { padding-left: calc(var(--space) * 6) !important; }
.px-28, .pl-28 { padding-left: calc(var(--space) * 7) !important; }
.px-32, .pl-32 { padding-left: calc(var(--space) * 8) !important; }
.px-36, .pl-36 { padding-left: calc(var(--space) * 9) !important; }
.px-40, .pl-40 { padding-left: calc(var(--space) * 10) !important; }

.px-base, .pr-base { padding-right: var(--space) !important; }
.px-8, .pr-8 { padding-right: calc(var(--space) * 2) !important; }
.px-12, .pr-12 { padding-right: calc(var(--space) * 3) !important; }
.px-16, .pr-16 { padding-right: calc(var(--space) * 4) !important; }
.px-20, .pr-20 { padding-right: calc(var(--space) * 5) !important; }
.px-24, .pr-24 { padding-right: calc(var(--space) * 6) !important; }
.px-28, .pr-28 { padding-right: calc(var(--space) * 7) !important; }
.px-32, .pr-32 { padding-right: calc(var(--space) * 8) !important; }
.px-36, .pr-36 { padding-right: calc(var(--space) * 9) !important; }
.px-40, .pr-40 { padding-right: calc(var(--space) * 10) !important; }

// GAP ----------------------------------------------
.g-base { gap: var(--space) !important; }
.g-8 { gap: calc(var(--space) * 2) !important; }
.g-12 { gap: calc(var(--space) * 3) !important; }
.g-16 { gap: calc(var(--space) * 4) !important; }
.g-20 { gap: calc(var(--space) * 5) !important; }
.g-24 { gap: calc(var(--space) * 6) !important; }
.g-28 { gap: calc(var(--space) * 7) !important; }
.g-32 { gap: calc(var(--space) * 8) !important; }
.g-36 { gap: calc(var(--space) * 9) !important; }
.g-40 { gap: calc(var(--space) * 10) !important; }

@media (min-width: 768px) {
    .g-md-base { gap: var(--space) !important; }
    .g-md-8 { gap: calc(var(--space) * 2) !important; }
    .g-md-12 { gap: calc(var(--space) * 3) !important; }
    .g-md-16 { gap: calc(var(--space) * 4) !important; }
    .g-md-20 { gap: calc(var(--space) * 5) !important; }
    .g-md-24 { gap: calc(var(--space) * 6) !important; }
    .g-md-28 { gap: calc(var(--space) * 7) !important; }
    .g-md-32 { gap: calc(var(--space) * 8) !important; }
    .g-md-36 { gap: calc(var(--space) * 9) !important; }
    .g-md-40 { gap: calc(var(--space) * 10) !important; }
}
@media (min-width: 992px) {
    .g-lg-base { gap: var(--space) !important; }
    .g-lg-8 { gap: calc(var(--space) * 2) !important; }
    .g-lg-12 { gap: calc(var(--space) * 3) !important; }
    .g-lg-16 { gap: calc(var(--space) * 4) !important; }
    .g-lg-20 { gap: calc(var(--space) * 5) !important; }
    .g-lg-24 { gap: calc(var(--space) * 6) !important; }
    .g-lg-28 { gap: calc(var(--space) * 7) !important; }
    .g-lg-32 { gap: calc(var(--space) * 8) !important; }
    .g-lg-36 { gap: calc(var(--space) * 9) !important; }
    .g-lg-40 { gap: calc(var(--space) * 10) !important; }
}
@media (min-width: 1280px) {
    .g-xl-base { gap: var(--space) !important; }
    .g-xl-8 { gap: calc(var(--space) * 2) !important; }
    .g-xl-12 { gap: calc(var(--space) * 3) !important; }
    .g-xl-16 { gap: calc(var(--space) * 4) !important; }
    .g-xl-20 { gap: calc(var(--space) * 5) !important; }
    .g-xl-24 { gap: calc(var(--space) * 6) !important; }
    .g-xl-28 { gap: calc(var(--space) * 7) !important; }
    .g-xl-32 { gap: calc(var(--space) * 8) !important; }
    .g-xl-36 { gap: calc(var(--space) * 9) !important; }
    .g-xl-40 { gap: calc(var(--space) * 10) !important; }
}

// BORDERS ----------------------------------------------
.rounded-4 { border-radius: 4px !important; }
.rounded-8 { border-radius: 8px !important; }
.rounded-12 { border-radius: 12px !important; }
.rounded-16 { border-radius: 16px !important; }

// TRANSITIONS ----------------------------------------------
.transition-150 { transition: all 150ms ease-out !important; }
.transition-300 { transition: all 300ms ease-out !important; }