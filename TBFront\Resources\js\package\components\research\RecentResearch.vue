<template>
	<section class="container c-br cb-in my-3 my-lg-5 pe-0 section_recent_searches px-md-0" v-if="initDone">
		<div class="d-block ps-0">
			<h4 class="mb-0 font-poppins-semibold">{{__('recentResearch.title')}}</h4>
			<p class="mb-2">{{__('recentResearch.subtitle')}}</p>
		</div>
		<div class="c-scroll-01">
			<div class="row c-row">
				<div class="cr-int">
					<div class="cole ps-0 cursor-pointer"
						 v-for="(fligth, index) in history.to" :index="fligth.code">
						<div @click="getUrlFlight(index)"
							 class="row border rounded mx-0 c-itemLastSearch">
							<div class="col-4 px-0 ">
								<img onerror="this.src='/assets-tb/img/tiquetesbaratos/default-flights.png';" class="w-100 h-100" :src="`https://www.tiquetesbaratos.com/assets/recent-search-imgs/iatas/${fligth.code}.jpg`">
							</div>
							<div class="col-8 px-2 cc-info">
								<p class="mb-0 pt-2">
									<i class="icon icon-plane-right"></i>
									<span class="c-gray ms-2">{{ fligth.tripMode === 0 ? __('booker.oneWayFligth') : __('booker.roundTripFligth') }}</span>
								</p>
								<h5 class="mb-0 strong text-truncate">{{ fligth.displayText}}</h5>
								<p class="mb-0">{{ getTripDates(fligth) }}</p>
								<p class="mb-0 c-gray">{{ getNumberPaxes(fligth) }}</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>

</template>

<script>
	import { __ } from '../../../utils/helpers/translate';
	import { useUserSelectionStore } from "../../stores/user-selection";
	import { getUrlWithQueryString } from "../../../utils/helpers/queryString";
	import { StorageService } from '../../../utils/helpers/storage';
    import { Booker } from '../../../utils/analytics/bookers.js'
	const bookerSettings = window.__pt.settings.site.booker;
	/*import dayjs from 'dayjs';*/
	const siteSettings = window.__pt.settings.site;

	export default {
		name: 'RecentResearch',
		data() {
			return {
				history: null,
				initDone: false
			}
		},
		computed: {

		},
		mounted() {
			this.history = StorageService.get(siteSettings.booker.historyResearchStorageKey);

			if (this.history !== null) {

				this.history.to = this.history.to.filter((search) => !this.$filters.isAfterDate(search.startingFromDateTime) || this.$filters.isSameDate(search.startingFromDateTime));

				StorageService.set(bookerSettings.historyResearchStorageKey, this.history)

				this.initDone = this.history.to.length > 0;
			}
		},
		methods: {

			getUrlFlight(index) {
				const userSelectionStore = useUserSelectionStore();
				const url = getUrlWithQueryString(userSelectionStore.newQueryStringRecentSearches(this.history, index));
				Booker.setDataLayerCards(this.history['to'][index]);
                window.location.href = `${siteSettings.listUrl}${url}`;
			},

            getTripDates(fligth) {
                if (fligth.tripMode === 0) {
                    return this.$filters.date(fligth.startingFromDateTime, 'DD MMM YYYY');
                }
                return `${this.$filters.date(fligth.startingFromDateTime, 'DD MMM YYYY')} - ${this.$filters.date(fligth.returningFromDateTime, 'DD MMM YYYY')}`;
            },
            getNumberPaxes(fligth) {
                let adultText = "";
                let kidsText = "";
                
                if (fligth.adults === 1) {
                    adultText = `${__('booker.adult')}`
                } else {
                    adultText = `${__('booker.adults')}`
                }
                if (fligth.kids === 1) {
                    kidsText = `${__('booker.child')}`
                } else {
                    kidsText = `${__('booker.children')}`
                }

                if (fligth.kids > 0) {
                    return `${fligth.adults} ${adultText}, ${fligth.kids} ${kidsText}`
                }

                return `${fligth.adults} ${adultText}`
            }
        }

	}
</script>