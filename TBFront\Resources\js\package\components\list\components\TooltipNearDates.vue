<template>
	<div :id="ideElement" class="m-tooltip px-2 py-3 shadow" :class="tooltiPosition">
		<span class="icon icon-close pointer font-18" @click.stop.prevent="closeTooltip()"></span>
		<div class="row px-3 mb-3">
			<div class="col-12 text-left pl-1">
                <img width="40" height="40" :src="`https://img.cdnpth.com/media/images/airlines-logos/${airline}.svg`" :alt="airline"/>
				<div>
					<span class="fw-bold float-start">Ida:</span>
					<span class="float-end">{{ $filters.date(startDate, 'ddd MMM. DD') }}</span>
				</div>
			</div>
		</div>
		<div class="row px-3" v-if="type === 1">
			<div class="col-12 text-left pl-1">
				<div>
					<span class="fw-bold float-start">Regreso:</span>
					<span class="float-end">{{ $filters.date(endDate, 'ddd MMM. DD') }}</span>
				</div>
			</div>
		</div>
		<div class="row mt-3 px-4">
			<div class="col-12 px-0">
                <a class="btn btn-primary w-100" @click="sendUrl">{{__("promotions.search")}}</a>
			</div>
		</div>
	</div>
</template>

<script>

    import { storeToRefs } from 'pinia';
    import { usePromotionStore } from '../../../stores/promotion';
    import { getUrlWithQueryString } from "../../../../utils/helpers/queryString";
    const configSite = {};
    const siteSettings = window.__pt.settings.site;
    const cultureData = window.__pt.cultureData;
    export default {
        data() {
            return {
                config: configSite,
            }
        },
        props: {
            rate: { type: Object, default: {} },
            airline: { type: String, default: "" },
            position: { type: Number, default: 0 },
            index: { type: Number, default: 0 },
            type: { type: Number, default: 0 },
            startDate: { type: Date, default: new Date() },
            endDate: { type: Date, default: new Date() },
            tooltiPosition: { type: String, default: "" },
        },
        computed: {
            ideElement() {
				return `contentTooltip-${this.airline}-${this.position}-${this.index}`;
            }
        },
        setup() {
            const usePromotion = usePromotionStore();
            const { setIdTootltip, noShowTooltip, noShowTooltipRoundTrip } = usePromotion;
            const { getNewQueryString, getDateStartingSelected, getDateReturningSelected, getTripMode, getAirline } = storeToRefs(usePromotion);
            return { setIdTootltip, getNewQueryString, getDateStartingSelected, getDateReturningSelected, getTripMode, getAirline, noShowTooltip, noShowTooltipRoundTrip }
        },
        methods: {
            closeTooltip() {
                if (this.type === 0) {
                    this.noShowTooltip(this.position, this.index, false);
                } else {
                    this.noShowTooltipRoundTrip(this.position, this.index, false);
                }
                this.$emit('close', { type: this.type, position: this.position, index: this.index });
            },
            sendUrl() {
                const url = getUrlWithQueryString(this.getNewQueryString);
                window.location.href = `/${cultureData.cultureCode}${siteSettings.listUrl}${url}`;
            }
        }
    }
</script>

<style lang="scss" scoped>
    .m-tooltip {
        background-color: #fff;
        border-radius: 10px;
        left: -64px;
        font-size: 14px;
        position: absolute;
        top: 45px;
        width: 200px;
        z-index: 9;

        &.lastrow {
            background-color: #fff;
            border-radius: 10px;
            left: 5px;
            font-size: 14px;
            position: absolute;
            top: -180px;
            width: 200px;
            z-index: 9;

            &:before {
                content: "";
                width: 0;
                height: 0;
                border-width: 0 10px 10px;
                border-color: transparent transparent #fff;
                border-style: solid;
                position: absolute;
                bottom: -10px;
                left: 25%;
                transform: rotate(181deg);
            }
        }

        &.down {
            &:before {
                content: "";
                width: 0;
                height: 0;
                border-width: 0 10px 10px;
                border-color: transparent transparent #fff;
                border-style: solid;
                position: absolute;
                top: -9px;
            }
        }


        &.rigth {
            background-color: #fff;
            border-radius: 10px;
            left: 125px;
            font-size: 14px;
            position: absolute;
            top: -61px;
            width: 200px;
            z-index: 9;

            &:before {
                content: "";
                width: 0;
                height: 0;
                border-width: 0 10px 10px;
                border-color: transparent transparent #fff;
                border-style: solid;
                position: absolute;
                bottom: 80px;
                left: -15px;
                transform: rotate(269deg);
            }
        }

        &.left {
            background-color: #fff;
            border-radius: 10px;
            left: -190px;
            font-size: 14px;
            position: absolute;
            top: -61px;
            width: 200px;
            z-index: 9;

            &:before {
                content: "";
                width: 0;
                height: 0;
                border-width: 0 10px 10px;
                border-color: transparent transparent #fff;
                border-style: solid;
                position: absolute;
                top: 50%;
                right: -14px;
                transform: rotate(90deg);
            }
        }

        &.top {
            background-color: #fff;
            border-radius: 10px;
            left: -64px;
            font-size: 14px;
            position: absolute;
            top: -180px;
            width: 200px;
            z-index: 9;

            &:before {
                content: "";
                width: 0;
                height: 0;
                border-width: 0 10px 10px;
                border-color: transparent transparent #fff;
                border-style: solid;
                position: absolute;
                bottom: -10px;
                left: 50%;
                transform: rotate(181deg);
            }
        }

        .icon-close {
            position: absolute;
            right: 10px;
            top: 10px;
            color:var(--icon-main);
        }
    }
</style>