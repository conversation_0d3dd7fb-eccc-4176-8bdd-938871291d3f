﻿using System.Text.Json.Serialization;

namespace TBFront.Models.Meta.Schema
{
    public class AggregateRating
    {
        [JsonPropertyName("@type")]
        public string? Type { get; set; }

        [JsonPropertyName("ratingValue")]
        public string? RatingValue { get; set; }

        [JsonPropertyName("bestRating")]
        public string? BestRating { get; set; }

        [JsonPropertyName("ratingCount")]
        public string? RatingCount { get; set; }

        [JsonPropertyName("reviewCount")]
        public string? ReviewCount { get; set; }

        [JsonPropertyName("worstRating")]
        public string? WorstRating { get; set; }

    }
}
