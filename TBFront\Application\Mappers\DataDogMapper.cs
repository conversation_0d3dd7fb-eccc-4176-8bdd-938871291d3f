﻿using TBFront.Models.Datadog;

namespace TBFront.Application.Mappers
{
    public class DataDogMapper
    {

        internal static DataDogRequest Error(DataDogRequest request)
        {
            var type = "";
            var engines = "";
            var tag = "error";
            var category = "generic";


            return new DataDogRequest
            {
                Event = $"webfront.{category}_{type}_error_{request.Code}_engines_{engines}",
                Value = 1,
                Tags = new List<string> { tag }
            };
        }

    }
}
