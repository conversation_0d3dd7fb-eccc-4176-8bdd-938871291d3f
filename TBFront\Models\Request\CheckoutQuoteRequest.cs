﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using System.Globalization;


namespace TBFront.Models.Request
{
    public class CheckoutQuoteRequest
    {
        public string? Site { get; set; }
        public DateTime CheckIn { get; set; }
        public DateTime CheckOut { get; set; }
        public int ChkSource { get; set; }
        public string? TripMode { get; set; }
        public string? StartingFromAirport { get; set; }
        public DateTime StartingFromDateTime { get; set; }
        public string? ReturningFromAirport { get; set; }
        public DateTime ReturningFromDateTime { get; set; }
        public string? FareKeyQuote { get; set; }
        public string? ReferralUrl { get; set; }
        public int Adults { get; set; }
        public int Kids { get; set; }
        public string? AgeKids { get; set; }
        public int Infants { get; set; }
        public string? CheckoutData { get; set; }
        public bool IsDomesticRoute { get; set; } = false;
        public string? Flights { get; set; }
        public string? Fares { get; set; }
        public bool IsMobile { get; set; }

        public double _lastFlightRate;

        public string LastFlightRate
        {
            get
            {
                return _lastFlightRate.ToString(CultureInfo.InvariantCulture);
            }
            set
            {
                _lastFlightRate = double.Parse(value, CultureInfo.InvariantCulture);
            }
        }

        [ModelBinder(typeof(PaxesBinder))]
        public List<Pax>? Paxes { get; set; }
        public bool IsRoundTrip { get; set; } = false;

        public bool IsValid()
        {
            return FareKeyQuote != null && StartingFromDateTime != null && ReturningFromDateTime != null;
        }

        public class PaxesBinder : IModelBinder
        {
            private const string valueDefault = "";
            private const int defaultAdults = 1;


            public Task BindModelAsync(ModelBindingContext bindingContext)
            {
                var name = bindingContext.ModelName;
                var pax = new List<Pax>();

                try
                {
                    var _children = new List<Children>();
                    var adult = bindingContext.ValueProvider.GetValue("Adults").FirstValue;
                    string kids = bindingContext.ValueProvider.GetValue("AgeKids").FirstValue;

                    if (kids != null)
                    {
                        var subs = kids.Split(',');

                        for (int j = 0; j < subs.Length; j++)
                        {
                            if (!String.IsNullOrEmpty(subs[j]))
                            {
                                _children.Add(new Children() { Year = Convert.ToInt32(subs[j]) });
                            }
                        }
                    }

                    if (String.IsNullOrEmpty(adult))
                    {
                        adult = "0";
                    }

                    pax.Add(new Pax() { Adults = Convert.ToInt32(adult), Children = _children });

                }
                catch (Exception ex)
                {
                    var _children = new List<Children>();
                    pax = new List<Pax>();
                    pax.Add(new Pax() { Adults = defaultAdults, Children = _children });
                }


                bindingContext.ModelState.SetModelValue(name, pax, valueDefault);
                bindingContext.Result = ModelBindingResult.Success(pax);
                return Task.CompletedTask;
            }
        }
    }
}
