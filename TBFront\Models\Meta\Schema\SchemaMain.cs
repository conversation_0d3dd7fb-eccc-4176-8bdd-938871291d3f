﻿using System.Text.Json.Serialization;

namespace TBFront.Models.Meta.Schema
{
    public class SchemaMain
    {
        [JsonPropertyName("@context")]
        public string? Context { get; set; }

        [JsonPropertyName("@type")]
        public string? Type { get; set; }

        [Json<PERSON>ropertyName("name")]
        public string? Name { get; set; }

        [Json<PERSON>ropertyName("alternateName")]
        public string? AlternateName { get; set; }

        [JsonPropertyName("logo")]
        public string? Logo { get; set; }

        [JsonPropertyName("url")]
        public string? Url { get; set; }

        [JsonPropertyName("sameAs")]
        public List<string>? SameAs { get; set; }

        [JsonPropertyName("contactPoint")]
        public List<ContactPoint>? ContactPoint { get; set; }

        public SchemaMain()
        {
            this.SameAs = new List<string>();
            this.ContactPoint = new List<ContactPoint>();
        }

    }
}
