﻿using TBFront.Models.Common;
using TBFront.Models.Flight.CreateBooking;
using TBFront.Models.Flight.Quote;

namespace TBFront.Models.Flight.Revalidate.Request
{
    public class CreateBookingRequest
    {
        public string ExternalId { get; set; }
        public string KeyValidation { get; set; }

        public CustomerInformation CustomerInformation { get; set; }
        public ReservationSetting ReservationSettings { get; set; }
        public ServiceFlightItem ServiceItems { get; set; }

        public CreateBookingRequest()
        {
            CustomerInformation = new CustomerInformation();
            ReservationSettings = new ReservationSetting();
        }
    }

    public class ServiceFlightItem
    {
        public double TotalAmount { get; set; }
        public FlightItem Flight { get; set; }

        public ServiceFlightItem()
        {
            Flight = new FlightItem();
        }

    }

    public class CustomerInformation
    {
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
        public string MobilePhone { get; set; }
    }

    public class ReservationSetting
    {
        public string Currency { get; set; }
        public string Language { get; set; }
        public string IpAddress { get; set; }
        public int OrganizationId { get; set; }

    }

    public class FlightItem
    {
        public int TripMode { get; set; }
        public int TripCabin { get; set; }
        public string FareKey { get; set; }
        public bool GetAllFares { get; set; }
        public bool NonStopOnly { get; set; }
        public int Seniors { get; set; }
        public string PromotionalCode { get; set; }
        public bool IsPackageRate { get; set; }
        public int Type { get; set; }
        public double TotalAmount { get; set; }
        public int Adults { get; set; } //<-- validar tipo de dato
        public List<int> ChildAges { get; set; }
        public int Infants { get; set; } //<-- validar tipo de dato
        public int Infant { get; set; } //<-- validar tipo de dato
        public FlightTemp Flights { get; set; } //<-- validar tipo de dato
        public EmergencyContact EmergencyContact { get; set; }
        public List<PassengerItem> FlightPassengers { get; set; }

        public RevalidateCreateBookingRequest Revalidate { get; set; }



        //public object Flights { get; set; } <-- VALIDAR EL MODELO
        //public object ExtraCodes { get; set; } <-- VALIDAR EL MODELO

        public FlightItem()
        {
            ChildAges = new List<int>();
            EmergencyContact = new EmergencyContact();
            FlightPassengers = new List<PassengerItem>();
            Flights = new FlightTemp();
        }

    }
    public class RevalidateCreateBookingRequest
    {

        public bool IsPackage { get; set; }
        public string Currency { get; set; }
        public bool ShowDetailAmounts { get; set; }
        public bool ShowRevenueByLeg { get; set; }
        public string TaskID { get; set; }
        public string ValidatingCarrierCode { get; set; }
        public string FareKey { get; set; }
        public Dictionary<string, FlightResponse> Flights { get; set; }
        public Dictionary<string, Fare> Fares { get; set; }
        public Dictionary<string, Dictionary<string, TBFront.Models.Common.BookingInfo>> BookingInfos { get; set; }
        public Context Context { get; set; }
        public Recommendation Recommendation { get; set; }
        public List<PassengerBooking> Passengers { get; set; }

        public RevalidateCreateBookingRequest()
        {
            Passengers = new List<PassengerBooking>();
        }
    }

    public class FlightTemp
    {
        public string Temp { get; set; }
        public FlightTemp() { }
    }
}
