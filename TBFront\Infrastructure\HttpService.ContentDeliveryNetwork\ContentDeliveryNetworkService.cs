﻿using Microsoft.AspNetCore.WebUtilities;
using System.Net.Mime;
using System.Text.Json;
using TBFront.Infrastructure.HttpService.ContentDeliveryNetwork.Dtos;
using TBFront.Interfaces;
using TBFront.Models.ContentDeliveryNetwork.Exchange;
using TBFront.Models.ContentDeliveryNetwork.FaqContent;
using TBFront.Models.ContentDeliveryNetwork.LegalContent;
using TBFront.Models.ContentDeliveryNetwork.Seo;

namespace TBFront.Infrastructure.HttpService.ContentDeliveryNetwork
{
    public class ContentDeliveryNetworkService : IContentDeliveryNetworkService
    {
        private readonly HttpClient _httpClient;
        private readonly ContentDeliveryNetworkConfiguration _configuration;
        private readonly static JsonSerializerOptions _jsonSerializerOptions = new() { PropertyNameCaseInsensitive = true };
        private readonly ILogger<ContentDeliveryNetworkService> _logger;
        private readonly ICacheService _cache;
        public ContentDeliveryNetworkService(HttpClient httpClient, ContentDeliveryNetworkConfiguration options, ILogger<ContentDeliveryNetworkService> logger, ICacheService cache)
        {
            _configuration = options;
            _httpClient = httpClient;
            _httpClient.DefaultRequestHeaders.Add("Accept", MediaTypeNames.Application.Json);
            _httpClient.BaseAddress = new Uri(_configuration.Uri);
            _cache = cache;
            _logger = logger;
        }
        public async Task<ExchangeResponse> QueryAsync(ExchangeRequest request, CancellationToken ct)
        {
            var key = $"ExchangeRate";
            var response = await _cache.GetCache<ExchangeResponse>(key, ct);
            if (response == null || request.Cache)
            {
                var query = new Dictionary<string, string>()
                {
                    ["version"] = request.Version
                };
                var uriService = $"{_configuration.ExchangeRate}";
                uriService = QueryHelpers.AddQueryString(uriService, query);
                var httpResponseMessage = await _httpClient.GetAsync(uriService, ct);
                using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync(ct);
                response = await JsonSerializer.DeserializeAsync<ExchangeResponse>(contentStream, _jsonSerializerOptions, ct);
                if (response.Currencies is not null && response.Currencies.Count > 0)
                {
                    _cache.SetCache(key, response);
                }
            }
            return response;
        }

        public async Task<List<FaqContentResponse>> QueryAsync(FaqContentRequest request, CancellationToken ct)
        {
            var response = new List<FaqContentResponse>();

            try
            {
                var key = $"FaqContentResponse:{request.SiteName}_{request.Path}";

                response = await _cache.GetCache<List<FaqContentResponse>>(key, ct);

                if (response == null || request.Cache)
                {

                    var query = new Dictionary<string, string>()
                    {
                        ["version"] = request.Version
                    };

                    var uriService = $"{_configuration.LegalContent}/faq.json?version={request.Version}";

                    uriService = QueryHelpers.AddQueryString(uriService, query);

                    var httpResponseMessage = await _httpClient.GetAsync(uriService, ct);

                    using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync(ct);

                    response = await JsonSerializer.DeserializeAsync<List<FaqContentResponse>>(contentStream, _jsonSerializerOptions, ct);


                    if (response.Count != 0)
                    {
                        _cache.SetCache(key, response);
                    }
                }
            }
            catch (Exception e)
            {
                response = new List<FaqContentResponse>();
                _logger.LogError($"FaqContentResponse Message: {e.Message} - Request: {JsonSerializer.Serialize(request)}");
            }

            return response;
        }

        public async Task<List<LegalContentResponse>> QueryAsync(LegalContentRequest request, CancellationToken ct)
        {
            var response = new List<LegalContentResponse>();

            try
            {
                var key = $"LegalContentRequest:{request.SiteName}_{request.Path}";

                response = await _cache.GetCache<List<LegalContentResponse>>(key, ct);

                if (response == null || request.Cache)
                {

                    var query = new Dictionary<string, string>()
                    {
                        ["version"] = request.Version
                    };

                    var uriService = $"{_configuration.LegalContent}/{request.Path}.json?version={request.Version}";

                    uriService = QueryHelpers.AddQueryString(uriService, query);

                    var httpResponseMessage = await _httpClient.GetAsync(uriService, ct);

                    using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync(ct);

                    response = await JsonSerializer.DeserializeAsync<List<LegalContentResponse>>(contentStream, _jsonSerializerOptions, ct);


                    if (response.Count != 0)
                    {
                        _cache.SetCache(key, response);
                    }
                }
            }
            catch (Exception e)
            {
                response = new List<LegalContentResponse>();
                _logger.LogError($"LegalContentResponse Message: {e.Message} - Request: {JsonSerializer.Serialize(request)}");
            }

            return response;
        }
        public async Task<SeoResponse> QueryAsync(SeoRequest request, CancellationToken ct)
        {
            var response = new SeoResponse();
            try
            {
                var key = $"SeoResponse:{request.Path}:Site{request.SiteName}";
                response = await _cache.GetCache<SeoResponse>(key, ct);

                if (response == null || request.Cache)
                {
                    var uriService = $"{_configuration.SeoContent}/{request.Path}.json";
                    var httpResponseMessage = await _httpClient.GetAsync(uriService, ct);

                    if (!httpResponseMessage.IsSuccessStatusCode)
                    {
                        return new SeoResponse();
                    }
                    using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync(ct);
                    response = await JsonSerializer.DeserializeAsync<SeoResponse>(contentStream, _jsonSerializerOptions, ct);

                    if (response != null)
                    {
                        _cache.SetCache(key, response);
                    }
                }
            }
            catch (Exception e)
            {
                response = new SeoResponse();
            }
            return response ?? new SeoResponse();
        }
    }
}