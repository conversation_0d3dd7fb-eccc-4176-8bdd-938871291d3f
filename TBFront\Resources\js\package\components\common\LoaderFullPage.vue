﻿<template>
	<div class="loader-full-page vw-100 vh-100 d-flex flex-column justify-content-center align-items-center" :class="{'d-none': !show}">
		<div class="lds-dual-ring"></div>
		<p class="mb-0 mt-2">Se está procesando tu solicitud</p>
	</div>
</template>
<script setup>
	const { show } = defineProps({
		show: {
			type: Boolean,
			default: false
		}
	});
</script>
<style lang="scss" scoped>
	.loader-full-page {
		position: fixed;
		top: 0;
		left: 0;
		background: rgba(255, 255, 255, 0.85);
		z-index: 100;
	}

	.lds-dual-ring {
		opacity: 1 !important;
	}

	.lds-dual-ring:after {
		content: " ";
		display: block;
		width: 40px;
		height: 40px;
		border-radius: 50%;
		border: 6px solid #037bba;
		border-color: #037bba transparent #037bba transparent;
		animation: lds-dual-ring 1.2s linear infinite;
	}

	@keyframes lds-dual-ring {
		0% {
			transform: rotate(0deg);
		}

		100% {
			transform: rotate(360deg);
		}
	}
</style>