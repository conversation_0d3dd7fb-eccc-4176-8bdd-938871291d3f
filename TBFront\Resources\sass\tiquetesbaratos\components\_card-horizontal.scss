.l-page-main a{
  color: $gray-900;
}

//CLASE QUE DEBE DE SER COLOCADA EN LAS IMAGENES DE LAS CARDS HORIZONTALES//
.img_card {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 5px 0 0 5px;
}

#image-map-card {
    width: 250px;
    height: 100%;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
}

.horizontal-card{
  width: 100%;
  max-width: 730px;
  background: $white;
  position: absolute;
  font-size: 14px;
  display: inline-flex;
  bottom: 70px;
  box-shadow: 1px -1px 24px 1px rgba(0, 0, 0, 0.37);
  -webkit-box-shadow: 1px -1px 24px 1px rgba(0, 0, 0, 0.37);
  -moz-box-shadow: 1px -1px 24px 1px rgba(0, 0, 0, 0.37);
  border-radius: 5px;
  left: 50%;
  transform: translateX(-50%);


  .mdc-card__media{

    img{
      border-radius: 5px 0 0 5px;
    }
  }
  .card-body{
    width:60%;
    float:left;
    border:none;
    height: auto;
    background-color: $white;

    .card-title {
      font-size: 1rem;
      font-weight: 500;
      display: inline-block;
      margin-inline-end: 1.5rem;
    }
    .card-rating .stars {
      width: auto;
      display: inline-block;
    }
    .card-rating .rating-description {
      width: auto;
      display: inline-block;
      margin: 0;
      vertical-align: super;
    }
  }
  .card-price{
    width: 40%;
    background-color: $gray-200;
    height: 100%;
    min-height: 117px;
    border-radius: 0 5px 5px 0;

    pth-rate-container{
      height: auto;
      min-height: 60px;
      border-radius: 0 5px 5px 0;
      padding: 20px;
      display: inherit;
      text-align: right;
      margin-top: 1.5rem;
      @media ($phone) {
        padding: 0;
        margin: 0;
      }
      div{
        line-height: 100%;
        padding: 3px;
      }

    }
    pth-rate{
      height: 100%;
    }
    .pth-typography--paragraph1{
      font-size: 1.2rem !important;
      line-height: 100%;
    }
    @media ($phone) {
      width: 100%;
      background-color: $white;
      height: auto;
      padding: 10px;
      margin: 0 !important;
    }
  }
  button.close{
    opacity: 1;
    padding: 0.25rem 0.6rem;
    color: $gray-600;
    display: inline-block;
    position: absolute;
    line-height: 1.5;
    font-size: 30px;
    border-radius: 0.25rem;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
    user-select: none;
    border: none;
    transition: 0.2s ease-in-out;
    right: 0;
    top:0;
    z-index: 1;
  }
  .disccount-icon {
    background-color: #489A00;
    color: $white;
    padding: 0.25rem 0.25rem 0.25rem 0;
    position: relative;
    margin-left: 0.75rem;
    height: 20px;
    float: right;
  
    &:before {
        position: absolute;
        content: "";
        display: inline-block;
        right: 100%;
        top: 0;
        bottom: 0;
        width: 0;
        height: 0;
        border-top: 10px solid transparent;
        border-right: 10px solid #489A00;
        border-bottom: 10px solid transparent;
    }
  }


}
// .horizontal-card .card-price .pth-typography--paragraph1{
//   color: $pink;
// }

.alert-center{
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translateX(-50%);
}

@media ($phone) {

  .horizontal-card{
    bottom: 0px;
    overflow: auto;
    width: 92%;
    max-width: 500px;
    height: 190px;
    overflow: hidden;
    z-index: 9;
  }
  .img_card{
    height: 190px;
    width: 90px;
  }
  .horizontal-card .card-body{
    width: 100%;
  }
  .horizontal-card .card-price{
    box-shadow: none;
  }
  .map-search .card-body{
    padding: 10px !important;
    padding-bottom: 0 !important;
  }
  .card-title{
    text-overflow: ellipsis;
    display: -webkit-box !important;
    -webkit-line-clamp: 2;
    overflow: hidden;
    -webkit-box-orient: vertical;
  }
  .horizontal-card .card-price pth-rate-container {
    display: block;
  }
}

.carousel-card-principal{
  height: 100%;
  .slick-track {
    height: 100%;
  }
}
.badge-disccount {
    border-bottom-left-radius: 0px !important;
    border-top-left-radius: 0px !important;
    color: #489A00;
    i{
      font-size: 14px;
    }
}

.badge-sale {
    border-bottom-left-radius: 0px !important;
    border-top-left-radius: 0px !important;
    color: #CB3234;
    i{
      font-size: 14px;
    }
}

.loading-card-gallery {
    position: absolute;
    z-index: 10;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgb(225 225 225 / 60%);

    .loader__circle {
        width: 35px;
        height: 35px;
    }
}