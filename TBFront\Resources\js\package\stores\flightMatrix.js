import { defineStore } from "pinia";
import { pillFiltersMapper } from "../mappers/pillFiltersMapper";

export const useFlightMatrixStore = defineStore({
	id: "flightMatrix",
	state: () => ({
		loading: false,
		flightMatrix: {
			avilableStops: {},
			airlines: []
		},
		escondido: 1,
		currentSelected: "",
		pillFilters: {},
		pagination: {}
	}),
	getters: {
		getLoading: (state) => {
			return state.loading;
		},
		getCantidadMatriz: (state) => {
			return state.flightMatrix?.airlines?.length ?? 0;
		},
		getAvilableStops: (state) => {
			return state.flightMatrix.avilableStops;
		},
		getAirlines: (state) => {
			if (state.flightMatrix && state.flightMatrix.airlines) {
				for (let index = 0; index < state.flightMatrix.airlines.length; index++) {
					state.flightMatrix.airlines[index].col = index + 1;
				}
			}

			return state.flightMatrix.airlines;
		},
		getEscondido: (state) => {
			return state.escondido;
		},
		getCounter: (state) => {
			return state.counter;
		},
		getPillFilters: (state) => {
			return state.pillFilters;
		},
		getPagination: (state) => {
			return state.pagination;
		},
	},
	actions: {
		setLoading(isActive) {
			this.loading = isActive;
		},
		setFlightMatrix(matrix) {
			this.flightMatrix = matrix;
			this.pillFilters = pillFiltersMapper.map(matrix);
		},
		setCurrentSelected(value) {
			this.currentSelected = value;
		},
		increment() {
			this.escondido++;
		},
		decrement() {
			this.escondido--;
		},
		changeLoading() {
			this.loading = !this.loading;
		},
		getAirlinesImage: (code) => {
			return `https://img.cdnpth.com/media/images/airlines-logos/${code}.svg`;
		},
		setPagination(code, value) {
			this.pagination[code] = value;
		},
		cleanPagination() {
			this.pagination = {};
		}
	},
});
