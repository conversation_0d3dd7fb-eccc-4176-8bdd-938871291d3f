﻿@using TBFront.Helpers
@using Microsoft.Extensions.Options
@using TBFront.Options;
@using TBFront.Types;
@using TBFront.Models.Meta.Metatags;
@using TBFront.Models.Request;

@inject IOptions<SettingsOptions> settingOptions
@inject IOptions<CurrencyOptions> currencyOptions

@inject ViewHelper viewHelper
@inject StaticHelper staticHelper

@inject ViewHelper _
@inject StaticHelper staticHelper
@{
    var resolution = _.GetImageResolution();
    var isMobile = resolution.Device == DeviceType.Mobile;
    var isRobot = _.IsRobot();

    var request = ViewData["request"] as FlightRequest;
    var metaTag = ViewData["MetaTag"] as MetaTag;
    var culture = ViewData["CultureData"] as Culture;

    ViewData["Page"] = "check-reservation";

    ViewData["IsRobot"] = isRobot;
    ViewData["isMobile"] = isMobile;
    ViewData["Resolution"] = resolution;

}

@await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.Code}/_Header.cshtml", new ViewDataDictionary(ViewData) { { "navs", true }, { "login", isRobot } })


<div class="container c-consultar py-3 pt-md-5">
    <div class="row">
        <div class="col-12 col-md-6 d-none d-md-block">
            <img class="w-100 h-100 rounded" src="/assets-tb/img/tiquetesbaratos/img-consultar.jpg" />
        </div>
        <check-reservation></check-reservation>
    </div>
</div>

<div>
    @await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.Code}/_Footer.cshtml", new ViewDataDictionary(ViewData) { { "newsletter", false } })
</div>

@section Css {
    <link type="text/css" rel="stylesheet"
          href="@staticHelper.GetVersion($"/{settingOptions.Value.SiteName}/css/check-reservation.css", settingOptions.Value.Assets)">
}


@section Meta {
    @await Html.PartialAsync("_MetaMain", new ViewDataDictionary(ViewData) { { "Meta", ViewData["MetaTag"] }})
}

@section ScriptsPriority {
}


@section Scripts {
    <script>
        window.__pt.data = @Json.Serialize(request);
         window.__pt.cultureData = @Json.Serialize((ViewData["CultureData"]));
        window.__pt.exchange = @Json.Serialize((ViewData["Exchange"]));
        window.__pt.alternates = @Json.Serialize((ViewData["Alternates"]));
        window.__pt.currency = @Json.Serialize((ViewData["CurrencyData"]));
        window.__pt.currencies = @Json.Serialize((currencyOptions.Value.Currencies));
    </script>

    <script src="@staticHelper.GetVersion("/js/vue/app.js", settingOptions.Value.Assets)"></script>
}