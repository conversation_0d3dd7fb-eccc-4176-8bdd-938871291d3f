﻿using System.Text.Json.Serialization;

namespace TBFront.Models.Meta.Schema
{
    public class Place
    {
        [JsonPropertyName("@context")]
        public string? Context { get; set; }

        [JsonPropertyName("@type")]
        public string? Type { get; set; }

        [JsonPropertyName("name")]
        public string? Name { get; set; }

        [JsonPropertyName("geo")]
        public Geo? Geo { get; set; }

        public Place()
        {
            Geo = new Geo();
        }

    }
}
