﻿using Microsoft.Extensions.Options;
using PT.Platform.B2C.User.Entities;
using PT.Platform.B2C.User.Interfaces;
using TBFront.Application.Implementations;
using TBFront.Interfaces;
using TBFront.Models.Request;
using TBFront.Models.User;
using TBFront.Options;

namespace FlightFront.Application.Implementations
{
    public class UserHandler : IUserHandler
    {
        private readonly IUserService _userService;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly SettingsOptions _options;

        public UserHandler(IUserService userService, IHttpContextAccessor httpContextAccessor, IOptions<SettingsOptions> options)
        {
            _userService = userService;
            _httpContextAccessor = httpContextAccessor;
            _options = options.Value;
        }

        public async Task<Reservation> QueryAsync(GetBookingRequest request, CancellationToken ct)
        {
            var (token, rememberToken) = GetTokensFromCookies();

            var reservation = new Reservation
            {
                Email = request.Em,
                ReservationId = request.Id,
            };

            await _userService.AddReservationToUserProfileAsync(reservation, token, rememberToken);

            return reservation;
        }

        public async Task<User> QueryAsync(UserRequest request, CancellationToken ct)
        {
            var (token, rememberToken) = GetTokensFromCookies();

            var user = await _userService.GetUserAsync(token, rememberToken);

            if (user is not null)
            {
                user.UserProfile.Reservations = [];
            }

            return user;
        }


        private (string sessionToken, string rememberToken) GetTokensFromCookies()
        {
            var context = _httpContextAccessor.HttpContext;
            var cookies = context?.Request.Cookies;

            string sessionToken = cookies?["session_token"] ?? string.Empty;
            string rememberToken = cookies?["remember_token"] ?? string.Empty;

            return (sessionToken, rememberToken);
        }
    }
}
