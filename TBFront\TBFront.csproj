﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Models\BookF\**" />
    <Content Remove="Models\BookF\**" />
    <EmbeddedResource Remove="Models\BookF\**" />
    <None Remove="Models\BookF\**" />
  </ItemGroup>

  <ItemGroup>
    <Content Remove="Views\Emails\Examples\Example.cshtml" />
  </ItemGroup>

  <ItemGroup>
    <None Include="Views\Emails\Examples\Example.cshtml" />
    <None Include="Views\Emails\Users\WelcomeUser.cshtml" />
  </ItemGroup>


  <ItemGroup>
    <PackageReference Include="AWSSDK.DynamoDBv2" Version="3.7.403.7" />
    <PackageReference Include="AWSSDK.KeyManagementService" Version="3.7.400.1" />
    <PackageReference Include="AWSSDK.S3" Version="3.7.400.1" />
    <PackageReference Include="DogStatsD-CSharp-Client" Version="8.0.0" />
    <PackageReference Include="FirebaseAdmin" Version="3.0.0" />
    <PackageReference Include="growthbook-c-sharp" Version="1.0.5" />
    <PackageReference Include="Hotel.Content.Standard" Version="1.0.20" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.8" />
    <PackageReference Include="Microsoft.AspNetCore.DataProtection.StackExchangeRedis" Version="8.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation" Version="8.0.7" />
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="8.0.7" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Places.Standard" Version="1.0.3" />
    <PackageReference Include="protobuf-net" Version="3.2.30" />
    <PackageReference Include="protobuf-net.Core" Version="3.2.30" />
    <PackageReference Include="PT.Platform.B2C.User" Version="1.0.14" />
    <PackageReference Include="SendGrid" Version="9.29.3" />
    <PackageReference Include="StackExchange.Redis" Version="2.8.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.0.1" />
    <PackageReference Include="WebMarkupMin.AspNetCore6" Version="2.16.1" />
  </ItemGroup>


  <ItemGroup>
    <Folder Include="Infrastructure\HttpService.Login\" />
    <Folder Include="Resources\json\" />
    <Folder Include="Views\Emails\Booking\" />
    <Folder Include="wwwroot\img\header\" />
  </ItemGroup>

</Project>
