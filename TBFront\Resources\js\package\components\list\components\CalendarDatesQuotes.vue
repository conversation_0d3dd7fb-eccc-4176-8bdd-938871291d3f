<template>
    <div class="ml-body p-3 py-md-0 overflow-auto bg-white position-relative z1-999">
        <div id="containerDates" class="row row-dates-mobile">
            <div class="col-12 border p-3 c-matrix position-relative" :class="{'rountrip': typeFlight === 1}">
                <template v-if="(flights != null && !getIsLoading)">
                    <div class="c-scroll-horizontal">
                        <div class="c-scroll">
                            <span class="font-16 px-0">
                                <span class="d-inline-grid">{{typeFlight === 0 ? __("promotions.more") : ''}}</span>
                                <span class="fw-bold ms-2 d-inline-grid">{{typeFlight === 0 ? __("promotions.dates") : __("promotions.go")}}</span>
                                <span :class="{'btn-disabled': !showOrHideArrow}" class="icon icon-chevron-left font-34 position-t-9 cursor-pointer" @click="changeStartDate(1)"></span>
                                <span :class="{'btn-disabled': validateRow('ida')}"
                                      class="icon icon-chevron-right font-34 position-t-9 cursor-pointer d-inline-grid"
                                      @click="changeStartDate(0)"></span>
                            </span>
                        </div>
                    </div>
                    <div class="c-scroll-vertical" :class="{'scroll-vertical-empty': isEmpty}" v-if="typeFlight === 1">
                        <div class="c-scroll">
                            <span class="position-relative font-16 px-4 float-right">
                                <span class="fw-bold"> {{__("promotions.return")}}</span>
                                <span class="icon icon-chevron-left font-34 position-t-9 cursor-pointer" @click="changeReturnDate(0)"></span>
                                <span :class="{'btn-disabled': showOrHideArrow && validateRow('regreso')}"
                                      class="icon icon-chevron-right font-34 position-t-9 cursor-pointer" @click="changeReturnDate(1)"></span>
                            </span>
                        </div>
                    </div>
                    <div class="c-int-matrix overflow-auto" v-if="!isEmpty">
                        <table class="table table-dates cursor-pointer">
                            <thead>
                                <tr>
                                    <template v-for="(dates, index) in flights.dates_quote">
                                        <th scope="col" class="bg-level1 fw-normal" :class="['th-x-'+index]" v-html="formatDateCalendar(dates.date)"> </th>
                                    </template>
                                    <th v-if="flights.typeFlight === 1" scope="col" class="bg-white border-none y-md-left"></th>
                                </tr>
                            </thead>
                            <tbody>
                                <template v-for="(departure, position) in flights.dates_departure">
                                    <tr>
                                        <template v-for="(rate,index) in departure.rates">
                                            <td @click.stop.prevent="showTooltip(rate,position,index, departure, flights.dates_quote.length, flights.dates_departure.length)"
                                                :class="[isLowestPrice(rate.price,rate.date, departure.date), 'td-'+index + '-' + position]"
                                                @mouseenter="onHoverCell(index, position, flights.dates_quote.length, flights.dates_departure.length)"
                                                @mouseleave="onLeaveCell(index, position, flights.dates_quote.length, flights.dates_departure.length)">
                                                <span class="pointer" v-if="$filters.isAfterDateFromDate(rate.date, departure.date)"><CurrencyDisplay :amount="rate.price" :showCurrencyCode="false" /></span>

                                                <!--<span class="pointer" v-else></span>-->
                                                <TooltipNearDates v-if="rate.show && $filters.isAfterDateFromDate(rate.date, departure.date) && rate.price != null"
                                                                  :airline="rate.airline"
                                                                  :position="position"
                                                                  :index="index"
                                                                  :type="flights.typeFlight"
                                                                  :startDate="rate.date"
                                                                  :endDate="departure.date"
                                                                  @close="handleTooltipClose"
                                                                  :tooltiPosition="setPositionTooltip(index, position)"></TooltipNearDates>
                                            </td>
                                        </template>
                                        <template v-for="i in bListTh(departure.rates.length, flights.dates_quote.length)">
                                            <td :class="['td-'+((departure.rates.length -1)+i) + '-' + position]" scope="col" class="bg-level2"></td>
                                        </template>
                                        <th :class="['th-y-'+position]" v-if="getTripMode === 1" scope="row" class="bg-level1 fw-normal l-date-matrix y-md-left" v-html="formatDateCalendar(departure.date)"></th>
                                    </tr>
                                </template>
                            </tbody>
                        </table>

                        <div class="d-none d-md-block"></div>
                    </div>
                    <p class="text-center" :class="{'empty-roundtrip': typeFlight === 1}" v-if="isEmpty">{{ __("promotions.noRatesQuotations") }}</p>
                </template>
                <Loading class="m-auto" v-if="getIsLoading" />
                <p class="text-center" :class="{'empty-roundtrip': typeFlight === 1}" v-if="(!getIsLoading && flights == null)">{{ __("promotions.noRatesQuotations") }}</p>
            </div>
        </div>
    </div>
</template>

<script>
    import { storeToRefs } from 'pinia';
    import { __ } from '../../../../utils/helpers/translate';
    import { usePromotionStore } from '../../../stores/promotion';
    import { getPromotionsCalendar, getParamsPromotions } from '../../../services/ApiPromotionsFrontServices';
    import CurrencyDisplay from '../../common/CurrencyDisplay.vue';

    const SITE_CODE = "TB";
    const ADULTS_PAX = 1;
    const INIT_QUOTATION = 3;
    const NO_REMAP_DATA = 2;
    const ONE_WAY_TYPE = 0;
    const ROUNTRIP_TYPE = 1;
    const DELAY_DATES = 1;
    const ADVANCE_DATES = 0;
    const culture = window.__pt.cultureData;

    export default {
        data() {
            return {
                componentKey: 0,
                startDateLocal: null,
                isEmpty: false
            }
        },
        components: {
            CurrencyDisplay
        },
        props: {
            typeFlight: { type: Number, default: 0 },
        },
        watch: {
        },
        computed: {
            flights() {
                if (this.typeFlight === ONE_WAY_TYPE) {
                    return this.mapCalendar(this.getCalendar);
                } else {
                    return this.mapCalendar(this.getCalendarRoundTrip);
                }
                return null;
            },
            showOrHideArrow() {
                if (this.$filters.isSameDate(this.getStartDate)) {
                    return false;
                }
                return true;
            }
        },
        setup() {
            const usePromotion = usePromotionStore();
            const { setStartingDate, setReturningDate, setDateStaringSelected, setDateReturningSelected, setAirline, noShowTooltip, noShowTooltipRoundTrip, hideTooltipOneWay, setPromotionsResponse, setPromotionResponseRoundtrip, hideTooltipRoundtrip, setIsLoading, setStartingDepartureDate } = usePromotion;
            const { getCalendar, getIdTooltip, getCalendarRoundTrip, getTripMode, getStartDate, getReturningDate, getFromLanding, getOrigin, getDestination, getSmallerTaxeOneWay, getSmallerTaxeRoundTrip, getIsLoading, getStartDepartureDate } = storeToRefs(usePromotion);
            return {
                getCalendar, getIdTooltip, getCalendarRoundTrip, getTripMode, getStartDate, getReturningDate,
                setStartingDate, setReturningDate, setDateStaringSelected, setDateReturningSelected, setAirline,
                noShowTooltip, noShowTooltipRoundTrip, hideTooltipOneWay, setPromotionsResponse, setPromotionResponseRoundtrip,
                getFromLanding, getOrigin, getDestination, hideTooltipRoundtrip, getSmallerTaxeOneWay,
                getSmallerTaxeRoundTrip, getIsLoading, setIsLoading, setStartingDepartureDate, getStartDepartureDate
            }
        },
        async mounted() {
            if (this.getFromLanding === "home") {
                await this.quoteFlight(INIT_QUOTATION, "start");
            }
            this.startDateLocal = this.getStartDate;
        },
        methods: {
            handleTooltipClose(payload) {
                this.onLeaveCell(payload.index, payload.position, this.flights.dates_quote.length, this.flights.dates_departure.length)
            },
            bListTh(items_x, length = 7) {
                return Array.from({ length: items_x < length ? length - items_x : 0 }, (_, index) => index + 1);
            },

            showTooltip(rate, position, index, departure, length_x = 0, lenght_y = 0) {
                if (this.getTripMode === 0) {
                    this.hideTooltipOneWay();
                    this.noShowTooltip(position, index, true);
                } else {
                    this.hideTooltipRoundtrip();
                    this.noShowTooltipRoundTrip(position, index, true);
                }
                this.setDateStaringSelected(this.$filters.date(rate.date, "YYYY-MM-DD"));
                this.setDateReturningSelected(this.$filters.date(departure.date, "YYYY-MM-DD"));
                this.setAirline(rate.airline);
                //this.onLeaveCell(index, position, length_x, lenght_y);
            },
            async changeStartDate(arrow) {
                this.setIsLoading(true, this.getTripMode);
                let newDate;
                if (this.getTripMode === 0) {
                    if (arrow === DELAY_DATES) {
                        newDate = this.$filters.subtractWeek(this.getStartDepartureDate);
                    } else {
                        newDate = this.$filters.increaseWeek(this.getStartDepartureDate);
                    }
                    const formatDate = this.$filters.date(newDate, "YYYY-MM-DD");
                    this.setStartingDepartureDate(this.$filters.createLocalDateFromISOString(formatDate));
                    this.setReturningDate(this.$filters.createLocalDateFromISOString(formatDate));
                } else {
                    if (arrow === DELAY_DATES) {
                        newDate = this.$filters.subtractWeek(this.getStartDate);
                    } else {
                        newDate = this.$filters.increaseWeek(this.getStartDate);
                    }
                    const formatDate = this.$filters.date(newDate, "YYYY-MM-DD");
                    this.setStartingDate(this.$filters.createLocalDateFromISOString(formatDate));
                }
                await this.quoteFlight(arrow, 'start');
            },
            async changeReturnDate(arrow) {
                this.setIsLoading(true, this.getTripMode);
                let newDate;
                if (arrow === 1) {
                    newDate = this.$filters.subtractWeek(this.getReturningDate);
                } else {
                    newDate = this.$filters.increaseWeek(this.getReturningDate);
                }
                const formatDate = this.$filters.date(newDate, "YYYY-MM-DD");
                this.setReturningDate(this.$filters.createLocalDateFromISOString(formatDate));
                await this.quoteFlight(arrow, 'return');
            },
            setPositionTooltip(index, position) {
                let positionClass = "down";
                const departuresLength = this.flights.dates_departure.length;
                const ratesLength = this.flights.dates_quote.length;
                if (index === 0 && this.getTripMode === 0) {
                    positionClass = "rigth";
                } else if (index === (ratesLength - 1)) {
                    positionClass = "left";
                } else if (position == 0 && this.getTripMode === 0) {
                    positionClass = "rigth";
                }

                if (this.getTripMode === 1) {
                    if (index == 0 && departuresLength == (position + 1)) {
                        positionClass = "lastrow";
                    }
                    else if (index == 0) {
                        positionClass = "rigth";
                    }

                    else if (position > (departuresLength - 4)) {
                        positionClass = "top";
                    }
                }
                return positionClass;
            },
            async quoteFlight(arrow, type) {
                const staticTripMode = this.getTripMode;
                const params = {
                    site: SITE_CODE,
                    origin: this.getOrigin,
                    destination: this.getDestination,
                    startDate: this.getTripMode === 0 ? this.$filters.date(this.getStartDepartureDate, 'YYYY-MM-DD') : this.$filters.date(this.getStartDate, 'YYYY-MM-DD'),
                    returnDate: this.$filters.date(this.getReturningDate, 'YYYY-MM-DD'),
                    adults: ADULTS_PAX,
                    tripMode: this.getTripMode,
                }
                const rq = getParamsPromotions(params);
                let responseDetail = await getPromotionsCalendar(rq);
                if (responseDetail.Response.Recommendations != null) {
                    if (staticTripMode === 0) {
                        this.setPromotionsResponse(responseDetail.Response.Recommendations, staticTripMode, params);
                    } else {
                        this.setPromotionResponseRoundtrip(responseDetail.Response.Recommendations, staticTripMode, params);
                    }
                } else {
                    if (arrow !== INIT_QUOTATION) {
                        const emptyCalendarDates = this.createEmptyCalendar(arrow, type);
                        this.isEmpty = true;
                        if (staticTripMode === 0) {
                            this.setPromotionsResponse(emptyCalendarDates, NO_REMAP_DATA);
                        } else {
                            this.setPromotionResponseRoundtrip(emptyCalendarDates, NO_REMAP_DATA);
                        }
                    }
                }
                const containerDates = document.getElementById('containerDatesMobile');
                if (containerDates !== null) {
                    containerDates.scrollIntoView();
                }
                this.setIsLoading(false, staticTripMode);
            },
            isLowestPrice(price, start, end) {
                let priceLowest = 0;
                let isDateAfterOrSameFromDate = this.$filters.isAfterDateFromDate(start, end);
                if (this.getTripMode === 0 && isDateAfterOrSameFromDate) {
                    priceLowest = this.getSmallerTaxeOneWay;
                } else if (this.getTripMode === 1 && isDateAfterOrSameFromDate) {
                    priceLowest = this.getSmallerTaxeRoundTrip;
                }
                return price === priceLowest ? "position-relative bg-yellow-active" : "position-relative";
            },
            createEmptyCalendar(arrow, type) {
                const result = {
                    dates_quote: [],
                    dates_departure: []
                };

                let calendar = null;
                let lengthCalendar = 0;
                let dateSelect = null;

                if (this.getTripMode === ONE_WAY_TYPE && this.getCalendar != null) {
                    calendar = this.getCalendar;
                } else if (this.getTripMode === ROUNTRIP_TYPE && this.getCalendarRoundTrip != null) {
                    calendar = this.getCalendarRoundTrip;
                }

                if (arrow == DELAY_DATES && type == "start") {

                    const finalDate = calendar != null ? calendar.dates_quote[0].date : this.getStartDate;
                    dateSelect = this.$filters.date(finalDate, 'YYYY-MM-DD');

                    for (let i = 1; i <= 7; i++) {
                        let newDate = this.$filters.addOrSubstractDays(dateSelect, -parseInt(i));
                        let formart = new Date(newDate);
                        result.dates_quote.push({
                            date: formart.toISOString(),
                            day: formart.getDate(),
                            month: formart.getMonth() + 1
                        });
                    }
                    result.dates_departure = [...calendar.dates_departure];
                }
                else if (arrow === ADVANCE_DATES && type == "start") {
                    if (calendar != null) {
                        lengthCalendar = calendar.dates_quote.length - 1;
                        dateSelect = this.$filters.date(calendar.dates_quote[lengthCalendar].date, 'YYYY-MM-DD');
                    } else {
                        dateSelect = this.$filters.date(this.getStartDate, 'YYYY-MM-DD');
                    }

                    for (let i = 1; i <= 7; i++) {
                        let newDate = this.$filters.addOrSubstractDays(dateSelect, i);
                        let formart = new Date(newDate);
                        result.dates_quote.push({
                            date: formart.toISOString(),
                            day: formart.getDate(),
                            month: formart.getMonth() + 1
                        });
                    }
                    result.dates_departure = [...calendar.dates_departure];
                }

                if (arrow === DELAY_DATES && type === "return") {
                    dateSelect = this.$filters.date(calendar.dates_departure[0].date, 'YYYY-MM-DD');
                    for (let i = 1; i <= 7; i++) {
                        let newDate = this.$filters.addOrSubstractDays(dateSelect, -parseInt(i));
                        let formart = new Date(newDate);
                        const departureObj = {
                            date: formart.toISOString(),
                            day: formart.getDate(),
                            month: formart.getMonth() + 1,
                            rates: []
                        };

                        for (let y = 0; y < 7; y++) {
                            let dateDefault = new Date();
                            departureObj.rates.push({
                                date: dateDefault,
                                day: dateDefault.getDate(),
                                month: dateDefault.getMonth() + 1,
                                price: null,
                                airline: null
                            });
                        }
                        result.dates_departure.push(departureObj);
                    }
                    result.dates_departure.sort((a, b) => a.month - b.month || a.day - b.day);
                    result.dates_quote = [...calendar.dates_quote];
                }
                else if (arrow === ADVANCE_DATES && type == "return") {
                    lengthCalendar = calendar.dates_departure.length - 1;
                    dateSelect = this.$filters.date(calendar.dates_departure[lengthCalendar].date, 'YYYY-MM-DD');

                    for (let i = 1; i <= 7; i++) {
                        let newDate = this.$filters.addOrSubstractDays(dateSelect, i);
                        let formart = new Date(newDate);
                        const departureObj = {
                            date: formart.toISOString(),
                            day: formart.getDate(),
                            month: formart.getMonth() + 1,
                            rates: []
                        };

                        for (let y = 0; y < 7; y++) {
                            let dateDefault = new Date();
                            departureObj.rates.push({
                                date: dateDefault,
                                day: dateDefault.getDate(),
                                month: dateDefault.getMonth() + 1,
                                price: null,
                                airline: null
                            });
                        }
                        result.dates_departure.push(departureObj);
                    }
                    result.dates_departure.sort((a, b) => a.month - b.month || a.day - b.day);
                    result.dates_quote = [...calendar.dates_quote];
                }


                result.dates_departure.forEach(entry => {
                    entry.rates.forEach(rate => {
                        rate.price = null;
                    });
                });

                return result;
            },
            formatDateCalendar(date) {
                if (!date) return "Invalid date";
                const dateWithCapitalLetters = this.$filters.date(date, 'ddd MMM. DD ');
                const splited = dateWithCapitalLetters.split(/[.\s]+/);
                const dayCapital = splited[1].trim();
                const dayCompleteCapitalize = dayCapital.charAt(0).toUpperCase()
                    + dayCapital.slice(1)

                const options = { weekday: 'short', month: 'short', day: '2-digit' };
                const userLocale = culture.internalCultureCode;
                const formattedDate = new Intl.DateTimeFormat(userLocale, options).format(new Date(date));

                const parts = formattedDate.split(' ');
                const capitalizedWeekday = parts[0].charAt(0).toUpperCase() + parts[0].slice(1);
                const cleanDay = capitalizedWeekday.replace(',', '').trim();

                return userLocale === "en-us"
                    ? `${cleanDay}</br> <span class="fw-bold">${parts[1]} ${parts[2]}</span>`
                    : `${cleanDay}</br> <span class="fw-bold">${parts[1]} ${parts[3]}</span>`;
            },
            validateRow(view) {
                let disable = false;
                if (this.getTripMode == 1) {
                    let newDate = view == 'ida' ? this.$filters.increaseWeek(this.getStartDate) : this.$filters.subtractWeek(this.getReturningDate);
                    const dateOne = new Date(newDate);
                    const dateTwo = new Date(view == 'ida' ? this.getReturningDate : this.getStartDate);
                    disable = view == 'ida' ? dateOne > dateTwo : dateTwo > dateOne;
                }
                return disable;
            },
            onHoverCell(index_x, index_y, length_x = 0, lenght_y = 0) {
                this.toggleClassNameElement(`.th-x-${index_x}`, "th-show", true);
                this.toggleClassNameElement(`.th-y-${index_y}`, "th-show", true);

                (this.bListTh(0, length_x) || []).forEach(th_x => {
                    const to_index_X = th_x - 1;
                    if (to_index_X !== index_x) {
                        this.toggleClassNameElement(`.td-${to_index_X}-${index_y}`, "td-show-x", true)
                    }
                });

                (this.bListTh(0, lenght_y) || []).forEach(th_y => {
                    const to_index_Y = th_y - 1;
                    if (to_index_Y !== index_y) {
                        this.toggleClassNameElement(`.td-${index_x}-${to_index_Y}`, "td-show-y", true)
                    }
                });
            },
            toggleClassNameElement(element, className, show = true) {
                element = document.querySelector(element);
                if (element) {
                    if (show && !element.classList.contains(className)) {
                        element.classList.add(className);
                    }
                    else if (!show && element.classList.contains(className)) {
                        element.classList.remove(className);
                    }
                }
            },
            onLeaveCell(index_x, index_y, length_x = 0, lenght_y = 0) {
                this.toggleClassNameElement(`.th-x-${index_x}`, "th-show", false);
                this.toggleClassNameElement(`.th-y-${index_y}`, "th-show", false);

                (this.bListTh(0, length_x) || []).forEach(th_x => {
                    const to_index_X = th_x - 1;
                    if (to_index_X !== index_x) {
                        this.toggleClassNameElement(`.td-${to_index_X}-${index_y}`, "td-show-x", false)
                    }
                });
                (this.bListTh(0, lenght_y) || []).forEach(th_y => {
                    const to_index_Y = th_y - 1;
                    if (to_index_Y !== index_y) {
                        this.toggleClassNameElement(`.td-${index_x}-${to_index_Y}`, "td-show-y", false)
                    }
                });
            },
            mapCalendar(calendar) {
                if (calendar) {
                    this.isEmpty = !calendar.dates_departure.some(item =>
                        Array.isArray(item.rates) && item.rates.some(rate => rate.price !== undefined && rate.price !== null)
                    );
                }
                return calendar
            }
        }
    }
</script>

<style lang="scss" scoped>
    .table-dates td, .table-dates th {
        background-color: var(--bg-base);
        border: .5px solid var(--border-subtle);
        padding: 0.25rem;
        width: 155px;
        height: 55px;
        position: relative;
        vertical-align: middle;
        white-space: nowrap;
        text-align: center;
    }

    .l-date-matrix {
        padding: 0.25rem !important;
    }

    .border-none {
        border-style: solid !important;
        border-width: 0 !important;
        border-color: transparent !important;
    }

    .table-dates {
        font: var(--body);
        background-color: var(--bg-primary-subtle);

        th {
            &:hover {
                cursor: initial;
            }
        }

        td {
            &:hover {
                font: var(--body-bold);
                cursor: pointer;
                box-shadow: inset 0px 0px 0px 2px var(--border-primary);

                span {
                    color: var(--text-primary) !important;
                }

                &.bg-level2 {
                    cursor: initial;
                    box-shadow: none;
                }
            }

            &.td-show-x {
                box-shadow: inset 0px -2px 0px 0px var(--border-primary), inset 0px 2px 0px 0px var(--border-primary) !important;
            }

            &.td-show-x, &.td-show-y {
                &:before {
                    content: "";
                    bottom: 0;
                    left: 0;
                    position: absolute;
                    top: 0;
                    right: 0;
                    cursor: pointer;
                    background-color: var(--border-primary) !important;
                    opacity: 0.03;
                }
            }

            &.td-show-y {
                box-shadow: inset -2px 0px 0px 0px var(--border-primary), inset 2px 0px 0px 0px var(--border-primary) !important;
            }
        }

        .th-show {
            box-shadow: inset 0px 0px 0px 2px var(--border-primary);
            background-color: var(--bg-primary) !important;
            color: var(--text-oncolor) !important;
        }
    }

    .bg-level1 {
        background-color: var(--bg-level1) !important;
    }

    .bg-yellow-active {
        box-shadow: inset 0px 0px 0px 2px #ffff54 !important;
        background-color: var(--bg-warning-subtle) !important;
    }

    .table {
        --bs-table-color-type: initial;
        --bs-table-bg-type: initial;
        --bs-table-color-state: initial;
        --bs-table-bg-state: initial;
        --bs-table-color: var(--text-strong);
        --bs-table-bg: var(--bs-body-bg);
        --bs-table-border-color: #dee2e6;
        --bs-table-accent-bg: none !important;
        --bs-table-striped-color: #212529;
        --bs-table-striped-bg: rgba(var(--bs-emphasis-color-rgb), 0.05);
        --bs-table-active-color: #212529;
        --bs-table-active-bg: rgba(0, 0, 0, 0.075);
        --bs-table-hover-color: #212529;
        --bs-table-hover-bg: rgba(0, 0, 0, 0.075);
        width: 100%;
        margin-bottom: 1rem;
        vertical-align: top;
        border-color: var(--bs-table-border-color);
    }

    .bg-td-5 {
        background-color: rgba(14, 165, 233, .05) !important;
    }

    .bg-td-10 {
        background-color: rgba(14, 165, 233, .1) !important;
    }

    .bg-td-15 {
        background-color: rgba(14, 165, 233, .15) !important;
    }

    .bg-td-20 {
        background-color: rgba(14, 165, 233, .2) !important;
    }

    .c-scroll-vertical {
        bottom: 334px;
        right: -54px;

        @media (max-width: 767px) {
            right: auto;
            left: -77px;
            transform: rotate(-90deg);
        }

        position: absolute;
        transform: rotate(90deg);

        .c-scroll {
            .icon-chevron-right {
                right: -10px;
            }
        }
    }

    .c-matrix {
        &.rountrip {
            padding-right: 60px !important;

            @media (max-width: 767px) {
                padding-right: 16px !important;
                padding-left: 36px !important;
            }
        }
    }

    .c-int-matrix {
        width: 100%;
        padding-top: 0.5rem;

        @media (max-width: 767px) {
            min-height: 250px;
            padding-bottom: 0;
            padding-top: 0;
        }
    }

    .c-scroll {
        color: var(--text-main);

        .icon-chevron-left {
            color: var(--icon-link);

            &:hover {
                color: var(--icon-link-hover);
            }
        }

        .icon-chevron-right {
            color: var(--icon-link);

            &:hover {
                color: var(--icon-link-hover);
            }
        }

        .bg-level2 {
            background: var(--bg-level2) !important;
        }

        .btn-disabled {
            color: var(--icon-disabled) !important;
            cursor: none;
            pointer-events: none;
        }
    }
    /* Requerido: convierte la fila de la tabla en un flex container */
    .table-dates tr {
        @media (max-width: 767px) {
            display: flex; /* Para permitir el reordenamiento de columnas */
            .y-md-left {
                order: -1;
            }

            td, th {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                width: 110px;
            }
        }
    }

    .empty-roundtrip {
        margin-top: 5rem !important;
        margin-bottom: 5rem !important;
    }

    .scroll-vertical-empty {
        bottom: 100px;

        @media (max-width: 767px) {
            bottom: 155px;
        }
    }
</style>