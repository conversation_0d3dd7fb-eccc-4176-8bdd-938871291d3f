﻿.form-check {
  // @include theme($secondary);
  min-height: 22px;
  padding-left: 0px;
  margin-bottom: 0.75rem;
  > label {
    padding-left: 29px !important;
    min-height: 22px;
    line-height: 1.5;
    display: inline-block;
    position: relative;
    vertical-align: top;
    margin-bottom: 0;
    font-weight: normal;
    cursor: pointer;
    color: $gray-800;
    [class^="icon-"] {
      margin-top: -4px;
      margin-left: -4px;
    }
  }

  > input:first-child {
    position: absolute !important;
    opacity: 0;
    margin: 0;
    background-color: #787878;
    border-radius: 50%;
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
    -ms-appearance: none;
    display: block;
    width: 18px;
    height: 18px;
    outline: none;
    transform: scale(2);
    -ms-transform: scale(2);
    transition: opacity 0.3s, transform 0.3s;
    &:disabled {
      cursor: default;
      + label,
      + input[type="hidden"] + label,
      + label::before,
      + input[type="hidden"] + label::before {
        pointer-events: none;
        cursor: default;
        filter: alpha(opacity=65);
        -webkit-box-shadow: none;
        box-shadow: none;
        opacity: .65;
      }
    }
    + label::before,
    + input[type="hidden"] + label::before {
      content: "";
      display: inline-block;
      position: absolute;
      width: 18px;
      height: 18px;
      border: 2px solid $gray-500;
      border-radius: 3px;
      margin-left: -29px;
      margin-top: 2px;
      box-sizing: border-box;
    }

    &:checked {
      + label::after,
      + input[type="hidden"] + label::after {
          content: "";
          display: inline-block;
          position: absolute;
          top: 0;
          left: 0;
          width: 7px;
          height: 10px;
          border: solid 2px #fff;
          border-left: none;
          border-top: none;
          transform: translate(7.75px, 4.5px) rotate(45deg);
          -ms-transform: translate(7.75px, 4.5px) rotate(45deg);
          box-sizing: border-box;
      }
      + label {
        color: $gray-700;
        // font-weight: $font-weight-base-500;
      }
    }
    &:not(:checked):not(:disabled):hover {
      + label::before,
      + input[type="hidden"] + label::before {
        border-width: 2px;
      }
    }
    &::-ms-check {
      opacity: 0;
      border-radius: 50%;
    }
    &:active {
      transform: scale(0);
      -ms-transform: scale(0);
      opacity: 1;
      transition: opacity 0s, transform 0s;
    }
  }

//Radio Button
  > input[type="radio"]:first-child {
    + label::before,
    + input[type="hidden"] + label::before {
      border-radius: 50%;
    }

    &:checked {
      + label::before,
      + input[type="hidden"] + label::before {
        background-color: transparent;
      }

      + label::after,
      + input[type="hidden"] + label::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        border: none;
        top: 6px;
        left: 4px;
        transform: none;
        -ms-transform: none;
      }
    }
  }

//Checkbox
  > input[type="checkbox"]:first-child {
    &:checked {
      + label::after,
      + input[type="hidden"] + label::after {
        width: 6px;
        height: 14px;
        margin-top: 2px;
        transform: translate(6px, 0px) rotate(45deg);
        -ms-transform: translate(6px, 0px) rotate(45deg);
      }
    }
  }
}

.form-list {
  // @include theme($secondary);
  min-height: 22px;
  padding-left: 0px;
  margin-bottom: 0.75rem;
  > label {
    padding-left: 0px !important;
    min-height: 22px;
    line-height: 1.5;
    display: inline-block;
    position: relative;
    vertical-align: top;
    margin-bottom: 0;
    font-weight: normal;
    cursor: auto;
    color: $gray-500;
    [class^="icon-"] {
      margin-top: -4px;
      margin-left: -4px;
    }
  }

  > input:first-child {
    position: absolute !important;
    opacity: 0;
    margin: 0;
    background-color: #787878;
    border-radius: 50%;
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
    -ms-appearance: none;
    display: block;
    width: 18px;
    height: 18px;
    outline: none;
    transform: scale(2);
    -ms-transform: scale(2);
    transition: opacity 0.3s, transform 0.3s;
    &:disabled {
      cursor: default;
      + label,
      + input[type="hidden"] + label,
      + label::before,
      + input[type="hidden"] + label::before {
        pointer-events: none;
        cursor: default;
        filter: alpha(opacity=65);
        -webkit-box-shadow: none;
        box-shadow: none;
        opacity: .65;
      }
    }
    + label::before,
    + input[type="hidden"] + label::before {
      content: "";
      display: inline-block;
      position: absolute;
      width: 18px;
      height: 18px;
      border: 2px solid $gray-500;
      border-radius: 3px;
      margin-left: -29px;
      margin-top: 2px;
      box-sizing: border-box;
    }

    &:checked {
      + label::after,
      + input[type="hidden"] + label::after {
          content: "";
          display: inline-block;
          position: absolute;
          top: 0;
          left: 0;
          width: 7px;
          height: 10px;
          border: solid 2px #fff;
          border-left: none;
          border-top: none;
          transform: translate(7.75px, 4.5px) rotate(45deg);
          -ms-transform: translate(7.75px, 4.5px) rotate(45deg);
          box-sizing: border-box;
      }
      + label {
        color: $gray-700;
        // font-weight: $font-weight-base-500;
      }
    }
    &:not(:checked):not(:disabled):hover {
      + label::before,
      + input[type="hidden"] + label::before {
        border-width: 2px;
      }
    }
    &::-ms-check {
      opacity: 0;
      border-radius: 50%;
    }
    &:active {
      transform: scale(0);
      -ms-transform: scale(0);
      opacity: 1;
      transition: opacity 0s, transform 0s;
    }
  }

//Radio Button
  > input[type="radio"]:first-child {
    + label::before,
    + input[type="hidden"] + label::before {
      border-radius: 50%;
    }

    &:checked {
      + label::before,
      + input[type="hidden"] + label::before {
        background-color: transparent;
      }

      + label::after,
      + input[type="hidden"] + label::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        border: none;
        top: 6px;
        left: 4px;
        transform: none;
        -ms-transform: none;
      }
    }
  }

//Checkbox
  > input[type="checkbox"]:first-child {
    &:checked {
      + label::after,
      + input[type="hidden"] + label::after {
        width: 6px;
        height: 14px;
        margin-top: 2px;
        transform: translate(6px, 0px) rotate(45deg);
        -ms-transform: translate(6px, 0px) rotate(45deg);
      }
    }
  }
}

// Add media query to validate iOS device and disable transition efects
@supports (-webkit-touch-callout: none) {
  .form-check > input:first-child {
    transition: none;
  }
}

.form-check>input[type="radio"]:first-child:checked+label::before, .filter-list-item>input[type="radio"]:first-child:checked+label::before, .filter-list-item pth-filter-item>input[type="radio"]:first-child:checked+label::before, .form-check>input[type="radio"]:first-child:checked+input[type="hidden"]+label::before, .filter-list-item>input[type="radio"]:first-child:checked+input[type="hidden"]+label::before, .filter-list-item pth-filter-item>input[type="radio"]:first-child:checked+input[type="hidden"]+label::before {
  background-color: transparent;
}
.form-check>input[type="radio"]:first-child+label::before, .filter-list-item>input[type="radio"]:first-child+label::before, .filter-list-item pth-filter-item>input[type="radio"]:first-child+label::before, .form-check>input[type="radio"]:first-child+input[type="hidden"]+label::before, .filter-list-item>input[type="radio"]:first-child+input[type="hidden"]+label::before, .filter-list-item pth-filter-item>input[type="radio"]:first-child+input[type="hidden"]+label::before {
  border-radius: 50%;
}
.form-check>input:first-child:checked+label::before, .filter-list-item>input:first-child:checked+label::before, .filter-list-item pth-filter-item>input:first-child:checked+label::before, .form-check>input:first-child:checked+input[type="hidden"]+label::before, .filter-list-item>input:first-child:checked+input[type="hidden"]+label::before, .filter-list-item pth-filter-item>input:first-child:checked+input[type="hidden"]+label::before {
  background-color: $color-primary;
  border-color: $color-primary;
}
.form-check>input:first-child+label::before, .filter-list-item>input:first-child+label::before, .filter-list-item pth-filter-item>input:first-child+label::before, .form-check>input:first-child+input[type="hidden"]+label::before, .filter-list-item>input:first-child+input[type="hidden"]+label::before, .filter-list-item pth-filter-item>input:first-child+input[type="hidden"]+label::before {
  content: "";
  display: inline-block;
  position: absolute;
  width: 18px;
  height: 18px;
  border: 2px solid gray;
  border-radius: 3px;
  margin-left: -29px;
  margin-top: 2px;
  box-sizing: border-box;
}

.form-check>input[type="radio"]:first-child:checked+label::after, .filter-list-item>input[type="radio"]:first-child:checked+label::after, .filter-list-item pth-filter-item>input[type="radio"]:first-child:checked+label::after, .form-check>input[type="radio"]:first-child:checked+input[type="hidden"]+label::after, .filter-list-item>input[type="radio"]:first-child:checked+input[type="hidden"]+label::after, .filter-list-item pth-filter-item>input[type="radio"]:first-child:checked+input[type="hidden"]+label::after {
  content: "";
  position: absolute;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: none;
  top: 6px;
  left: 4px;
  transform: none;
  -ms-transform: none;
}
.form-check>input[type="radio"]:first-child:checked+label::after, .filter-list-item>input[type="radio"]:first-child:checked+label::after, .filter-list-item pth-filter-item>input[type="radio"]:first-child:checked+label::after, .form-check>input[type="radio"]:first-child:checked+input[type="hidden"]+label::after, .filter-list-item>input[type="radio"]:first-child:checked+input[type="hidden"]+label::after, .filter-list-item pth-filter-item>input[type="radio"]:first-child:checked+input[type="hidden"]+label::after {
  background-color: $color-primary;
}