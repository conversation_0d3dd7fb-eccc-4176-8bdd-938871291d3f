﻿using Microsoft.AspNetCore.Mvc.ModelBinding;

namespace TBFront.Providers.ModelBinder
{
    public class MultiNameModelBinder(string[] propertyNames, Type targetType) : IModelBinder
    {
        private readonly string[] _propertyNames = propertyNames;
        private readonly Type _targetType = targetType;

        public Task BindModelAsync(ModelBindingContext bindingContext)
        {
            if (bindingContext == null)
            {
                throw new ArgumentNullException(nameof(bindingContext));
            }

            foreach (var name in _propertyNames)
            {
                var valueResult = bindingContext.ValueProvider.GetValue(name);
                if (valueResult != ValueProviderResult.None)
                {
                    try
                    {
                        var value = Convert.ChangeType(valueResult.FirstValue, _targetType);
                        bindingContext.Result = ModelBindingResult.Success(value);
                        return Task.CompletedTask;
                    }
                    catch (Exception)
                    {
                        continue;
                    }
                }
            }

            if (_targetType.IsValueType)
            {
                bindingContext.Result = ModelBindingResult.Success(Activator.CreateInstance(_targetType));
            }
            else
            {
                bindingContext.Result = ModelBindingResult.Success(null);
            }

            return Task.CompletedTask;
        }
    }

    [AttributeUsage(AttributeTargets.Property, AllowMultiple = false)]
    public class FromMultipleQueryAttribute(params string[] names) : Attribute, IBindingSourceMetadata
    {
        public string[] Names { get; } = names;

        public BindingSource BindingSource => BindingSource.Query;

        public IModelBinder GetBinder(Type targetType)
        {
            return new MultiNameModelBinder(Names, targetType);
        }
    }

    public class MultiNameModelBinderProvider : IModelBinderProvider
    {
        public IModelBinder GetBinder(ModelBinderProviderContext context)
        {
            if (context.Metadata.ContainerType?.GetProperty(context.Metadata.PropertyName)?.GetCustomAttributes(typeof(FromMultipleQueryAttribute), false).FirstOrDefault() is FromMultipleQueryAttribute attribute)
            {
                return attribute.GetBinder(context.Metadata.ModelType);
            }

            return null;
        }
    }
}
