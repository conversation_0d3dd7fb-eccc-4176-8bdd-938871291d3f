﻿module.exports = {
	startingAirport: "startingAirport",
	returningAirport: "returningAirport",
	outboundFlight: "outboundFlight",
	returningFlight: "returningFlight",
	bookerTypes: {
		hotelList: "hotel-list",
		detailRoom: "detail-room",
		flightList: "flight-list"
	},
	placeType: {
		none: 0,
		continent: 1,
		region: 2,
		destination: 3,
		country: 4,
		state: 5,
		city: 6,
		zone: 7,
		area: 8,
		transferZone: 9,
		airportRange: 10,
		airport: 11,
		interestPoint: 12,
		pickupPoint: 13,
		hotel: 14,
		tour: 15,
		busOffice: 16,
		busOfficeProvider: 17,
		airportColletion: 18
	},
	desktop: "desktop",
	mobile: "mobile",
	tablet: "tablet",
	customFilter: "custom_filter",
	originalFilter: "original_filter",
	summaryDetail: {
		open: 'open',
		close: 'close'
	},
	detailFlight: {
		flight: "flightDetail",
		luggage: "luggage",
		luggageFlight: "luggageFlight"
	},
	oneWay: 'oneway',
	roundTrip: 'roundtrip',
	placeType: {
		none: 0,
		continent: 1,
		region: 2,
		destination: 3,
		country: 4,
		state: 5,
		city: 6,
		zone: 7,
		area: 8,
		transferZone: 9,
		airportRange: 10,
		airport: 11,
		interestPoint: 12,
		pickupPoint: 13,
		hotel: 14,
		tour: 15,
		busOffice: 16,
		busOfficeProvider: 17,
		airportColletion: 18
	},
	cheapestFlights: 'cheapestFlights',
	methods: {
		GET: 'get',
		POST: 'post',
		PUT: 'put',
		PATCH: 'patch',
		DELETE: 'delete'
	},
	consoleType: {
		LOG: 'log',
		ERROR: 'error',
		WARN: 'warn',
	}
}