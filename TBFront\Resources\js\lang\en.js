﻿window.i18n = {
	"messages": {
		"domain": "www.TiquetesBaratos.com",
		"search_with_all_airlines": "Search with all airlines",
		"direct": "Direct",
		"scale": "Stop",
		"scales": "Stops",
		"rate": "Rate",
		"arrival": "Arrives",
		"departure": "Departs",
		"departure_flight": "Outbound",
		"return_flight": "Return",
		"total": "Total",
		"flight": "Flight",
		"adult": "Adult",
		"child": "Child",
		"infant": "Infant",
		"upgrade_your_flight": "Upgrade your flight",
		"view_flight_details": "View flight details",
		"we_found": "We found",
		"fares": "fares",
		"available": "available",
		"stops_0": "Direct",
		"stops_1": "stop",
		"stops_2": "stops",
		"message_list_title": "Fares per traveler with taxes and surcharges in ({0})",
		"near_dates": "Nearby dates",
		"cheapest_flights": "Cheapest flights",
		"more_flights": "More economical",
		"more": "More",
		"dates": "Dates",
		"select": "Select",
		"rate_type": "Fare type",
		"connection_in": "Connection in",
		"with_waiting": "with a wait of",
		"flight_detail": "Flight detail",
		"what_includes": "What does it include?",
		"RATE_NOT_FOUND": "An error occurred with the current quote, please contact <a href='tel:6017436620'>************</a> for follow-up",
		"ROOM_NOT_FOUND": "An error occurred with the current quote, please contact <a href='tel:6017436620'>************</a> for follow-up",
		"HOTEL_NOT_FOUND": "Hotel not found",
		"VOUCHER_NOT_FOUND": "Data not found",
		"ERROR_DEFAULT": "An error occurred",
		"error_title": "An error occurred",
		"error_description": "Our apologies. We couldn't get the requested information.",
		"error_list_one": "Servers remove search information after a period of inactivity. This protects your privacy and helps the site work better.",
		"error_list_two_bold": "Link from favorites",
		"error_list_two": "Pages you add to your browser's favorites are built with information based on your previous selections (such as fares for a specific date). These pages cannot be used to obtain updated information.",
		"go_home": "Go back to the home page",
		"detail_fly": "Flight details",
		"to_destination": "to",
		"duration_fly": "Total flight duration",
		"fly": "Flight",
		"operate_by": "Operated by ",
		"departure_of": "Departs from ",
		"arrival_to": "Arrives at",
		"time_flight": "Time between flights",
		"huso_horario_local": "Local time of each city.",
		"fare": "Fare: ",
		"unscaled": "Non-stop",
		"aircraft": "Aircraft",
		"selected_flight": "Selected flight",
		"flight_leg_title_starting": "Outbound flight",
		"flight_leg_title_returning": "Return flight",
		"selected_fare": "Selected fare",
		"includes_all_taxes": "Includes all taxes",
		"continue_button_text": "Continue",
		"select_button_text": "Select",
		"success_voucher": "Great, get ready to travel!",
		"processing_booking": "We are processing your booking",
		"reservacion_id": "Your reservation locator",
		"we_send_email": "We sent the details to your email",
		"description_voucher": "Make sure to receive our messages",
		"description_voucher_detail": "If you don't see our message in your inbox after a few minutes, check the spam folder. If it's there, mark it as a safe email to receive CheapTickets emails without issues",
		"go_home": "Go back to the home page",
		"thankyou_page": "Purchase confirmation",
		"please_wait_voucher": "We are processing your reservation, please wait a moment.",
		"voucher_departure_date": "Departure date: ",
		"voucher_hrs": "hrs",
		"voucher_passangers": "Passengers: ",
		"voucher_info_mail": "We sent you an email with your reservation information to the following email: ",
		"voucher_info_mail_phone": "If you don't receive the reservation email, please contact us at the following phone number: ",
		"voucher_total": "Total paid: ",
		"voucher_method_payment": "Payment method ",
		"voucher_airline": "Airline: ",
		"voucher_header_one": "Hello  ",
		"voucher_header_two": "Thank you for booking with ",
		"voucher_header_three": "TiquetesBaratos.com ",
		"voucher_header_four": "Your reservation with number ",
		"voucher_header_five": " is confirmed and your payment is being validated.",
		"voucher_adults": " adults",
		"voucher_kids": " children",
		"voucher_infants": " infants",
		"voucher_adult": " adult",
		"voucher_kid": " child",
		"voucher_infant": " infant",
		"flight_operated_by": "Flight operated by",
		"to_document": "To check-in, you must go to its area",
		"children": "Children",
		"chage_price": "A fare change has been detected",
		"chage_price_package": "Your flight price has",
		"chage_price_down": "dropped ",
		"chage_price_up": "risen ",
		"close": "Close",
		"return_label": "Return",
		"error_reserv": "Sorry. The selected option is no longer available.",
		"single_fare": "fare",
		"only_available": "available",
		"by_passenger": "Per person",
		"from": "From",
		"terminal": "Terminal",
		"flight_not_available_title": "Flight from {0} to {1} not available.",
		"flight_not_available": "No available flights found, please select another flight. If you need help, please contact one of our advisors.",
		"promotions_not_avaible": "No promotions found at the moment for the selected origin, please select a different destination. Please",
		"spent_some_time": "Some time has passed since your search",
		"update_search": "Update search",
		"wait_on_board": "Wait on board at {0} from",
		"hour": "hour",
		"hours": "hours",
		"minute": "minute",
		"minutes": "minutes",
		"date_validation_roundtrip": "<p class='body'>It's not possible to select that flight combination. The outbound flight arrives after the return flight.</p>",
		"by_adult": "Per adult",
		"checkout_family_change_your_fare": "Your fare ",
		"checkout_family_change_no_available": "was not available, and has been updated to fare ",
		"checkout_family_change_no_available_route": "for the trip from ",
		"enter_your_data": "Enter your data",
		"change_your_reservation": "Change your reservation",
		"before": "Before",
		"today": "Now",
		"price_your_flight_has_down": "Your flight price has decreased",
		"finalize_your_reservation": "Finalize the reservation and secure your seat on the flight!",
		"ok": "Understood",
		"the_rate": "The rate",
		"is_not_available_html": "<strong class='text-danger'>is not available</strong> for the outbound flight, we have updated to the fare",
		"choose_another_flight": "Choose another flight",
		"cabin": "Cabin",
		"several": "Several",
		"options": "options",
		"flight_recommended": "Your outbound flight details",
		"show": "Show",
		"hide": "Hide",
		"choose_another_flight": "Choose another flight",
		"what_includes": "What does this rate include?",
		"seeLuggage": "See luggage",
		
	},
	"checkout": {
		"note_charge": "Please note that the service charge will be made in {0} so the conversion will be made at the time of payment in ",
		"pay_will_be": "Your payment will be for ",
		"title": "Secure your reservation now!",
		"subtitle": "Traveler Information",
		"security_title": "Your personal information and transaction are secure",
		"security_alert": "We DO NOT STORE INFORMATION ",
		"security_text": "about users' credit cards who make transactions on our platform. Our online payment system is endorsed by VeriSign",
		"your_reservation": "Your reservation",
		"tax": "Taxes and fees",
		"rate_by_adult": "Rate per adult",
		"phone": "Phone or Mobile (10 digits)",
		"contact_information": "Contact information",
		"email": "Email",
		"checkout_legals": "By clicking the Book and Pay button, you are accepting the ",
		"checkout_legals_terms": "Terms and Conditions of the purchase and the Privacy Policy.",
		"booknow": "Book and Pay",
		"first_name": "First name",
		"last_name": "Last name",
		"identification_code": "Citizenship ID",
		"identification_code_minor": "Identity document",
		"frequent_flyer_number_text": "Add Frequent Flyer Number",
		"frequent_flyer_number": "Frequent flyer number",
		"date_of_birth": "Date of Birth",
		"day": "Day",
		"month": "Month",
		"year": "Year",
		"interest_text": "I am interested in a hotel in {0}",
		"interest_text_body": "Book your flight and get exclusive prices for our clients",
		"off": "Up to -{0}%",
		"modal_ready": "Your flight is almost ready",
		"modal_detail": "Flight details:",
		"modal_family": "Family"
	},
	"breakdown": {
		"TaxExcluded": "Taxes not included",
		"TaxExcluded_CO": "Local residents VAT*",
		"TaxIncluded": "Taxes included",
		"HotelFees": "Hotel fees",
		"ResortFees": "Resort fees",
		"HotelMandatoryTax": "Hotel mandatory taxes",
		"HospitalityTax": "Hospitality tax",
		"charges_not_included": "Charges not included",
		"adults_txt": "Adult(s)",
		"children_txt": "Child(ren)",
		"infant_txt": "Infant(s)",
		"service_charge_txt": "Service charge",
		"taxes_and_fees": "Taxes",
		"all_charges_txt": "Taxes, fees and charges"
	},
	"errors": {
		"default": "The field is not valid",
		"alpha": "The field can only contain letters",
		"alpha_spaces": "The field can only contain letters",
		"confirmed": "The field does not match",
		"email": "It must be an email field",
		"required": "The field is required",
		"integer": "The field can only contain digits",
		"distinctTo": "The {0} is equal to {1}",
		"ageKids": "Check the ages of minors",
		"is_not": "The field is not valid",
		"date_invalid": "The date is invalid",
		"name is a required field": "Please enter a name",
		"email is a required field": "Please enter an email",
		"phone number is not valid": "Please enter a valid phone number",
		"message is a required field": "Please add a message",
		"email must be a valid email": "Please enter a valid email",
		"origin is a required field": "Please enter the origin of your trip",
		"destination is a required field": "Please enter the destination of your trip",
		"adults is a required field": "Please enter the number of adults for your trip",
		"groupType is a required field": "Please select the group type",
		"phone is a required field": "Please enter a phone number",
		"adults must be greater than or equal to 1": "At least one adult must travel",
		"kids must be greater than or equal to 0": "The number of children must be 0 or greater",
		"must be a number": "You must enter a number",
		"otherGroup is a required field": "Please enter the group type",
		"recaptcha_error": "Please verify the recaptcha",
		"error_dates": "Check your travel dates",
		"distinct": "The fields must be different"
	},
	"booker": {
		"adult": "Adult",
		"adults": "Adults",
		"and": "and",
		"child": "Child",
		"children": "Children",
		"age": "Age",
		"roundtrip": "Round trip",
		"edit_search": "Edit search",
		"origin": "Origin",
		"destination": "Destination",
		"departure_date": "Departure",
		"return_date": "Return",
		"passengers": "Passengers",
		"choose_your_passengers": "Choose your passengers",
		"from_up_to_years": "From {0} to {1} years",
		"from_years": "From {0} years",
		"ok": "OK",
		"search": "Search",
		"year_old": "0 to 24 months (In arms)",
		"years_old": "years",
		"child_age": "Child's age",
		"choose_your_origin": "Choose your departure airport",
		"choose_your_destination": "Choose your destination",
		"availableDates": "Dates",
		"add_room": "Add room",
		"room": "Room",
		"remove": "Remove",
		"rooms": "Rooms",
		"dateAvailable": "Date",
		"oneway": "One way",
		"ageLabel": "What is your age?",
		"oneWayFligth": "One-way flight",
		"roundTripFligth": "Round-trip flight",
		"day": "day",
		"days": "days",
		"mostOrigins": "Most searched origins",
		"mostDestinations": "Most searched destinations",
		"originDefaultName": "Bogotá - Colombia (BOG)",
		"originSimpleName": "Bogotá",
		"errorPaxes": "Sorry, we do not support more than 9 passengers",
		"originDisplayHtml": "<em>Bogotá</em>",
		"recentResearchs": "Recent searches",
		"what_origin": "What is your origin?",
		"what_destination": "What is your destination?"
	},
	"newsletter": {
		"title": "Don't miss any offer!",
		"subtitle": "You will receive information to have the best experience",
		"email": "Email",
		"suscribe": "Subscribe now",
		"validatorMessage": "Enter a valid email address"
	},
	"checkReservation": {
		"title": "Check your reservation itinerary",
		"tab1": "Flights",
		"tab2": "Hotels and Packages",
		"title2": "Please enter your reservation code.",
		"codeReservation": "Reservation code or locator",
		"btnReservation": "Next",
		"codeReservationPackages": "Please enter your locator code (9-character reservation code, e.g. 123455687) and your email to get detailed information about your trip.",
		"title3": "Locator",
		"title4": "Email (the one used to book)",
		"btnReservation2": "View Itinerary"
	},
	"recentResearch": {
		"title": "Continue with your recent searches",
		"subtitle": "Find what you were looking for"
	},
	"NearByDates": {
		"EconomicFlights": "Cheapest Flights"
	},
	"payOnline": {
		"title": "Pay Online",
		"subtitle": "Enter your locator and pay online. (We accept all Debit and Credit cards).",
		"code": "Locator",
		"codePlaceHolder": "E.g., 129668555",
		"email": "Email (the one used to book)",
		"emailPlaceholder": "E.g., <EMAIL>",
		"consult": "Consult",
		"title_token": "Your reservation details are as follows",
		"description": "Mr/Mrs {0}, in case of doubts, you can contact us at our helpline:",
		"tel": "<a href='tel:6017436620'>************ </a>",
		"owner_name": "In the name of",
		"owner_email": "Email",
		"total_amount": "For a total of",
		"go_back": "Go back to the beginning",
		"create_link": "Proceed to pay",
		"error": "An error occurred while checking the reservation data",
		"cancelled": "The reservation has been canceled",
		"not_found": "We could not find your reservation",
		"success": "Your reservation details",
		"description_cancelled": "You can try creating a new reservation on our portal, for any doubts, you can contact us at our helpline",
		"description_not_found": "Check if you entered the correct details, for any doubts, you can contact us at our helpline",
		"paid_amount": "Amount paid",
		"balance_amount": "Pending amount"
	},
	"urls": {
		"url_terms": "/terms-conditions",
		"url_best_price": "/help/price-guarantee",
		"url_go_home": "https://www.tiquetesbaratos.com/",
		"url_go_itinerary": "/help/cheap-tickets/reservation",
		"url_detail": "/hotel/",
		"url_list": "/hotels/"
	},
	"filters": {
		"nonStopsRates": "Non-stop",
		"oneStopRates": "With stops",
		"searching": "Searching",
		"airlines": "Airlines",
		"stops": "Stops",
		"filters": "Filters",
		"apply": "Apply",
		"allFlights": "All flights",
		"allAirlines": "All airlines",
		"validating": "validating...",
		"any_flights": "¡Ups! We didn't find any flights",
		"all_filters": "All filters",
		"cheapest": "Cheapest"
	},
	"flightList": {
		"lastChair": "",
		"fairesIncluded": "Includes all taxes",
		"moreFlightsFrom": "More flights from",
		"lessFlightsFrom": "Fewer flights from",
		"seeMore": "See more",
		"stop": "Stop",
		"stops": "Stops",
		"flightsEconomies": "Cheapest flights",
		"adultsFroms": "Per passenger, from:",
		"direct": "Direct",
		"roundTrip": "Round trip",
		"seeMoreFligths": "See more flights",
		"flight_departure": "Departure flights",
		"flight_arrival": "Return flights",
		"seeMoreEconomicFligthsFrom": "See more cheapest flights from",
		"seeLessFligths": "See fewer flights",
		"book": "Book",
		"select_departure_flight": "Select departure flight",
		"select_your_departure_flight": "<span>Select your <strong>departure flight</strong></span>",
		"select_your_flight": "<span>Select your <strong>flight</strong></span>",
		"select_your_departure_flight_2": "Select your <strong>departure flight</strong>",
		"select_your_returning_flight_2": "Select your <strong>return flight</strong>",
		"select_returning_flight": "Select return flight",
		"select_your_returning_flight": "<span>Select your <strong>return flight</strong></span>",
		"origin": "Origin",
		"destination": "Destination",
		"sold_out": "Sold out",
		"seeMoreFligthsDeparture": "See more departure flights",
		"seeMoreFligthsReturning": "See more return flights",
		"seeMoreEconomicFligthsDeparture": "See more cheapest departure flights",
		"seeMoreEconomicFligthsReturning": "See more cheapest return flights",
		"selected_departure_flight": "Selected departure flight",
		"change_flight": "Change flight",
		"departure": "Departure",
		"returning": "Returning",
		"show_arrival_flights": "Show return flights",
		"what_each_rate_includes": "What does each rate include?",
		"youArriveOn": "You arrive on",
		"baggage": "Checked baggage",
		"carryOn": "Carry-on baggage",
		"notIncluded": "Not included",
		"included": "Included"
	},
	"NearByDates": {
		"EconomicFlights": "Cheapest Flights",
		"select_your_departure_flight": "Select your departure flight",
		"select_your_arrival_flight": "Select your return flight"
	},
	"contactForm": {
		"title": "Send us a message",
		"name": "Name",
		"entreprises": "Companies",
		"tel": "Phone (optional)",
		"email": "Email",
		"message": "Message",
		"checkText1": "I want to receive a copy of the message to my email",
		"checkText2": "I want to receive exclusive promotions",
		"sendFormBtn": "Send message",
		"sendingFormBtn": "Sending...",
		"success": "Thank you! We will contact you soon.",
		"error": "There was an issue processing your request"
	},
	"groupsForm": {
		"mainMessage": "If you want to travel with your family, friends, or work group and need all the information about tickets, transfers, or car rentals, just fill out this form, and an advisor will contact you shortly.",
		"origin": "Origin",
		"originPlaceholder": "Where does your trip start?",
		"destination": "Destination",
		"destinationPlaceholder": "What is the destination?",
		"outboundDate": "Departure date",
		"returningDate": "Return date",
		"travelDate": "Travel date",
		"adults": "Adults",
		"kids": "Kids",
		"groupType": "Group type",
		"needHotel": "Do you need a hotel?",
		"needShuttle": "Do you need transfers?",
		"needCar": "Do you need a car?",
		"family": "Family",
		"schools": "Schools",
		"universities": "Universities",
		"conventions": "Conventions",
		"artistic": "Artistic",
		"sports": "Sports",
		"others": "Others",
		"contact": "Contact",
		"full_name": "Full name",
		"email": "Email",
		"email_placeholder": "e.g., <EMAIL>",
		"phone": "Phone",
		"observations": "Observations (optional)",
		"send": "Send"
	},
	"need_a_call": {
		"need_a_call?": "Do you need us to call you?",
		"your_name": "Your name",
		"phone_number": "Phone number (10 digits)",
		"send": "Send",
		"openning_hours": "Opening hours",
		"openning_hours_weekdays": "Monday to Friday from {0} to {1}",
		"openning_hours_weekends_holidays": "Saturdays, Sundays, and holidays from {0} to {1}",
		"Ok": "We will contact you shortly",
		"No available": "One of our advisors will contact you as soon as possible.",
		"available_agents": "Available advisors",
		"accept": "Accept"
	},
	"promotions": {
		"titleForm": "Leaving from:",
		"labelPlaceHolder": "Origin",
		"titleFormDestination": "Select your destination city",
		"labelPlaceHolderDestination": "Destination",
		"originTitle": "TRAVEL!",
		"originSubtitle": "Super rates to any destination",
		"destinationTitle": "Thinking of traveling to",
		"destinationTitleList": "Select your origin city",
		"salesDestinations": "Sales in destinations",
		"internationals": "INTERNATIONALS",
		"nationals": "NATIONALS",
		"titleCalendar": "Flights from",
		"otherDestination": "Search another destination",
		"tab1": "Round trip",
		"tab2": "One-way",
		"from": "from",
		"taxesWarning": "Rates per adult with taxes and surcharges",
		"hotelsDestination": "See all hotels in",
		"datesFroms": "DATES FROM",
		"go": "GO",
		"return": " RETURN",
		"more": "MORE",
		"dates": "DATES",
		"noRatesQuotations": "Sorry, but no prices were found for nearby dates for the destination. Please try with new dates or a different destination",
		"noDestinationPromotions": "No destinations found at the moment",
		"noOriginsPromotions": "No origins found at the moment",
		"other_destination": "Find another destination",
		"rates_per_adult": "Rates per adult with taxes and surcharges",
		"see": "See",
		"to": "to",
		"search" : "Search",
		"nationalTitle": "National",
 		"internationalTitle": "International",
		"travelfrom": "Traveling from",
		"travelto": "Traveling to",
		"searchFlight": "Search flight"
	},
	"multiticket": {
		"search_flights": "Searching for the best flight deals...",
		"price_per_pax": "price per person",
		"tax_include": "includes all taxes",
		"price_from": "Per person from:",
		"price_RoundTrip_from": "Round trip per person from:",
		"price_RoundTrip": "Round trip per person from:",
		"per_pax": "per person",
		"direct": "Non-stop",
		"stops": "stops",
		"stop": "stop",
		"show_more_flights_web_cheap_mobile": "View <span class='cheap-active border-0'>cheaper</span> flights",
		"show_more_flights_web_mobile": "View more flights",
		"show_more_flights_departure_web": "View more outbound flights",
		"show_more_flights_departure_web_cheap": "View <span class='cheap-active border-0'>cheaper</span> outbound flights",
		"show_more_flights_arrival_web": "View more return flights",
		"show_more_flights_arrival_web_cheap": "View <span class='cheap-active border-0'>cheaper</span> return flights",
		"change_flight": "Change flight",
		"hide_less_flights": "View fewer flights",
		"day": "+{0} day",
		"days": "+{0} days",
		"departure_flight": "Outbound flights",
		"returning_flight": "Return flights",
		"selected_departure_flight": "Selected outbound flight",
		"departure_flight_upsell": "Outbound flight <span class='hide-xs'>selected</span>",
		"returning_flight_upsell": "Return flight <span class='hide-xs'>selected</span>",
		"notification_flight": "We couldn't find flights with ",
		"notification_flight_options": "These other options might interest you.",
		"price_RoundTrip_per_person": "round price per person",
		"arrive_on": "Arrives  "
	},
	"culture": {
		"title": "Language and Currency Selection",
		"language": "Language",
		"active": "Active",
		"currency": "Currency",
		"price_warning": "Prices will be shown in the currency you select. The currency you pay in may vary depending on the booking.",
		"apply": "Apply"
	},
	"policy": {
		"terms": "Terms and Conditions of Purchase and Privacy Policy.",
		"terms_paragraph1": 'Dear user: We recommend not making more than one reservation per individual journey you need, as this could cause issues with the airline that may lead to the automatic cancellation of flights due to duplicate bookings.',
		"terms_paragraph2": 'The response to your search may, in some cases, not yield results or may vary depending on the available classes. The reason could be that the airlines operating on the route you are checking have not yet joined the reservation system we use.',
		"terms_paragraph3": "In some cases, the flights confirmed by this search engine cannot be sold from Colombia. Our advisor will inform you at the time of contact.",
		"terms_paragraph4": "For transactions made through Electronic Payment on our Website, an Internet Administrative Fee will be applied, which will be calculated according to the airline and the selected route.",
		"terms_paragraph5": "If you only make the reservation and complete the payment through another method, the Administrative Fee applied will be higher than the one charged via Electronic Payment.",
		"terms_payment": "View Electronic Payment Conditions",
		"terms_payment_paragraph1": "The electronic payment must be made as soon as the reservation is completed. If only the reservation is made, payment must be made through our various offline payment methods.",
		"terms_payment_paragraph2": "The traveler must be the owner of the credit card used for the payment.",
		"terms_payment_paragraph3": "Certain parameters will be validated during the process, and in the final stage, you will be informed whether you can proceed with electronic payment or need to complete it through an advisor.",
		"terms_payment_paragraph4": "It is not allowed to combine airlines in the purchase process with electronic payment.",
		"terms_payment_paragraph5": "One of the flight legs must originate from or arrive in Colombia.",
		"terms_payment_paragraph6": "You must accept that you have read and agree with all the terms of ticket purchase. Once the process is completed, you will receive an email confirmation of your reservation and ticket purchase.",
		"rates": "Administrative Fees",
		"rates_paragraph1": "For transactions made through Electronic Payment on our Website, an Internet Administrative Fee will be applied, which will be calculated according to the airline and the selected route.",
		"privacy": "Privacy Policy",
		"terms_conditions": "Terms and Conditions",
		"terms_conditions_paragraph": "Using this website confirms your agreement with the terms and conditions stated below. Please read them carefully before using the site. If you do not agree with any section of the content, please contact us before using this website.",
		"from_bogota": "From Bogotá: 601 745 7878",
		"from_cali": "From Cali: 602 485 0400",
		"from_medellin": "From Medellín: 604 604 1777",
		"from_barranquilla": "From Barranquilla: 605 385 2828",
		"from_cucuta": "From Cúcuta: 607 594 3050",
		"from_pasto": "From Pasto: 602 736 5080",
		"from_manizales": "From Manizales: 606 891 8920",
		"from_pereira": "From Pereira: 606 340 0720",
		"from_armenia": "From Armenia: 606 735 9840",
		"from_bucaramanga": "From Bucaramanga: 607 697 0400",
		"from_cartagena": "From Cartagena: 605 693 0520",
		"from_santa_marta": "From Santa Marta: 605 436 6180",
		"from_valledupar": "From Valledupar: 605 598 4120",
		"terms_conditions_paragraph1": "www.TiquetesBaratos.com does not guarantee that its applications will function uninterrupted or error-free, that defects will be corrected, or that the site or server is free of viruses or other harmful components. Under no circumstances shall www.TiquetesBaratos.com be liable for any direct or indirect damages, or consequential damages, including but not limited to lost profits, product replacement costs, inability to use the content, errors made in access via mouse clicks, even if www.TiquetesBaratos.com is warned of the possibility of such damages. If local laws do not allow liability exclusions, the exclusions listed above do not apply to your case.",
		"terms_conditions_paragraph2": "This site is offered to you as a user for your acceptance without negotiation of the terms, conditions, and clauses contained herein. The relationship between the www.TiquetesBaratos.com website and you shall be that of independent contractors, and neither party (including officers, agents, and employees) shall be considered or constituted as partners, joint ventures, trustees, employees, and/or common agents.",
		"terms_conditions_paragraph3": "You may not assign, agree, subcontract, or delegate your rights, duties, and obligations stated here.",
		"terms_conditions_general": "General Conditions",
		"terms_organization": "Organization and Responsibilities:",
		"terms_conditions_paragraph4": "The website www.TiquetesBaratos.com, owned by PRICE RES S.A.S, hereinafter referred to as the Operating Agent, declares that it acts as an intermediary between users and entities or persons responsible for providing air or land transportation services, accommodation, food, or any other service contracted through the www.TiquetesBaratos.com website. In this sense, www.TiquetesBaratos.com commits to fulfilling the mentioned intermediation services, with the exceptions specified in these General Conditions. It is not responsible for the non-fulfillment of these entities in the execution of their obligations or for unforeseen events caused by strikes, weather conditions, delays, earthquakes, quarantines, as well as for material, personal, or moral damages that the passenger may suffer due to loss, damage, or theft of luggage, accidents, illnesses, or deaths. The user must claim directly from the service providers for any non-compliance, in which www.TiquetesBaratos.com will assist the user as much as possible. These \"General Conditions\" are governed by the rules of commercial and civil law and other applicable laws.",
		"terms_conditions_paragraph5": "www.TiquetesBaratos.com, in its capacity as a travel agency, is subject to the liability regime established by Law 300/96, D.R. 1075/97, and other regulatory decrees.",
		"terms_responsibility": "Liability Clause",
		"terms_conditions_paragraph6": "www.TiquetesBaratos.com is subject to the liability regime established by Law 300/96, D.R. 1075/97, Decree 2438 of 2010, and any regulations that modify, add to, or reform them. The responsibility of the organizer of the tour package is limited to the terms and conditions of the program regarding the provision and quality of services. The agency assumes no responsibility towards the user for air transportation services unless it is a charter flight, in accordance with the conditions of the transportation contract.",
		"terms_conditions_paragraph7": "The refund policies for services not provided due to force majeure or unforeseen circumstances, actions or omissions by third parties or the passenger, not attributable to the travel agency, before or during the trip, will be defined by each operator and confirmed to the user once travel documents are reserved and issued. This includes applicable penalties or deductions. www.TiquetesBaratos.com is not jointly liable for the amounts requested for refunds. Refunds, if applicable, will be processed within 30 calendar days following the request. However, if the process takes longer due to reasons beyond www.TiquetesBaratos.com's control, no interest will be recognized on the refundable amounts. The refund percentage will depend on the provider’s conditions and the agency’s administrative costs.",
		"terms_conditions_paragraph8": "The agency assumes no responsibility towards the user or traveler for events such as accidents, strikes, riots, earthquakes, climatic or natural phenomena, security conditions, political factors, denial of entry permits or visas, legal issues of the traveler, health concerns, and any other cases of force majeure or unforeseen circumstances that may occur before or during the trip.",
		"terms_conditions_paragraph9": "In cases of force majeure or unforeseen circumstances before or during the trip (accidents, strikes, riots, earthquakes, climatic factors, security conditions, political factors, denial of entry permits, health concerns, among others), or simply to ensure the success of the plan, the operator and/or the agency may modify, replace, or cancel itineraries, dates, flights, hotels, and optional services, which the passenger accepts upon purchasing the services.",
		"terms_conditions_paragraph10": "If a visa is required, www.TiquetesBaratos.com will provide the necessary guidance; however, all matters related to the application, required documents, processing, costs, duration, and approval or denial are exclusively at the discretion of the consular authority. In case of a visa denial, no refunds will be issued for payments made by the applicant. It is the passenger's sole responsibility to complete the application and meet the specified requirements.<br> The specific payment methods and deadlines for each plan will be defined accordingly. The payment methods and amounts for deposits, advances, tickets, reservations for sporting and cultural events, fairs, exhibitions, and similar events will be subject to the organizer's conditions, which will be informed at the time of purchase.",
		"terms_conditions_paragraph11": "The passenger is solely responsible for the custody of their luggage and travel documents. www.TiquetesBaratos.com may assist the passenger in cases of lost luggage or travel documents; however, under no circumstances will it be liable for their loss, damage, deterioration, or misplacement. www.TiquetesBaratos.com will inform the passenger of airline restrictions regarding prohibitions, maximum weight, and the number of pieces per passenger, as well as restrictions on tourist attractions, maximum capacity limits, or access limitations. However, it is the passenger's sole responsibility to comply with these policies, which may vary based on transportation companies or service providers' regulations.",
		"terms_conditions_paragraph12": "All additional information regarding validity periods, conditions, exit taxes from Colombia and other countries, fees, charges, mandatory payments, preventive health measures for the destination, and assistance services must be consulted with the travel advisor or on the website www.TiquetesBaratos.com when making the reservation. This information will also be provided to the passenger in the travel documents, according to the applicable conditions.",
		"terms_conditions_paragraph13": "All prices, rates, taxes, fees, or contributions presented in this bulletin or quotation are subject to change, availability, and validity without prior notice. These must be assumed by the passenger at the time of issuing the travel documents. Restrictions and conditions apply to each published rate based on its validity.",
		"terms_conditions_paragraph14": "Hotel rates depend on the selected accommodation. Cancellation policies, penalties, restrictions, and specific conditions of the package will be informed to the passenger at the time of issuing the travel documents.",
		"terms_conditions_paragraph15": "The client declares that they fully acknowledge and accept these conditions, which constitute the sole, total, and exclusive agreement, overriding any contrary agreement or legal provision regarding the terms, conditions, and restrictions of the contracted services.",
		"terms_conditions_paragraph16": "RNT No.44091",
		"terms_neg_responsability": "Disclaimer of Liability",
		"terms_conditions_paragraph17": "The information, software, products, and services published on this website may contain inaccuracies or errors, including incorrect rates and/or prices. www.TiquetesBaratos.com and its affiliates do not guarantee accuracy and disclaim all liability for errors in the information and descriptions of content and/or services on the website, some of which are provided by third-party service providers. Additionally, www.TiquetesBaratos.com expressly reserves the right to correct any pricing errors on the website or reservations made under incorrect pricing. In such cases, if available, you will be given the opportunity to keep your reservation at the correct price or cancel the reservation without penalty, provided it meets the above conditions.",
		"terms_minors": "Unaccompanied Minors",
		"terms_link": "www.TiquetesBaratos.com",
		"terms_conditions_paragraph18": "The search engine does not allow generating fares for unaccompanied minors. These must be handled exclusively via telephone due to special conditions and restrictions that vary by airline and type of trip. Manipulating the search engine to book minors as adults may result in penalties for refunds, fare differences, and additional charges. The person who made the reservation and payment is solely responsible for covering any resulting fare differences.",
		"terms_authorization": "Authorizations, Licenses, and Trademarks",
		"terms_conditions_paragraph19": "If you are aware of an infringement of our trademark, please notify us via email at ",
		"terms_programs_computation": "Computing Programs",
		"terms_info": "<EMAIL>",
		"terms_programs": "<EMAIL>",
		"terms_conditions_paragraph20": "All software available for download from this site is copyrighted and owned by www.TiquetesBaratos.com. Its use is governed by the terms of the End User License Agreement that accompanies or is included with the software (License Agreement). No software covered by a license agreement may be installed or used without first agreeing to the terms of the agreement. For any software not accompanied by a license agreement, the owner grants the user a personal, non-transferable license for its use, according to the terms and conditions stipulated in this document.",
		"terms_services": "Services",
		"terms_conditions_paragraph21": "Additional terms and conditions may apply to reservations, the purchase of goods and services, and other sections of this site, depending on the specific service provider, and you, as a user, agree to accept such terms.",
		"terms_inscriptions": "Registrations",
		"terms_conditions_paragraph22": "Simply registering to participate in any trip under these general conditions implies that nothing should be understood or assumed as included unless it is explicitly described in the programs.",
		"terms_limitation": "Limitations on Personal and Commercial Use",
		"terms_conditions_paragraph23": "This site may only be used to make legitimate reservations or purchases and may not be used for any purposes other than those described herein. No speculative, false, or fraudulent reservations will be made. You, as a user, assume that you have the legal age required to use the site and accept the legal and financial obligations that this entails. You acknowledge that you are responsible for all activities resulting from your use of the site www.TiquetesBaratos.com, whether by you or by third parties operating under your password. The information, programming codes, products, and other services published on this portal may contain typographical errors and inaccuracies. www.TiquetesBaratos.com, its affiliates, suppliers, and collaborators periodically update this information.",
		"terms_conditions_paragraph24": "Description of terms for some of the products or services found on the website www.TiquetesBaratos.com",
		"terms_access": "Technical access requirements",
		"terms_conditions_paragraph25": "To access the Portal, the USER must have Internet access, pay the corresponding access and connection fees, and have the necessary equipment and computer systems to connect to the Network, including a suitable terminal (computer, phone, etc.) and a modem or other access device. Proper access and use of certain Portal content and services may require downloading specific software or other logical elements onto their computer. This installation will be the responsibility of the USER, and www.TiquetesBaratos.com disclaims any liability that may arise from it. The homepage always informs users about the necessary specifications to properly view and use the website.",
		"terms_air": "AIR",
		"terms_fee": '"RESTRICTED TOURIST FARE":',
		"terms_conditions_paragraph26": 'When the buyer selects the "Restricted Tourist Fare," unless expressly indicated otherwise, this fare does not allow changes, cancellations, or refunds of the booking amount (except by paying a penalty established by the airline according to the applicable fare). This means that the airline ticket cannot be used differently than contracted, including attempting to use the return flight without having used the outbound flight first.',
		"terms_adminitrative": "ADMINISTRATIVE FEE:",
		"terms_conditions_paragraph27": "An administrative fee is charged per passenger, which is non-refundable. In case of changes or modifications requiring a ticket change, this fee will be charged again.",
		"terms_flight": "Low-cost airline flights",
		"terms_conditions_paragraph28": "The regulations of low-cost airlines generally require that all children under 14 years of age must always travel accompanied by an adult and properly documented with a PASSPORT. Failure to comply with this regulation will result in boarding denial for children under 14 years old.",
		"terms_no_minors": "UNACCOMPANIED MINORS:",
		"terms_conditions_paragraph29": "(Regular flights).",
		"terms_conditions_paragraph30": "For air transportation purposes, a minor is a person who has not reached 18 years of age on the flight date.",
		"terms_conditions_paragraph31": "* BABY/INFANT (INF): A minor who, on the flight date, has not yet reached 2 years of age. Travels without occupying a seat, accompanied by a person over 18 years old, paying a variable percentage of the adult fare.",
		"terms_conditions_paragraph32": "* CHILD (CHD): A minor who has turned 2 years old but has not yet reached 12 years of age on the flight date and always travels accompanied by a person over 18 years old. Travels occupying a seat.",
		"terms_conditions_paragraph33": "* YOUNG PASSENGER (YP): A minor who has turned 12 years old but has not yet reached 18 years of age on the flight date and travels unaccompanied and/or with in-flight assistance if necessary. Pays the adult fare.",
		"terms_conditions_paragraph34": "Minor limitations per adult:",
		"terms_conditions_paragraph35": "Regular flight airlines typically allow each adult passenger a maximum of:",
		"terms_conditions_paragraph36": "One BABY (INF).",
		"terms_conditions_paragraph37": "One BABY (INF) and one CHILD (CHD) under 5 years old.",
		"terms_conditions_paragraph38": "Two CHILDREN (CHD) under 5 years old.",
		"terms_conditions_paragraph39": "For traveling with more than two CHILDREN (CHD) between 5 and 12 years old, consultation with the operating airline is required.",
		"terms_conditions_paragraph40": "Exceptionally (always subject to consultation with the corresponding airline), each adult passenger may be allowed to travel with two BABIES (INF), provided that one travels in the adult passenger's arms and the second in an approved car seat occupying the adjacent seat. The fare applied to the BABY occupying the seat will be the same as that of a CHILD (CHD).",
		"terms_conditions_paragraph41": "Consult with an advisor for other restrictions.",
		"terms_conditions_paragraph42": "Packages (Ticket + Hotel)",
		"terms_conditions_paragraph43": "Airlines may restrict mileage accumulation in certain classes due to special fares.",
		"terms_conditions_paragraph44": "Electronic payments",
		"terms_conditions_paragraph45": "The user agrees to be consulted in risk centers to validate the information provided for the purchase.",
		"terms_conditions_paragraph46": "To complete the purchase, the client will be asked for the identifying details of the passengers/travelers and their payment method. This process is carried out in several steps. Once this information is entered, and before making the purchase, the details will be displayed for verification, allowing the client to proceed with the purchase. Additionally, once the purchase is made, a confirmation email will be sent with all the details of the purchase and the invoice information, which will also be archived by www.TiquetesBaratos.com.",
		"terms_conditions_paragraph47": "The traveler declares that they are aware of and will comply with government requirements for exit, entry, and other documentation. This information is provided on the website for Colombian citizens (link Documents for Travel). In case of doubt, please contact Online Help before making any purchase or consult advisors at any office or Call Center.",
		"terms_conditions_paragraph48": "The purchase of any product will only be effective when www.TiquetesBaratos.com successfully charges the provided credit card or receives the payment via transfer, and it has been verified by the financial department. Until that moment, the purchase may be canceled by www.TiquetesBaratos.com.",
		"terms_conditions_paragraph49": "www.TiquetesBaratos.com reserves the right to request additional information from the client to verify their purchase.",
		"terms_conditions_paragraph50": "If the credit card charge cannot be processed (transaction declined), the client should be aware that without payment, the purchase may be canceled by www.TiquetesBaratos.com and/or the service provider.",
		"terms_conditions_paragraph51": "The payment for the airline ticket(s) is made via credit card before the issuance of the ticket(s).",
		"terms_conditions_paragraph52": "Not all prices displayed on the website include airport taxes and other charges. Visa expenses and departure taxes from a country, which may be payable in local currency or US dollars at the airport, are not included. Any changes in the applicable fare due to delays in payment of the reservation or variations in airport tax amounts from the time of booking until the ticket is issued will be passed on to the client.",
		"terms_conditions_paragraph53": "Currency Converter",
		"terms_conditions_paragraph54": "Exchange rates are based on various public sources and should be used as a guide only. Rates are not exact, and actual rates may vary. Currency exchange rates are not updated daily. Please check the date in the currency converter function for the last update. The information provided by this application is considered accurate, but www.TiquetesBaratos.com, its affiliates, and/or our respective suppliers do not guarantee its accuracy. When using this information for any financial purpose, we advise consulting a qualified professional to verify exchange rate accuracy. We do not authorize the use of this information for any purpose other than personal use, and resale, redistribution, or commercial use of this information is expressly prohibited.",
		"terms_conditions_paragraph55": "Travel Destinations",
		"terms_conditions_paragraph56": "It is recommended that travelers check the warnings for their chosen travel destination.",
		"terms_conditions_paragraph57": "By selling travel services, www.TiquetesBaratos.com does not represent or guarantee that travel to these destinations is advisable or risk-free and is not responsible for any damages or losses that may result from traveling to these locations.",
		"terms_conditions_paragraph58": "DOCUMENTATION: All passengers must carry the necessary personal documentation. The Operator disclaims all responsibility for information, withdrawal, and visa rejections. The user is responsible for the documents provided by the Operator.",
		"terms_conditions_paragraph59": "LUGGAGE: For land transportation, the Operator expressly disclaims any responsibility in case of loss, damage, or theft under any circumstances. We recommend that passengers be present during the handling, loading, and unloading of luggage. For air transportation, the IATA (International Air Transport Association) regulations will apply. Clarifications: 1. The porter service, when included in the program, is provided at airports and other boarding locations. Hotels have their own staff for this purpose. 2. Tips represent appreciation for efficient service received and are always voluntary. Airport check-in must comply with the times established by the airlines.",
		"terms_conditions_paragraph60": "CANCELLATIONS: The user may cancel requested and contracted services at any time and will be entitled to a refund of the deposit, provided that the contracted program does not state otherwise. In case of airline ticket cancellations, airline company regulations and relevant authorities (IATA) will apply. For chartered flight operations, the applicable cancellation clauses will be those stated in the program.",
		"terms_conditions_paragraph61": "CLAIMS: Claims must be submitted in writing or via email. The information can be found in our Customer Service section.",
		"terms_conditions_paragraph62": "AGREEMENT: By requesting enrollment in any travel program sold by www.TiquetesBaratos.com, the client and/or passenger declares that they are aware of and accept all terms of these 'General Conditions,' which are available for printing, as well as the special conditions described in the travel program or specific travel project.",
		"terms_conditions_paragraph63": "Connections and Links to Third-Party Portals",
		"terms_conditions_paragraph64": "This site may contain links that connect to portals operated by third parties independent of www.TiquetesBaratos.com.",
		"terms_conditions_paragraph65": "www.TiquetesBaratos.com has no control over these and is not responsible for their content. The inclusion of these sites on www.TiquetesBaratos.com does not imply any association with these third parties or their operators. www.TiquetesBaratos.com has links to other websites; please be aware that when entering one of these links, you are accessing a portal external to www.TiquetesBaratos.com and, therefore, it has no responsibility over it. We advise you to read the privacy policies of these sites, as they may differ from those offered by www.TiquetesBaratos.com. You are solely responsible for maintaining the confidentiality of your passwords and account information. Please be very careful with this information.",
		"terms_conditions_paragraph66": "Advertising",
		"terms_conditions_paragraph67": "To file any claim related to advertising content inserted on the Portal, you may contact the following email address:",
		"terms_conditions_paragraph68": "and from there, contact with the advertiser will be established.",
		"terms_conditions_paragraph69": "Amadeus and/or Sabre",
		"terms_conditions_paragraph70": "The information provided on our website regarding schedules, availability, and online reservations for cars, hotels, and flights is the property of the Global Distribution System Amadeus and/or Sabre. Neither Amadeus and/or Sabre’s data nor any other data accessed through this service may be reproduced, sold, transmitted, modified, redistributed, republished, or commercially exploited in any way without prior written consent from Amadeus and/or Sabre and, when necessary, other travel information providers.",
		"terms_conditions_paragraph71": "This website has been created with data obtained from various sources. Amadeus and/or Sabre do not guarantee the availability, accessibility, accuracy, timeliness, or any other aspect of the information contained herein.",
		"terms_conditions_paragraph72": "Security and Privacy",
		"terms_conditions_paragraph73": "All information you provide on our site at the time of registration is transmitted via SSL (Security Socket Layer). This proven encryption system allows your device to encode the information before it is sent to our system.",
		"terms_conditions_paragraph74": "The encrypted information reaches our servers, which have all security patches updated, unused ports closed, and protected with a firewall. It is then restored to its original form and stored in our database.",
		"terms_conditions_paragraph75": "Security Risks to Consider When Conducting Transactions on the Internet...",
		"terms_conditions_paragraph76": "It is possible that a user may be deceived through emails or DNS server spoofing to visit a fake site that mimics the same design, but where card details are entered into the fraudulent system, stealing the cardholder’s information. Therefore, it is important to promote awareness that users should only conduct transactions directly through known domains to reduce risks.",
		"terms_conditions_paragraph77": "It is possible that the computer where the user is making the transaction has spyware or malicious software installed without the user’s knowledge, which captures all keystrokes or input device data and sends it to a network or host on the internet. For this reason, it is recommended that transactions be carried out on a home or office computer whenever possible.",
		"terms_conditions_paragraph78": "User impersonation may occur, or the user may deny having sent and/or received the transaction, allowing it to be used by a third party.",
		"terms_conditions_paragraph79": "Help Service",
		"terms_conditions_paragraph80": "The website www.TiquetesBaratos.com offers an online help service to resolve any inquiries. This service is available 24 hours a day, 7 days a week.",
		"terms_conditions_paragraph81": "Use of bulletin boards, chat rooms, and other communication forums",
		"terms_conditions_paragraph82": "If the site contains bulletin boards, chat rooms, and other message communication media or forums, you, as a user, agree to send and receive only messages and material that are appropriate and relevant to the forum. Examples of forum misuse include:",
		"terms_conditions_paragraph83": "Defaming, insulting, abusing, harassing, threatening, or violating the legal rights (such as privacy and publicity rights) of third parties.",
		"terms_conditions_paragraph84": "Posting, distributing, advertising, or disseminating any defamatory, discriminatory, obscene, indecent, or illegal information or material.",
		"terms_conditions_paragraph85": "Uploading files that contain software or other material protected by intellectual property laws (or copyright) unless you own the rights or have received all necessary permissions for use.",
		"terms_conditions_paragraph86": "Uploading files that contain viruses, are corrupted, or contain any kind of programs or code that may damage the operations of third-party computers.",
		"terms_conditions_paragraph87": "Removing any author attributions, legal notices, ownership designations, and/or trademarks from any system file.",
		"terms_conditions_paragraph88": "Falsifying the origin of the source, software, or any material contained in the uploaded file.",
		"terms_conditions_paragraph89": "Advertising or offering the sale of goods and/or services, conducting surveys, contests, or sending chain letters.",
		"terms_conditions_paragraph90": "Downloading any file uploaded by another forum user with knowledge that it should not legally be distributed in that manner.",
		"terms_conditions_paragraph91": "As a user, you agree that forums are for public, not private, communications. Likewise, you acknowledge that chats, conferences, bulletins, and other communications among users are not endorsed by www.TiquetesBaratos.com or its affiliates and that such communications will not be reviewed, virus-checked, and/or approved by www.TiquetesBaratos.com or its affiliates. www.TiquetesBaratos.com reserves the right to remove any forum content without prior notice, as well as the full right to deny access to any portal user or any part of it at its discretion, without prior notice.",
		"terms_conditions_paragraph92": "Use of cookie technology",
		"terms_conditions_paragraph93": "www.TiquetesBaratos.com reserves the right to use so-called 'cookies' in any type of use of the portal. Cookies are small data files generated on the user’s computer that allow us to collect the following information:",
		"terms_conditions_paragraph94": "The date and time of the user's last visit to our website.",
		"terms_conditions_paragraph95": "The content layout the user selected during their first visit to our website.",
		"terms_conditions_paragraph96": "Security elements involved in access control to restricted areas.",
		"terms_conditions_paragraph97": "However, users are informed of the possibility of disabling cookie usage on their computer. Through these 'cookies,' other users, once registered, can see whether you are online or not and thus contact you more quickly and efficiently.",
		"terms_conditions_paragraph98": "Data Protection",
		"terms_conditions_paragraph99": "www.TiquetesBaratos.com, in accordance with the provisions of Decree 1377 of 2013, implements its data protection policy, which aims to ensure the protection and storage of databases containing personal or socially relevant information and to safeguard the fundamental rights of our customers.",
		"terms_conditions_paragraph100": "Conditions of the Code-Share Agreement",
		"terms_conditions_paragraph101": "The Commercial Cooperation Agreement is an agreement through which two or more airline carriers agree to establish one or more joint work arrangements to achieve better business opportunities.",
		"terms_conditions_paragraph102": "The Code-Share Agreement is an agreement through which two or more airline carriers market one or more flights that are operated by only one of them on authorized routes, jointly using their designation and identification codes.",
		"terms_conditions_paragraph103": "Commercial Cooperation Agreements, regardless of their modality, and Code-Share Agreements must be in writing and submitted and registered with the Civil Aeronautics Directorate before their execution.",
		"terms_conditions_paragraph104": "In Code-Share Agreements, the entity responsible for operating the aircraft is the party that actually carries out the flights in question. The parties are jointly and severally liable to passengers and transported cargo, without prejudice to the obligations established in the respective contract.",
		"terms_conditions_paragraph105": "Colombian Air Transport Contract",
		"terms_conditions_paragraph106": "Here you can review the contract.",
		"terms_conditions_paragraph107": "Recommendation for Advance Airport Arrival",
		"terms_conditions_paragraph108": "You must be at the airport at least 2 hours before departure for domestic flights and 3 hours before for international flights.",
		"terms_conditions_paragraph109": "Right of Withdrawal Law",
		"terms_conditions_paragraph110": "Passenger rights to exercise withdrawal or cancellation.",
		"terms_conditions_paragraph111": "Withdrawal.",
		"terms_conditions_paragraph112": "The general right of withdrawal in purchases, as established in the Consumer Statute in Colombia, Law 1480, among other requirements, stipulates that it must be exercised by the consumer within five (5) days after the transaction is completed, provided that the services are not acquired to be provided within those five (5) days. If the right of withdrawal is exercised, the contract will be terminated, and the money paid by the consumer will be refunded. The costs associated with the transaction will be covered by the consumer, meaning that the administrative fee will not be refunded as it is a mandatory charge for issuing airline tickets applicable to the sale of national and international air transport services. This refund will be processed within thirty (30) calendar days from the moment the right was exercised.",
		"terms_conditions_paragraph113": "Cancellation.",
		"terms_conditions_paragraph114": "In accordance with the provisions of the RAC, the passenger may exercise their right to cancel before the start of the service by providing at least twenty-four (24) hours' notice before the flight. This does not apply in the case of PROMOTIONAL OR NON-REFUNDABLE FARES. The carrier may withhold 10% of the amount received as a fare charge, excluding taxes, fees, and the administrative fee. The travel agency, acting as an intermediary in the air transport contract, will process the refund once the airline reimburses the money, without prejudice to the thirty (30) calendar-day period from the communication of the cancellation."
	},
	"method_payments": {
		"title": "Payment methods",
		"tc_td": "We accept all credit and debit cards",
		"until": "Until",
		"quotas": "quotas",
		"payments_options": "Installment payment options depend on the service selected and the value of the purchase",
		"have_credit_card": "Don't you have a credit card?",
		"more_payments_methods": "We have more payment methods:",
		"bancolombia_btn": "Bancolombia Button",
		"bancolombia_pay_online": "Pay online with your Bancolombia savings or current account",
		"PSE_payment": "Online payment with PSE",
		"PSE_pay_online": "Pay online with your checking or savings account",
		"cash": "Cash",
		"pay_cash": "Pay in cash at your nearest location:",
		"effecty_pay": "Pay in cash with the collection code 112232 and the payment reference at any effective point nationwide",
		"bank_deposit": "Bank Deposit",
		"deposit_pay": "Pay online with your checking or savings account",
	},
	"categories": {
    "paymentsAndReservations": "Payments and Reservations",
    "changesAndCancellations": "Changes and Cancellations",
    "baggageAndProcesses": "Baggage and Processes",
    "billing": "Billing",
    "onlineSecurity": "Online Security",
    "additionalServices": "Additional Services"
  	},
	"faq": {
    "searchPlaceholder": "Enter a keyword or question",
   "paymentsAndReservations": [
    {
      "question": "How much time do I have to pay for my ticket after making the reservation?",
      "answer": "You have 24 hours to make the payment after making the reservation."
    },
    {
      "question": "How can I buy and pay for my ticket online?",
      "answer": "You can buy and pay for your ticket through our website using a credit or debit card."
    },
    {
      "question": "If I already made a reservation, can I continue with the payment process for my ticket?",
      "answer": "Yes, if you've made a reservation, you just need to go to the payment section and continue the process."
    },
    {
      "question": "Can I make the online payment for my reservation with two (2) credit or debit cards?",
      "answer": "No, you can only use one credit or debit card per payment."
    },
    {
      "question": "Can I make the online payment for two (2) or more reservations with the same credit or debit card?",
      "answer": "Yes, you can pay for multiple reservations using the same card."
    },
    {
      "question": "Can I make the payment for my reservations by phone?",
      "answer": "No, we do not offer phone payment, only online payments."
    },
    {
      "question": "Can I pay for my reservation with a credit card?",
      "answer": "Yes, you can pay for your reservation with a credit card."
    },
    {
      "question": "How do I pay for my ticket if the transaction with my card was rejected or canceled?",
      "answer": "If the transaction is rejected, you can try another payment method or check your card details."
    },
    {
      "question": "Can I pay for a ticket for a family member or friend?",
      "answer": "Yes, you can pay for the ticket on behalf of someone else."
    },
    {
      "question": "Can I pay for a ticket at night?",
      "answer": "Yes, online payments are available 24/7."
    },
    {
      "question": "How can I pay?",
      "answer": "You can pay online with a credit card, debit card, or other payment methods available on the website."
    },
    {
      "question": "Can I change the payment method?",
      "answer": "No, once the payment is made, you cannot change the method."
    },
    {
      "question": "If I live outside Colombia, how can I pay for a ticket and how can I claim it?",
      "answer": "You can pay from any country using international payment methods, and the ticket will be sent to your email."
    },
    {
      "question": "Can I pay for a ticket in another country and have someone else claim it in a different location?",
      "answer": "Yes, you can pay from another country, and the person traveling can claim the ticket."
    },
    {
      "question": "If my company is interested in corporate accounts, what should we do?",
      "answer": "You should contact our sales department for corporate accounts."
    },
    {
      "question": "How are corporate accounts handled?",
      "answer": "Corporate accounts come with exclusive benefits such as discounts, consolidated billing, and more."
    }
  ],
  "changesAndCancellations": [
    {
      "question": "What happens if I cancel my flight? How much time do I have to cancel?",
      "answer": "Cancellation depends on the selected fare. Generally, you have up to 24 hours before the flight to cancel."
    }
  ],
  "baggageAndProcesses": [
    {
      "question": "How early should I be at the airport before boarding my flight?",
      "answer": "We recommend arriving at least 2 hours before domestic flights and 3 hours for international flights."
    },
    {
      "question": "What is the maximum weight allowed for my baggage?",
      "answer": "The maximum allowed weight is 23 kg per bag for domestic flights and 30 kg for international flights."
    },
    {
      "question": "What should I do if my baggage exceeds the allowed weight?",
      "answer": "You will need to pay an additional charge for excess baggage weight."
    },
    {
      "question": "How can I check if I have a reservation?",
      "answer": "You can verify your reservation by entering the reservation code on our website."
    },
    {
      "question": "What happens if I miss my flight?",
      "answer": "If you miss your flight, you will need to contact customer service for rebooking options."
    },
    {
      "question": "If I bought my ticket on an economy fare and miss the flight, what should I do?",
      "answer": "If you miss the flight, you will need to pay an additional fee to reschedule your flight depending on availability."
    }
  ],
  "billing": [
    {
      "question": "What should I do to request my invoice or payment receipt?",
      "answer": "You can request your invoice through our support section on the website."
    }
  ],
  "onlineSecurity": [
    {
      "question": "What security do I have when making my online purchase?",
      "answer": "Our website uses SSL encryption to ensure the security of your transactions."
    },
    {
      "question": "What companies validate the security on www.TiquetesBaratos.com?",
      "answer": "We are validated by companies like Norton and McAfee to ensure the security of our transactions."
    },
    {
      "question": "Can I make a payment any day and at any time?",
      "answer": "Yes, you can make a payment online 24/7."
    },
    {
      "question": "Does electronic payment have any value for me as a buyer?",
      "answer": "Electronic payment is fast, secure, and efficient, ensuring a hassle-free process."
    },
    {
      "question": "What should I do if my transaction did not complete?",
      "answer": "If the transaction doesn't complete, we recommend reviewing your payment details or trying another method."
    },
    {
      "question": "What should I do if I didn’t receive the payment receipt?",
      "answer": "If you didn't receive the payment receipt, check your email or contact our support team."
    }
  ],
  "additionalServices": [
    {
      "question": "How can I add additional services to my reservation (seat selection, extra baggage, special meals)?",
      "answer": "You can add additional services during the booking process or in the reservation management section online."
    },
    {
      "question": "Can I make a reservation for an underage child traveling alone?",
      "answer": "Yes, you can make a reservation for a minor, but it requires special authorization."
    },
    {
      "question": "How can I book special assistance (wheelchair, assistance for elderly passengers, etc.)?",
      "answer": "You can book special assistance during the ticket purchase or by contacting our customer service."
    }
  ]
    }
};

window.__pt.settings.formData = {
	"Nationality": "co",
	"nation_options": () => { return [{ "text": "mexico", "value": "mx" }] }, /* () => {
        let nations = [];
        const countryData = window.intlTelInputGlobals.getCountryData();
        for (let index = 0; index < countryData.length; index++) {
            nations.push({ "text": countryData[index].name, "value": countryData[index].iso2 });
        }
        return nations;
    } */
	"day_selected": "0",
	"day_options": [
		{ "text": "--", "value": "0" },
		{ "text": "1", "value": "01" },
		{ "text": "2", "value": "02" },
		{ "text": "3", "value": "03" },
		{ "text": "4", "value": "04" },
		{ "text": "5", "value": "05" },
		{ "text": "6", "value": "06" },
		{ "text": "7", "value": "07" },
		{ "text": "8", "value": "08" },
		{ "text": "9", "value": "09" },
		{ "text": "10", "value": "10" },
		{ "text": "11", "value": "11" },
		{ "text": "12", "value": "12" },
		{ "text": "13", "value": "13" },
		{ "text": "14", "value": "14" },
		{ "text": "15", "value": "15" },
		{ "text": "16", "value": "16" },
		{ "text": "17", "value": "17" },
		{ "text": "18", "value": "18" },
		{ "text": "19", "value": "19" },
		{ "text": "20", "value": "20" },
		{ "text": "21", "value": "21" },
		{ "text": "22", "value": "22" },
		{ "text": "23", "value": "23" },
		{ "text": "24", "value": "24" },
		{ "text": "25", "value": "25" },
		{ "text": "26", "value": "26" },
		{ "text": "27", "value": "27" },
		{ "text": "28", "value": "28" },
		{ "text": "29", "value": "29" },
		{ "text": "30", "value": "30" },
		{ "text": "31", "value": "31" },

	],
	"month_selected": "0",
	"month_options": [
		{ "text": "--", "value": "0" },
		{ "text": "01", "value": "01" },
		{ "text": "02", "value": "02" },
		{ "text": "03", "value": "03" },
		{ "text": "04", "value": "04" },
		{ "text": "05", "value": "05" },
		{ "text": "06", "value": "06" },
		{ "text": "07", "value": "07" },
		{ "text": "08", "value": "08" },
		{ "text": "09", "value": "09" },
		{ "text": "10", "value": "10" },
		{ "text": "11", "value": "11" },
		{ "text": "12", "value": "12" },
	],
	"year_selected": "0",
	"year_options": (age = 0) => {

		let today = new Date();
		let year = today.getFullYear();
		let yearUntil = age == 0 ? 1900 : (year - age - 2);
		let years = [];

		years.push({ "text": "--", "value": "0" })
		for (let i = year; i > yearUntil; i--) {
			years.push({ "text": String(i), "value": String(i) })

		}
		return years;
	}
	
}