﻿using ProtoBuf;

namespace TBFront.Models.HotelFacade.Response
{
    [ProtoContract]
    public class ContentHotelResponse
    {

        [ProtoMember(1)]
        public string Description { get; set; } = string.Empty;

        [ProtoMember(2)]
        public List<Gallery> Gallery { get; set; } = [];

        [ProtoMember(3)]
        public int HotelId { get; set; }

        [ProtoMember(4)]
        public List<string> ImportantFacilities { get; set; } = [];

        [ProtoMember(5)]
        public LocationInfo Location { get; set; } = new LocationInfo();

        [ProtoMember(6)]
        public string Name { get; set; } = string.Empty;

        [ProtoMember(7)]
        public List<PlaceContainers> PlaceContainers { get; set; } = [];

        [ProtoMember(8)]
        public int PlaceId { get; set; }

        [ProtoMember(9)]
        public double Stars { get; set; }

        [ProtoMember(10)]
        public string Title { get; set; } = string.Empty;

        [ProtoMember(11)]
        public string Uri { get; set; } = string.Empty;

        [ProtoMember(12)]

        public string Message { get; set; } = string.Empty;

        [ProtoMember(13)]
        public string Redirect { get; set; } = string.Empty;

        [ProtoMember(14)]
        public bool IsActive { get; set; }

    }

  

    [ProtoContract]
    public class Gallery
    {
        [ProtoMember(1)]
        public string BaseUri { get; set; } = string.Empty;

        [ProtoMember(2)]
        public string Caption { get; set; } = string.Empty;

        [ProtoMember(3)]
        public string CloudUri { get; set; } = string.Empty;

        [ProtoMember(4)]
        public string Description { get; set; } = string.Empty;

        [ProtoMember(5)]
        public string Extension { get; set; } = string.Empty;

        [ProtoMember(6)]
        public int PhotoId { get; set; }

        [ProtoMember(7)]
        public string ThumbnailCloudUri { get; set; } = string.Empty;
    }

    [ProtoContract]
    public class PlaceContainers
    {
        [ProtoMember(1)]
        public string DisplayText { get; set; } = string.Empty;

        [ProtoMember(2)]
        public int Id { get; set; }

        [ProtoMember(3)]
        public int Type { get; set; }

        [ProtoMember(4)]
        public string Uri { get; set; } = string.Empty;
    }



    [ProtoContract]
    public class LocationInfo
    {
        [ProtoMember(1)]
        public string Country { get; set; } = string.Empty;

        [ProtoMember(2)]
        public string Latitude { get; set; } = string.Empty;

        [ProtoMember(3)]
        public string Longitude { get; set; } = string.Empty;

        [ProtoMember(4)]
        public string City { get; set; } = string.Empty;

        [ProtoMember(5)]
        public string Street { get; set; } = string.Empty;

        [ProtoMember(6)]
        public string PostalCode { get; set; } = string.Empty;

        [ProtoMember(7)]
        public string State { get; set; } = string.Empty;

        [ProtoMember(8)]
        public string CountryCode { get; set; } = string.Empty;
    }
}
