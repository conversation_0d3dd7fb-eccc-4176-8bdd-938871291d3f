﻿using Microsoft.Extensions.Options;
using System.Text.Json;
using TBFront.Interfaces;
using TBFront.Models;
using TBFront.Models.Common;
using TBFront.Models.Configuration;
using TBFront.Models.Request;
using TBFront.Models.Response;
using TBFront.Options;
using TBFront.Types;

namespace TBFront.Mappers
{
    public class FlightContentMapper
    {
        private readonly SettingsOptions _settings;
        private readonly SettingsOptions _options;
        private readonly ICommonHandler _commonHandler;
        private readonly string _sourceDefault = "default";


        public FlightContentMapper(IOptions<SettingsOptions> settings, IOptions<SettingsOptions> options, ICommonHandler commonHandler)
        {
            _settings = settings.Value;
            _options = options.Value;
            _commonHandler = commonHandler;
        }

        public async Task<CheckoutQuoteResponse> MapResponsesToQuoteResponse(CheckoutQuoteRequest requestQuote, string sessionId, CancellationToken token)
        {
            var CheckoutQuoteResponse = new CheckoutQuoteResponse();

            var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, token);
            var channelConfiguration = await _commonHandler.QueryAsync(new ChannelConfiguration { Id = userSelection.Context.Location.Country }, token);

            var channelConfig = channelConfiguration.ChannelConfig.Find(s => s.Source.Equals(requestQuote.ChkSource.ToString(), StringComparison.CurrentCultureIgnoreCase));
            channelConfig ??= channelConfiguration.ChannelConfig.Find(s => s.Source.Equals(_sourceDefault));

            var checkoutData = GetCheckoutData(requestQuote.CheckoutData);
            CheckoutQuoteResponse.Quote = GetContent(checkoutData, requestQuote, userSelection, channelConfig, channelConfiguration);
            CheckoutQuoteResponse.Quote.Rate = GetRate(requestQuote, checkoutData);
            CheckoutQuoteResponse.Quote.FlightItinerary = FlightMapper.FlightItineary(checkoutData.Summary, CheckoutQuoteResponse.Quote.Rate.IsRoundtrip, _options);
            CheckoutQuoteResponse.Quote.ExtraInfoFlight = FlightMapper.ExtraInfoFlight(checkoutData.Summary, CheckoutQuoteResponse.Quote.Rate.IsRoundtrip);
            CheckoutQuoteResponse.Flights = checkoutData.Flights;
            CheckoutQuoteResponse.Fares = checkoutData.Fares;
            CheckoutQuoteResponse.Quote.FlightItinerary.FamilyFareCode = checkoutData.Fares.FirstOrDefault().Value.FamilyFare.Code;
            CheckoutQuoteResponse.Quote.QuoteTaskID = checkoutData.Summary.QuoteTaskID;
            CheckoutQuoteResponse.Quote.SessionId = sessionId;
            CheckoutQuoteResponse.Quote.FlightItinerary.Starting.FamilyFareCode = checkoutData.Fares.FirstOrDefault().Value.FamilyFare.Code;
            CheckoutQuoteResponse.Quote.QuoteToken = checkoutData.Summary.QuoteToken;
            CheckoutQuoteResponse.Quote.Multiple = checkoutData.Summary?.Multiple;

            if (CheckoutQuoteResponse.Quote.Rate.IsRoundtrip)
            {
                CheckoutQuoteResponse.Quote.FlightItinerary.Returning.FamilyFareCode = checkoutData.Fares.LastOrDefault().Value.FamilyFare.Code;
            }

            return CheckoutQuoteResponse;
        }


        private QuoteApiResponse GetContent(CheckoutData responseContent, CheckoutQuoteRequest requestQuote, UserSelection site, ChannelOptions channel, ChannelConfiguration channelConfiguration)
        {
            var quote = new QuoteApiResponse();

            if (responseContent == null)
            {
                throw SetError(StatusType.HOTEL_NOT_FOUND);
            }

            quote.CheckIn = requestQuote.StartingFromDateTime;
            quote.CheckOut = requestQuote.ReturningFromDateTime;
            quote.Days = (requestQuote.CheckOut - requestQuote.CheckIn).Days;
            quote.Paxes = requestQuote.Paxes;
            quote.Adults = requestQuote.Paxes.Sum(x => x.Adults);
            quote.Children = requestQuote.Paxes.Sum(x => x.Children.Count);
            quote.TotalPaxes = quote.Adults + quote.Children;
            quote.Currency = channelConfiguration.Currency;
            quote.Language = site.Culture.Language;
            quote.ChannelId = requestQuote.IsMobile ? channel.Mobile.ChannelId : channel.Desktop.ChannelId;
            quote.Culture = site.Culture.InternalCultureCode;
            quote.Organization = _settings.OrganizationId;
            quote.QuoteExpirationDate = DateTime.UtcNow.AddHours(_options.HoursDepositLimit);
            quote.Site = channelConfiguration.Domain;
            quote.Source = requestQuote.ChkSource == 1 ? "Desktop" : "Mobile";
            quote.Token = responseContent.Summary.Token;
            quote.FareKey = responseContent.Summary.FareKey;
            quote.ReferralUrl = requestQuote.ReferralUrl;
            quote.IsDomesticRoute = requestQuote.IsDomesticRoute;
            quote.IsRoundTrip = requestQuote.IsRoundTrip;

            return quote;
        }

        private RateSelect GetRate(CheckoutQuoteRequest requestQuote, CheckoutData checkout)
        {
            var rateSelect = new RateSelect();
            var roomCount = requestQuote.Paxes.Count;
            var summary = checkout.Summary;
            var campaignToken = "";
            var days = (requestQuote.StartingFromDateTime - requestQuote.ReturningFromDateTime).TotalDays;
            var isRoundtrip = string.Equals(requestQuote.TripMode, "RoundTrip", StringComparison.OrdinalIgnoreCase);

            var totalAmountArrival = checkout.Summary.Origin.Fares.Sum(fa => fa.AverageAmount);
            var totalAmountDeparture = isRoundtrip ? checkout.Summary.Destination.Fares.Sum(fa => fa.AverageAmount) : 0;
            var totalAmount = requestQuote._lastFlightRate;

            rateSelect.IsAvailable = true;
            rateSelect.AverageRate = totalAmount;
            rateSelect.AverageRateWithTaxes = totalAmount;
            rateSelect.TotalAmount = totalAmount;
            rateSelect.PrePromotionalRate = totalAmount;
            rateSelect.PrePromotionalRateWithTaxes = totalAmount;
            rateSelect.HasTaxes = true;
            rateSelect.Cost = totalAmount;
            rateSelect.DiscountAmount = 0;
            rateSelect.Amount = totalAmount;
            rateSelect.CampaignToken = campaignToken;
            rateSelect.Site = requestQuote.Site;
            rateSelect.ChkSource = requestQuote.ChkSource;
            rateSelect.Country = "";
            rateSelect.Breakdown = GetBreakDown(summary, isRoundtrip);
            rateSelect.IsRoundtrip = isRoundtrip;

            return rateSelect;
        }

        private List<FareDetail> GetBreakDown(Summary summary, bool roundtrip)
        {
            var mergedFareDetails = new List<FareDetail>();

            return mergedFareDetails;

            mergedFareDetails = MergeFares(summary.Origin, mergedFareDetails);

            if (roundtrip)
            {
                mergedFareDetails = MergeFares(summary.Destination, mergedFareDetails);
            }

            return mergedFareDetails;
        }

        private static List<FareDetail> MergeFares(Models.FlightCheckout flight, List<FareDetail> mergedFareDetails)
        {

            foreach (var fare in flight.Fares)
                foreach (var fareDetail in fare.FareDetails)
                {
                    var existingFareDetail = mergedFareDetails.FirstOrDefault(fd => fd.DisplayText == fareDetail.DisplayText);
                    if (existingFareDetail != null)
                    {
                        existingFareDetail.Amount += fareDetail.Amount;
                    }
                    else
                    {
                        mergedFareDetails.Add(new FareDetail
                        {
                            DisplayText = fareDetail.DisplayText,
                            Amount = fareDetail.Amount
                        });
                    }
                }

            return mergedFareDetails;
        }

        private CheckoutData GetCheckoutData(string checkoutHash)
        {

            if (string.IsNullOrEmpty(checkoutHash))
            {
                return new CheckoutData();
            }
            var cast = checkoutHash.Replace(@"\", "");
            var checkout = JsonSerializer.Deserialize<CheckoutData>(cast, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
            return checkout;

        }

        private static ArgumentException SetError(string error)
        {
            throw new ArgumentException(error);
        }
    }
}
