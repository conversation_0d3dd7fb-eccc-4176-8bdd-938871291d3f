﻿using TBFront.Models.Configuration;
using TBFront.Models.ContentDeliveryNetwork.Exchange;
using TBFront.Options;

namespace TBFront.Interfaces
{
    public interface ICommonHandler :
       IQueryHandlerAsync<Options.Currency, Options.Currency>,
       IQueryHandlerAsync<Culture, Culture>,
       IQueryHandlerAsync<ChannelOptions, ChannelOptions>,
       IQueryHandlerAsync<ChannelConfiguration, ChannelConfiguration>,
       IQueryHandlerAsync<ExchangeRequest, ExchangeClient>,
       IQueryHandlerAsync<UserSelectionRequest, UserSelection>,
       IQueryHandlerAsync<string, Culture>
    {
    }
}
