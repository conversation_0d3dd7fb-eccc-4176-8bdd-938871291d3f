import { setDatalayer, UTILS, flightToString  } from "./main"
import { searchParams } from '../../utils/helpers';
import _ from 'lodash';

export default class FlightListAnalytics {


    constructor() {
        this.settings = window.__pt.settings.site;
        this.request = window.__pt.data || {};
        this.hotel = window.__pt.hotel || {};
        this.places_info = window.__pt.places_info || {};
        this.box = window.__pt.box || {};
        this.isDomestic = false;//this.places_info.returning.country == this.places_info.starting.country ;
    }

    changeFlightInit(resFlight = { }){

        const paramsObj = searchParams();
        let urlToDetail =  `${this.settings.pathHotelDetail}${paramsObj.uri}${this.settings.uriHotelDetailPath}`;
        const startingFlight = _.get(resFlight, ['packageSelected', 'flightItinerary', 'stating'], {});
        const returningFlight = _.get(resFlight, ['packageSelected', 'flightItinerary', 'returning'], {});
        let event = {
            "event": UTILS.events.gtmEvent,
            "eventName": UTILS.events.gtmEvent,
            "eventCategory": UTILS.categories.chage_flight,
            "eventAction": `${_.get(startingFlight, ['departure', 'airport'], '' )}-${_.get(returningFlight, ['departure', 'airport'], '')} | ${urlToDetail}`,
            "eventLabel": `${flightToString(startingFlight)} || ${flightToString(returningFlight)}`,
            "eventExtra": resFlight.packageSelected.packageRate.totalAmount,
            "eventSource": UTILS.misc.source_list
        }

        setDatalayer(event);
    }

    changeFlightChangeFilter(filtersApplied){ 
        // 1 aerolineas, 2 escalas, 3 vuelos ida, 4 vuelos regreso
        let airline = filtersApplied.filter(fa => fa.groupId == 1 ).map(fa => fa.uri).join(",");
        let scale = filtersApplied.filter(fa => fa.groupId == 2 ).map(fa => fa.uri).join(",");
        let startTime = filtersApplied.filter(fa => fa.groupId == 3 ).map(fa => fa.uri).join(",");
        let returnTime = filtersApplied.filter(fa => fa.groupId == 4 ).map(fa => fa.uri).join(","); 
        let event = {
            "event": UTILS.events.gtmEvent,
            "eventName": UTILS.events.gtmEvent,
            "eventCategory": UTILS.categories.change_flight_filter,
            "eventExtra": '',
            "eventAction": `o:${this.places_info.starting.code} | d:${this.places_info.returning.code} | o:${this.places_info.starting.description} | d:${this.places_info.returning.description} ` +
             ` | o:${this.places_info.starting.placeId} | d:${this.places_info.returning.placeId} | ci:${this.request.checkIn} | co:${this.request.checkOut} | r:${this.request.rooms} ` +
             ` | a:${this.request.adults} | k:${this.request.kids} | kwId:0 | kwT:0`,
            "eventLabel": `Aerolineas{${airline ? airline : "All"}}|Escalas{${scale ? scale : "All"}}|Horarios ida{${startTime ? startTime : "All"}}|Horarios regreso{${returnTime ? returnTime : "All"}}`
        }

        setDatalayer(event);
    }

    changeFlightShowModalUpsell(resFlight, resUpsell, flights , groupResumenSelected){ 
        const startingFlight =_.get(flights, ['outboundFlight'], {});
        const returningFlight = _.get(flights, ['returningFlight'], {});
        let event = {
            "event": UTILS.events.gtmEvent,
            "eventName": UTILS.events.gtmEvent,
            "eventExtra": '',
            "eventCategory": UTILS.categories.upsell,
            "eventAction": UTILS.actions.show_modal,
            "eventLabel": `${resUpsell.length} upsell options | ${this.isDomestic ? "Domestic" : "International" } ` +
            ` | ${_.get(startingFlight, ['departure', 'airport'], '' )}-${_.get(returningFlight, ['departure', 'airport'], '')} | ${startingFlight.airlineCode} | currentFare: ${groupResumenSelected.familyFareCode} ` +
            ` | currentRate:${groupResumenSelected.rate.totalAmount} | flights:${startingFlight.flightNumber}-${returningFlight.flightNumber} ` +
            ` | ${this.request.checkIn} | ${this.request.checkOut} | adults:${this.request.adults} | kids:${this.request.kids}`
        }
        setDatalayer(event);
    }

    changeFlightSelectModalUpsell(upsellOptions, flights, upsellSelected, groupResumenSelected){
        const startingFlight =_.get(flights, ['startingFlight'], {});
        const returningFlight = _.get(flights, ['returningFlight'], {});

        let event = {
            "event": UTILS.events.gtmEvent,
            "eventName": UTILS.events.gtmEvent,
            "eventExtra": '',
            "eventCategory": UTILS.categories.upsell,
            "eventAction": UTILS.actions.click_modal_family,
            "eventLabel": `${upsellOptions} upsell options | ${this.isDomestic ? "Domestic" : "International" } | ${_.get(startingFlight, ['departure', 'airport'], '' )}-${_.get(returningFlight, ['departure', 'airport'], '')} `+
            ` | ${startingFlight.airlineCode} | currentFare:${groupResumenSelected.familyFareCode}  | currentRate:${groupResumenSelected.rate.totalAmount} | flights:${startingFlight.flightNumber}-${returningFlight.flightNumber} ` +
            ` | ${this.request.checkIn} | ${this.request.checkOut} | adults:${this.request.adults} | kids:${this.request.kids} | upsellFare:${upsellSelected.name} | rateDiff:${upsellSelected.difference}`
        }
        setDatalayer(event);
    }

    changeFlightChangeFlight(rate, flights){
        const paramsObj = searchParams();
        let urlToDetail =  `${this.settings.pathHotelDetail}${paramsObj.uri}${this.settings.uriHotelDetailPath}`;
        const startingFlight =_.get(flights, ['startingFlight'], {});
        const returningFlight = _.get(flights, ['returningFlight'], {});

        let event = {
            "event": UTILS.events.gtmEvent,
            "eventName": UTILS.events.gtmEvent,
            "eventCategory": UTILS.categories.chage_flight,
            "eventAction": `${_.get(startingFlight, ['departure', 'airport'], '' )}-${_.get(returningFlight, ['departure', 'airport'], '')} | ${urlToDetail}`,
            "eventLabel": `${flightToString(startingFlight)} || ${flightToString(returningFlight)}`,
            "eventExtra": rate,
            "eventSource": UTILS.misc.source_list
        }
        setDatalayer(event);
    }

    changeFlightChangeUpsell(upsellOptions, flights, upsellSelected, groupResumenSelected){ 
        const startingFlight =_.get(flights, ['startingFlight'], {});
        const returningFlight = _.get(flights, ['returningFlight'], {});
        let event = {
            "event": UTILS.events.gtmEvent,
            "eventName": UTILS.events.gtmEvent,
            "eventExtra": '',
            "eventCategory": UTILS.categories.upsell,
            "eventAction": UTILS.actions.modal_continue_upsell,
            "eventLabel": `${upsellOptions} upsell options | ${this.isDomestic ? "Domestic" : "International" } | ${_.get(startingFlight, ['departure', 'airport'], '' )}-${_.get(returningFlight, ['departure', 'airport'], '')}` +
            ` | ${startingFlight.airlineCode} | currentFare:${groupResumenSelected.familyFareCode}  | currentRate:${groupResumenSelected.rate.totalAmount} | flights:${startingFlight.flightNumber}-${returningFlight.flightNumber} ` + 
            ` | ${this.request.checkIn} | ${this.request.checkOut} | adults:${this.request.adults} | kids:${this.request.kids} | upsellFare:${upsellSelected.name} | rateDiff:${upsellSelected.difference} `
        }
        setDatalayer(event);
    }

    showDetailFlight(itinerary){
        let event = {
            "event": UTILS.events.gtmEvent,
            "eventName": UTILS.events.gtmEvent,
            "eventCategory": UTILS.categories.view_flight_detail,
            "eventAction": UTILS.actions.flight_list,
            "eventLabel": `${itinerary.departure.airport}-${itinerary.arrival.airport} | ${itinerary.airlineCode}`,
            "eventExtra": ''
        }
        setDatalayer(event);
    }

    showDetailFlightUpsell(upsellOptions, flights, groupResumenSelected){
        const startingFlight =_.get(flights, ['startingFlight'], {});
        const returningFlight = _.get(flights, ['returningFlight'], {});
        let event = {
            "event": UTILS.events.gtmEvent,
            "eventName": UTILS.events.gtmEvent,
            "eventExtra": UTILS.events.gtmEvent,
            "eventCategory": UTILS.categories.upsell,
            "eventAction": UTILS.actions.modal_view_flight,
            "eventLabel": `${upsellOptions} upsell options | ${this.isDomestic ? "Domestic" : "International" } | ${_.get(startingFlight, ['departure', 'airport'], '' )}-${_.get(returningFlight, ['departure', 'airport'], '')} `+
            ` | ${startingFlight.airlineCode} | currentFare:${groupResumenSelected.familyFareCode}  | currentRate:${groupResumenSelected.rate.totalAmount} | flights:${startingFlight.flightNumber}-${returningFlight.flightNumber} `+  
            ` | ${this.request.checkIn} | ${this.request.checkOut} | adults:${this.request.adults} | kids:${this.request.kids}`
        }
        setDatalayer(event);
    }

    showMoreFlights(flights){ 
        const startingFlight =_.get(flights, ['stating'], {});
        const returningFlight = _.get(flights, ['returning'], {});
        let event = {
            "event": UTILS.events.gtmEvent,
            "eventName": UTILS.events.gtmEvent,
            "eventExtra": '',
            "eventCategory": UTILS.actions.more_groups,
            "eventAction": UTILS.actions.modal_view_flight,
            "eventLabel": `${this.isDomestic ? "Domestic" : "International" } | ${_.get(startingFlight, ['departure', 'airport'], '' )}-${_.get(returningFlight, ['departure', 'airport'], '')}`
        }
        setDatalayer(event);
    }

    showMoreFlightsItem(isStarting, itinerary){ 
        const startingFlight =_.get(itinerary, ['stating'], {});
        const returningFlight = _.get(itinerary, ['returning'], {});
        let event = {
            "event": UTILS.events.gtmEvent,
            "eventName": UTILS.events.gtmEvent,
            "eventExtra": '',
            "eventCategory": UTILS.categories.load_more_flights,
            "eventAction": isStarting ? "Starting" : "Returning",
            "eventLabel": `${this.isDomestic ? "Domestic" : "International" } | ${_.get(startingFlight, ['departure', 'airport'], '' )}-${_.get(returningFlight, ['departure', 'airport'], '')}`
        }
        setDatalayer(event);
    }
} 
