﻿using TBFront.Models.Places.Response;
using TBFront.Models.Request;
using TBFront.Options;
using Places.Standard.Services;
using System.Globalization;
using System.Text.RegularExpressions;
using System.Text;

namespace TBFront.Helpers
{
    public class FlightParamsHelper
    {
        private static readonly string CountryBaseISO = "COL";
        private static readonly string CountryBase = "CO";
        private static readonly string RoundtripOld = "redondo";
        private static readonly string Roundtrip = "1";


        internal static FlightRequest GetFlightRequest(FlightParamsRequest request, SettingsOptions options, string internalCode)
        {
            var childAges = string.Join(",", request.Paxes.First().Children);
            var dates = GetDates(request, options);
            var tripMode = 1;
            
            var isRoundTrip = IsRoundtrip(request.Mode);
            if (request.Mode != null && !isRoundTrip)
            {
                tripMode = 0;
            }

            return new FlightRequest
            {
                Adults = request.Adults,
                Agekids = childAges,
                Kids = request.Paxes.First().Children.Count,
                AirlineCode = request.AirlineCode ?? "",
                Callus = request.Callus ?? false,
                CheckIn = dates.StartingDate.ToString("yyyy-MM-dd"),
                CheckOut = dates.ReturningDate.ToString("yyyy-MM-dd"),
                DaysInAdvance = Convert.ToInt32(request.DaysInAdvance),
                Mode = isRoundTrip ? 1 : 0,
                TripMode = tripMode,
                StartingFromAirport = request.OriginCode,
                ReturningFromAirport = request.DestinationCode,
                StartingFromDateTime = dates.StartingDate.ToString("yyyy-MM-dd"),
                ReturningFromDateTime = dates.ReturningDate.ToString("yyyy-MM-dd"),
                Paxes = request.Paxes,
                AddFirst = request.AddFirst ?? "",
                StartingAirportPlace = new FlightItem
                {
                    Airport = request.OriginName ?? "",
                    AirportCode = (request.OriginCode ?? "").ToUpper(),
                    AirportType = 11,
                    City = request.OriginName ?? "",
                    CityFullName = request.OriginName ?? ""
                },
                ReturningAirportPlace = new FlightItem
                {
                    Airport = request.DestinationName ?? "",
                    AirportCode = (request.DestinationCode ?? "").ToUpper(),
                    AirportType = 11,
                    City = request.DestinationName ?? "",
                    CityFullName = request.DestinationName ?? ""
                },
                Culture = internalCode,
            };
        }
        internal static FlightRequest MapFlightItemRequest(FlightRequest request, List<PlaceResponse> responses, SettingsOptions options)
        {
            var origin = responses.FirstOrDefault(p => string.Equals(p.Code, request.StartingFromAirport, StringComparison.OrdinalIgnoreCase));
            var destination = responses.LastOrDefault(p => p.Id != origin?.Id && string.Equals(p.Code, request.ReturningFromAirport, StringComparison.OrdinalIgnoreCase));
            request.StartingAirportPlace = FlightParamsHelper.GetAirportDetailByPlaceResponse(origin, request.StartingAirportPlace, request.StartingFromAirport);
            request.ReturningAirportPlace = FlightParamsHelper.GetAirportDetailByPlaceResponse(destination, request.ReturningAirportPlace, request.ReturningFromAirport);
            request.StartingFromAirport = request.StartingAirportPlace.AirportCode.ToUpper();
            request.ReturningFromAirport = request.ReturningAirportPlace.AirportCode.ToUpper();

            if (origin is not null && destination is not null)
            {
                request.IsNational = (origin.LocationInfo.CountryISO == CountryBaseISO && destination.LocationInfo.CountryISO == CountryBaseISO) || (origin.LocationInfo.CountryA2 == CountryBase && destination.LocationInfo.CountryA2 == CountryBase);
            }
            return request;
        }


        internal static FlightParamsRequest OverwriteRequest(FlightParamsRequest request, string originCode, string destinationCode, string airline, SettingsOptions options)
        {
            if (!string.IsNullOrEmpty(originCode))
            {
                request.OriginCode = originCode.ToUpper();
            }

            if (!string.IsNullOrEmpty(destinationCode))
            {
                request.DestinationCode = destinationCode.ToUpper();
            }

            if (!string.IsNullOrEmpty(airline))
            {
                request.AddFirst = airline.ToLower();
            }

            if (string.IsNullOrEmpty(request.Mode))
            {
                request.Mode = Roundtrip;
            }

            if (request.Adults == 0 && request.Paxes.Count != 0)
            {
                request.Adults = request.Paxes.FirstOrDefault().Adults;
            }

            if (string.IsNullOrEmpty(request.OriginCode))
            {

                var pickupCode = options.SEOSettings.PickupCode;

                if (!string.IsNullOrEmpty(destinationCode) && destinationCode.Equals(pickupCode, StringComparison.OrdinalIgnoreCase))
                {
                    request.OriginCode = options.SEOSettings.PickupCodeAlternative;
                }
                else
                {
                    request.OriginCode = pickupCode;
                }
            }


            return request;
        }



        private static DateRange GetDates(FlightParamsRequest request, SettingsOptions _options)
        {

            var _dates = new DateRange();
            var now = DateTime.Today.ToUniversalTime();
            var checkin = request.DateFrom;
            var checkout = request.DateTo ?? request.DateFrom;
            var isDefault = false;
            DateTime inDate;
            DateTime onDate;

            var weeksPrequote = 1;
            var daysPrequote = 4;
            var startDate = 3;

            var valid = IsValidDatesQuote(checkin, checkout, request.Mode);
            var isRoundtrip = string.Equals(request.Mode, Roundtrip, StringComparison.OrdinalIgnoreCase);
            
            if (valid && (!String.IsNullOrEmpty(checkin) || !String.IsNullOrEmpty(checkout)))
            {
                inDate = DateTime.Parse(checkin).ToUniversalTime();
                onDate = DateTime.Parse(checkout).ToUniversalTime();
                var validateDates = DateTime.Compare(inDate, onDate);
                var validateCheckIn = DateTime.Compare(now, inDate);
                var validateCheckOut = DateTime.Compare(now, onDate);

                if ((validateDates > 0 && isRoundtrip) || validateCheckIn > 0 || validateCheckOut > 0)
                {

                    DateTime newDateParse = DateTime.Now;
                    newDateParse = newDateParse.AddDays(weeksPrequote * 7);
                    newDateParse = newDateParse.AddDays(-((int)newDateParse.DayOfWeek) + startDate);
                    inDate = newDateParse;
                    onDate = newDateParse.AddDays(daysPrequote);
                }
            }
            else
            {
                isDefault = true;

                if (request.DaysInAdvance is not null)
                {
                    DateTime newDateParse = DateTime.Now;
                    newDateParse = newDateParse.AddDays(Convert.ToInt32(request.DaysInAdvance));
                    inDate = newDateParse;
                    _dates.Starting = inDate.ToString("yyyy'-'MM'-'dd");
                    onDate = newDateParse.AddDays(Convert.ToInt32(request.TripDays ?? "1"));
                    _dates.Returning = onDate.ToString("yyyy'-'MM'-'dd");
                }
                else
                {

                    DateTime newDateParse = DateTime.Now;
                    newDateParse = newDateParse.AddDays(weeksPrequote * 7);
                    newDateParse = newDateParse.AddDays(-((int)newDateParse.DayOfWeek) + startDate);
                    inDate = newDateParse;
                    onDate = newDateParse.AddDays(daysPrequote);
                }
            }



            _dates.StartingDate = new DateTime(inDate.Year, inDate.Month, inDate.Day, 0, 0, 0);
            _dates.ReturningDate = new DateTime(onDate.Year, onDate.Month, onDate.Day, 0, 0, 0);
            _dates.Starting = _dates.StartingDate.ToString("yyyy'-'MM'-'dd");
            _dates.Returning = _dates.ReturningDate.ToString("yyyy'-'MM'-'dd");
            _dates.IsDefault = isDefault;

            return _dates;
        }


        private static bool IsValidDatesQuote(string checkin, string checkout, string mode)
        {
            var valid = false;
            var isRoundtrip = string.Equals(mode, Roundtrip, StringComparison.OrdinalIgnoreCase);

            if (isRoundtrip)
            {
                if (DateTime.TryParse(checkin, out _) && DateTime.TryParse(checkout, out _))
                {
                    valid = true;
                }
            }
            else
            {
                if (DateTime.TryParse(checkin, out _))
                {
                    valid = true;
                }
            }


            return valid;
        }
        private static FlightItem GetAirportDetailByPlaceResponse(PlaceResponse place, FlightItem flightItem, string codeDefault = "")
        {
            var airportDetail = flightItem;
            var displayDestinationHtml = string.Empty;
            if (place != null && place.DisplayText != null && place.Name != null && place.Code != null)
            {
                var displayTextArr = place.DisplayText.Split("-");
                var cityArr = displayTextArr[0].Split(" ");
                cityArr = cityArr.Where(i => i != cityArr[0]).ToArray();
                var cityFullName = string.Join(" ", cityArr);
                var city = cityFullName.Split(",")[0];

                airportDetail.Airport = place.Name;
                airportDetail.City = city;
                airportDetail.CityName = city;
                airportDetail.CityFullName = !string.IsNullOrEmpty(flightItem.CityFullName) ? flightItem.CityFullName : cityFullName;
                airportDetail.AirportCode = place.Code;
                airportDetail.AirportType = place.Type;
                airportDetail.CityCountryName = city;
                airportDetail.CountryISO = place.LocationInfo.CountryISO;
            }
            else
            {
                if (place == null)
                {
                    place = GetDefaultPlace(codeDefault ?? "");
                }
                airportDetail.Airport = place.Name;
                airportDetail.City = string.Empty;
                airportDetail.CityName = string.Empty;
                airportDetail.CityFullName = !string.IsNullOrEmpty(flightItem.CityFullName) ? flightItem.CityFullName : place.Name;
                airportDetail.AirportCode = place.Code;
                airportDetail.AirportType = place.Type;
                airportDetail.CityCountryName = string.Empty;
                airportDetail.CountryISO = place.LocationInfo.CountryISO;
            }


            return airportDetail;
        }

        public static FlightItem GetAirportDetailByPlaceResponse(PlaceResponse place)
        {
            var airportDetail = new FlightItem();
            if (place != null && place.DisplayText != null && place.Name != null && place.Code != null)
            {
                var displayTextArr = place.DisplayText.Split("-");
                var cityArr = displayTextArr[0].Split(" ");
                cityArr = cityArr.Where(i => i != cityArr[0]).ToArray();
                var cityFullName = string.Join(" ", cityArr);
                var city = cityFullName.Split(",")[0];

                airportDetail.Airport = place.Name;
                airportDetail.City = city;
                airportDetail.CityName = city;
                airportDetail.CityFullName = cityFullName;
                airportDetail.AirportCode = place.Code;
                airportDetail.AirportType = place.Type;
                airportDetail.CityCountryName = city;
                airportDetail.CountryISO = place.LocationInfo.CountryISO;
            }

            return airportDetail;
        }

        public static string GenerateSlug(string phrase)
        {
            var str = RemoveDiacritics(phrase).ToLower();

            str = Regex.Replace(str, @"\s", "-");
            str = Regex.Replace(str, @"[^a-z0-9\-]", "");
            str = Regex.Replace(str, @"-{2,}", "-");
            str = str.Trim('_');

            if (!string.IsNullOrEmpty(str) && str[str.Length - 1] == '-')
            {
                str = str.Substring(0, str.Length - 1);
            }

            if (!string.IsNullOrEmpty(str) && str[0] == '-')
            {
                str = str.Substring(1);
            }
            return str;
        }

        public static string RemoveDiacritics(string text)
        {
            var normalizedString = text.Normalize(NormalizationForm.FormD);
            var stringBuilder = new StringBuilder();

            foreach (char c in normalizedString)
            {
                var unicodeCategory = CharUnicodeInfo.GetUnicodeCategory(c);
                if (unicodeCategory != UnicodeCategory.NonSpacingMark)
                {
                    stringBuilder.Append(c);
                }
            }

            return stringBuilder.ToString().Normalize(NormalizationForm.FormC);
        }

        public static string ReplaceDoubleHyphen(string phrase)
        {
            return Regex.Replace(phrase, @"--", "-");
        }

        private static PlaceResponse GetDefaultPlace(string codeDefault)
        {
            var place = new PlaceResponse();

            place.Name = codeDefault;
            place.Code = codeDefault;
            place.LocationInfo.CountryISO = "";

            return place;
        }

        private static bool IsRoundtrip(string mode) => string.Equals(mode, RoundtripOld, StringComparison.OrdinalIgnoreCase) || string.Equals(mode, Roundtrip, StringComparison.OrdinalIgnoreCase);
    }
}
