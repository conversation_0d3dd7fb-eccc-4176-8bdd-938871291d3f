﻿using TBFront.Models.ContentDeliveryNetwork.FaqContent;
using TBFront.Models.ContentDeliveryNetwork.LegalContent;

namespace TBFront.Application.Mappers
{
    public class GeneralContentMapper
    {
        public static FaqContentResponse Map(FaqContentRequest request, List<FaqContentResponse> responses)
        {
            var content = responses.FirstOrDefault(c => c.Country.Any(co => co.Contains(request.UserCountry)));

            if (content is null)
            {
                content = responses.FirstOrDefault(c => !c.Country.Any());
            }

            if (content is null)
            {
                content = responses.FirstOrDefault();
            }

            if (content is null)
            {
                content = new FaqContentResponse();
            }
            return content;
        }

        public static List<LegalContentResponse> Map(LegalContentRequest request, List<LegalContentResponse> responses)
        {
            var content = responses.OrderBy(c =>
            {
                if (c.Country != null && c.Country.Any(co => co.Contains(request.UserCountry)))
                    return 0;

                // Si el array "Country" está vacío y no hay coincidencias con el país solicitado, darle la prioridad más alta (valor 1)
                if (c.Country == null || !c.Country.Any())
                    return 1;

                // Caso general, si no contiene el país solicitado, darle una prioridad más baja (valor 2)
                return 2;
            }).ToList();


            if (!content.Any())
            {
                content = responses.Order().ToList(); ;
            }


            if (!content.Any())
            {
                content = new List<LegalContentResponse>();
            }

            return content;

        }
    }
}
