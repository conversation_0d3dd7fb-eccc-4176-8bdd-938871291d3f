﻿@using TBFront.Helpers
@using Microsoft.Extensions.Options
@using TBFront.Models.Request
@using TBFront.Options;
@using TBFront.Types;
@using TBFront.Models.Meta.Metatags
@using TBFront.Models.Flight.Revalidate;
@inject IOptions<SettingsOptions> settingOptions
@inject IOptions<CurrencyOptions> currencyOptions

@inject ViewHelper _
@inject StaticHelper staticHelper
@{
    var resolution = _.GetImageResolution();
    var isMobile = resolution.Device == DeviceType.Mobile;
    var isRobot = _.IsRobot();

    ViewData["Page"] = "Checkout";

    ViewData["IsRobot"] = isRobot;
    ViewData["isMobile"] = isMobile;
    ViewData["Resolution"] = resolution;

    var data = ViewData["Request"] as CheckoutQuoteRequest;
    var culture = ViewData["CultureData"] as Culture;
    var isValid = (bool)ViewData["ValidRequest"];
}

@await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.Code}/_Header.cshtml", new ViewDataDictionary(ViewData) { { "navs", false }, { "login", isRobot }, { "Checkout", true } })



<div class="container-default-loading">
    <checkout-one-page></checkout-one-page>
</div>


<footer class="ck-footer container-fluid p-4">
    <p class="mb-2 text-center font-14">@_.Localizer("footerCheckoutText") <a class="color-cellphone" href="tel:************">************</a>. @_.Localizer("footerCheckoutRights")</p>
    <p class="mb-2 text-center font-14">@_.Localizer("footerCheckoutAddress")</p>
    <p class="mb-2 text-center font-14">@_.Localizer("footerCheckoutSubtitle")</p>
</footer>

@section Meta {
    @await Html.PartialAsync("_MetaMain", new ViewDataDictionary(ViewData) { { "Meta", ViewData["MetaTag"] }})
    <meta name="robots" content="noindex,nofollow" />
    <meta name="googlebot" content="noindex,nofollow" />
}


@section Preload {
    <link rel="preconnect" href="@settingOptions.Value.CloudCdn">
    <link rel="preload" href="@staticHelper.GetVersion($"/{settingOptions.Value.SiteName}/css/checkout.css", settingOptions.Value.Assets)" as="style" />
}

@section Css {
    <link type="text/css" rel="stylesheet" href="@staticHelper.GetVersion($"/{settingOptions.Value.SiteName}/css/checkout.css", settingOptions.Value.Assets)">
}



@section ScriptsPriority {
}


@section Scripts {
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js" integrity="sha512-v2CJ7UaYy4JwqLDIrZUI/4hqeoQieOmAZNXBeQyjo21dadnwR+8ZaIJVT8EE2iyI61OV8e6M8PP2/4hpQINQ/g==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>

    <script>
        window.__pt = window.__pt || {};
    </script>

    @if (isValid)
    {
        <script>
            window.__pt.data = @Json.Serialize(data);
            window.__pt.ip = "@ViewData["X-Forwarded-For"]";
            window.__pt.cultureData = @Json.Serialize((ViewData["CultureData"]));
            window.__pt.exchange = @Json.Serialize((ViewData["Exchange"]));
            window.__pt.alternates = @Json.Serialize((ViewData["Alternates"]));
            window.__pt.currencies = @Json.Serialize((currencyOptions.Value.Currencies));
            window.__pt.currency = @Json.Serialize((ViewData["CurrencyData"]));
        </script>
    }

    <script src="@staticHelper.GetVersion("/js/vue/app.js", settingOptions.Value.Assets)"></script>
}