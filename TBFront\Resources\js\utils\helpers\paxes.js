import { mapPaxToUrl } from "./queryString";
import dayjs from 'dayjs';

export const getSelection = (userSelection) => {

	let data = mapPaxToUrl(userSelection.pax);
	let checkIn = dayjs(userSelection.checkIn);
	let checkOut = dayjs(userSelection.checkOut);

	const diff = checkOut.$d - checkIn.$d;

	const day = 86400000;
	const diffInday = Math.floor(diff / day);
	return {
		people: data.adults + data.kids + data.infants,
		nights: diffInday
	};
};
