﻿@using TBFront.Helpers
@using Microsoft.Extensions.Options
@using TBFront.Models.MKTCollection.Response;
@using TBFront.Models.Request;
@using TBFront.Options;
@using TBFront.Types;
@using TBFront.Models.Meta.Metatags
@inject IOptions<SettingsOptions> settingOptions
@inject IOptions<SiteOptions> siteOptions
@inject IOptions<CurrencyOptions> currencyOptions
@inject ViewHelper _
@inject StaticHelper staticHelper
@{
    var resolution = _.GetImageResolution();
    var isMobile = resolution.Device == DeviceType.Mobile;
    var isRobot = _.IsRobot();

    var request = ViewData["request"] as FlightRequest;
    var sectionPromotion = ViewData["section"] as Section;
    var destinations = (bool)ViewData["isDestinations"];
    var airline = ViewData["airline"] as String;
    var metaTag = ViewData["MetaTag"] as MetaTag;
    var culture = ViewData["CultureData"] as Culture;

    ViewData["Page"] = "home";
    ViewData["IsRobot"] = isRobot;
    ViewData["isMobile"] = isMobile;
    ViewData["Resolution"] = resolution;



}

@await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.Code}/_Header.cshtml", new ViewDataDictionary(ViewData) { { "navs", true }, { "login", isRobot } })

<div class="c-box-home @airline pb-4">
    <div class="container ">

        <div class="row">


            <div class="col-12 col-md-4">
                <div id="promo" class="bg-img"></div>
            </div>

            @{
                var title = "";
                var subtitle = "";
            }

            @if (!destinations)
            {
                title = _.Localizer($"tiquetesBaratos{airline}");
                subtitle = _.Localizer($"subtitleHome{airline}");
            }
            else
            {
                title = _.Localizer("destinationsTitle", request.StartingAirportPlace.CityName, request.ReturningAirportPlace.CityName);
                subtitle = _.Localizer("destinationsSubtitle");
            }

            <div class="col-12 col-md-8 text-end text-white mt-3 c-title-home mb-3 mb-md-0 px-3 px-md-0 z-1">
                <h1 class="pt-3 pt-md-2 pt-lg-3 text-white position-relative">@title</h1>
                <h2 class="f-r-medium">@subtitle</h2>
            </div>

        </div>

        <div class="row">
            <flight-booker :airline='@Json.Serialize(airline)' :is-home-landing="true"></flight-booker>
        </div>
    </div>
</div>
<recent-research></recent-research>

<section class="container-fluid c-banner-more py-5 mb-4">
    <div class="container px-0 px-md-3">
        <div class="row">
            <div class="col-12 col-md-4 text-center mb-5 mb-md-0 px-0 px-md-3">
                <div class="icon icon-plane-right m-auto font-30"></div>
                <p class="font-16 f-p-semibold mt-3 mb-1">@_.Localizer("oneFly")</p>
                <p class="color-black-light mb-1">@_.Localizer("oneFlySubtitle")</p>
                <a href="@(settingOptions.Value.SiteUrl)/vuelos/promociones-tiquetes-aereos-origen" class="a-link font-16">@_.Localizer("oneFlyLinkTitle")</a>
            </div>
            <div class="col-12 col-md-4 text-center mb-5 mb-md-0 px-0 px-md-3">
                <div class="icon icon-credit-card m-auto font-30"></div>
                <p class="font-16 f-p-semibold mt-3 mb-1">@_.Localizer("methodPayments")</p>
                <p class="color-black-light mb-1">@_.Localizer("methodPaymentsSubtitle")</p>
                <a href="#" data-bs-toggle="modal" data-bs-target="#modal-payform" class="a-link font-16">@_.Localizer("methodPaymentsLinkTitle")</a>
            </div>
            <div class="col-12 col-md-4 text-center px-0 px-md-3">
                <div class="icon icon-call-center m-auto font-30"></div>
                <p class="font-16 f-p-semibold mt-3 mb-1">@_.Localizer("expertsService")</p>
                <p class="color-black-light mb-1">@_.Localizer("expertsServiceSubtitle")</p>
                <a href="@(settingOptions.Value.SiteUrl)/@(culture.CultureCode)/vuelos/escribenos" class="a-link font-16">@_.Localizer("expertsServiceLinkTitle")</a>
                   
            </div>
        </div>
    </div>
</section>
<modal-check-payment-methods></modal-check-payment-methods>
<section class="container c-banners my-5 my-md-5">


    @if (sectionPromotion is not null && sectionPromotion.Cards.Count() >= 1)
    {
        var sectionCard = sectionPromotion.Cards.First();
        var titleSales = "";
        var subtitleSales = "";

        @if (!destinations)
        {
            titleSales = _.Localizer($"salesTitle{airline}");
            subtitleSales = _.Localizer($"salesSubtitle{airline}");
        }
        else
        {
            titleSales = _.Localizer("destinationPromotionsTitle", request.StartingAirportPlace.CityName, request.ReturningAirportPlace.CityName);
            subtitleSales = _.Localizer("destinationsPromotionsSubtitle", request.StartingAirportPlace.CityName, request.ReturningAirportPlace.CityName);
        }

        @if (!string.IsNullOrWhiteSpace(sectionPromotion.Description))
        {
            titleSales = sectionPromotion.Description;
        }

        <h3 class="mb-1 font-poppins-semibold titles-home mt-md-5">@titleSales</h3>
        <p class="color-black-light">@subtitleSales</p>


        <div class="row c-carousel-2">
            <div class="col-12 col-md-6 mb-3">
                <div class="item">
                    <div class="c-figure position-relative p-0 m-0">
                        <div width="100%">
                            <a href="@(!string.IsNullOrWhiteSpace(sectionCard.Url) ? sectionCard.Url : "https://www.tiquetesbaratos.com/index.php/component/promociones_tiquetes_aereos/" )" class="w-100 h-100" title="Promotions tiquetes baratos">
                                <img class="d-block w-100 rounded-4 lazy" alt="image promotions" data-src="@sectionCard.ImageUrl" data-srcset="@sectionCard.ImageUrl x1">
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 col-md-6">
                @if (sectionPromotion.Cards.Count() >= 2)
                {

                    <div id="carouselExampleAutoplaying" class="carousel slide carousel-fade" data-bs-ride="carousel">
                        <div class="carousel-inner">
                            @{
                                int i = 0;
                            }

                            @foreach (var card in sectionPromotion.Cards.Skip(1))
                            {
                                <div class="carousel-item @(i == 0 ? "active" : "")">
                                    <a href="@(!string.IsNullOrWhiteSpace(card.Url) ? card.Url : "https://www.tiquetesbaratos.com/index.php/component/promociones_tiquetes_aereos/")" target="_blank">
                                        <img data-src="@card.ImageUrl" data-srcset="@card.ImageUrl x1" class="d-block w-100 rounded-4 lazy" alt="@card.Notes">
                                    </a>
                                </div>

                                i++;
                            }
                        </div>
                    </div>
                }
            </div>

        </div>
    }

</section>

<loader-page></loader-page>



@await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.Code}/_Footer.cshtml", new ViewDataDictionary(ViewData) { { "newsletter", true } })


@section Meta {
    @await Html.PartialAsync("_MetaMain", new ViewDataDictionary(ViewData) { { "Meta", ViewData["MetaTag"] } })
}

@section Preload {
    @if (!string.IsNullOrEmpty(settingOptions.Value.CloudCdn))
    {
        <link rel="preconnect" href="@settingOptions.Value.CloudCdn">
    }

    <link rel="preload" href="@staticHelper.GetVersion($"/fonts/icomoon.woff2", "/assets-tb", false)" as="font" type="font/woff2" crossorigin="" />
    <link rel="preload" href="@staticHelper.GetVersion($"/fonts/Roboto-Medium.woff2", "/assets-tb", false)" as="font" type="font/woff2" crossorigin="" />
    <link rel="preload" href="@staticHelper.GetVersion($"/fonts/Roboto-Regular.woff2","/assets-tb", false)" as="font" type="font/woff2" crossorigin="" />
    <link rel="preload" href="@staticHelper.GetVersion($"/fonts/Poppins-Medium.woff2", "/assets-tb", false)" as="font" type="font/woff2" crossorigin="" />
    <link rel="preload" href="@staticHelper.GetVersion($"/fonts/Roboto-Light.woff2", "/assets-tb", false)" as="font" type="font/woff2" crossorigin="" />
    <link rel="preload" href="@staticHelper.GetVersion($"/fonts/Poppins-Regular.woff2", "/assets-tb", false)" as="font" type="font/woff2" crossorigin="" />

    <link rel="preload" href="@staticHelper.GetVersion($"/img/{settingOptions.Value.SiteName}/bg-45.svg", "/assets-tb")" as="image" />
    <link rel="preload" href="@staticHelper.GetVersion($"/{settingOptions.Value.SiteName}/css/home.css", settingOptions.Value.Assets)" as="style" />


}

@section Css {
    <style>
        body.cloak {
            display: none !important;
        }
    </style>
    <link type="text/css" rel="stylesheet" href="@staticHelper.GetVersion($"/{settingOptions.Value.SiteName}/css/home.css", settingOptions.Value.Assets)">

}



@section ScriptsPriority {
}


@section Scripts {
    <script>
        if (performance.navigation.type == 2) {
            location.reload();
        }
        window.__pt.data = @Json.Serialize(request)
        window.__pt.cultureData = @Json.Serialize((ViewData["CultureData"]));
        window.__pt.exchange = @Json.Serialize((ViewData["Exchange"]));
        window.__pt.alternates = @Json.Serialize((ViewData["Alternates"]));
        window.__pt.currencies = @Json.Serialize((currencyOptions.Value.Currencies));
        window.__pt.currency = @Json.Serialize((ViewData["CurrencyData"]));

    </script>


    <script src="@staticHelper.GetVersion("/js/vue/app.js", settingOptions.Value.Assets)"></script>
}