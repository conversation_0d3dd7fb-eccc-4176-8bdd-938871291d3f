export const isRequired = (value) => {
	return value && value.trim();
};

export const isEqualsTo = (value, valueToCompare) => {
	return value === valueToCompare;
};

export const distinctTo = (value, valueToCompare) => {
	return value !== valueToCompare;
};

export const minLength = (value, min) => {
	return value && (value.length <= min)
};

export const maxLength = (value, max) => {
	return value && (value.length >= max);
};

export const minValue = (value, min) => {
	return value && (value <= min);
};

export const maxValue = (value, max) => {
	return value && (value >= max);
};

export const validateAges = (ages, kids) => {
	let isValid = true;

	if (!ages || ages.length < kids) {
		isValid = false;
	}

	for (let i = 0; i < ages.length; i++) {
		if (!ages[i]) {
			isValid = false;
			break;
		}
	}
	return isValid;
};
export const isDate = (value) => {
	return value && dayjs(value).isValid();
};

export const isEmail = (value) => {
	if (value.length === 0) {
		return false;
	} 
	const re = /\S+@\S+\.\S+/;
	return re.test(value);
};