﻿using System.Text.Json.Serialization;

namespace TBFront.Models.Meta.Schema
{
    public class PostalAddress
    {
        [JsonPropertyName("@type")]
        public string Type { get; set; }

        [JsonPropertyName("streetAddress")]
        public string? StreetAddress { get; set; }

        [JsonPropertyName("addressLocality")]
        public string? AddressLocality { get; set; }

        [JsonPropertyName("addressRegion")]
        public string? AddressRegion { get; set; }

        [JsonPropertyName("addressCountry")]
        public string? AddressCountry { get; set; }

    }
}
