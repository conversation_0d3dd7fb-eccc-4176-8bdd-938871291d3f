@import './app', './base/_variables', './_variables.scss', 'base/_core', 'components/footer', 'components/header','components/_loading';

$color_1: #fff;
$color_2: #0e61aa;
$background-color_1: #0e61aa;

.container-default-loading {
    min-height: 800px;
}

.container-app {
    min-height: 700px;
}

.c-tp {
    background-color: $background-color_1;
    padding-bottom: 90px;

    h1 {
        color: $color_1;
        text-shadow: none;

        * {
            color: $color_1;
        }
    }

    h2 {
        color: $color_1;
        text-shadow: none;
        font-size: 23px;

        * {
            color: $color_1;
        }
    }

    p {
        color: $color_1;
        text-shadow: none;

        * {
            color: $color_1;
        }
    }
}

.ctp-in {
    border-radius: 20px;
    margin-top: -90px;

    h3 {
        * {
            color: $color_2;
            font-weight: 700;
        }
    }
}

@media (max-width:767px) {
    .c-tp {
        h2 {
            font-size: 20px;
        }

        h1 {
            font-size: 30px;
        }
    }

    .ctp-in {
        h3 {
            font-size: 23px;
        }
    }
}
