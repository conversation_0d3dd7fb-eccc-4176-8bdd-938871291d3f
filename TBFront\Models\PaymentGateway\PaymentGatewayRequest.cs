﻿using TBFront.Models.PaymentGateway.Dtos;

namespace TBFront.Models.PaymentGateway
{
    public class PaymentGatewayRequest
    {
        public int LocatorId { get; set; }

        public string Reference { get; set; } = string.Empty;

        public string Currency { get; set; } = string.Empty;

        public bool Process3DSecure { get; set; }

        public string KeyValidation { get; set; } = string.Empty;

        public string SessionId { get; set; } = string.Empty;

        public string Host { get; set; } = string.Empty;

        public string ResponseUrl { get; set; } = string.Empty;

        public bool ReturnPaymentProcessOption { get; set; }

        public bool ReturnPaymentOption { get; set; }

        public PaymentConfigInfoRequest PaymentConfigInfoRequest { get; set; } = new PaymentConfigInfoRequest();

        public QuoteInfo QuoteInfo { get; set; } = new QuoteInfo();

        public TagManagerDataLayer TagManagerDataLayer { get; set; } = new TagManagerDataLayer();

        public bool ShowConfirmationPage { get; set; }

        public string ContactPhone { get; set; } = string.Empty;

        public IEnumerable<int> ThirdPartyCheckoutProvider { get; set; } = Enumerable.Empty<int>();

        public CouponDiscount CouponDiscount { get; set; } = new CouponDiscount();

    }

    public class CouponDiscount
    {
        public string Code { get; set; } = string.Empty;
        public bool IsHidden { get; set; }
    }
}
