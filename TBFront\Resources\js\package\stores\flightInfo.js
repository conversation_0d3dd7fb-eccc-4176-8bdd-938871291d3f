import { defineStore } from "pinia";

export const useFlightInfoStore = defineStore({
    id: "flightInfo",
    state: () => ({
        loading: false,
        showInfo: false,
        departureFlight: {},
        arrivalFlight: {},
    }),
    getters: {
        getLoading: (state) => {
            return state.loading;
        },
        getShowInfo: (state) => {
            return state.showInfo;
        },
    },
    actions: {
        setDepartureFlight(data) {
            this.departureFlight = data;
        },
        setArrivalFlight(data) {
            this.arrivalFlight = data;
        },
        changeOpenCloseModalInfo() {
            this.showInfo = true;
        },
    },
});