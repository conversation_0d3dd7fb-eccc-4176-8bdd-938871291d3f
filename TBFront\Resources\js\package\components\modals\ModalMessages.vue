<template>
    <div class="modal fade modal-tb" id="modalMessages" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-md modal-content">
                <div class="modal-header bg-base d-flex justify-content-between">
                    <h2 class="mb-0 font-main text-strong" style="font-size: 20px;" v-html="title"></h2>
                    <span class="icon icon-close btn-close ms-auto cursor-pointer" data-bs-dismiss="modal" aria-label="cerrar ventana emergente"></span>
                </div>
                <div class="modal-body">
                    <div class="d-flex gap-2">
                        <span class="icon icon-info font-22" style="line-height: 1.3;" alt=""></span>
                        <div v-html="msg"></div>
                    </div>
                    <div class="d-flex justify-content-end font-16">
                        <button aria-label="intenta de nuevo" class="btn btn-blue" data-bs-dismiss="modal">{{btn}}</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        props: {
            msg: String,
            title: String,
            btn: String
        }
    }
</script>