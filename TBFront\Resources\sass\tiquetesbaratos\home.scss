﻿@import './app', './base/_variables', './_variables.scss', 'base/_core', 'components/footer', 'components/_loading.scss', 'components/header';

.titles-home {
    font-size: 28px;
}

.a-link {
    color: $color_18;
    font-family: $font-family_5;
    text-decoration: underline;

    &:hover {
        text-decoration: none;
    }
}

.c-banner-more {
    background-color: $background-color_16;

    .icon {
        color: $color_2;
    }

    .icon-plane-right {
        transform: rotate(270deg);
    }
}

.c-banners {

    &:before {
        display: block;
        content: '';
        height: 150px;
        background-color: #fff;
        background-image: url(https://viajes.tiquetesbaratos.com/img/banner-tiquetes.png?v=3);
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
        margin-bottom: 40px;
        @media ($phone) {
            height: 90px;
            margin-bottom: 20px;        
        }
    }
    

    h2 {
        text-shadow: none;
    }

    .c-figure {
        img {
            border-radius: 20px;
        }
    }
}

.c-box-home {
    background: #0461b2;
    background: linear-gradient(180deg,#0461b2,#1d87da);
    position: relative;
    .c-booker {
        @media ($phone) {            
            margin-left: 0 !important;
            margin-right: 0 !important;
            padding-left: 0 !important;
            padding-right: 0 !important;         
        }
    }

    .bg-img {
        background-position: 50%;
        height: 90px;
        width: 150px;
        background-repeat: no-repeat;
        background-size: contain;
        position: relative;
    }

    &:before {
        background-repeat: no-repeat;
        background-size: contain;
        background-image: url(/assets-tb/img/tiquetesbaratos/bg-45.svg);
        background-position: 0;
        content: "";
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
    }


    h1 {
        font-family: Poppins-semibold;
        font-size: 40px;
        //font-family: 'Roboto-Regular';
        text-shadow: 2px 2px 6px rgba(0, 0, 0, .4);
        @media ($tablet) {
            font-size: 25px;
        }
    }

    h2 {
        font-size: 20px;
        //font-family: 'Roboto-Regular';
        text-shadow: 2px 2px 6px rgba(0, 0, 0, .4);
        @media ($tablet) {
            font-size: 18px;
        }
    }


    @media (max-width:767px) {

        h1 {
            font-size: 28px;
        }

        h2 {
            font-size: 16px;
            font-weight: normal;
        }
    }

    &.avianca {
        background: #d41520;
        background: linear-gradient(180deg, #d41520, #ff152b);

        .bg-img {
            background-image: url(/assets-tb/img/tiquetesbaratos/logos/logo-avianca.svg);
        }
    }

    &.copa {
        background: #12416e;
        background: linear-gradient(180deg, #12416e 0%, #0d3050 100%);

        .bg-img {
            background-image: url(/assets-tb/img/tiquetesbaratos/logos/logo-copa.svg);
        }
    }

    &.latam {
        background: #4303d2;
        background: linear-gradient(180deg, #4303d2, #2a0088);

        .bg-img {
            background-image: url(/assets-tb/img/tiquetesbaratos/logos/logo-latam.png);
        }
    }

    &.iberia {
        background: #d9272e;
        background: linear-gradient(180deg, #ee7f21, #ff152b);

        .bg-img {
            background-image: url(/assets-tb/img/tiquetesbaratos/logos/logo-iberia.svg);
        }
    }

    &.airfrance {
        background: #131d41;
        background: linear-gradient(180deg, #131d41, #003b98);

        .bg-img {
            background-image: url(/assets-tb/img/tiquetesbaratos/logos/logo-airfrance.svg);
        }
    }

    &.aeromexico {
        background: #003365;
        background: linear-gradient(180deg, #003365, #00468e);

        .bg-img {
            background-image: url(/assets-tb/img/tiquetesbaratos/logos/logo-aeromexico.svg);
        }
    }

    &.united {
        background: #039;
        background: linear-gradient(180deg, #039, #001f5c);

        .bg-img {
            background-image: url(/assets-tb/img/tiquetesbaratos/logos/logo-united.svg);
        }
    }

    &.continental {
        background: #005a9f;
        background: linear-gradient(180deg, #005a9f, #114e92);

        .bg-img {
            background-image: url(/assets-tb/img/tiquetesbaratos/logos/logo-continental.svg);
        }
    }

    &.american {
        background: #02abe4;
        background: linear-gradient(180deg, #02abe4, #003b98);

        .bg-img {
            background-image: url(/assets-tb/img/tiquetesbaratos/logos/logo-american-airlines.svg);
        }
    }

    &.aircanada {
        background: #d9272e;
        background: linear-gradient(180deg, #d9272e, #ff152b);

        .bg-img {
            background-image: url(/assets-tb/img/tiquetesbaratos/logos/logo-air-canada.svg);
        }
    }

    &.jetblue {
        background: #203c73;
        background: linear-gradient(180deg, #203c73, #002060);

        .bg-img {
            background-image: url(/assets-tb/img/tiquetesbaratos/logos/logo-jet-blue.svg);
        }
    }

    &.satena {
        background: #11304c;
        background: linear-gradient(180deg, #1d4a77, #11304C);

        .bg-img {
            background-image: url(/assets-tb/img/tiquetesbaratos/logos/logo-satena.svg);
        }
    }

    &.clicair {
        background: #003c66;
        background: linear-gradient(180deg, #045394, #003c66);

        .bg-img {
            background-image: url(/assets-tb/img/tiquetesbaratos/logos/logo-clic.svg);
        }
    }

    &.vivaaerobus {
        background: #004a23;
        background: linear-gradient(180deg, #8cd400, #004a23);

        .bg-img {
            background-image: url(/assets-tb/img/tiquetesbaratos/logos/logo-viva-aerobus.svg);
        }
    }

    &.volaris {
        background: #a12885;
        background: linear-gradient(180deg, #d02faa, #a12885);

        .bg-img {
            background-image: url(/assets-tb/img/tiquetesbaratos/logos/logo-volaris.png);
        }
    }

    &.wingo {
        background: #321b74;
        background: linear-gradient(180deg, #6633cc, #321b74);

        .bg-img {
            background-image: url(/assets-tb/img/tiquetesbaratos/logos/logo-wingo.png);
        }
    }

    &.jetsmart {
        background: #11304c;
        background: linear-gradient(180deg, #1d4a77, #11304C);

        .bg-img {
            background-image: url(/assets-tb/img/tiquetesbaratos/logos/logo-smart.png);
        }
    }

    &.delta {
        background: #11172b;
        background: linear-gradient(180deg, #1c2649, #11172b);

        .bg-img {
            background-image: url(/assets-tb/img/tiquetesbaratos/logos/logo-delta.svg);
        }
    }
}

@media (max-width:767px) {
    .c-phone-numbers {
        .float-right {
            padding-right: 40px;
        }

        background-color: $background-color_2;
        border: none;
        border-radius: 0;
        bottom: 0;
        left: 0;
        position: fixed;
        right: 69px;
        top: 0;
        width: auto;

        &:before {
            content: "";
            background-color: $background-color_3;
            bottom: 0;
            left: 0;
            position: absolute;
            right: -69px;
            top: 0;
        }
    }



    .h-mobile {
        height: 77px;
        top: 0;
        left: 0;
        position: absolute;
        right: 0;
    }

    .b-mobile {
        background-color: $background-color_2;
        display: block;
    }

    .c-header {
        .navbar-brand {
            img {
                height: 80px;
                width: 80px;
            }
        }

        .ch-dropdown {
            padding: 9px 0;
            top: 4px;

            .nav-link {
                &:after {
                    background: transparent;
                }
            }

            .dropdown-menu {
                background-color: $background-color_1;
                border: none;
                border-radius: 0;
                bottom: 0;
                left: 0;
                position: fixed;
                right: 0;
                top: 0;
            }
        }

        box-shadow: 0 .125rem .25rem rgba(0,0,0,.075) !important;
    }

    .c-box-home {

        .row {
            margin-right: auto !important;
            margin-left: auto !important;
        }

        &:after {
            background-image: url(/assets-tb/img/tiquetesbaratos/bg-30.svg);
            background-position: top;
            left: -1px;
            top: -1px;
        }
    }

    .c-box {
        .tab-content {
            .btn {
                padding-left: 0 !important;
                padding-right: 0 !important;
                width: 100%;
            }

            .c-input-box {
                .c-going {
                    -ms-flex: inherit !important;
                    flex: inherit !important;
                    max-width: 100% !important;
                }
            }
        }
    }

    .c-banner-full {
        h2 {
            font-size: 30px !important;
        }

        background-size: inherit;
        background-position: 60% 0;
    }

    .cp-banner-full {
        h2 {
            font-size: 30px !important;
        }

        background-size: inherit;
        background-position: 60% 0;
    }

    .c-banners {
        .c-figure {
            img {
                -o-object-fit: cover;
                object-fit: cover;
                -o-object-position: left;
                object-position: left;
            }
        }
    }

    .line-fligths-2 {
        .f-01 {
            left: 9px;
            top: -2px;
        }
    }

    .d-txt-mobile {
        left: 0;
        margin: auto;
        position: absolute;
        right: 0;
        top: -1px;
    }

    .c-row-line {
        .icon-plane-right {
            top: 13px;
        }
    }

    .c-click-detail {
        .icon {
            display: none;
        }
    }

    .c-if-round {
        border: none;
    }

    .c-matrix {
        background-color: $background-color_1;
    }

    .c-int-matrix {
        height: 95%;

        .table-dates {
            height: 95%;
        }
    }

    .row-dates-mobile {
        height: 100%;
    }

    .date-mobile {
        position: absolute;
        right: 15px;
        top: 22px;
    }

    .h-mobile-1 {
        height: 120px;
        top: 0;
        left: 0;
        padding: 0 15px;
        position: absolute;
        right: 0;
    }

    .b-mobile-1 {
        left: 0;
        padding: 0 15px;
        position: absolute;
        right: 0;
        bottom: 0;
        overflow-x: hidden;
        overflow-y: auto;
        top: 120px;
    }

    .c-view-detail-info {
        .circle-top {
            left: -20px;
        }

        .circle-bottom {
            bottom: 34%;
            left: -20px;
        }

        .line {
            bottom: 40%;
            left: -20px;
        }
    }

    .c-modal-lateral-mobile {
        bottom: 0;
        position: fixed;
        z-index: 9999;
        background-color: $background-color_9;
        left: 0;
        right: 0;
        top: 0;
    }

    .ml-header {
        background-color: $background-color_9;
        left: 0;
        right: 0;
        top: 0;
        height: 80px;
        position: absolute;
        z-index: 999;
    }

    .ml-body {
        bottom: 80px;
        top: 80px;
        background-color: $background-color_2;
        left: 0;
        position: absolute;
        right: 0;
        z-index: 999;
    }

    .ml-footer {
        background-color: $background-color_2;
        left: 0;
        position: absolute;
        right: 0;
        z-index: 999;
        bottom: 0;
        height: 80px;
    }

    .c-autocomplete {
        right: 0;
    }

    .a-link {
        &:hover {
            text-decoration: none;
        }

        span {
            color: $color_18;
        }
    }

    .mobile-slider-active {
        .list-1 {
            padding-right: 90px !important;
        }
    }

    .c-box-promo {
        .tab-content {
            .btn {
                padding-left: 0 !important;
                padding-right: 0 !important;
                width: 100%;
            }

            .c-input-box {
                .c-going {
                    -ms-flex: inherit !important;
                    flex: inherit !important;
                    max-width: 100% !important;
                }
            }
        }
    }
}

@media (min-width:768px) and (max-width:1024px) {
    .c-phone-numbers {
        &:after {
            border-color: $border-color_1;
            top: -17px;
            content: "";
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 0 17px 17px;
            position: absolute;
            right: 50px;
        }

        &:before {
            content: "";
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 0 17px 17px;
            position: absolute;
            right: 50px;
            border-color: $border-color_2;
            top: -18px;
        }
    }



    .c-icon-menu {
        display: none;
    }

    .c-passengers {
        font-size: 14px;
    }

    .c-box-home {
        &:after {
            background-image: url(/assets-tb/img/tiquetesbaratos/bg-30.svg);
            background-position: top;
            left: -1px;
            top: -1px;
        }
    }

    .c-box {
        .tab-content {
            .c-input-box {
                .c-button {
                    .btn {
                        font-size: 12px;
                    }
                }
            }
        }

        .s-nav-md {
            margin-top: -30px;
        }
    }

    .c-box-mobile {
        display: none;
    }

    .c-box.c-flights {
        .c-btn-up {
            display: none;
        }
    }

    .c-banners {
        .c-figure {
            img {
                -o-object-fit: cover;
                object-fit: cover;
                -o-object-position: left;
                object-position: left;
            }
        }
    }

    .c-modal-desk {
        background-color: $background-color_1;
        border-radius: 0;
        bottom: 0;
        display: none;
        left: 15px;
        margin: auto;
        position: absolute;
        right: 15px;
        top: 0;
        z-index: 999;
        width: 100%;
    }

    .c-overlay-desk {
        background-color: $background-color_47;
        bottom: 0;
        content: "";
        left: 0;
        position: fixed;
        right: 0;
        top: 0;
        z-index: 998;
    }

    .view-detail {
        cursor: pointer;
    }

    .header-logo-redondo {
        position: relative;
        top: 36px !important;
        z-index: 10;
    }

    .header-logo-sencillo {
        position: relative;
        top: 34px !important;
        z-index: 10;
    }

    .c-out-off {
        padding: 10px 0;
    }

    .cvd-flights {
        .circle-bottom {
            left: auto;
            right: 18px;
        }

        .circle-top {
            left: auto;
            right: 18px;
        }

        .line {
            left: auto;
            right: 29px;
        }
    }

    .cbt-date {
        top: -4px;
        left: -100px;
    }

    .c-detail-modal-desktop {
        .c-view-detail {
            .line {
                left: auto;
                margin: initial;
                right: 37px;
            }

            .circle-bottom {
                left: auto;
                margin: initial;
                right: 33px;
            }

            .circle-top {
                left: auto;
                margin: initial;
                right: 33px;
            }
        }
    }

    .line-fligths-1 {
        .f-01 {
            left: -38px;
            top: -2px;
        }
    }

    .line-fligths-2 {
        .f-01 {
            left: 17px;
            top: -2px;
        }
    }

    .d-txt-mobile {
        left: 0;
        margin: auto;
        position: absolute;
        right: 0;
        top: -1px;
    }

    .row-active {
        &:hover {
            background-color: $background-color_1;

            &:before {
                border: 1px solid #003b98;
                border-radius: 50px;
                bottom: 4px;
                content: "";
                left: 4px;
                position: absolute;
                right: 4px;
                top: 4px;
            }
        }
    }

    .c-click-detail {
        .icon {
            right: 18px;
            top: 8px;
        }
    }

    .c-autocomplete {
        right: 0;
    }


    .c-box-promo {
        .tab-content {
            .c-input-box {
                .c-button {
                    .btn {
                        font-size: 12px;
                    }
                }
            }
        }

        .s-nav-md {
            margin-top: -30px;
        }
    }
}

@media (min-width:1025px) {
    .c-phone-numbers {
        &:after {
            border-color: $border-color_1;
            top: -17px;
            content: "";
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 0 17px 17px;
            position: absolute;
            right: 50px;
        }

        &:before {
            content: "";
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 0 17px 17px;
            position: absolute;
            right: 50px;
            border-color: $border-color_2;
            top: -18px;
        }
    }

    .c-icon-menu {
        display: none;
    }

    .c-header {
        .ch-dropdown {
            .dropdown-menu {
                box-shadow: 0 .5rem 1rem rgba(0,0,0,.15);
                left: -150px;
                top: 60px;
                width: 440px;

                .dropdown-item {
                    span {
                        color: $color_3;

                        + {
                            span {
                                color: $color_4;
                                float: right;
                            }
                        }
                    }

                    &:active {
                        background: transparent;
                    }
                }
            }
        }
    }

    .c-modal-desk {
        background-color: $background-color_1;
        border-radius: 0;
        bottom: 0;
        display: none;
        left: 15px;
        margin: auto;
        position: absolute;
        right: 15px;
        top: 0;
        z-index: 999;
        width: 100%;
    }

    .c-overlay-desk {
        background-color: $background-color_47;
        bottom: 0;
        content: "";
        left: 0;
        position: fixed;
        right: 0;
        top: 0;
        z-index: 998;
    }

    .view-detail {
        cursor: pointer;
    }

    .ch-row {
        left: 0;
        position: absolute;
        right: 0;
    }

    .header-logo-redondo {
        position: relative;
        top: 30px !important;
        z-index: 10;
    }

    .header-logo-sencillo {
        position: relative;
        top: 30px !important;
        z-index: 10;
    }

    .c-out-off {
        padding: 8px 0 12px;
    }

    .cvd-flights {
        .circle-bottom {
            left: auto;
            right: 25px;
        }

        .circle-top {
            left: auto;
            right: 25px;
        }

        .line {
            left: auto;
            right: 29px;
        }
    }

    .cbt-date {
        top: -7px;
        left: -100px;
    }

    .c-detail-modal-desktop {
        .c-view-detail {
            .line {
                left: auto;
                margin: initial;
                right: 37px;
            }

            .circle-bottom {
                left: auto;
                margin: initial;
                right: 33px;
            }

            .circle-top {
                left: auto;
                margin: initial;
                right: 33px;
            }
        }
    }

    .row-active {
        &:hover {
            background-color: $background-color_1;

            &:before {
                border: 1px solid #003b98;
                border-radius: 50px;
                bottom: 4px;
                content: "";
                left: 4px;
                position: absolute;
                right: 4px;
                top: 4px;
            }
        }
    }

    .c-click-detail {
        .icon {
            right: 24px;
            top: 8px;
        }
    }

    .c-txt-total {
        font-size: 18px;
    }

    .c-txt-rate {
        font-size: 22px;
    }

    .list-1 {
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }

    .c-box-promo {
        .tab-content {
            .c-input-box {
                .c-button {
                    .btn {
                        position: relative;
                        top: -17px;
                    }
                }
            }
        }

        .s-nav-md {
            margin-top: -30px;
        }
    }
}

@media (min-width:991px) and (max-width:1199px) {
    .c-header {
        .md-container {
            max-width: 100%;
        }
    }
}

@media (min-width:769px) and (max-width:803px) and (orientation:landscape) {
    .c-header {
        .navbar {
            a {
                * {
                    font-size: 14px !important;
                }
            }
        }
    }
}

@media (max-width:767px) and (orientation:landscape) {
    .c-header {
        [aria-label=secundary] {
            .c-mobile {
                max-width: 100%;
            }
        }
    }
}

@media (min-width:768px) and (max-width:1024px) and (orientation:portrait) {
    .c-passengers {
        width: 50%;
    }

    .c-banner-full {
        background-position: -240px 0;
    }

    .cp-banner-full {
        background-position: -240px 0;
    }
}

@media (min-width:768px) and (max-width:1024px) and (orientation:landscape) {
    .c-passengers {
        width: 100%;
    }

    .c-box {
        .tab-content {
            .btn {
                padding-left: 0 !important;
                padding-right: 0 !important;
                width: 100%;
            }
        }
    }

    .c-box-promo {
        .tab-content {
            .btn {
                padding-left: 0 !important;
                padding-right: 0 !important;
                width: 100%;
            }
        }
    }
}

@media (min-width:576px) {
    .modal-md {
        margin: 1.75rem auto;
        max-width: 690px;
    }
}

.cr-int, .cole {
    display: flex;
    padding-right: 15px;

    .rounded {
        border-radius: 0.25rem;
    }
}
.cr-int .cole:last-child {
    @media ($desktop) {
        padding-right: 0 !important;
    }
}

.cole {
    a, a:active, a:hover {
        text-decoration: none;
        color: gray !important;
    }
}



@media (max-width: 767px), (min-width: 768px) and (max-width: 1024px) {
    .c-box-home {
        .booker {
            z-index: 999;
        }

        .bg-img {
            display: none;
            z-index: 1;
        }

        &:before {
            background-image: none !important;
            position: relative;
        }

        &.avianca {
            &:after {
                background-repeat: no-repeat;
                background-size: contain;
                background-image: url(/assets-tb/img/tiquetesbaratos/bg-30.svg);
                background-position: top;
                content: "";
                position: absolute;
                left: 0;
                right: 0;
                top: 0;
                bottom: 0;
            }

            .bg-img {
                display: block;
            }

            .c-title-home {
                display: none;
            }
        }

        &.copa {
            &:after {
                background-repeat: no-repeat;
                background-size: contain;
                background-image: url(/assets-tb/img/tiquetesbaratos/bg-30.svg);
                background-position: top;
                content: "";
                position: absolute;
                left: 0;
                right: 0;
                top: 0;
                bottom: 0;
            }

            .bg-img {
                display: block;
            }

            .c-title-home {
                display: none;
            }
        }

        &.latam {
            &:after {
                background-repeat: no-repeat;
                background-size: contain;
                background-image: url(/assets-tb/img/tiquetesbaratos/bg-30.svg);
                background-position: top;
                content: "";
                position: absolute;
                left: 0;
                right: 0;
                top: 0;
                bottom: 0;
            }

            .bg-img {
                display: block;
            }

            .c-title-home {
                display: none;
            }
        }

        &.iberia {
            &:after {
                background-repeat: no-repeat;
                background-size: contain;
                background-image: url(/assets-tb/img/tiquetesbaratos/bg-30.svg);
                background-position: top;
                content: "";
                position: absolute;
                left: 0;
                right: 0;
                top: 0;
                bottom: 0;
            }

            .bg-img {
                display: block;
            }

            .c-title-home {
                display: none;
            }
        }

        &.airfrance {
            &:after {
                background-repeat: no-repeat;
                background-size: contain;
                background-image: url(/assets-tb/img/tiquetesbaratos/bg-30.svg);
                background-position: top;
                content: "";
                position: absolute;
                left: 0;
                right: 0;
                top: 0;
                bottom: 0;
            }

            .bg-img {
                display: block;
            }

            .c-title-home {
                display: none;
            }
        }

        &.aeromexico {
            &:after {
                background-repeat: no-repeat;
                background-size: contain;
                background-image: url(/assets-tb/img/tiquetesbaratos/bg-30.svg);
                background-position: top;
                content: "";
                position: absolute;
                left: 0;
                right: 0;
                top: 0;
                bottom: 0;
            }

            .bg-img {
                display: block;
            }

            .c-title-home {
                display: none;
            }
        }

        &.united {
            &:after {
                background-repeat: no-repeat;
                background-size: contain;
                background-image: url(/assets-tb/img/tiquetesbaratos/bg-30.svg);
                background-position: top;
                content: "";
                position: absolute;
                left: 0;
                right: 0;
                top: 0;
                bottom: 0;
            }

            .bg-img {
                display: block;
            }

            .c-title-home {
                display: none;
            }
        }

        &.continental {
            &:after {
                background-repeat: no-repeat;
                background-size: contain;
                background-image: url(/assets-tb/img/tiquetesbaratos/bg-30.svg);
                background-position: top;
                content: "";
                position: absolute;
                left: 0;
                right: 0;
                top: 0;
                bottom: 0;
            }

            .bg-img {
                display: block;
            }

            .c-title-home {
                display: none;
            }
        }

        &.american {
            &:after {
                background-repeat: no-repeat;
                background-size: contain;
                background-image: url(/assets-tb/img/tiquetesbaratos/bg-30.svg);
                background-position: top;
                content: "";
                position: absolute;
                left: 0;
                right: 0;
                top: 0;
                bottom: 0;
            }

            .bg-img {
                display: block;
            }

            .c-title-home {
                display: none;
            }
        }

        &.aircanada {
            &:after {
                background-repeat: no-repeat;
                background-size: contain;
                background-image: url(/assets-tb/img/tiquetesbaratos/bg-30.svg);
                background-position: top;
                content: "";
                position: absolute;
                left: 0;
                right: 0;
                top: 0;
                bottom: 0;
            }

            .bg-img {
                display: block;
            }

            .c-title-home {
                display: none;
            }
        }

        &.jetblue {
            &:after {
                background-repeat: no-repeat;
                background-size: contain;
                background-image: url(/assets-tb/img/tiquetesbaratos/bg-30.svg);
                background-position: top;
                content: "";
                position: absolute;
                left: 0;
                right: 0;
                top: 0;
                bottom: 0;
            }

            .bg-img {
                display: block;
            }

            .c-title-home {
                display: none;
            }
        }

        &.satena {
            &:after {
                background-repeat: no-repeat;
                background-size: contain;
                background-image: url(/assets-tb/img/tiquetesbaratos/bg-30.svg);
                background-position: top;
                content: "";
                position: absolute;
                left: 0;
                right: 0;
                top: 0;
                bottom: 0;
            }

            .bg-img {
                display: block;
            }

            .c-title-home {
                display: none;
            }
        }

        &.clicair {
            &:after {
                background-repeat: no-repeat;
                background-size: contain;
                background-image: url(/assets-tb/img/tiquetesbaratos/bg-30.svg);
                background-position: top;
                content: "";
                position: absolute;
                left: 0;
                right: 0;
                top: 0;
                bottom: 0;
            }

            .bg-img {
                display: block;
            }

            .c-title-home {
                display: none;
            }
        }

        &.vivaaerobus {
            &:after {
                background-repeat: no-repeat;
                background-size: contain;
                background-image: url(/assets-tb/img/tiquetesbaratos/bg-30.svg);
                background-position: top;
                content: "";
                position: absolute;
                left: 0;
                right: 0;
                top: 0;
                bottom: 0;
            }

            .bg-img {
                display: block;
            }

            .c-title-home {
                display: none;
            }
        }

        &.volaris {
            &:after {
                background-repeat: no-repeat;
                background-size: contain;
                background-image: url(/assets-tb/img/tiquetesbaratos/bg-30.svg);
                background-position: top;
                content: "";
                position: absolute;
                left: 0;
                right: 0;
                top: 0;
                bottom: 0;
            }

            .bg-img {
                display: block;
            }

            .c-title-home {
                display: none;
            }
        }

        &.wingo {
            &:after {
                background-repeat: no-repeat;
                background-size: contain;
                background-image: url(/assets-tb/img/tiquetesbaratos/bg-30.svg);
                background-position: top;
                content: "";
                position: absolute;
                left: 0;
                right: 0;
                top: 0;
                bottom: 0;
            }

            .bg-img {
                display: block;
            }

            .c-title-home {
                display: none;
            }
        }

        &.jetsmart {
            &:after {
                background-repeat: no-repeat;
                background-size: contain;
                background-image: url(/assets-tb/img/tiquetesbaratos/bg-30.svg);
                background-position: top;
                content: "";
                position: absolute;
                left: 0;
                right: 0;
                top: 0;
                bottom: 0;
            }

            .bg-img {
                display: block;
            }

            .c-title-home {
                display: none;
            }
        }
    }
    /*
    .c-title-home {
        h2 {
            font-size: 18px;
            text-shadow: 2px 2px 6px rgba(0,0,0,.4);
        }
    }*/
}

@media (max-width: 767px), (min-width: 768px) and (max-width: 1300px) {
    .c-br .c-scroll-01 {
        overflow-x: auto;
    }
}


@media (max-width: 767px) {
    .c-br .c-scroll-01 .c-row .cr-int .cole {
        width: 300px;
    }
}

.c-scroll-01 {
    -webkit-overflow-scrolling: touch;
    -ms-overflow-style: -ms-autohiding-scrollbar;
}

.c-br .c-scroll-01 .c-row {
    width: auto;
}

.c-br .c-scroll-01 .c-row {
    white-space: nowrap;
}

.c-br .c-scroll-01 .c-row .cole {
    min-width: 300px
}

.c-br {
    p {
        font-size: 14px;
    }

    h4 {
        font-size: 28px;
        @media ($phone) {
            font-size: 18px;
        }
    }

    h5 {
        color: #555;
        font-size: 16px;
    }

    .strong {
        font-family: Roboto-Bold;
    }

    .text-truncate {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}

.c-br.cb-in {
    h4 {
        @media ($tablet) {
            font-size: 18px;
        }
    }
}

.input-group {
    .icon {
        color: var(--icon-strong) !important;
    }
}

.c-box-home.clicair{
    .bg-img{
        margin-top: 1rem !important;
    }
}


// Variables
$gray-black-color: #4C4C4C;
$icon-size: 50px;
$large-icon-size: 60px;
$extra-large-icon-width: 130px;
$extra-large-icon-height: 100px;

// Estilos generales
.gray-black-color {
    color: $gray-black-color;
}

// Modal de formulario de pago
.modal-payform {
    i {
        background-size: $icon-size;
        background-repeat: no-repeat;
        width: $icon-size;
        height: $icon-size;
        margin-left: 1rem;
        margin-right: 1rem;
        background-position: center;

        @media screen and (max-width: 500px) {
            margin-left: 0.5rem;
            margin-right: 0.5rem;
        }
    }
}

// Contenido de marcas de bancos
.content-banks-brand {
    .icon-bank-brand {
        width: $large-icon-size;
        height: $large-icon-size;
        background-position: center;
    }

    .ptw-icon-multiple-co {
        width: $extra-large-icon-width;
        height: $extra-large-icon-height;

        @media screen and (max-width: 769px) {
            width: 200px;
        }
    }

    .ptw-icon-bancolombia {
        width: $extra-large-icon-width;
        height: 70px;
    }
}

// Logos del banner
.logos-ba-banner {
    i {
        background-size: 45px;
        background-repeat: no-repeat;
        width: $icon-size;
        height: $icon-size;
        background-position: center;
    }
}

// Íconos de métodos de pago
.ptw-icon-bancolombia {
    background: url(https://3.cdnpt.com/images/bancos/logo-bancolombia.svg) no-repeat;
    height: $icon-size;
    width: $icon-size;
}

.ptw-icon-efecty {
    background: url(https://3.cdnpt.com/images/bancos/logo-efecty.svg) no-repeat;
    height: $icon-size;
    width: $icon-size;
}

.ptw-icon-creditcard {
    background: url(https://3.cdnpt.com/images/bancos/logo-pse.svg) no-repeat;
    height: $icon-size;
    width: $icon-size;
}

.ptw-icon-nequi {
    background: url(/assets-tb/img/logos_banks/nequi.svg) no-repeat;
    height: $icon-size;
    width: $icon-size;
}

.ptw-icon-plata {
    background: url(/assets-tb/img/logos_banks/daviplata.png) no-repeat 100% 100%;
    height: $icon-size;
    width: $icon-size;
}

.ptw-icon-multiple-co {
    background: url(/assets-tb/img/logos_banks/multiple_banks.svg) no-repeat;
    height: $icon-size;
    width: $icon-size;
}

// Media queries
@media (min-width: 576px) {
    .modal-dialog {
        margin: 1.75rem auto;
    }

    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
