<template>
    <div class="modal fade modal-change" id="modal-change-booking" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered modal-md">
            <div class="modal-content">
                <div class="modal-header">
                    <p class="modal-title font-16 font-regular">{{__('messages.change_your_reservation')}}</p>
                    <!--<button type="button" class="btn-close" v-on:click="close()"></button>-->
                </div>
                <div class="modal-body px-2">

                    <!--- Cambio de precios -->
                    <template v-if="strPrice == 'priceUp'">
                        <span class="icon icon-bell d-block text-center color-green mb-3 font-26" :class="{'color-yellow': strPrice == 'priceUp' }"></span>
                        <h5 class="text-center mb-3">{{ __('messages.chage_price') }}</h5>
                    </template>
                    <template v-if="strPrice == 'priceDown'">
                        <span class="icon icon-bell d-block text-center color-green mb-3 font-26"></span>
                        <h5 class="text-center mb-3">
                            ¡{{ __('messages.price_your_flight_has_down') }} <br> <span class="color-green">
                                <CurrencyDisplay :amount="diffPrice" :showCurrencyCode="true" />
                            </span> !
                        </h5>
                    </template>
                    <template v-if="strPrice == 'noAvailability'">
                        <div class="row">
                            <div class="col-12 d-flex justify-content-center mb-3">
                                <svg width="45" height="45" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M10.656 8.864q0-2.208 1.568-3.776t3.776-1.568 3.776 1.568 1.6 3.776q0 0.256-0.064 0.448l-1.76 6.944q-0.096 1.408-1.12 2.368t-2.432 0.96q-1.376 0-2.4-0.928t-1.152-2.304q-0.32-0.96-0.672-2.112t-0.736-2.784-0.384-2.592zM12.416 24.928q0-1.472 1.056-2.496t2.528-1.056 2.528 1.056 1.056 2.496q0 1.504-1.056 2.528t-2.528 1.056-2.528-1.056-1.056-2.528z"
                                          fill="#bd030a"></path>
                                </svg>
                            </div>
                            <div class="col-12 d-flex justify-content-center mb-3 text-center">
                                <h4 style="color: #bd030a;">{{ __('messages.error_reserv') }}</h4>
                            </div>

                            <div class="col-6 d-flex justify-content-center mb-3">

                            </div>
                        </div>
                    </template>

                    <template v-if="strPrice == 'noChange'">
                        <div class="row">
                            <div class="col-12 d-flex justify-content-center mb-3">
                                <svg width="45" height="45" viewBox="0 0 45 45" fill="none"
                                     xmlns="http://www.w3.org/2000/svg">
                                    <path d="M22.5 45C34.1268 45 43.8158 36.0646 44.8923 24.7608C45 23.5766 44.0311 22.5 42.8469 22.5C41.7703 22.5 40.9091 23.2536 40.8014 24.3301C39.9402 33.6962 31.9737 41.0168 22.5 41.0168C16.1483 41.0168 10.4426 37.7871 7.21292 32.8349H10.2273C11.3038 32.8349 12.2727 31.866 12.2727 30.7895C12.2727 29.7129 11.3038 28.744 10.2273 28.744H2.04545C0.968899 28.744 0 29.7129 0 30.7895V38.9713C0 40.0478 0.968899 41.0168 2.04545 41.0168C3.12201 41.0168 4.09091 40.0478 4.09091 38.9713V35.5263C8.18182 41.2321 14.9641 45 22.5 45ZM22.5 0C10.8732 0 1.29187 8.82775 0.107656 20.2392C2.40629e-08 21.4234 0.968899 22.5 2.15311 22.5C3.22967 22.5 4.09091 21.7464 4.19856 20.6699C5.05981 11.3038 13.0263 3.98325 22.5 3.98325C28.8517 3.98325 34.5574 7.21292 37.7871 12.1651H34.7727C33.6962 12.1651 32.7273 13.134 32.7273 14.2105C32.7273 15.2871 33.6962 16.256 34.7727 16.256H42.9545C44.0311 16.256 45 15.2871 45 14.2105V6.13636C45 5.05981 44.0311 4.09091 42.9545 4.09091C41.878 4.09091 40.9091 5.05981 40.9091 6.13636V9.58134C36.9258 3.76794 30.1435 0 22.5 0ZM20.7775 10.012C20.7775 9.04306 21.6388 8.18182 22.6077 8.18182C23.5766 8.18182 24.4378 8.93541 24.4378 10.012V10.7656C26.5909 11.1962 27.9904 12.2727 28.8517 13.4569C29.4976 14.3182 29.1746 15.7177 28.0981 16.1483C27.3445 16.4713 26.4833 16.256 26.0526 15.61C25.5144 14.8565 24.4378 13.9952 22.823 13.9952C21.4234 13.9952 19.1627 14.7488 19.1627 16.7943C19.1627 18.7321 20.8852 19.4856 24.5455 20.6699C29.4976 22.3923 30.6818 24.8684 30.6818 27.7751C30.6818 33.1579 25.5144 34.2344 24.5455 34.3421V35.0957C24.5455 36.0646 23.7919 36.9258 22.7153 36.9258C21.7464 36.9258 20.8852 36.1723 20.8852 35.0957V34.2345C19.5933 33.9115 16.9019 32.9426 15.3947 29.9282C14.9641 29.067 15.5024 27.8828 16.3636 27.4522C17.2249 27.1292 18.1938 27.4522 18.6244 28.2057C19.2703 29.4976 20.5622 31.0048 22.9306 31.0048C24.8684 31.0048 27.0215 30.0359 27.0215 27.6675C27.0215 25.7297 25.622 24.6531 22.3923 23.4689C20.1316 22.7153 15.5024 21.3158 15.5024 16.6866C15.5024 16.4713 15.5024 11.7344 20.8852 10.6579V10.012H20.7775Z"
                                          fill="#EFBC38" />
                                </svg>
                            </div>
                            <div class="col-12 d-flex justify-content-center mb-3">
                                <label>{{ __('messages.chage_price') }}</label>
                            </div>
                        </div>
                    </template>

                    <!--- Cambio de familias en la reservacion -->

                    <template v-if="summary.flightInfo.changeFamilyFare">
                        <div v-for="familyfare in summary.flightInfo.familyFares">
                            <div class="alert alert-light px-2 font-14 mx-2 py-1">
                                {{__("messages.the_rate")}} <strong>{{familyfare.familyFareOld}}</strong> <span v-html="__('messages.is_not_available_html')"></span> <strong>{{familyfare.familyFare}}</strong>.
                            </div>
                        </div>
                    </template>
                    <template v-if="strPrice == 'priceUp' || strPrice == 'priceDown'">
                        <div class="row mx-0">
                            <div class="col pe-0 ps-0">
                                <div class="alert alert-light px-2 font-14 mx-2 py-1 bg-white">
                                    <span class="d-block text-center font-14 font-semibold color-gray-100">{{ __('messages.before') }}</span>
                                    <span class="d-block text-center font-18 font-semibold color-gray-100">
                                        <CurrencyDisplay :amount="summary.detail.totalAmountOld" :showCurrencyCode="true" />
                                    </span>
                                </div>
                            </div>
                            <div class="col-1 px-0">
                                <span class="icon icon-arrow-forward color-green font-26 d-block mt-2 pt-2 ms-1 color-green" :class="{'color-yellow': strPrice == 'priceUp' }"></span>
                            </div>
                            <div class="col ps-0 pe-0">
                                <div class="alert alert-light px-2 font-14 mx-2 py-1 bg-white border-hard">
                                    <span class="d-block text-center color-green font-14" :class="{'color-yellow': strPrice == 'priceUp' }">{{__('messages.today')}}:</span>
                                    <span class="d-block text-center font-18 font-bold" :class="{'color-green': strPrice == 'priceDown' }">
                                        <CurrencyDisplay :amount="summary.detail.totalAmount" :showCurrencyCode="true" />
                                    </span>
                                </div>
                            </div>
                            <div class="col-12">
                                <p class="text-center">{{__('messages.finalize_your_reservation')}}</p>
                            </div>

                        </div>
                    </template>

                </div>
                <div class="modal-footer border-0 pt-0">
                    <button id="CloseButton" v-if="strPrice != 'noAvailability'" type="button" class="btn w-100-xs py-3 btn-orange px-4" v-on:click="close()">{{__('messages.ok')}}</button>
                    <button id="CloseButton" v-else type="button" class="btn w-100-xs py-3 btn-orange px-4" v-on:click="goBack()">{{ __('messages.return_label') }} </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>

    const site = window.__pt.settings.site;
    import { storeToRefs } from 'pinia';
    import { FlightsAnalytic } from '../../../../utils/analytics/FlightsAnalytics'
    import { useFingerStore } from '../../../stores/fingerprint';
    import CurrencyDisplay from '../../common/CurrencyDisplay.vue';
    export default {
        props: {
            quote: {},
            summary: {},
            productUrl: '',
        },
        data() {
            return {
                diffPrice: 0,
                strPrice: "",
                siteConfig: site
            };
        },
        setup() {
            const storeFingerStore = useFingerStore();
            const { getFingerprintHash } = storeToRefs(storeFingerStore);

            return {
                getFingerprintHash
            };
        },
        async mounted() {

            this.setDiff();
        },
        components: {
            CurrencyDisplay
        },
        methods: {
            setDiff() {
                this.diffPrice = this.summary.detail.totalAmount - this.summary.detail.totalAmountOld;

                if (this.summary.status == "ERROR_TOKEN") {
                    this.strPrice = "noAvailability";
                    FlightsAnalytic.setErrorToken({
                        action: "fingerprint",
                        oldRate: 0,
                        newRate: 0
                    }, this.quote, this.getFingerprintHash, this.summary.bookingId);

                    return;
                }

                if (this.summary.detail.totalAmount > this.summary.detail.totalAmountOld) {
                    this.strPrice = "priceUp";
                    FlightsAnalytic.setChangeFare({
                        action: "mayor",
                        oldRate: this.summary.detail.totalAmountOld,
                        newRate: this.summary.detail.totalAmount
                    }, this.quote);
                }

                if (this.summary.detail.totalAmount < this.summary.detail.totalAmountOld) {
                    this.strPrice = "priceDown";
                    FlightsAnalytic.setChangeFare({
                        action: "menor",
                        oldRate: this.summary.detail.totalAmountOld,
                        newRate: this.summary.detail.totalAmount
                    }, this.quote);
                }

                if (this.summary.detail.totalAmountOld == 0) {
                    this.strPrice = "noChange";
                }

                if (this.summary.detail.totalAmount == 0) {
                    this.strPrice = "noAvailability";
                    FlightsAnalytic.setChangeFare({
                        action: "no-disponible",
                        oldRate: this.summary.detail.totalAmountOld,
                        newRate: this.summary.detail.totalAmount
                    }, this.quote);
                }

                if (this.summary.flightInfo.changeFamilyFare) {
                    this.strPrice = this.strPrice == "" ? "noChange" : this.strPrice;
                    FlightsAnalytic.setChangeFare({
                        action: "cambio-familia",
                        oldRate: this.summary.detail.totalAmountOld,
                        newRate: this.summary.detail.totalAmount
                    }, this.quote);
                }
            },
            goBack() {
                window.location.href = `${this.siteConfig.siteUrl}${this.productUrl}`;
            },

            close() {
                window.location.href = `${this.summary.urlRedirect}`;
                var ChangeBookingModal = bootstrap.Modal.getInstance(document.getElementById('modal-change-booking'));
                ChangeBookingModal.hide();
            }
        }
    }
</script>
