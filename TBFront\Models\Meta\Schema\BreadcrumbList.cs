﻿using System.Text.Json.Serialization;

namespace TBFront.Models.Meta.Schema
{
    public class BreadcrumbList
    {
        [JsonPropertyName("@context")]
        public string? Context { get; set; }

        [JsonPropertyName("@type")]
        public string? Type { get; set; }

        [JsonPropertyName("itemListElement")]
        public List<BreadcrumbElement>? ItemListElement { get; set; }

        [JsonIgnore]
        public int Count { get; set; }


        public BreadcrumbList()
        {
            ItemListElement = new List<BreadcrumbElement>();
        }

    }
}
