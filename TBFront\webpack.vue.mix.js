﻿const mix = require('laravel-mix');
const path = require('path');

const environment = process.env?.MIX_ENV ? process.env.MIX_ENV : "test";
console.log(environment, 'environment');

mix.options({
	processCssUrls: true,
	runtimeChunkPath: '.'
});

mix.copy('Resources/js/lang/es.js', `wwwroot/assets-tb-${environment}/js/lang`);
mix.copy('Resources/js/lang/en.js', `wwwroot/assets-tb-${environment}/js/lang`);

mix.combine(['./node_modules/jquery/dist/jquery.slim.min.js',
	'./node_modules/slick-carousel/slick/slick.min.js'], `wwwroot/assets-tb-${environment}/js/libs/slick.bundle.min.js`);

mix.combine([
	'node_modules/bootstrap/dist/js/bootstrap.bundle.min.js',
	'node_modules/bootstrap-slider/dist/bootstrap-slider.min.js',
	'Resources/js/settings.js',
	'./node_modules/@easepick/core/dist/index.umd.js',
	'./node_modules/@easepick/datetime/dist/index.umd.js',
	'./node_modules/@easepick/base-plugin/dist/index.umd.js',
	'./node_modules/@easepick/lock-plugin/dist/index.umd.js',
	'./node_modules/@easepick/range-plugin/dist/index.umd.js',
], `wwwroot/assets-tb-${environment}/js/default.js`);

mix.js('Resources/js/package/app.js', `js/vue`).vue({
	extractStyles: false
}).webpackConfig({
	output: {
		chunkFilename: (pathData) => {
			return pathData.chunk.runtime + "/[name].[contenthash].js"
		},
		filename: "[name].js",
		publicPath: `/assets-tb-${environment}`, //Validar si cambia la url del cdn o el encarpetado
		path: path.resolve(__dirname, `wwwroot/assets-tb-${environment}/`)
	}
});

mix.copyDirectory(`wwwroot/assets-tb/tiquetesbaratos/css`, `wwwroot/assets-tb-${environment}/tiquetesbaratos/css`);

mix.disableSuccessNotifications();