@import './app', './base/_variables', './_variables.scss', 'base/_core', 'components/footer', 'components/header';



.airlines-container {
    display: grid;
    grid-template-columns: repeat(5, 1fr); // 5 columnas
    gap: 15px;
    max-width: 1300px;

    .airline-option {
        display: flex;
        align-items: center;
        padding: 10px 15px;
        border: 1px solid #ccc;
        border-radius: 4px;
        /*background-color: #f9f9f9;*/
        cursor: pointer;
        transition: 0.3s ease;
        /*min-width: 180px;*/
        text-align: left;
        position: relative;
        font-size: 1rem;

        input {
            display: none;
        }

        img {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            object-fit: contain; 
            background-color: white; 
            margin-right: 10px;
            display: block;
        }

        a {
            text-decoration: none;
            color: #007bff;
            font-weight: 500;
            flex-grow: 1;
        }

        &:has(input:checked) {
            background-color: #e0f3ff;
            border-color: #007bff;
        }
    }
}

.title {
    text-align: center;
    font-family: 'Roboto-Regular';
    font-size: 30px;
}


@media (max-width: 900px) {
    .airlines-container {
        grid-template-columns: repeat(3, 1fr); 
    }
}

@media (max-width: 600px) {
    .airlines-container {
        grid-template-columns: repeat(2, 1fr);
    }
}
