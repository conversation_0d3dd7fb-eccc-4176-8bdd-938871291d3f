﻿using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Options;
using System.Globalization;
using System.Net;
using System.Text.RegularExpressions;
using TBFront.Agent;
using TBFront.Models.Configuration;
using TBFront.Options;
using TBFront.Types;

namespace TBFront.Helpers
{
    public class ViewHelper
    {
        private readonly SettingsOptions _options;
        private readonly IStringLocalizer<Language> _localizer;
        private readonly AgentBrowser _agent;
        private readonly TextInfo _textInfo = new CultureInfo("en-US", false).TextInfo;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private string _device = string.Empty;
        private string _bot = null;
        private bool _isBot = false;

        public ViewHelper(IOptions<SettingsOptions> option, IStringLocalizer<Language> localizer, IHttpContextAccessor httpContextAccessor, AgentBrowser agentBrowser)
        {
            _agent = agentBrowser;
            _options = option.Value;
            _localizer = localizer;
            _httpContextAccessor = httpContextAccessor;
        }

        public string Localizer(string name, params object[] args)
        {
            return String.Format(_localizer[name], args);
        }

        public bool IsRobot(string? userAgent = null)
        {
            var userAgentHeader = userAgent ?? _httpContextAccessor.HttpContext.Request.Headers["User-Agent"];
            if (_bot is null)
            {
                _isBot = _agent.IsCrawler(userAgentHeader);
                _bot = "";
            }

            return _isBot;
        }


        public string DetectAgent(string? userAgent = null)
        {
            var userAgentHeader = userAgent ?? _httpContextAccessor.HttpContext.Request.Headers["User-Agent"];

            
            if (string.IsNullOrEmpty(_device))
            {
                _device = _agent.Device(userAgentHeader);
            }

            return _device;
        }

        public string Capitalize(string value = "")
        {
            return _textInfo.ToTitleCase(value.ToLower());
        }






        public string GetStarsHtml(double? stars = 0)
        {
            var htmlStar = "";
            var limit = 5;

            for (int i = 0; i < limit; i++)
            {
                if (stars > i)
                {
                    if ((stars - i) == 0.5)
                    {
                        htmlStar += "<i class='icons-halfstar'></i>";
                    }
                    else
                    {
                        htmlStar += "<i class='icons-star'></i>";
                    }
                }
                else
                {
                    htmlStar += "";
                }
            }


            return htmlStar;
        }

        public SectionResolution GetImageResolution()
        {
            var devices = _options.ImageResolutions;
            var device = DetectAgent();
            var section = devices.Desktop;
            section.Device = DeviceType.Desktop;

            if (device == DeviceType.Mobile)
            {
                section = devices.Mobile;
                section.Device = DeviceType.Mobile;
            }

            if (device == DeviceType.Tablet)
            {
                section = devices.Tablet;
                section.Device = DeviceType.Tablet;
            }

            return section;

        }


        public string LimitText(string text, int limit, bool addDots = true)
        {
            if(text.Length > limit)
            {
                return string.Concat(text.Substring(0, limit), addDots ? "..." : "");
            }

            
            return text;
        }

        public bool IsTablet()
        {
            var httpContext = _httpContextAccessor.HttpContext;
            string agent = httpContext?.Request?.Headers?["CloudFront-Is-Tablet-Viewer"].FirstOrDefault() ?? string.Empty;
            return _agent.IsTablet(agent);
        }


        public Dictionary<string, string> GetSpecialCitiesWords()
        {
            Dictionary<string, string> citysUtf8 = new Dictionary<string, string>();
            citysUtf8.Add("Bogota", "Bogotá");
            citysUtf8.Add("Medellin", "Medellín");
            citysUtf8.Add("Cucuta", "Cúcuta");

            return citysUtf8;
        }


        public string GetCurrentQueryString(HttpContext context)
        {
            return $"{context.Request.QueryString}";
        }
        public string GetCurrentQueryStringCom(HttpContext context, bool isLogout = false)
        {
            var path = context.Request.Path;
            var queryString = context.Request.QueryString;
            var encodedPath = WebUtility.UrlEncode(path);
            var encodedQueryString = WebUtility.UrlEncode(queryString.ToString());
            var fullUrlWithoutPath = $"{(isLogout ? path : encodedPath)}{(isLogout ? queryString : encodedQueryString)}";

            return fullUrlWithoutPath;
        }
        public string CastIcon(string strIcon)
        {
            if(strIcon.Contains("cutlery")){
                return "restaurant";
            }else{
                return strIcon;
            }
        }

        public string ReplaceDoubleHyphen(string phrase)
        {
            return Regex.Replace(phrase, @"--", "-");
        }


        public string Currency(double amount = 0)
        {
            var culture = new CultureInfo(_options.Culture).NumberFormat;
            culture.CurrencyPositivePattern = 2;
            return string.Format(culture, "{0:C0}", Math.Round(amount));
        }

        public string DateEmailCheckout(string dateString = "")
        {
            DateTime date = DateTime.Parse(dateString, CultureInfo.InvariantCulture);
            CultureInfo spanishCulture = new CultureInfo(_options.Culture);
            return  date.ToString("ddd, dd 'de' MMMM yyyy", spanishCulture);
        }

    }
}
