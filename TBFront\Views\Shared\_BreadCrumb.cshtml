﻿@using TBFront.Helpers
@using TBFront.Models.Meta.Metatags

@inject ViewHelper viewHelper

@{
    var metatag = ViewData["MetaTag"] as MetaTag;
    var breadCrumbClass = ViewData["BreadCrumbClass"] ?? "";
    var bgColor = ViewData["BgColor"] ?? "bg-white";
    var anchorColor = ViewData["AnchorColor"] ?? "";
}


<nav aria-label="breadcrumb">
    <ol class="breadcrumb @breadCrumbClass @bgColor  p-0" itemscope itemtype="https://schema.org/BreadcrumbList">
        @if (metatag != null)
        {
            foreach (var breadcrumb in metatag.BreadCrumbs.ItemListElement)
            {
                <li class="breadcrumb-item text-truncate" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
                    <a class="@anchorColor" itemprop="item" href="@(breadcrumb.Item.Id)">
                        <span itemprop="name">@(breadcrumb.Item.Name)</span>
                    </a>
                    <meta itemprop="position" content="@(breadcrumb.Position)" />
                </li>
            }
        }
    </ol>
</nav>