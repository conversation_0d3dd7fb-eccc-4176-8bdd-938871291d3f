﻿using Microsoft.AspNetCore.Localization.Routing;
using Microsoft.AspNetCore.Localization;
using System.Globalization;
using WebMarkupMin.AspNetCore6;

namespace TBFront.ServicesRegister
{
    public class WebMarkupRegister
    {

        public static void AddRegisters(IServiceCollection services, IConfiguration Configuration)
        {
           

            services.AddWebMarkupMin(options =>
            {
                options.AllowMinificationInDevelopmentEnvironment = true;
                options.AllowCompressionInDevelopmentEnvironment = true;
            })
            .AddHtmlMinification(options =>
            {
                options.MinificationSettings.RemoveRedundantAttributes = true;
                options.MinificationSettings.RemoveHttpProtocolFromAttributes = true;
                options.MinificationSettings.RemoveHttpsProtocolFromAttributes = false;
            })
            .AddHttpCompression();
        }

        public static void AddLanguagesRegisters(IServiceCollection services, IConfiguration Configuration)
        {

            var cultures = Configuration.GetSection("Settings:CulturesAllowed").Value;
            var supportedCultures = cultures.Split("|").Select(c => new CultureInfo(c)).ToList();
            var supportedCulturesList = cultures.Split("|").ToList();


            services.Configure<RequestLocalizationOptions>(options =>
            {
                options.DefaultRequestCulture = new RequestCulture(Configuration.GetSection("Settings:CultureApp").Value);
                options.SupportedUICultures = supportedCultures;
                options.RequestCultureProviders = new[]
                {
                    new RouteDataRequestCultureProvider { Options = options }
                };
            });
        }

    }
}
