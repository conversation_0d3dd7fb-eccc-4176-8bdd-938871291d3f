

.alert-warning-pt {
    border-left: 0.5rem solid $yellow-300 !important;
}

.alert-custom {
    color: $gray-800;
}
//Nuevos estilos
.alert-content{
    display: table-cell;
    vertical-align: top;
    padding-left: 10px;
}
.alert-icon{
    width: 24px;
    padding-right: 0.5rem;
    text-align: center;
}
.alert-content-message{
    margin-bottom: 0;
}
.alert-primary{
    background-color: $pink-100;
    button, a{
        color:  $pink-900;
    }
    button:hover, a:hover{
        color:  $pink-700;
    }
    .alert-content{
        color:  $pink-700;
    }
}

.alert-info-pt {
    border-left: .5rem solid $blue-300;
}
.alert-secondary {
    color: $color-primary;
    background-color: $blue-100;
    border-color: $blue-300;
}
