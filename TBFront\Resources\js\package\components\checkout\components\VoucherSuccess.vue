<template>
    <section class="c-tp container-fluid pt-5">
        <div class="container">
            <div class="col-12 col-md-8 offset-md-2 px-0 px-md-3">

                <h1>{{ __("messages.voucher_header_one") }} <strong>{{ data.customerFirstName }}</strong>!</h1>
                <h2 class="mb-3">
                    {{ __("messages.voucher_header_two") }}
                    <strong>{{ __("messages.voucher_header_three") }}</strong>
                </h2>
                <p>
                    {{ __("messages.voucher_header_four") }}
                    <strong> {{ data.bookingId }}</strong>{{ __("messages.voucher_header_five") }}
                </p>
            </div>
        </div>
    </section>
    <div class="container">
        <div class="col-12 col-md-8 offset-md-2">
            <div class="bg-white ctp-in shadow py-md-5 py-4 px-md-4 px-3 mb-4">
                <div class="row">
                    <div class="col-12 col-md-7">

                        <div class="cf-block" v-for="(flight, index) in data.flightInformation">
                            
                            <h3 class="mb-0" v-if="index == 0">
                                <span class="icon icon-plane-right"></span>
                                <strong>{{ __("messages.flight_leg_title_starting") }}</strong>
                            </h3>

                            <h3 class="mb-0 mt-4"  v-if="index != 0">
                                <span class="icon icon-plane-left"></span>
                                <strong>{{ __("messages.flight_leg_title_returning") }}</strong>
                            </h3>

                            <p class="mb-0">
                                <strong>{{ flight.originAirport }}</strong>
                                <span> - </span>
                                <strong>{{ flight.destinationAirport }}</strong>
                            </p>
                            <p class="mb-0">
                                <span>{{ __("messages.voucher_departure_date") }}</span>
                                <strong>
                                    {{ $filters.date(flight.startDate, 'DD MMM YY HH:mm') }} {{ __("messages.voucher_hrs") }}
                                </strong>
                            </p>
                            <p class="mb-0">
                                <span>{{ __("messages.voucher_passangers") }} </span>
                                <strong>
                                    <span v-if="flight.adults > 1">
                                    {{ flight.adults }} {{ __("messages.voucher_adults") }}
                                     </span>
                                     <span v-if="flight.adults == 1">
                                    {{ flight.adults }} {{ __("messages.voucher_adult") }}
                                     </span>
                                    <span v-if="flight.kids > 1">
                                        , {{ flight.kids }} {{ __("messages.voucher_kids") }}
                                    </span>
                                    <span v-if="flight.kids == 1">
                                        , {{ flight.kids }} {{ __("messages.voucher_kid") }}
                                    </span>
                                    <span v-if="flight.infants > 1">
                                        , {{ flight.infants }} {{ __("messages.voucher_infants") }}
                                    </span>
                                    <span v-if="flight.infants == 1">
                                        , {{ flight.infants }} {{ __("messages.voucher_infant") }}
                                    </span>
                                </strong>
                            </p>
                            <p class="mb-0">
                                <span>{{ __("messages.voucher_airline") }} </span>
                                <strong>{{ flight.airline }}</strong>
                            </p>
                        </div>

                       

                        <template v-for="payment in data.payments">
                            <div class="col-2 px-0">
                                <hr />
                            </div>

                            <p class="mb-0">
                                <span>{{ __("messages.voucher_total") }}</span>
                                <strong>{{ $filters.currency(payment.paymentAmount) }}</strong>
                            </p>
                        </template>
                    </div>
                    <div class="col-12 col-md-5 d-none d-md-flex align-items-center">
                        <img class="w-100 pr-5" src="/assets-tb/img/tiquetesbaratos/plane.svg" />
                    </div>
                </div>

                <div class="col-12 pt-3 px-0 px-md-3">
                </div>

                <div class="col-12 px-0">
                    <hr class="my-0" />
                </div>
                <div class="col-12 pt-3 px-0">
                    <p class="font-12 mb-1">
                        {{ __("messages.voucher_info_mail") }} <strong>{{ data.customerEmail }} </strong>
                    </p>
                    <p class="font-12 mb-0">
                        {{ __("messages.voucher_info_mail_phone") }}
                        <a href="mailto:601743662" class="a-link">(601)7436620</a>
                    </p>
                </div>

            </div>
        </div>

    </div>
</template>
<script>

    export default {
        props: ['data', 'item'],
        data() {
            return {
                type: {},
                modalCancellation: 0,
                cancellation: {},
                departureSegment: {},
                arrivalSegment: {},
                flightInformation: {}
            };
        },
        async mounted() {
            this.initialize();
            this.getEventAlgolia();
        },
        methods: {
            initialize() {
                //this.flightInformation = this.data.flightInformation[0];
                //this.departureSegment = this.data.flightInformation[0].segments[0]

                //this.arrivalSegment = this.data.flightInformation.length == 1 ? this.data.flightInformation[0].segments[this.data.flightInformation[0].segments.length - 1] : this.data.flightInformation[1].segments[0];
            },
            getEventAlgolia() {
                //AlgoliaServices.getEventAlgolia("Order");
            },

        },
        components: {
        }
    }

</script>