@import '../_variables.scss';

.card-flight-selected {

	.flight-selected-scale {
		span {
			&:nth-child(1) {
				font-size: 14px;
				color: $yellow-600;
				font-weight: 600;
			}
		}
	}
}

.room-card {
	.room-description-title {
		font-size: 18px;
		margin-bottom: 1rem;
	}


	.room-img img {
		object-fit: cover;
		max-width: 100%;
		height: 100%;
		max-height: 250px;
		width: 100%;
		background-color: #e7e7e7;
	}

	.room-description {
		span {
			font-size: 14px;
			display: block;
		}

		button {
			font-size: 16px;
		}

		.room-description-features {
			margin-bottom: .5rem;
		}

		.room-description-feat {
			color: $color-gray-muted;
			margin-bottom: .5rem;
		}
	}

	.room-mealplan {
		.mealplans-title {
			font-size: 14px;
		}

		.room-mealplan-name {
			font-size: 16px;
		}

		.room-mealplan-price {
			font-size: 16px;
		}

		.room-mealplan-feat {
			//display: block;
			font-size: 14px;
			margin-bottom: .5rem;
		}
	}

	.room-price {
		text-align: right;

		.room-price-title {
			font-size: 14px;
			display: block;
			margin-bottom: .5rem;
		}

		.room-price-discount {
			font-size: 14px;
			display: block;
			margin-bottom: .5rem;
			position: relative;
		}

		.room-price-person {
			font-size: 14px;
			display: block;
			margin-bottom: .5rem;

			span {
				font-size: 22px;
			}
		}

		.room-price-total {
			font-size: 14px;
			display: block;
			margin-bottom: .5rem;
		}

		.room-price-tax {
			display: block;
			font-size: 12px;
		}
	}

	@media (max-width: 990px) {
		.room-description-title {
			margin-top: .5em;
		}

		.room-price {
			text-align: start;
		}
	}

	@media($phone) {
		.room-description {
			span {
				font-size: 16px;
				display: block;
			}
		}
	}
}

.card-saving-nigth {
	.card-header {
		background-color: rgba(251, 228, 182, 1);
	}

	.bg-text {
		background-color: rgba(228, 228, 231, 0.25);
	}
}
