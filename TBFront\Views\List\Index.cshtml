@using TBFront.Helpers
@using Microsoft.Extensions.Options
@using TBFront.Options;
@using TBFront.Types;
@using TBFront.Models.Meta.Metatags;
@using TBFront.Models.Request;
@using TBFront.Models.ContentDeliveryNetwork.Seo;

@inject IOptions<SettingsOptions> settingOptions
@inject IOptions<CurrencyOptions> currencyOptions

@inject ViewHelper viewHelper
@inject StaticHelper staticHelper

@inject ViewHelper _
@inject StaticHelper staticHelper
@{
    var resolution = _.GetImageResolution();
    var isMobile = resolution.Device == DeviceType.Mobile ;
    var isTablet =_.IsTablet();
   
    var isRobot = _.IsRobot();

    var request = ViewData["request"] as FlightRequest;
    var metaTag = ViewData["MetaTag"] as MetaTag;
    var culture = ViewData["CultureData"] as Culture;
    var pageType = (PageType)ViewData["PageType"];
    var seoContent = ViewData["seoContent"] as SeoResponse;
    ViewData["Page"] = "list";
    ViewData["IsRobot"] = isRobot;
    ViewData["isMobile"] = isMobile;
    ViewData["Resolution"] = resolution;
    

}

@await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.Code}/_Header.cshtml", new
ViewDataDictionary(ViewData) { { "navs", true }, { "login", isRobot } })

@if (request?.IsCanonical ?? false)
{
    <div class="container mb-3 d-none d-lg-block" id="app">

        <div class="row">
            <div class="col-12  pt-3" style="margin-bottom: -2.5rem !important;">

                <h1 class="title"> @(!string.IsNullOrEmpty(seoContent.Seo.Meta.H1) ? seoContent.Seo.Meta.H1 : @_.Localizer("flights_to", request?.ReturningAirportPlace.City))</h1>
            </div>
        </div>

    </div>
}
<div class="container mt-none mt-md-3 mb-3 mb-md-none px-0">
    <flight-booker :is-home-landing="false"></flight-booker>
</div>

@if (pageType == PageType.DestinationFlightList && (isMobile || isTablet))
{
    <div class="container mb-3 d-block d-lg-none" id="app">

        <div class="row">
            <div class="col-12  pt-3" style="">

                <h1 class="title p-0" >@_.Localizer("flights_to", request?.ReturningAirportPlace.City)</h1>
            </div>
        </div>

    </div>
}


<list-page :mobile='@Json.Serialize(isMobile)' landing="internationals"></list-page>

<modal-flight-detail></modal-flight-detail>

<loader-page></loader-page>
@if (request?.IsCanonical ?? false)
{
@await Html.PartialAsync($"~/Views/Shared/Components/Questions.cshtml", new ViewDataDictionary(ViewData) {  { "seoContents", seoContent }, { "Title", request?.ReturningAirportPlace.City } })

@if (ViewData["ErrorMgs"] is not null)
{
    <div style="display: none;"> @ViewData["ErrorMgs"] </div>
}

@section Meta {
    @if (metaTag is not null)
    {
        @await Html.PartialAsync("_MetaMain", new ViewDataDictionary(ViewData) { { "Meta", metaTag}})
        @if (metaTag.Question.mainEntity.Count > 0)
        {
            <script type="application/ld+json">
                @Json.Serialize(metaTag.Question)
            </script>
        }
    }
}


@section Preload {
    @if (!string.IsNullOrEmpty(settingOptions.Value.CloudCdn))
    {
        <link rel="preconnect" href="@settingOptions.Value.CloudCdn">
    }
    <link rel="preconnect" href="@settingOptions.Value.ApiFlights.Domain">


    <link rel="preload" href="@staticHelper.GetVersion($"/fonts/icomoon.woff2", "/assets-tb", false)" as="font"
        type="font/woff2" crossorigin="" />
    <link rel="preload" href="@staticHelper.GetVersion($"/fonts/Roboto-Medium.woff2", "/assets-tb", false)" as="font"
        type="font/woff2" crossorigin="" />
    <link rel="preload" href="@staticHelper.GetVersion($"/fonts/Roboto-Regular.woff2","/assets-tb", false)" as="font"
        type="font/woff2" crossorigin="" />
    <link rel="preload" href="@staticHelper.GetVersion($"/fonts/Roboto-Bold.woff2", "/assets-tb", false)" as="font"
        type="font/woff2" crossorigin="" />


    <link rel="preload"
        href="@staticHelper.GetVersion($"/{settingOptions.Value.SiteName}/css/list.css", settingOptions.Value.Assets)"
        as="style" />

}

@section Css {
    <style>
        body.cloak {
            display: none !important;
        }
    </style>
    <link type="text/css" rel="stylesheet"
        href="@staticHelper.GetVersion($"/{settingOptions.Value.SiteName}/css/list.css", settingOptions.Value.Assets)">
}



@section ScriptsPriority {

}


@section Scripts {
    <script>
        window.__pt.data = @Json.Serialize(request);
        window.__pt.cultureData = @Json.Serialize((ViewData["CultureData"]));
        window.__pt.exchange = @Json.Serialize((ViewData["Exchange"]));
        window.__pt.alternates = @Json.Serialize((ViewData["Alternates"]));
        window.__pt.currencies = @Json.Serialize((currencyOptions.Value.Currencies));
        window.__pt.currency = @Json.Serialize((ViewData["CurrencyData"]));

    </script>

    <script src="@staticHelper.GetVersion("/js/vue/app.js", settingOptions.Value.Assets)"></script>
}
