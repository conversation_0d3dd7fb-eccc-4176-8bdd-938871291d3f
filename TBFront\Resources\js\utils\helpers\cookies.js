/**
 * @name getCookie
 * @description Get cookie value by name
 * 
 * @param {string} name required cookie name
 * 
 * @returns {string} cookie value
 */
export const getCookie = (name) => {
	const nameEQ = name + "=";
	const ca = document.cookie.split(';');
	for (let i = 0; i < ca.length; i++) {
		let c = ca[i];
		while (c.charAt(0) == ' ') c = c.substring(1, c.length);
		if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
	}
	return null;
};

/**
 * @name ereaseCookie
 * @description Erease a cookie
 * 
 * @param {name} name Cookie name
 * 
 */
export const eraseCookie = (name) => {
	document.cookie = name + '=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';
};
