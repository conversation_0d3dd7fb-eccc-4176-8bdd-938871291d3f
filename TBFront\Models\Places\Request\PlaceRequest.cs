﻿using Hotel.Content.Standard.Dtos;
using Places.Standard.Dtos;
using TBFront.Types;

namespace TBFront.Models.Places.Request
{
    public class PlaceRequest
    {
        public int? Id { get; set; }
        public string Code { get; set; }
        public PageType Type { get; set; }
        public string Culture { get; set; }
        public int OrganizationId { get; set; }
        public int PropertyId { get; set; }
        public string StartingAirport { get; set; } = string.Empty;
        public string ReturninAirport { get; set; } = string.Empty;
        public string InternalCulture { get; set; } = string.Empty;
        public string Uri { get; set; } = string.Empty;
        public string Path { get; set; } = string.Empty;
        public PageType PageType { get; set; } = PageType.Generic;
        public string Route { get; set; } = string.Empty;
    }
    public class FrontFlightContentDetailResponse
    {
        public HotelContentDetailResponse Hotel { get; set; } = new HotelContentDetailResponse();
        public string Culture { get; set; } = string.Empty;
    }

    public class FrontBasePlace
    {
        public BasePlace Place { get; set; } = new BasePlace();
        public string Culture { get; set; } = string.Empty;
    }

}