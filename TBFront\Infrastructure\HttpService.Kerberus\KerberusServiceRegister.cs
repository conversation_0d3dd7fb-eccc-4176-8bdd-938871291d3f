﻿
using TBFront.Interfaces;
using TBFront.Models.Kerberus;
using TBFront.Options;

namespace TBFront.Infrastructure.HttpService.Kerberus
{
    public static class KerberusServiceRegister
    {
        public static void AddKerberusServiceRegister(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddHttpClient<KerberusService>("");
            services.AddSingleton(s => configuration.GetSection("SettingsOptions").Get<SettingsOptions>());
            services.AddSingleton<IQueryHandlerAsync<KerberusRequest, string>, KerberusService>();
        }
    }
}
