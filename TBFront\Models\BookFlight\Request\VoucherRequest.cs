﻿using Microsoft.AspNetCore.Mvc;

namespace TBFront.Models.Request
{
    public class VoucherRequest
    {
        public string? KeyValidation { get; set; }

        [FromQuery(Name = "id")]
        public string? Id { get; set; }

        [FromQuery(Name = "em")]
        public string? Em { get; set; }

        [FromQuery(Name = "place")]
        public string? Place { get; set; }

        [FromQuery(Name = "ri")]
        public string? Ri { get; set; }

        [FromQuery(Name = "rp")]
        public string? Rp { get; set; }

        [FromQuery(Name = "rc")]
        public string? Rc { get; set; }

        public bool IsValid()
        {
            return Em != null && Id != null;
        }
    }

}
