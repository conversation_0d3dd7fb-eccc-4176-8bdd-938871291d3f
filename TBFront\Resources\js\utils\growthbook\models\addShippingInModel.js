import {BaseModel} from "./baseModel";

export class AddShippingInModel extends BaseModel{
    constructor(args) {
        super()
        const defaults = {
            Currency: { required: false, default: "COP", type: "string" },
            ItemBrand: { required: true, default: "", type: "any" },
            FlightType: { required: true, default: "", type: "string" },
            RouteType: { required: true, default: "", type: "string" },
            FieldOriginIata: { required: true, default: "", type: "string" },
            FieldOriginName: { required: true, default: "", type: "string" },
            FieldDestinationIata: { required: true, default: "", type: "string" },
            FieldDestinationName: { required: true, default: "", type: "string" },
            Value: { required: true, default: 0, type: "number" },
            FieldDate1: { required: true, default: "", type: "string" },
            FieldDate2: { required: true, default: "", type: "string" },
            TravelersAdults: { required: true, default: 0, type: "number" },
            TravelersChildren: { required: true, default: 0, type: "number" },
            TravelersInfants: { required: true, default: 0, type: "number" },
            TravelersInfantsOnlap: { required: true, default: 0, type: "number" },
            TravelersInfantsInseat: { required: true, default: 0, type: "number" },
            Items: { required: false, default: "[]", type: "string" },
            Layer: { required: false, default: "", type: "string" }
        };

        this.map(defaults, args)
    }
}