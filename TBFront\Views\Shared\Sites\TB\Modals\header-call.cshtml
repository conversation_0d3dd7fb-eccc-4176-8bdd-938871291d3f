﻿@using Microsoft.Extensions.Options
@using TBFront.Options;
@using TBFront.Helpers
@inject IOptions<SettingsOptions> settingOptions
@inject IOptions<SiteOptions> siteOptions
@inject ViewHelper viewHelper


<div id="modal-call" class="modal fade" ng-click="vm.clickOutsideCall($event, 'modal-call',formllamadatos)">
    <div class="modal-dialog modal-dialog-centered modal-lg " role="document">
        <div class="modal-content">
            <div class="modal-body p-0">
                <div class="row">
                    <div class="col-12 col-lg-6 bg-modal-call d-none d-lg-inline-block">
                        <div class="c-animated-call cac-1">
                            <span class="font-icons icons-phone d-block text-center font-30 pt-2"></span>
                            <small class="d-block px-3 text-center mt-1">Asesores disponibles</small>
                            <img alt="asesores" width="60" src="/assets-tb/img/tiquetesbaratos/animated-call.svg">
                        </div>
                    </div>
                    <div class="col-12 col-lg-6">
                        <div class="modal-header border-0 pb-1 pb-md-3">
                            <button modal-close=".modalCall" ng-click="vm.hideModalCall('modal-call',formllamadatos)" type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                        </div>
                        <div class="modal-body pt-0 pl-4 pr-5">
                            <form novalidate="true" method="post" name="formllamadatos" ng-submit="vm.submitFormtWrite(formllamadatos)" id="formllamadatos">
                                <h5 class="mb-2">¿Necesitas que te llamemos?</h5>
                                <div class="row">
                                    <div class="col-12">
                                        <div class="form-group mt-1">
                                            <input name="nombre" ng-model="vm.userDataWriten.nombre" id="nombrecall" type="text" class="form-control" data-invalid="nombrecall_invalid" placeholder="Tu nombre" required>
                                            <div ng-class="{  'block-home' : vm.hasError(formllamadatos, 'nombre') }" id="nombrecall_invalid" class="invalid-feedback">
                                                ¡Por favor digite su nombre de contacto!
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <div class="form-group mt-1">
                                            <input type="text" name="telefono" ng-model="vm.userDataWriten.telefono" id="telefonocall" data-invalid="telefonocall_invalid" size="10" class="form-control" placeholder="Teléfono o celular (10 dígitos)" required>
                                            <div ng-class="{  'block-home' : vm.hasError(formllamadatos, 'telefono') }" id="telefonocall_invalid" class="invalid-feedback">
                                                ¡Por favor digite su numero de Teléfono!
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="g-recaptcha">
                                    <div id="g-recaptcha"></div>
                                    <input type="text" style="    z-index: -1;display: contents;" data-invalid="g_recaptcha_invalid" name="recatcha" ng-model="vm.userDataWriten.recatcha" id="recatchacall" class="form-control" required />
                                    <div id="g_recaptcha_invalid" ng-class="{  'block-home' : vm.hasError(formllamadatos, 'recatcha') }" class="invalid-feedback">
                                        Por favor verifique el recaptcha!
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <div class="col-12 px-0">
                                        <button class="btn btn-primary w-100 py-3" type="submit">Enviar</button>
                                    </div>
                                </div>
                                <div class="c-recapcha mt-3">
                                    <div class="col-12 bg-light py-3 px-4">
                                        <div class="row">
                                            <div class="col-12">
                                                <p class=" mb-1 "><b>Horario de atención</b>  </p>
                                                <p class="mb-1 gray-200">De lunes a viernes de 06:00 AM<br /> a 11:50 PM</p>
                                                <p class=" mb-1 gray-200">Sábados, domingos y festivos<br />07:00 AM a 11:50 PM </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

