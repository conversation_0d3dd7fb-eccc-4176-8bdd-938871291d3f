﻿using Newtonsoft.Json;
using TBFront.Models.Common;
using TBFront.Models.Flight.Quote;

namespace TBFront.Models.Flight.Revalidate
{
    public class RevalidateResponse
    {
        public RevalidateRootResponse Response { get; set; }
        public SummaryBreakdown Summary { get; set; }
        public string? StatusType { get; set; }
        public string? Message { get; set; }
    }


    public class RevalidateRootResponse
    {
        public int TotalRecommendations { get; set; }
        public string QuoteTaskID { get; set; }
        public string Currency { get; set; }
        public int StatusCode { get; set; }
        public string StatusMessage { get; set; }
        public ICollection<int> EnginesInResponse { get; set; }
        public MoreInformation MoreInformation { get; set; }
        public Dictionary<string, Dictionary<string, Quote.Flight>> Flights { get; set; }
        public Dictionary<string, Recommendation> Recommendations { get; set; }
        public Dictionary<string, Dictionary<string, FareLeg>> FaresByLeg { get; set; }
        public Dictionary<string, Dictionary<string, BookingInfo>>? BookingInfos { get; set; }


    }

    public class MoreInformation
    {
        public Dictionary<string, Engine> Engines { get; set; }
    }
    public class SummaryBreakdown
    {
        public int StatusCode { get; set; }
        public string? StatusMessage { get; set; }
        public double TotalAmount { get; set; }
        public double TotalAmountOld { get; set; }
        public List<FareDetail>? Breakdown { get; set; }

        public SummaryBreakdown()
        {
            Breakdown = new List<FareDetail>();
        }
    }
    public class Engine
    {
        public bool IsHomogeneous { get; set; }
    }
  
}
