﻿@import '../_variables', 'typography';

.alert-telephone {
	color: $body-color;
}

.alert-telephone p, .alert-telephone p strong, .alert-telephone p strong a {
	color: $body-color;
}

.alert-telephone p {
	margin: 0;
	font-size: 14px;
}

.alert-telephone svg {
	width: 24px;
	vertical-align: middle;
}

@media (min-width: 768px) {
	.my-md-40 {
		margin-top: 40px !important;
		margin-bottom: 40px !important;
	}

	.me-md-30 {
		margin-right: 30px;
	}

	.mt-md-30 {
		margin-top: 30px;
	}

	.me-md-20 {
		margin-right: 20px;
	}

	.border-md-0 {
		border: none;
	}
	.border-bottom-md-0 {
		border-bottom: none !important;
	}
}

@media (min-width: 1400px) {
	.container, .container-lg, .container-md, .container-sm, .container-xl, .container-xxl {
		max-width: 1140px;
	}
}

.h-0 {
	height: 0;
}

.cursor-pointer { 
	cursor: pointer;
}

.cursor-default {
	cursor: default !important;
}

.bg-navy{
	background-color: rgba(24, 108, 223, 0.03) !important;
}

.text-navy {
	color: rgba(24, 108, 223, 1) !important;
}

.border-navy {
	border-color: rgba(24, 108, 223, 1) !important;
}

.bg-pt{
	color: #fff !important;
	background-color: $color-primary !important;
	border-color: $color-primary !important;
}

.spacing-t-12{
	margin-top: 12px ;
}

.spacing-b-12{
	margin-bottom: 12px ;
}

.spacing-t-20{
	margin-top: 20px ;
}
.spacing-b-20{
	margin-bottom: 20px ;
}
.spacing-t-28{
	margin-top: 28px ;
}
.spacing-b-28{
	margin-bottom: 28px ;
}

.spacing-t-48{
	margin-top: 48px ;
}

.spacing-b-48{
	margin-bottom: 48px ;
}


@media (min-width: 576px) {
    .container,.container-sm {
        max-width:540px !important
    }
}

@media (min-width: 768px) {
    .container,.container-md,.container-sm {
        max-width:720px !important
    }
}

@media (min-width: 992px) {
    .container,.container-lg,.container-md,.container-sm {
        max-width:960px !important
    }
}

@media (min-width: 1280px) {
    .container,.container-lg,.container-md,.container-sm,.container-xl {
        max-width:1280px !important
    }
}
