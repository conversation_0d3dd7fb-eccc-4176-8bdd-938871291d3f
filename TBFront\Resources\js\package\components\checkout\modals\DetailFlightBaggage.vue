<template>
    <div class="modal fade" :id=idModal>
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-md modal-st">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="mb-0">  {{__("messages.what_includes")}}</h4>
                    <!--<h5 class="modal-title" id="exampleModalLabel"> <strong class="font-28">{{ __("messages.fare") }} {{ airlineGroups.familyFareName }} </strong> </h5>-->
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div :class="['cm-family-0'+(indexFamily() + 1)]" class="modal-body">
                    <div class="w-100">
                        <template v-if="airlineGroups.familyFareContentToArray">                          
                            <div class="modal-info-family-details flight-item-details-table-baggage-info w-100">
                                <div class="flight-atributes border rounded px-3 position-relative ps-4 pt-2 pb-2">
                                    <div class="c-rate-style"></div>
                                    <div class="pl-3 font-20 my-2 cm-txt fw-bold">
                                        {{ __("messages.fare") }} {{ airlineGroups.familyFareName }}
                                    </div>
                                    <template v-for="(familyFareContent, indexFamilyFareContent) in mappingFamilyFare(airlineGroups.familyFareContentToArray)">
                                        <div :data-next="airlineGroups.familyFareContentToArray[indexFamilyFareContent + 1]?.category"
                                             :data-category="familyFareContent.category" class="mb-3">
                                            <p class="mb-0 position-relative">
                                                <span class="icon" :class="familyFareContent.class"></span>
                                                <span class="font-medium ps-4 d-inline-block">{{familyFareContent.title}}</span>
                                            </p>
                                            <p class="ps-24 font-regular"
                                               :class="{'color-disabled-text': familyFareContent.include === 2 || familyFareContent.include === 0}">
                                                {{familyFareContent.description}}
                                            </p>
                                        </div>
                                        <template v-if="indexFamilyFareContent === 1">
                                            <div class="col-12 border-bottom mb-3 ms-1"></div>
                                        </template>
                                    </template>
                                </div>
                            </div>                        
                        </template>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>

import { mappingFamilyFare } from '../../../../utils/utils';

export default {
    props: {
        detailFlightBaggage: {},
        idModal: "",
        logoUri: "",
    }, data() {
        return {
            configSite: window.__pt.settings.site.airlineConfiguration.national ?? [],
            airlineGroups: {
                familyFareName: "",
                familyFareContentToArray: []
            },
            airlineLogoUri: ""
        }
    },
    setup() {
        return { mappingFamilyFare }
    },
    async mounted() {
        this.airlineGroupsFunction();
        this.airlineLogoUriFunction();
    },
    methods: {
        async airlineGroupsFunction() {
            try {
                this.airlineGroups = {
                    familyFareName : this.detailFlightBaggage["familyFareName"],
                    familyFareContentToArray: this.detailFlightBaggage["familyFareContent"]
                }
            } catch (error) {
                this.airlineGroups = {
                    familyFareName: "",
                    familyFareContentToArray: []
                }
            }
        },
        async airlineLogoUriFunction() {
            this.airlineLogoUri = `${this.logoUri}?tx=w_33,g_auto,c_fill`;
        },
        configFilter(name=null) {
            let config = (this.configSite || []);
            return name ? ((config).find(item => (String(item.airlineName)).toUpperCase() === (String(name)).toUpperCase()) ?? {}) : config
        },
        indexFamily(){
            let index = -1
            if(this.detailFlightBaggage?.params){
                let config = this.configFilter(this.detailFlightBaggage.params?.airlineName);
                if(config){
                    index = (config?.families ?? []).findIndex(item => (item.familyFareCode ?? []).filter(code => (String(code)).toUpperCase() === (String(this.detailFlightBaggage["familyFareName"])).toUpperCase()).length > 0)
                }
            }
            return index

        }
    }
}
</script>

<style>
@media (min-width: 576px) {
    .modal-st.modal-dialog {
        max-width: 500px !important;
        margin: 1.75rem auto;
    }
}
.list-flight-details {
    li {
        font-size: 16px;
    }
}
</style>
