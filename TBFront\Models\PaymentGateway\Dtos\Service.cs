﻿namespace TBFront.Models.PaymentGateway.Dtos
{
    public class Service
    {
        public string? Type { get; set; }
        public bool HasTaxesAndOtherCharges { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal TaxAmount { get; set; }
        public string? FlightDescription { get; set; }
        public bool HasReturnFlights { get; set; }
        public List<FlightReservationSegment>? FlightReservationSegment { get; set; }
        public List<FlightReservationSegmentIntermediatePoint>? FlightReservationSegmentIntermediatePoint { get; set; }
        public List<AirPassengers>? AirPassengers { get; set; }
        public string? PNR { get; set; }
        public string? FlightRateDescription { get; set; }
        public string? FlightCheckedLuggage { get; set; }
        public string? FlightCarryOnLuggage { get; set; }
        public decimal? ServiceCharge { get; set; }
        public string? FlightExtraInfo { get; set; }
        public string? FlightExtraLuggage { get; set; }
        public bool ShowLuggageInfo { get; set; }

        //Hotel
        public string? Name { get; set; }
        public int Id { get; set; }
        public string? Destination { get; set; }
        public string? ZipCode { get; set; }
        public string? CountyA2 { get; set; }
        public int CityId { get; set; }
        public string? CityName { get; set; }
        public string? Address { get; set; }
        public string? Phone { get; set; }
        public int Category { get; set; }
        public DateTime DtStartService { get; set; }
        public DateTime DtEndService { get; set; }
        public int Days { get; set; }
        public List<Rooms>? Rooms { get; set; }
        public decimal PercentageDiscount { get; set; }
        public string? ThumbnailUrl { get; set; }
        public double DiscountAmount { get; set; }
        public int TaxScheme { get; set; }
        public bool HasTaxes { get; set; }
        public double Tax { get; set; }
        public bool ProviderCollect { get; set; }

        //Package

        public double MonthlyAmount { get; set; }
        public double PackageTotalAmount { get; set; }
        public string TransferDescription { get; set; } = string.Empty;

    }

    public class FlightReservationSegment
    {
        public int SegmentId { get; set; }
        public int SegmentIndex { get; set; }
        public string? AirlineName { get; set; }
        public string? AirlineCode { get; set; }
        public string? ArrivalAirportCode { get; set; }
        public string? ArrivalCity { get; set; }
        public string? ArrivalTime { get; set; }
        public int ArrivalTimeZone { get; set; }
        public string? DepartAirportCode { get; set; }
        public string? DepartCity { get; set; }
        public string? DepartureTime { get; set; }
        public int DepartTimeZone { get; set; }
        public string? FlightNumber { get; set; }
        public string? TicketClass { get; set; }

    }
    public class FlightReservationSegmentIntermediatePoint
    {
        public int SegmentId { get; set; }
        public string? LocationAirportCode { get; set; }
        public string? ArrivalCity { get; set; }
        public string? ArrivalTime { get; set; }
        public string? DepartureTime { get; set; }
        public string? FlightNumber { get; set; }
        public string? TicketClass { get; set; }
    }

    public class AirPassengers
    {
        public string? Designation { get; set; }
        public string? FistName { get; set; }
        public string? LastName { get; set; }
    }
}
