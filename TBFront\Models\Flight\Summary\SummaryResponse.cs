﻿
using TBFront.Models.Flight.CreateBooking;

namespace TBFront.Models.Flight.Summary
{
    public class SummaryResponse
    {
        public string Id { get; set; }
        public string ExternalId { get; set; }
        public CustomerInformation? CustomerInformation { get; set; }
        public SummaryItem? Items { get; set; }
        public double TotalAmount { get; set; }
        public double TotalTaxes { get; set; }
        public int BookingStatus { get; set; }
        public DateTime? ExpirationDate { get; set; }
        public bool Status { get; set; }
        public string KeyValidation { get; set; }

    }


    public class SummaryFamilyFareContent
    {
        public int CategoryType { get; set; }
        public string Description { get; set; }
        public string Title { get; set; }
        public int ContentType { get; set; }
        public int FlightIndex { get; set; }
        public int SegmentIndex { get; set; }
        public int Quantity { get; set; }
        public int Include { get; set; }
        public double Amount { get; set; }
    }

    public class SummaryFlight
    {
        public string Id { get; set; }
        public int TripMode { get; set; }
        public List<SummaryFlightSegment> FlightDepartureSegments { get; set; }
        public List<SummaryFlightSegment> FlightReturningSegments { get; set; }
        public List<SummaryFlightPassenger> FlightPassengers { get; set; }
        public int PaxCount { get; set; }
        public double TotalAmount { get; set; }
        public List<TotalTaxes> Taxes { get; set; }
        public string ConfirmationCode { get; set; }
    }
    public class TotalTaxes
    {
        public double Amount { get; set; }
        public string Currency { get; set; }
        public string Code { get; set; }
        public string CurrencyCode { get; set; }
        public string Type { get; set;}
       
    }
    public class SummaryFlightSegment
    {
        public string FlightNumber { get; set; }
        public string Airline { get; set; }
        public string AirlineCode { get; set; }
        public string OperatingAirline { get; set; }
        public string OperatingAirlineCode { get; set; }
        public string DepartureAirport { get; set; }
        public string DepartureAirportCode { get; set; }
        public string ArrivalAirport { get; set; }
        public string ArrivalAirportCode { get; set; }
        public DateTime DepartureDate { get; set; }
        public DateTime ArrivalDate { get; set; }
        public string DepartureAirportTerminal { get; set; }
        public string ArrivalAirportTerminal { get; set; }
        public DateTime ArrivalTime { get; set; }
        public DateTime DepartureTime { get; set; }
        public int FlightSegmentIndex { get; set; }
        public List<object> IntermediatePoints { get; set; }
        public string FamilyFareName { get; set; }
        public List<SummaryFamilyFareContent> FamilyFareContent { get; set; }
    }

    public class SummaryFlightPassenger
    {
        public string Title { get; set; }
        public int Type { get; set; }
        public string Names { get; set; }
        public string LastNames { get; set; }
        public int PassengerIndex { get; set; }
        public int PassengerNumber { get; set; }
        public bool RequireAdditionalUsaInformation { get; set; }
        public int Sex { get; set; }
        public int BirthDateDay { get; set; }
        public int BirthDateMonth { get; set; }
        public int BirthDateYear { get; set; }
        public EmergencyContact EmergencyContact { get; set; }
        public string Nationality { get; set; }
        public int CustomerIdentityDocumentType { get; set; }
        public string CustomerDocumentNumber { get; set; }
        public string CustomerId { get; set; }
    }

    public class SummaryItem
    {
        public List<SummaryFlight> Flights { get; set; }
    }


}
