import _ from 'lodash';

/**
 * @name mapperQueryStrParams
 * @alias mapObjectToQueryString
 * @description Convert any object to a valid query string
 *
 * @param  {Object}  data  required  Object to convert in query string, e.g. { id: 1, date: '13/01/2020' }.
 *
 * @returns {String}  Query String, e.g. '?id=1&date=13/01/2020'.
 */
export const mapperQueryStrParams = (data, keysForNormalizeSpaces = []) => {
	const keys = _.keys(data);

	if (keys.length === 0) return '';
	let strParams = '?';

	const normalizedKeys = {};

	_.forEach(keys, key => {
		const requireSpaceNormalization =
			_.indexOf(keysForNormalizeSpaces, key) >= 0;

		const dataByKey = data[key];

		if (requireSpaceNormalization) {
			normalizedKeys[key] = encodeURI(`${key}=${dataByKey}`);
		}

		if (_.isArray(dataByKey)) {
			_.forEach(dataByKey, item => {
				if (key === 'rooms') {
					strParams += strParams === '?' ? `rooms=${item}` : `&rooms=${item}`;
				} else {
					strParams +=
						strParams === '?' ? `${key}[]=${item}` : `&${key}[]=${item}`;
				}
			});
		} else {
			strParams +=
				strParams === '?' ? `${key}=${dataByKey}` : `&${key}=${dataByKey}`;
		}
	});

	let urlEncoded = encodeURI(strParams);

	/* eslint-disable no-restricted-syntax */
	for (const key of _.keys(normalizedKeys)) {
		const normalizedValue = normalizedKeys[key].replace(/\+/g, '%2B');

		urlEncoded = urlEncoded.replace(normalizedKeys[key], normalizedValue);
	}

	return urlEncoded;
};

export const mapObjectToQueryString = mapperQueryStrParams;

/**
 * @name getUrlWithQueryString
 * @description Gets the current URL and update its query string using the object provided
 * @returns {String} The current URL with new params eg https://www.pricetravel.com/vuelos/resultados?startingFromAirport=CUN&returningFromAirport=MEX ...
 * @param {Object} Object to be used as query string
 */
export const getUrlWithQueryString = (query) => {
	const queryParams = new URLSearchParams(query);
	return `?${queryParams.toString()}`;
};


//export const getUrlWithQueryString = (query, baseUrl= "") => {
//	const queryParams = new URLSearchParams(query);
//	const url = baseUrl ? baseUrl : `${window.location.origin}${window.location.pathname}`;
//	return `${url}?${queryParams.toString()}`;
//};


/**
 * @name mapPaxToUrl
 * @description Gets the object in legacy format
 * @returns {String}
 * @param {Object} Object to be used as query string
 */
export const mapPaxToUrl = (pax, capitalize, toCheckout) => {
	let capitilize = !!capitalize;
	let checkout = !!toCheckout;
	let paxesKeys = {};
	let adults = 0;
	let kids = 0;
	let infants = 0;
	let agekids = [];
	let rooms = pax.length;

	for (let i = 0; i < rooms; i++) {
		const room = pax[i];
		let position = i + 1;

		paxesKeys[`${capitilize ? "R" : "r"}oom${position}.${capitilize ? "A" : "a"}dults`] = room.adults;
		adults += room.adults;

		let childrens = room.children.length;
		let childreJoin = [];
		for (let c = 0; c < childrens; c++) {
			const childrenRoom = room.children[c];

			const kidAge = childrenRoom.year ? childrenRoom.year : childrenRoom;

			childreJoin.push(kidAge);
			agekids.push(kidAge);

			if (kidAge > 5) {// Revisar wea
				kids += 1;
			} else {
				infants += 1;
			}
		}
		paxesKeys[`${capitilize ? "R" : "r"}oom${position}.${capitilize ? "K" : "k"}ids`] = childrens;
		paxesKeys[`${capitilize ? "R" : "r"}oom${position}.${capitilize ? "A" : "a"}gekids`] = childreJoin.join(",");

	}
	paxesKeys[`${capitilize ? "R" : "r"}ooms`] = rooms;
	paxesKeys[`${capitilize ? "A" : "a"}dults`] = adults;
	paxesKeys[`${capitilize ? "K" : "k"}ids`] = kids;

	if (checkout) {
		paxesKeys[`${capitilize ? "K" : "k"}ids`] = paxesKeys[`${capitilize ? "K" : "k"}ids`] + infants
	} else {
		paxesKeys[`${capitilize ? "I" : "i"}nfants`] = infants;
	}

	paxesKeys[`${capitilize ? "A" : "a"}gekids`] = agekids.join(",");


	return paxesKeys;
};

/**
 * @name mapToQueryString
 * @description Converts an object to a string formatted as querystring
 * @return {String}
 * @para {Object} Object to be converted
 */
export const mapToQueryString = (obj) => {
	return Object.keys(obj)
		.map(key => obj[key] !== null ? `${encodeURIComponent(key)}=${encodeURIComponent(obj[key])}` : '')
		.filter(Boolean)
		.join('&');
};


export const searchParams = () => {
	let urlParams = new URLSearchParams(location.search);
	return Object.fromEntries(urlParams);
};

export const updateQueryParams = (key, value) => {
	if (history && history.pushState) {
		const url = new URL(window.location);
		url.searchParams.set('' + key, value);
		window.history.pushState(null, '', url.toString());
	}
};

export const mapPaxToUrlWithOutYearProperty = (pax, cap, check) => {
	let capitilize = !!cap;
	let checkout = !!check;
	let paxesKeys = {};
	let adults = 0;
	let kids = 0;
	let infants = 0;
	let agekids = [];
	let rooms = pax.length;

	for (let i = 0; i < rooms; i++) {
		const room = pax[i];
		let position = i + 1;

		paxesKeys[`${capitilize ? "R" : "r"}oom${position}.${capitilize ? "A" : "a"}dults`] = room.adults;
		adults += room.adults;

		let childrens = room.children.length;
		let childreJoin = [];
		for (let c = 0; c < childrens; c++) {
			const childrenRoom = room.children[c];
			childreJoin.push(childrenRoom);
			agekids.push(childrenRoom);
			if (childrenRoom > 5) {
				kids += 1;
			} else {
				infants += 1;
			}
		}
		paxesKeys[`${capitilize ? "R" : "r"}oom${position}.${capitilize ? "K" : "k"}ids`] = childrens;
		paxesKeys[`${capitilize ? "R" : "r"}oom${position}.${capitilize ? "A" : "a"}gekids`] = childreJoin.join(",");

	}
	paxesKeys[`${capitilize ? "R" : "r"}ooms`] = rooms;
	paxesKeys[`${capitilize ? "A" : "a"}dults`] = adults;
	paxesKeys[`${capitilize ? "K" : "k"}ids`] = kids;

	if (checkout) {
		paxesKeys[`${capitilize ? "K" : "k"}ids`] = paxesKeys[`${capitilize ? "K" : "k"}ids`] + infants
	} else {
		paxesKeys[`${capitilize ? "I" : "i"}nfants`] = infants;
	}

	paxesKeys[`${capitilize ? "A" : "a"}gekids`] = agekids.join(",");


	return paxesKeys;
}
/**
 * Extracts the query parameters from a given URL and returns them as an object.
 *
 * @param {string} url - The URL from which to extract query parameters.
 * @return {Object} An object containing the query parameters as key-value pairs.
 */
export function getQueryParams(url) {
	const queryParams = {};
	const urlObj = new URL(url);
	urlObj.searchParams.forEach((value, key) => {
		queryParams[key] = value;
	});
	return queryParams;
}