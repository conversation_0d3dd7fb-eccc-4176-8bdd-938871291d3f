
#gallery-modal {
	.modal-content {
		height: auto;
	}

	.modal-dialog {
		margin: 0;
		padding: 10px;
		height: 100%;
		max-width: none;

		@media (min-width: 576px) {
			margin: 0;
		}
	}

	.grid-container {
		display: grid;
		grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
		grid-auto-rows: minmax(200px, auto);
		gap: 10px;
		padding: 10px;
		grid-auto-flow: dense;

		@media (max-width: 617px) {
			grid-template-columns: repeat(auto-fill,minmax(120px,1fr));
		}

		.tall {
			grid-row: span 2;
		}

		.grid-item {
			display: flex;
			align-items: center;
			justify-content: center;
			background-color: #ccc;
			border-radius: 4px;
			transition: transform 0.3s ease-in-out;
			background-size: cover;
			background-position: center;
			background-repeat: no-repeat;
			cursor: pointer;
		}

		.wide {
			grid-column: span 2;
		}
	}

	.galery-modal-header-hotel {
		margin: 0rem -1rem -1rem auto;
		display: flex;
	}

	@media (max-width: 472px) {
		.galery-modal-header-hotel {
			display: block;
		}
	}

	.galery-modal-title-hotel {
		display: flex;
		align-self: center;
		align-items: center;
		gap: 0.5rem;
	}
}

#slide-gallery-modal {
	z-index: 999999;
	background: rgba(51,51,51,0.9);

	.modal-dialog {
		max-width: 820px;
	}

	.slider-gallery-view {
		opacity: 0;

		.slider-gallery-item {
			img {
				width: 100%;
			}
		}
	}

	.modal-content {
		background-color: unset;
		border: unset;
		color: white;
	}

	.modal-header {
		border-bottom: none;
		flex-direction: row-reverse;
		padding: 0;

		.icon-close:after, .icon-close:before {
			background-color: white;
		}
	}

	.slick-arrow {
		background: $white;
		font-weight: 500;
		text-align: center;
		white-space: nowrap;
		user-select: none;
		border: none;
		transition: 0.2s ease-in-out;
		border-radius: 50%;
		color: $color-primary;
		box-shadow: 0 3px 1px -2px rgb(0 0 0 / 2%), 0 2px 2px 0 rgb(0 0 0 / 14%), 0 1px 5px 0 rgb(0 0 0 / 12%);
		font-size: 0px;
		line-height: 1;
		width: 40px;
		height: 40px;
		position: absolute;
		top: 0;
		bottom: 0;
		margin: auto;
		z-index: 1;

		&.slick-next {
			right: -1rem;
		}

		&.slick-prev {
			left: -1rem;
		}
	}
}

.modal-gallery .icons-close {
	color: #fff;
}
