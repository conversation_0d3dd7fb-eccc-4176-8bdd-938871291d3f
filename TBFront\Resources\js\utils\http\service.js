import _ from 'lodash';

import { objectFormatter, mapperQueryStrParams } from '../helpers';
import { api, axiosProvider } from './instance';
// import { authenticationApiRequestService } from './authenticationService';
import AuthenticationStorageService from '../storage/authenticationStorageService';
import { Logger } from '../helpers/logger';

const authenticationStorageService = AuthenticationStorageService.getService();

/**
 * @name apiRequestService
 * @description Request Service fro call any resource api.
 *
 * @param  {{ method: '', uri: '' }}  resource        required  Object with API resource definition, needs [method] and [uri] keys
 * @param  {Object}                   uriParams       optional  Object with url params for request.
 * @param  {Object}                   payload         optional  Object with body params for request.
 * @param  {Object}                   responseMapper  optional  Object with mapper model for parse response of request.
 *
 * @fires objectFormatter
 * @fires mapperQueryStrParams
 *
 * @returns {Object} Response of called service
 */
export const apiRequestService = (
	resource,
	uriParams = {},
	payload = {},
	responseMapper
) => {
	if (
		!_.has(resource, 'method') ||
		(!_.has(resource, 'uri') && !_.has(resource, 'fullUri'))
	) {
		// eslint-disable-next-line no-console
		return Logger.error(
			'Error in [apiRequestService] invalid resource. Resource object requires a valid [method] and [uri || fullUri] keys.'
		);
	}

	const fullUri = _.get(resource, 'fullUri');
	const strParams = mapperQueryStrParams(uriParams).replace(/\+/g, '%2B');

	if (!_.isEmpty(fullUri)) {
		return requestWithouthInstance(
			resource,
			strParams,
			payload,
			responseMapper
		);
	}

	return api[resource.method](
		`${resource.uri}${strParams}`,
		payload || null
	).then(response =>
		_.isEmpty(responseMapper)
			? response.data
			: objectFormatter(responseMapper, response.data)
	);
};

const requestWithouthInstance = (resource, strParams, payload, mapper) => {
	const axios = axiosProvider();
	return axios({
		method: resource.method,
		url: `${resource.fullUri}${strParams}`,
		data: payload
	})
		.then(response =>
			_.isEmpty(mapper) ? response.data : objectFormatter(mapper, response.data)
		)
		.catch(err => {
			Logger.error(err);
		});
};

export const requestWithoutCors = (resource, uriParams = {}, mapper) => {
	const strParams = mapperQueryStrParams(uriParams).replace(/\+/g, '%2B');

	return axios({
		method: resource.method,
		url: `${resource.fullUri}${strParams}`,
		headers: {
			'X-Requested-With': null
		}
	}).then(response =>
		_.isEmpty(mapper) ? response.data : objectFormatter(mapper, response.data)
	);
};

//// Add a request interceptor
//api.interceptors.request.use(
//	originalRequestConfig => {
//		const requestConfig = originalRequestConfig;
//		const token = authenticationStorageService.getToken();

//		if (token) {
//			requestConfig.headers.Authorization = `Bearer ${token}`;
//		} else {
//			return authenticationApiRequestService().then(response => {
//				if (response.status === 200) {
//					authenticationStorageService.setToken(response.data);
//					requestConfig.headers.Authorization = `Bearer ${authenticationStorageService.getToken()}`;
//				}

//				return requestConfig;
//			});
//		}

//		return requestConfig;
//	},
//	error => {
//		Promise.reject(error);
//	}
//);

//// Add a response interceptor
//api.interceptors.response.use(
//	response => response,
//	error => {
//		const requestConfig = error.config;

//		if (
//			error.response &&
//			error.response.status === 401 &&
//			requestConfig.baseURL !== window.__pt.settings.site.auth.uri &&
//			!requestConfig.retry
//		) {
//			requestConfig.retry = true;

//			return authenticationApiRequestService().then(response => {
//				if (response.status === 200) {
//					authenticationStorageService.setToken(response.data);
//					api.defaults.headers.common.Authorization = `Bearer ${authenticationStorageService.getToken()}`;
//				}

//				return api(requestConfig);
//			});
//		}

//		return Promise.reject(error);
//	}
//);
