
.c-multiticket {
    @media (max-width: 991px) {
        .column-list-flights {
            flex-direction: column !important;
        }
    }

    @media (min-width: 992px) {
        .column-list-flights {
            flex-direction: initial !important;
        }
    }

    @media (min-width: 1025px) {
        .hide-lg {
            display: none !important;
        }
    }

    @media (min-width: 991px) and (max-width: 1199px) {
        .hide-desk-md {
            display: block !important;
        }
    }

    @media (min-width: 992px) and (max-width: 1199px) {
        .hide-desk-md {
            display: none !important;
        }
    }

    @media (min-width: 991px) {
        .show-desktop {
            display: none !important;
        }
    }

    @media (min-width: 992px) {
        .show-desktop {
            display: block !important;
        }
    }

    @media (min-width: 768px) and (max-width: 1024px) {
        .c-flights-list .c-bar-info span {
            display: inline-block !important;
            text-align: center;
        }
    }


    @media (min-width: 992px) and (max-width: 1199px) {

        .ch-row {
            .icon {
                right: 2px;
                top: 0;
            }
            .header-color {
                p {
                    display: flex;
                    position: relative;
                    width: 90%;
                    span {
                        display: inline-block !important;
                    }
                    span.text-truncate {
                        width: 75%;
                    }
                    span:first-child {
                        order: 2;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        position: absolute;
                        left: 16px;
                        right: 0;
                        text-align: left;
                        /*@media ($tablet) { 
                            top: 24px;
                        }*/
                    }
                    span + span {
                        order: 1;
                        overflow: initial;
                        text-overflow: initial;
                        white-space: initial;
                        /*@media ($tablet) {
                            top: 10px;
                        }*/
                    }
                }
            }
        }
    }
    
}

@media (min-width: 992px) and (max-width: 1199px){
    .c-multiticket {
        .ch-row {
            .header-color {
                p {                
                    span:first-child {
                        top: 11px;
                        left: 20px;
                    }
                }
            }
        }
    }
}


.text-right {
    text-align: right !important;
}
.f-bold {
    font-family: Roboto-Bold;
}
@media (min-width: 1025px) {
    .c-out-off {
        font-size:11px;
        padding: 8px 0 12px;
    }
}

/**added**/
@media (min-width:768px) and (max-width:1023px){
    .hide-md-xs{
        display:none!important
    }
}
@media (max-width:767px),(min-width:768px) and (max-width:1024px){
    .hide-xs-md{
        display:none
    }
}
@media (max-width:767px){
    .hide-xs{
        display:none!important
    }
}@media (min-width:768px) and (max-width:1024px){
    .hide-md{
        display:none!important
    }
}
@media (min-width:1025px){
    .hide-lg{
        display:none!important
    }
}
@media (min-width:1025px){
    .hide-desk{
        display:none
    }
}@media (min-width:991px) and (max-width:1199px){
    .hide-desk-md{
        display:none
    }
}@media (min-width:1200px){
    .hide-desk-lg{
        display:none
    }
}
.cp-auto{
    cursor: pointer !important;
    pointer-events: auto !important;
}
.list-flights-v-2 .c-tf {
    position: fixed !important;
}

.btn-none{
    all: unset; /* Resetea todos los estilos */
    display: inline-block; /* Necesario para que el botón mantenga su comportamiento */
    padding: 0; /* Remueve cualquier padding por defecto */
    margin: 0; /* Remueve cualquier margen por defecto */
    border: none; /* Remueve cualquier borde por defecto */
    background: none; /* Remueve cualquier fondo por defecto */
    color: inherit; /* Usa el color de texto del contenedor */
    font: inherit; /* Usa la fuente del contenedor */
    line-height: normal; /* Normaliza el line-height */
    overflow: visible; /* Permite el overflow */
    text-align: inherit; /* Usa la alineación de texto del contenedor */
    -webkit-appearance: none; /* Remueve el estilo específico de Webkit */
    -moz-appearance: none; /* Remueve el estilo específico de Mozilla */
}
#horarioMobileIda{
 margin-left: 6px;   
}
@media (max-width: 767px) {
    .c-info-flight-xs .icon-info {
        top: 0 !important;
    }
}
.ml-05{
    margin-left: 6px !important;
}
.mr-05{
    margin-right: 6px !important;
}
.mr-025{
    margin-right: 3px !important;
}
.ml-025{
    margin-right: 3px !important;
}

@media (min-width: 1025px), (min-width: 768px) and (max-width: 1024px) {
    #detailInter #flight-info {
        width: 100% !important;
    }
}
.c-widget-empty{
    p{
        color: #333333 !important; 
    }
}

.c-multiticket {
    .cb-flights {
        left: 0;
        position: fixed;
        right: 0;
    }
}

/*ListPAge*/
.alert-box {
    display: flex;
    align-items: center;
    background-color: #FFF9E6;
    color: #6B5600;
    border: 2px solid #E6C200;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 14px;
    font-family: "Roboto", "Arial", sans-serif;
    font-weight: 500;
}

.alert-icon {
    padding-left: 10px;
    color: #f9bb2c;
}

.alert-text {
    flex: 1;
}

@media (max-width: 720px) {
    .container-notification-mobile {
        margin-left: 15px;
        margin-right: 15px;
        width: 92%;
    }
}