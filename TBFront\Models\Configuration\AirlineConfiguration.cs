namespace TBFront.Models.Configuration
{
    public class AirlineConfiguration
    {
        public List<AirlineType> National { get; set; }
        public List<AirlineType> International { get; set; }
    }

    public class AirlineType
    {
        public List<string> AirlineCode { get; set; }
        public string AirlineName { get; set; }
        public bool HiddenEmptyFamily { get; set; }
        public bool? SearchArrival { get; set; }
        public bool? Adjustable { get; set; }
        public List<ConfigurationFamily> Families { get; set; }

    }
    public class ConfigurationFamily
    {
        public string Name { get; set; }
        public List<string> FamilyFareCode { get; set; }
        public bool IsAvailable { get; set; }
    }
}