﻿using Amazon.DynamoDBv2;
using Amazon.DynamoDBv2.DataModel;

namespace TBFront.Infrastructure.DatabaseService.DynamoDB
{
    public class DynamoServices
    {

        private readonly IAmazonDynamoDB _dynamoDBClient;
        private readonly DynamoDBContext _dynamoDBContext;

        public DynamoServices(IAmazonDynamoDB dynamoDBClient)
        {
            _dynamoDBClient = dynamoDBClient;
            _dynamoDBContext = new DynamoDBContext(_dynamoDBClient);
        }


        public DynamoDBContext DynamoDBContext()
        {
            return _dynamoDBContext;
        }

    }
}
