<template>
    <form class="c-form p-4 border rounded">
        <h3 class="font-28 f-p-semibold">{{__('contactForm.title')}}</h3>
        <div class="row">
            <div class="col-12 mb-2">
                <div class="form-group position-relative">
                    <label class="px-1 mb-2">{{__('contactForm.name')}}</label>
                    <input type="text" class="form-control" :class="{'is-invalid': errors.name && submitCount > 0}" v-model="name">
                    <span class="invalid-feedback">{{__(`errors.${errors.name}`)}}</span>
                </div>
            </div>
            <!--<div class="col-12 col-md-6 mb-2">
                <div class="form-group position-relative">
                    <label class="px-1 mb-2">{{__('contactForm.entreprises')}}</label>
                    <input type="text" class="form-control" :class="{'is-invalid': errors.company && submitCount > 0}" v-model="company">
                    <span class="invalid-feedback">{{__(`errors.${errors.company}`)}}</span>
                </div>
            </div>-->
            <div class="col-12 mb-2">
                <div class="form-group position-relative">
                    <label class="px-1 mb-2">{{__('contactForm.tel')}}</label>
                    <input type="text" class="form-control" :class="{'is-invalid': errors.phone && submitCount > 0}" v-model="phone">
                    <span class="invalid-feedback">{{__(`errors.${errors.phone}`)}}</span>
                </div>
            </div>
            <div class="col-12 mb-2">
                <div class="form-group position-relative">
                    <label class="px-1 mb-2">{{__('contactForm.email')}}</label>
                    <input type="email" class="form-control" :class="{'is-invalid': errors.email && submitCount > 0}" v-model="email">
                    <span class="invalid-feedback">{{__(`errors.${errors.email}`)}}</span>
                </div>
            </div>
            <div class="col-12">
                <div class="form-group position-relative">
                    <label class="px-1 mb-2">{{__('contactForm.message')}}</label>
                    <textarea rows="6" cols="50" class="w-100 border rounded" :class="{'is-invalid': errors.message && submitCount > 0}" v-model="message"></textarea>
                    <span class="invalid-feedback">{{__(`errors.${errors.message}`)}}</span>
                </div>
            </div>
            <div class="col-12 pr-0">
                <div class="col-12 px-0">
                    <div class="custom-control custom-checkbox py-1 d-flex">
                        <input type="checkbox" class="custom-control-input" id="customCheck1" v-model="copyEmail">
                        <label class="custom-control-label" for="customCheck1">
                            <span class="c-text font-14 font-mobile-12 position-tn-2 ms-1">{{__('contactForm.checkText1')}}</span>
                        </label>
                    </div>
                </div>
                <div class="col-12 px-0">
                    <div class="custom-control custom-checkbox py-1 d-flex">
                        <input type="checkbox" class="custom-control-input" id="customCheck2" v-model="getPromos">
                        <label class="custom-control-label" for="customCheck2">
                            <span class="c-text font-14  position-tn-2 ms-1">
                                {{ __('contactForm.checkText2') }}
                            </span>

                        </label>
                    </div>
                </div>
            </div>
            <div class="col-12">
                <div class="d-flex flex-column flex-md-row justify-content-between px-2">
                    <div class="cap">
                        <div class="g-recaptcha-grupos">
                            <div class="g-recaptcha" :class="{'border-error': errorCaptcha}" id="recaptcha-contact" :data-sitekey="config.recaptchaKey"></div>
                        </div>
                        <p class="invalid-feedback text-left d-block mb-0" v-if="errorCaptcha && submitCount > 0">
                            {{__('errors.recaptcha_error')}}
                        </p>
                    </div>
                    <button class="btn btn-blue py-2 px-5 mt-2" type="button" :disabled="submitting" @click="submit()">
                        {{submitting ? __('contactForm.sendingFormBtn') : __('contactForm.sendFormBtn') }}
                    </button>
                </div>
            </div>
        </div>
    </form>

    <!-- Modal -->
    <div class="modal fade" id="contact-success" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-blue">
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body d-flex flex-column justify-content-center align-items-center">
                    <i v-if="submitError" class="icons icon-warning text-warning font-60"></i>
                    <i v-else class="icons icon-check-circle text-success font-60"></i>
                    <p>{{submitError ? __('contactForm.error') : __('contactForm.success')}}</p>
                </div>
            </div>
        </div>
    </div>

    <LoaderFullPage :show="submitting" />
</template>

<script setup>
    import { ref } from 'vue';
    import { useForm } from 'vee-validate';
    import * as yup from 'yup';
    import { __ } from '../../../utils/helpers/translate';
    import { apiRequestService } from '../../../utils/http';
    import { methods } from '../../../constants';
    import {getWgetIdCaptcha} from "../../../utils/helpers/recaptcha";
    
    const validateCaptcha = () => {
        const wgetId = getWgetIdCaptcha("#recaptcha-contact")
        const recaptchaRes = grecaptcha.getResponse(wgetId);
        if (!recaptchaRes) {
            errorCaptcha.value = true;
            submitError.value = true;
        } else {
            errorCaptcha.value = false;
        }
        grecaptcha.reset(wgetId)
        return recaptchaRes;
    };
    const phoneRegExp = /^((\\+[1-9]{1,4}[ \\-]*)|(\\([0-9]{2,3}\\)[ \\-]*)|([0-9]{2,4})[ \\-]*)*?[0-9]{3,4}?[ \\-]*[0-9]{3,4}?$/;
    const config = window.__pt.settings.site || {};

    const { handleSubmit, errors, submitCount, defineField, resetForm } = useForm({
        validationSchema: yup.object({
            name: yup.string().required(),
            //company: yup.string(),
            phone: yup.string().matches(phoneRegExp, 'phone number is not valid'),
            email: yup.string().email().required(),
            message: yup.string().required()
        }),
    });

    const [name, nameAttrs] = defineField('name');
    //const [company, companyAttrs] = defineField('company');
    const [phone, phoneAttrs] = defineField('phone');
    const [email, emailAttrs] = defineField('email');
    const [message, messageAttrs] = defineField('message');
    const copyEmail = ref(false);
    const getPromos = ref(false);
    const submitError = ref(true);
    const errorCaptcha = ref(false);

    const submitting = ref(false);

    const submit = handleSubmit(async values => {
        const token = validateCaptcha();
        if(token){
            submitting.value = true;
            const params = { ...values, copyEmail: copyEmail.value, getPromos: getPromos.value };
            const resource = { uri: config.formsConfiguration.pathContact, method: methods.POST }
            const modal = new bootstrap.Modal(document.getElementById('contact-success'), null);

            try {
                params.recaptchaToken = token;
                const response = await apiRequestService(resource, {}, params);
                if (response && response.data) {
                    submitError.value = false;
                    resetForm();
                } else {
                    submitError.value = true;
                }
            } catch (e) {
                submitError.value = true;
            } finally {
                submitting.value = false;
                modal.show();
            }
        }
    });
</script>