@import '../variables';

.loader {
    border: 5px solid #f3f3f3; /* Light grey */
    border-top: 5px solid $color-primary; /* Blue */
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 2s linear infinite;
}


.loader__logo {
    display: inline-block;
    width: 64px;
    height: 64px;

    &::before {
        content: "";
        display: inline-block;
        width: 64px;
        height: 64px;
        background-image: url(/assets-tb/img/tiquetesbaratos/loader-logo.svg);
        background-repeat: no-repeat;
        background-size: 100%;
        animation: rotatepulse 2s linear;
        animation-iteration-count: infinite;
    }
}

.loader__circle {
    border: 5px solid #f3f3f3;
    border-top: 5px solid $color-primary;
    border-bottom: 5px solid $color-secundary;
    border-left: 5px solid #4dbae9;
    border-right: 5px solid #fa9f26 ;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
}
/*
.loader__circle {
    display: inline-block;
    width: 50px;
    height: 50px;

    &::before {
        content: "";
        display: inline-block;
        width: 50px;
        height: 50px;
        background-image: url(/img/loader-circle.svg);
        background-repeat: no-repeat;
        background-size: 100%;
        animation: rotating 0.8s linear;
        animation-iteration-count: infinite;
    }
}*/

.loading-rates {
    justify-content: end;
    padding-right: 40px;

    .loader__circle {
        width: 35px;
        height: 35px;
    }
}

.loading-rates-table {
    .loader__circle {
        width: 35px;
        height: 35px;
    }
}

.loading-page {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    background-color: rgb(255 255 255 / 40%);
    z-index: 999;
}

@keyframes rotating {
    from {
        -ms-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        -webkit-transform: rotate(0deg);
        -o-transform: rotate(0deg);
        transform: rotate(0deg)
    }

    to {
        -ms-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -webkit-transform: rotate(360deg);
        -o-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}

@keyframes rotatepulse {
    0% {
        transform: rotate(0) scale(1);
        animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19)
    }

    50% {
        transform: rotate(400deg) scale(0.6);
        animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1)
    }

    100% {
        transform: rotate(1080deg) scale(1)
    }
}


@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}
