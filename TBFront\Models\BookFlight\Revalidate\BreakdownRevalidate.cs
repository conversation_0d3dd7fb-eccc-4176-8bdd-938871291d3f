﻿using Newtonsoft.Json;

namespace TBFront.Models.BookFlight
{
    public class BreakdownRevalidate
    {
        public double Amount { get; set; }
        public double Taxes { get; set; }
        public int Quantity { get; set; }

    }

    /*public class SummaryBreakdown
    {
        public int StatusCode { get; set; }
        public string? StatusMessage { get; set; }
        public double TotalAmount { get; set;}
        public double TotalAmountOld { get; set; }
        public List<TBFront.Models.Common.FareDetail>? Breakdown { get; set;}

        public SummaryBreakdown()
        {
            Breakdown = new List<TBFront.Models.Common.FareDetail>();
        }
    }
    */
}
