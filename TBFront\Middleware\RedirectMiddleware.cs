﻿using Microsoft.Extensions.Options;
using Microsoft.Extensions.Primitives;
using TBFront.Application.Mappers;
using TBFront.Interfaces;
using TBFront.Options;

namespace TBFront.Middleware
{
    public class RedirectMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly RedirectOptions _settings;
        private readonly SettingsOptions _settingsOptions;
        private readonly ICommonHandler _commonHandler;
        private readonly List<string> _resourcesPaths = ["/assets", "/img/", "/vuelo/api/"];
        private readonly List<string> _cultures;
        private readonly ILogger<RedirectMiddleware> _logger;
        public RedirectMiddleware(RequestDelegate next, ILogger<RedirectMiddleware> logger, IOptions<RedirectOptions> settings, IOptions<SettingsOptions> settingsOptions, ICommonHandler commonHandler)
        {
            _next = next;
            _settings = settings.Value;
            _commonHandler = commonHandler;
            _settingsOptions = settingsOptions.Value;
            _cultures = [.. _settingsOptions.CulturesAllowed!.Split("|")];
            _logger = logger;
        }
        public async Task InvokeAsync(HttpContext context)
        {

            var host = context.Request.Headers.TryGetValue("Host", out StringValues hostHeader) ? hostHeader.ToString() : string.Empty;
            var subdomain = host.Split('.')[0];
            var path = context.Request.Path.Value;
            var redirectUrl = string.Empty;
            var segments = context.Request.Path.Value?.Split('/', StringSplitOptions.RemoveEmptyEntries) ?? [];
            var cultureCode = segments.FirstOrDefault() ?? string.Empty;
            var isCultureValid = FlightMapper.IsCultureValid(cultureCode);
            if (_resourcesPaths.Any(p => path.Contains(p)) || path.StartsWith("/error"))
            {
                await _next(context);
                return;
            }


            if (host.Equals(_settings.RootDomain, StringComparison.OrdinalIgnoreCase))
            {
                await _next(context);
                return;
            }
            var token = new CancellationTokenSource(60000).Token;
            var cultureSelected = await _commonHandler.QueryAsync(new Culture { CultureCode = cultureCode }, token);
            var site = _settings.SitesRedirect.FirstOrDefault(s => string.Equals(s.Host, host, StringComparison.OrdinalIgnoreCase));
            var query = FlightMapper.ToQueryString(context);

            context.Items["origin"] = host;

            //Redirect por sitio que no es el default
            if (site is not null)
            {

                var cultureFromSettings = site.Culture;
                var pathParts = path.TrimStart('/').Split('/');
                if (_cultures.Contains(pathParts[0]))
                {
                    pathParts[0] = cultureFromSettings;
                    path = "/" + string.Join("/", pathParts);
                }
                else
                {
                    path = $"/{cultureFromSettings}{(path == "/" ? "" : path)}";
                }
                redirectUrl = $"{_settings.RootDomain}{path}{query}";
                context.Items["redirectUrlSite"] = redirectUrl;
                context.Response.Redirect(redirectUrl, false); //false de momento para no hacerlo permanente en caso de rollback, mandarlo true cuando estemos seguro que funciona todo bien
                await _next(context);
                return;
            }
            //redirect por cultura que no esta en la lista
            if (isCultureValid && !string.Equals(cultureSelected.CultureCode, cultureCode, StringComparison.OrdinalIgnoreCase))
            {
                var pathParts = path.TrimStart('/').Split('/');
                pathParts[0] = cultureSelected.CultureCode;
                path = "/" + string.Join("/", pathParts);
                redirectUrl = $"{_settings.RootDomain}{path}{query}";
                context.Items["redirectUrlCulture"] = redirectUrl;
                context.Response.Redirect(redirectUrl, false);
                await _next(context);
                return;
            }
            await _next(context);
        }

    }
}