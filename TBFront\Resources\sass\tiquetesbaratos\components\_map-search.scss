.go-map {
    position: relative;
    cursor: pointer;
    //height: 100px;
    /*height: 100px;
    //background-image: url("/img/map.svg");
    background-size: cover;
    background-position: center;
    border-radius: 5px;
    display: block !important;
    text-align: center;
    line-height: 100px;
        */
    button {
        background: $purple-300;
        padding: 0.5rem;
        border-radius: 5px;
        color: $white
    }

    &:hover {
        span {
            background: $purple;
        }
    }
}

.btn-go-map {
    top: 32%;
    left: 50%;
    transform: translateX(-50%);
}

.filters-map {
    height: 100vh;
    overflow-y: auto;
    overflow-x: clip;
    padding-top: 46px;
}

.modal-header {
    z-index: 1051;
    background-color: $white;
}

.map-search {

    .toolbar__left {
        width: max-content;
    }

    .card-body {
        padding: 0.5rem !important;
    }

    .modal-title {
        display: inline-block;
    }

    .modal-header .filter-list-item {
        display: inline-block;
        margin-bottom: 0;
    }

    .map {
        width: 100%;
        height: 100vh;
        position: absolute;
        top: 0;
        left: 0;
        padding-bottom: 46px;
    }

    .tooltip-link {
        background: $white;
        padding: 0.3rem 0.5rem;
        position: relative;
        top: 0;
        left: 0;
        width: max-content;
        border-radius: 6px;
        border: 1px solid $white;
        cursor: pointer;
        color: $black;
        box-shadow: 1px -1px 24px 1px rgba(0,0,0,0.37);
        -webkit-box-shadow: 1px -1px 24px 1px rgba(0,0,0,0.37);
        -moz-box-shadow: 1px -1px 24px 1px rgba(0,0,0,0.37);

        .stars {
            font-size: 0.8rem;
            display: inline-block;
            vertical-align: middle;
        }

        p {
            margin: 0;
            font-size: 0.9rem;
            display: inline-block;
            vertical-align: middle;
        }

        i {
            vertical-align: middle;
        }

        .icons-carry-on-bag-off {
            color: $gray-500;
        }
    }

    i.disabled {
        color: #ccc;
    }

    .tooltip-link::before {
        content: '';
        position: absolute;
        left: calc(50% - 7px);
        top: -10px;
        width: 15px;
        height: 15px;
        margin-top: 35px;
        overflow: hidden;
        transform: rotate(45deg);
        background: $white;
    }

    .tooltip-link-active {
        border: 1px solid $pink;
        background: $pink-100 !important;
    }

    .tooltip-link-active::before {
        border-right: 1px solid $pink;
        border-bottom: 1px solid $pink;
        background: $pink-100 !important;
    }

    .tooltip-link-inactive {
        border: none !important;
        background: $gray-200 !important;
    }

    .tooltip-link-inactive::before {
        background: $gray-200 !important;
    }

    .featured {
        width: 250px;
        height: auto;
        position: absolute;
        bottom: 5px;
        left: 100px;
        background-color: $white;
        padding: 1rem;
        border-radius: 6px 6px 0 0;
        box-shadow: 1px -1px 24px 1px rgba(0, 0, 0, 0.37);
        -webkit-box-shadow: 1px -1px 24px 1px rgba(0, 0, 0, 0.37);
        -moz-box-shadow: 1px -1px 24px 1px rgba(0, 0, 0, 0.37);

        .filter-list-item {
            margin: 0;
        }
    }

    .l-page-main {
        flex: none;
        padding: 0;
    }

    .l-page-sidebar {
        padding-right: 0;
    }

    .gm-style {
        height: 96%;
    }

    .cluster-marker {
        color: $white;
        background: #333;
        border-radius: 50%;
        padding: 14px;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 3;
        font-size: 16px;
        font-weight: bold;
        cursor: pointer;
    }
}
//fin de map-seach anidados

.only-desktop {
    display: none;
}

.start-gray {
    height: 15px;
    width: 15px;
    display: inline-block;
    margin-top: 0;
    vertical-align: sub;
    background-size: 15px;
}

#modal-mapsearch .form-check > label {
    padding-left: 29px !important;
    min-height: 22px;
    line-height: 1.5;
    display: inline-block;
    position: relative;
    vertical-align: top;
    margin-bottom: 0;
    font-weight: 400;
    cursor: pointer;
    color: #343a40;
}

.map-search {

    .close {
        position: absolute;
        top: 0;
        right: 0;
    }

    .modal-body {
        width: 100%;
        height: 100vh;
    }
}

.modal-fullscreen .modal-body {
    overflow: hidden;
}

@media ($phone) {
    .filters-map {
        display: none;
    }

    .map-search h5.modal-title {
        white-space: normal;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        padding-right: 30px;
    }

    #modal-filters {
        z-index: 1200;

        .modal-dialog {
            margin: 0;
        }

        .modal-footer {
            display: block;
            position: fixed;
            width: 100%;
            background: $white;
            bottom: 0;
        }
    }
}


@media #{$tablet} {
    .filters-map {
        display: none;
    }

    #modal-filters {
        z-index: 1200;
    }

    .map-search {
        .modal-body {
            width: 100%;
            overflow: auto;
        }
    }
}


.button--elevation, .hotel-map__button {
    background: #fff;
    color: $color-primary-hover;
    box-shadow: 0 3px 1px -2px rgba(0,0,0,0.02),0 2px 2px 0 rgba(0,0,0,0.14),0 1px 5px 0 rgba(0,0,0,0.12);

    &:hover {
        background-color: $white;
        box-shadow: 0 5px 5px -3px rgba(0,0,0,0.02),0 8px 10px 1px rgba(0,0,0,0.14),0 3px 14px 2px rgba(0,0,0,0.12);
    }
}

.hotel-map__button {
    cursor: pointer;
}

.icons-location {
    font-size: 24px;
}
