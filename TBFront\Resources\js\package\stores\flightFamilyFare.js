import { defineStore } from "pinia";
import { ListMapper, LuggageMapper } from '../mappers/luggageMapper';

export const useFlightFamilyFareStore = defineStore({
    id: "flightFamilyFare",
    state: () => ({
        flightFamilyFareResponse: {
            familyFareContent: []
        },
        luggage: {},
        luggageResponse: [],
        isLuggage: false,
        isLoadingLuggages: true
    }),
    getters: {
        getFlightFamilyFare: (state) => {
            return state.flightFamilyFareResponse;
        },
        getLuggage: (state) => {
            return state.luggage;
        },
        getIsLuggage: (state) => {
            return state.isLuggage;
        },
        getIsLoadingLuggages: (state) => {
            return state.isLoadingLuggages;
        }
    },
    actions: {
        setFlightFamilyFareResponse(response) {
            this.flightFamilyFareResponse = response;
        },
        setLuggage(response) {
            this.luggageResponse.push(response);
            this.luggage = LuggageMapper.map(this.luggageResponse);
        },
        setIsLuggage(response) {
            this.isLuggage = response;
        },
        setIsLoadingLuggages(response) {
            this.isLoadingLuggages = response;
        }
    },
});