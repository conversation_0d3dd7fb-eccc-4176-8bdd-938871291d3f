﻿
using Microsoft.Extensions.Caching.Distributed;
using ProtoBuf;
using StackExchange.Redis;
using System.Text.Json;
using TBFront.Infrastructure.DatabaseService.Redis.Dtos;
using TBFront.Interfaces;

namespace TBFront.Infrastructure.DatabaseService.Redis
{
    public class RedisCacheService : ICacheService
    {
        private readonly ILogger<RedisCacheService> _logger;
        private readonly IDistributedCache _cache;
        private readonly IDatabase _dbCache;
        private readonly RedisCacheConfiguration _redisCacheConfiguration;
        private readonly DistributedCacheEntryOptions _distributedCacheEntryOptions;
        private readonly JsonSerializerOptions _jsonSerializerOptions;
        private readonly ConnectionMultiplexer _cacheMultiplex;


        public RedisCacheService(RedisCacheConfiguration redisCacheConfiguration, IDistributedCache cache, ILogger<RedisCacheService> logger)
        {
            _redisCacheConfiguration = redisCacheConfiguration;
            _logger = logger;

            _jsonSerializerOptions = new JsonSerializerOptions
            {
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            };

            _distributedCacheEntryOptions = new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(_redisCacheConfiguration.Expiration)
            };

            _cache = cache;

            if (_redisCacheConfiguration.Active)
            {
                _cacheMultiplex = ConnectionMultiplexer.Connect($"{_redisCacheConfiguration.Host}:{_redisCacheConfiguration.Port},abortConnect=false");
                _dbCache = _cacheMultiplex.GetDatabase();
            }
        }


        public async Task<T> RedisGetCache<T>(string redisKey, CancellationToken ct)
        {
            if (_redisCacheConfiguration.Active)
            {
                try
                {
                    var jsonString = await _dbCache.StringGetAsync(redisKey);

                    if (jsonString.HasValue)
                    {
                        return JsonSerializer.Deserialize<T>(jsonString, _jsonSerializerOptions);
                    }

                }
                catch (Exception e)
                {
                    _logger.LogError($"RedisGetCache: message: {e.Message} - key: {redisKey}");
                }
            }

            return default;
        }



        public void RedisSetCache<T>(string key, T value)
        {
            if (_redisCacheConfiguration.Active)
            {
                try
                {
                    if (value != null)
                    {
                        var str = JsonSerializer.Serialize(value, _jsonSerializerOptions);
                        _dbCache.StringSet(key, str, TimeSpan.FromMinutes(_redisCacheConfiguration.Expiration), flags: CommandFlags.FireAndForget);
                    }
                }
                catch (Exception e)
                {
                    _logger.LogError($"RedisSetCache: message: {e.Message} - key:{key}");
                }
            }
        }

        //***
        //
        //
        //protobuf implementation
        //
        //
        //

        public async Task<T> GetCache<T>(string redisKey, CancellationToken ct)
        {
            if (_redisCacheConfiguration.Active)
            {
                var redisByteResponse = await Get(redisKey, ct);

                if (redisByteResponse != null)
                {
                    try
                    {
                        using (var stream = new MemoryStream(redisByteResponse))
                        {
                            return Serializer.Deserialize<T>(stream);
                        }

                    }
                    catch (Exception e)
                    {
                        Console.Write(e);
                    }
                }
            }

            return default;
        }

        private async Task<byte[]> Get(string key, CancellationToken ct)
        {
            if (_redisCacheConfiguration.Active)
            {
                try
                {
                    var wrapper = await _dbCache.StringGetAsync(key);
                    var result = await Task.Run(async () => await Task.FromResult(wrapper), ct);

                    return result;
                }
                catch (Exception e)
                {
                    Console.Write(e);
                }
            }

            return default;
        }

        public void SetCache<T>(string key, T value, int expiration = 0)
        {
            if (_redisCacheConfiguration.Active)
            {
                try
                {
                    if (value != null)
                    {

                        using (var stream = new MemoryStream())
                        {
                            Serializer.Serialize(stream, value);

                            var bytesResponse = stream.ToArray();
                            var expirationOptions = expiration > 0 ? new DistributedCacheEntryOptions
                            {
                                AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(expiration)
                            } : _distributedCacheEntryOptions;

                            _dbCache.StringSet(key, bytesResponse, TimeSpan.FromMinutes(_redisCacheConfiguration.Expiration), flags: CommandFlags.FireAndForget);

                        }
                    }
                }
                catch (Exception e)
                {
                    Console.Write(e);
                }
            }
        }
    }
}
