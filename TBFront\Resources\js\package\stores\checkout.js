import { defineStore } from "pinia";

export const useCheckoutStore = defineStore('checkout', {
    state: () => ({
        quoteResponse: {
            totalAmout: 0
        },
        voucherResponse: {
            info: {},
            quote: {},
            reservation: {}
        }
    }),
    getters: {
        getTotalAmount: (state) => state.quoteResponse.totalAmout,
        getQuote: (state) => state.quoteResponse,
        getVoucher: (state) => state.voucherResponse,
    },
    actions: {
        setTotalAmount(total) {
            this.quoteResponse.totalAmout = total;
        },
        setVoucher(voucherResponse) {
            this.voucherResponse = voucherResponse;
        },
        setQuote(quoteResponse) {
            this.quoteResponse = quoteResponse;
        }
    }
});