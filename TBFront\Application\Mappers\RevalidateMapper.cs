﻿using TBFront.Models.BookFlight;
using TBFront.Models.Common;
using TBFront.Models.Flight.Quote;
using TBFront.Models.Flight.Revalidate;
using TBFront.Models.Request;
using TBFront.Options;
using TBFront.Types;

namespace TBFront.Application.Mappers
{
    public class RevalidateMapper
    {
        public static RevalidateResponse Map(RevalidateResponse response)
        {
            return response;
        }

        public static RevalidateRequest Request(CheckoutRevalidateRequest request, SettingsOptions options)
        {
            return new RevalidateRequest
            {
                Currency = request.Currency,
                Fares = request.Fares,
                Flights = request.Flights,
                IsPackage = false,
                ShowDetailAmounts = true,
                ShowRevenueByLeg = true,
                TaskID = request.QuoteTaskID!,
                Context = ContextOrganization(request.Channel, options.OrganizationContent),
                NegotiatedFareId = request.NegotiatedFareId
            };
        }


        public static SummaryBreakdown Revalidate(RevalidateRootResponse response, bool isRoundtrip, double totalAmountOld)
        {

            var summary = new SummaryBreakdown
            {
                StatusMessage = response.StatusMessage,
                StatusCode = response.StatusCode,
                TotalAmountOld = totalAmountOld,
            };

            if (response.TotalRecommendations > 0)
            {
                var recommendation = response.Recommendations["1"];
                var totalAmount = recommendation.FareCombinables.Sum(r => r.TotalFare.Amount);
                var flightFare = response.FaresByLeg["1"]["1"];
                var extraFee = RevalidateMapper.ExtraFeeAmount(flightFare.RevenueCharges);
                var breakdownAdult = RevalidateMapper.BaseAmount(flightFare.PassengerFareBreakDown, new BreakdownRevalidate(), BookingType.Adult, true);
                var breakdownChildren = RevalidateMapper.BaseAmount(flightFare.PassengerFareBreakDown, new BreakdownRevalidate(), BookingType.Children, true);
                var breakdownInfant = RevalidateMapper.BaseAmount(flightFare.PassengerFareBreakDown, new BreakdownRevalidate(), BookingType.Infant, true);

                if (isRoundtrip)
                {
                    flightFare = response.FaresByLeg["2"]["1"];

                    extraFee += RevalidateMapper.ExtraFeeAmount(flightFare.RevenueCharges);
                    breakdownAdult = RevalidateMapper.BaseAmount(flightFare.PassengerFareBreakDown, breakdownAdult, BookingType.Adult, false);
                    breakdownChildren = RevalidateMapper.BaseAmount(flightFare.PassengerFareBreakDown, breakdownChildren, BookingType.Children, false);
                    breakdownInfant = RevalidateMapper.BaseAmount(flightFare.PassengerFareBreakDown, breakdownInfant, BookingType.Infant, false);

                }

                summary.Breakdown = RevalidateMapper.GetBreakdown(extraFee, breakdownAdult, breakdownChildren, breakdownInfant);
                summary.TotalAmount = totalAmount;
            }


            return summary;

        }

        private static double ExtraFeeAmount(RevenueCharges charges)
        {
            double total = 0;

            if (charges is not null)
            {
                total += charges.EXTRAFEE.Sum(s => s.Amount);
                total += charges.MARGIN.Sum(s => s.Amount);
                total += charges.EXTRASERVICES.Sum(s => s.Amount);
            }


            return total;
        }

        private static BreakdownRevalidate BaseAmount(List<PassengerFareBreakDown> breakDown, BreakdownRevalidate breakdownRevalidate, int passengerType, bool quantitySum)
        {

            var filtersBreakdown = breakDown.Where(b => b.PassengerType == passengerType).ToList();

            foreach (var breakdown in filtersBreakdown)
            {
                breakdownRevalidate.Amount += breakdown.ServiceCharges.ProviderEquiv.Sum(s => s.Amount) * breakdown.Quantity;
                breakdownRevalidate.Taxes += breakdown.ServiceCharges.ProviderTax.Sum(s => s.Amount) * breakdown.Quantity;

                if (quantitySum)
                {
                    breakdownRevalidate.Quantity += breakdown.Quantity;
                }
            }

            return breakdownRevalidate;
        }


        private static List<TBFront.Models.Common.FareDetail> GetBreakdown(double extraFee, BreakdownRevalidate breakdownAdult, BreakdownRevalidate breakdownChildren, BreakdownRevalidate breakdownInfant)
        {
            var breakdowns = new List<FareDetail>();

            if (breakdownAdult.Amount > 0)
            {
                breakdowns.Add(new FareDetail
                {
                    Amount = breakdownAdult.Amount,
                    DisplayText = $"{breakdownAdult.Quantity} ",
                    Type = BookingType.Adult
                });
            }

            if (breakdownChildren.Amount > 0)
            {
                breakdowns.Add(new FareDetail
                {
                    Amount = breakdownChildren.Amount,
                    DisplayText = $"{breakdownChildren.Quantity} ",
                    Type = BookingType.Children
                });
            }

            if (breakdownInfant.Quantity > 0)
            {
                breakdowns.Add(new FareDetail
                {
                    Amount = breakdownInfant.Amount,
                    DisplayText = $"{breakdownInfant.Quantity} ",
                    Type = BookingType.Infant
                });
            }
           

            if (breakdownAdult.Taxes > 0)
            {
                breakdowns.Add(new FareDetail
                {
                    Amount = breakdownAdult.Taxes + breakdownChildren.Taxes + breakdownInfant.Taxes,
                    DisplayText = $"Impuestos",
                    Type = BookingType.Taxes
                });
            }

            if (extraFee > 0)
            {
                breakdowns.Add(new FareDetail
                {
                    Amount = extraFee,
                    DisplayText = $"Cargo por servicio",
                    Type = BookingType.ServiceCharge
                });
            }

            if (breakdownAdult.Taxes > 0)
            {
                breakdowns.Add(new FareDetail
                {
                    Amount = breakdownAdult.Taxes + breakdownChildren.Taxes + breakdownInfant.Taxes + extraFee,
                    DisplayText = $"Impuestos, tasas y cargos",
                    Type = BookingType.AllCharges
                });
            }

            return breakdowns;
        }

  
        private static Context ContextOrganization(int channel, int organizationID)
        {
            return new Context
            {
                ClientID = 4,
                ChannelID = channel,
                OrganizationID = organizationID
            };
        }

    }
}
