﻿using Microsoft.Extensions.Options;
using TBFront.Interfaces;
using TBFront.Models.Configuration;
using TBFront.Models.Quote;
using TBFront.Options;

namespace TBFront.Mappers
{
    public class ConfigService
    {
        private readonly SettingsOptions _options;
        private IHttpContextAccessor _httpContextAccessor;
        private readonly string _sourceDefault = "default";
        private readonly ICommonHandler _commonHandler;
        public ConfigService(IOptions<SettingsOptions> options, IHttpContextAccessor httpContextAccessor, ICommonHandler commonHandler)
        {
            _options = options.Value;
            _httpContextAccessor = httpContextAccessor;
            _commonHandler = commonHandler;
        }

        public async Task<AppConfiguration> GetAppConfiguration(bool robot = false, bool mobile = false)
        {
            var token = new CancellationTokenSource(TimeSpan.FromMilliseconds(1000)).Token;

            var source = _httpContextAccessor.HttpContext.Request.Query["source"].FirstOrDefault();
            var countryCode = _httpContextAccessor.HttpContext.Items["countryCode"]?.ToString() ?? string.Empty;

            var siteTask = _commonHandler.QueryAsync(new ChannelOptions { Country = countryCode, Source = source }, token);
            var channelTask = _commonHandler.QueryAsync(new ChannelConfiguration { Id = countryCode }, token);
            var siteSelectionTask = _commonHandler.QueryAsync(new UserSelectionRequest { }, token);

            if (string.IsNullOrEmpty(source))
            {
                source = _sourceDefault;
            }

            await Task.WhenAll(siteTask, channelTask);

            var siteConfig = await siteTask;
            var channelConfigSelected = await channelTask;
            var siteSelection = await siteSelectionTask;

            var channelConfig = channelConfigSelected.ChannelConfig.Find(s => s.Source.Contains(source, StringComparison.CurrentCultureIgnoreCase));
            channelConfig ??= channelConfigSelected.ChannelConfig.Find(s => s.Source.Contains(_sourceDefault));

            return new AppConfiguration()
            {
                SiteName = _options.SiteName,
                Language = _options.Language,
                Culture = _options.Culture,
                Currency = _options.Currency,
                CurrencySymbol = siteSelection.Currency.CurrencyCodeSymbol,
                CurrencyCodeName = siteSelection.Currency.CurrencyCodeName,
                DecimalDigits = siteSelection.Currency.DecimalDigits,
                Domain = _options.Domain,
                ApiB2C = _options.ApiB2C,
                Mobile = mobile,
                ChannelConfig = channelConfig,
                Channel = mobile ? channelConfig.Mobile.ChannelId.ToString() : channelConfig.Desktop.ChannelId.ToString(),
                CheckoutUrl = _options.CheckoutUrl,
                QuoteCheckoutStepOne = _options.QuoteCheckoutStepOne,
                RangeTotalAmount = _options.RangeTotalAmount,
                RevalidateTimeOut = _options.RevalidateTimeOut,
                VoucherCheckout = _options.VoucherCheckout,
                ListUrl = _options.ListUrl,
                RevalidateCheckout = _options.RevalidateCheckout,
                BookingCheckout = _options.BookingCheckout,
                EmailCheckouttUrl = _options.EmailCheckouttUrl,
                CloudCdn = _options.CloudCdn,
                SiteUrl = _options.SiteUrl,
                ChannelLegacy = _options.Channel,
                GoogleMapsApiKey = _options.GoogleMapsApiKey,
                ShowConsoleLogs = _options.ShowConsoleLogs,
                RecaptchaKey = _options.RecaptchaKey,
                TimeToShowRequoteModal = _options.TimeToShowRequoteModal,
                Sufix = _options.Sufix,
                Property = _options.Property,
                Organization = _options.Organization,
                Code = channelConfigSelected.Domain,
                QuoteConfiguration = _options.QuoteConfiguration,
                Retry = _options.Retry,
                RetryTimeOut = _options.RetryTimeOut,
                Assets = _options.Assets,
                AirlineConfiguration = _options.AirlineConfiguration,
                FlightValidationStops = _options.FlightValidationStops,
                FormsConfiguration = _options.FormsConfiguration,
                ContactMeAvailableTime = _options.ContactMeConfiguration.AvailableTime,
                AlgoliaFlightIndex = _options.AlgoliaFlightIndex,
                BreakdownList = _options.BreakdownList,
                RetryCheckout = _options.RetryCheckout,
                RetryCheckoutAllowed = _options.RetryCheckoutAllowed,
                Version = _options.Version,
                FingerPrintkey = _options.FingerPrintkey,
                FingerPrintURL = _options.FingerPrintURL,
                FingerPrintkeyEnableSdk = _options.FingerPrintkeyEnableSdk,
                InternalAPI = _options.InternalAPI,
                EmailDomains = _options.EmailDomains,
                ApiFlights = new ApiFlights
                {
                    Domain = _options.ApiFlights.Domain,
                    Path = _options.ApiFlights.Path,
                    PathSearch = _options.ApiFlights.PathSearch,
                    PathPromotions = _options.ApiFlights.PathPromotions,
                    PaxConfig = _options.ApiFlights.PaxConfig,
                    PathFilterSearch = _options.ApiFlights.PathFilterSearch,
                    PathMatrix = _options.ApiFlights.PathMatrix,
                    PathFlightDetail = _options.ApiFlights.PathFlightDetail,
                    PathFlightFamilyFare = _options.ApiFlights.PathFlightFamilyFare,
                    PathRate = _options.ApiFlights.PathRate,
                    PathUpsell = _options.ApiFlights.PathUpsell,
                    PathCheckout = _options.ApiFlights.PathCheckout,
                    PathLuggage = _options.ApiFlights.PathLuggage,
                    SiteConfig = channelConfigSelected.Site,
                },
                Booker = new BookerConfig
                {
                    Adults = _options.Booker.Adults,
                    Kids = _options.Booker.Kids,
                    KidsAge = _options.Booker.KidsAge,
                    ServiceUrl = _options.Booker.ServiceUrl,
                    ServicePath = _options.Booker.ServicePath,
                    AlgoliaSiteName = siteSelection.Culture.Code.ToUpper(),
                    AutocompleteItems = _options.Booker.AutocompleteItems,
                    PlaceTypes = _options.Booker.PlaceTypes,
                    HistoryStorageKey = _options.Booker.HistoryStorageKey,
                    HistoryResearchStorageKey = _options.Booker.HistoryResearchStorageKey,
                }

            };
        }

    }
}