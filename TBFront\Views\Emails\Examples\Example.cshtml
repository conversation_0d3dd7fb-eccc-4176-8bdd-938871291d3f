﻿@using Microsoft.Extensions.Options
@using TBFront.Options;
@inject IOptions<SettingsOptions> settingOptions
@{
    Layout = null;
    var welcomeMessage = "¡Te damos la bienvenida a PriceTravel!";
}

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" >
    <meta http-equiv="X-UA-Compatible" content="IE=edge" >
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>
</head>
<body style="margin: 0; padding: 0;">
    <center style="-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; margin: 0; padding: 0; width: 100% !important; background-color: #F7F7F8; table-layout: fixed;">
        Hola !!
    </center>
    @section Preload {
    <link rel="preconnect" href="@settingOptions.Value.SiteUrl">
    <link rel="preconnect" href="https://img.cdnpth.com">
    }
</body>
</html>
