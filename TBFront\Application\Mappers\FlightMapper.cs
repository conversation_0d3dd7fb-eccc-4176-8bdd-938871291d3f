﻿using System.Text.RegularExpressions;
using TBFront.Models.Request;
using TBFront.Options;

namespace TBFront.Application.Mappers
{
    public class FlightMapper
    {
        private static readonly string _patternCulture = @"^[a-zA-Z]{2}(-[a-zA-Z]{2})?$";

        public static FlightContentRequest GetRequestHotelContent(SettingsOptions options, string hotelId)
        {
            return new FlightContentRequest
            {
                Culture = options.Culture!,
                OrganizationId = options.Organization,
                HotelId = hotelId
            };
        }

        public static string ToQueryString(HttpContext httpContextAccessor)
        {
            var deserialized = httpContextAccessor.Request.Query.ToDictionary(x => x.Key, x => x.Value.ToString());
            var queryString = "";

            if (deserialized.Count > 0)
            {
                queryString = deserialized.Select((kvp) => kvp.Key.ToString() + "=" + Uri.EscapeDataString(kvp.Value)).Aggregate((p1, p2) => p1 + "&" + p2);
                queryString = $"?{queryString}";
            }

            return queryString;
        }

        public static bool IsCultureValid(string culture)
        {
            return Regex.IsMatch(culture, _patternCulture);
        }

    }
}