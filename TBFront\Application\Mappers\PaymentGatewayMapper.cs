﻿using TBFront.Aplication.Analytics;
using TBFront.Infrastructure.HttpService.PaymentGateway.Dtos;
using TBFront.Models;
using TBFront.Models.Flight.CreateBooking;
using TBFront.Models.Flight.Summary;
using TBFront.Models.PaymentGateway;
using TBFront.Models.PaymentGateway.Dtos;
using TBFront.Models.Request;
using TBFront.Models.Response;
using TBFront.Options;

namespace TBFront.Application.Mappers
{
    public class PaymentGatewayMapper
    {
        public static PaymentGatewayRequest Request(CheckoutBookingRequest request, CreateBookingResponse createbookingResponse, SettingsOptions _options, PaymentGatewayConfiguration configuration, string sessionId, string id, string email, SummaryResponse summaryResponse)
        {


            var customerInfo = request.Customer;
            var bookingQuote = request.Quote;

            var channelConfig = _options.ChannelConfig.First();


            var paymentTokenConfig = new PaymentTokenConfig
            {
                Is3DSecureProcessingEnabled = false,
                HostUrl = _options.SiteUrl,
                UrlRedirectTo = $"{_options.SiteUrl}{_options.RedirectToPath}?keyValidation={request.Revalidate.QuoteTaskID}&id={id}&em={email}",
                RedirectToPaymentGatewayConfirmation = false,
                AnalyticsId = _options.GTM,
                Analytics = DataLayerHandler.GetAnalyticsPaymentGateway(request),
                ChannelGroupId = channelConfig.ChannelGroupId,
                ChannelId = channelConfig.Desktop,
                PaymentGatewayApp = configuration.PaymentGatewayApp,
                ContactPhone = _options.ContactPhone,
                ThirdPartyCheckoutProvider = configuration.thirdPartyCheckoutProvider.ToList(),
                ExternalProvider = configuration.CheckoutProvider.ToList(),
                SessionId = sessionId ?? "",
                AffiliateId = _options.AffiliateId,
                AffiliateSiteId = _options.AffiliateSiteId,
            };

            var requestPaymentGateway = RequestToGeneratePaymentToken(request, paymentTokenConfig, createbookingResponse, summaryResponse);


            return requestPaymentGateway;
        }


        private static PaymentGatewayRequest RequestToGeneratePaymentToken(CheckoutBookingRequest bookingRequest, PaymentTokenConfig paymentTokenConfig, CreateBookingResponse responseBook, SummaryResponse summaryResponse)
        {
            var customerInfo = bookingRequest.Customer;
            var firtsCustomerInfo = bookingRequest.Customer.PassengersBooking.FirstOrDefault().Passengers.FirstOrDefault();
            var engine = bookingRequest.Revalidate.EnginesInResponse.First();
            var bookingQuote = bookingRequest.Quote;
            var totalAmount = responseBook.AmountTotal;

            var services = new List<Service>() {
                ToServices(bookingRequest, responseBook, 0, summaryResponse) //to do: actualmente solo va por el primer PNR de un solo vuelos, en multivuelos hay que hacer la implementacion
            };


            var paymentTokenRequest = new PaymentGatewayRequest
            {
                LocatorId = int.Parse(responseBook.Id),
                Reference = responseBook.Id,
                Currency = bookingQuote.Currency,
                Process3DSecure = paymentTokenConfig.Is3DSecureProcessingEnabled,
                KeyValidation = bookingRequest.Revalidate.QuoteTaskID,
                SessionId = paymentTokenConfig.SessionId,
                Host = paymentTokenConfig.HostUrl,
                ResponseUrl = paymentTokenConfig.UrlRedirectTo,
                ReturnPaymentOption = true,
                ReturnPaymentProcessOption = true,
                PaymentConfigInfoRequest = new PaymentConfigInfoRequest
                {
                    Currency = bookingQuote.Currency,
                    Amount = decimal.Round((decimal)totalAmount, 2, MidpointRounding.AwayFromZero),
                    ExternalProviders = paymentTokenConfig.ExternalProvider,
                    BookingChannelId = bookingQuote.ChannelId,
                    ChannelGroupId = paymentTokenConfig.ChannelGroupId,
                    Product = 3, // 1 => para hotel, 3 => para solo vuelo y 6 => para paquete hotel+ vuelo
                    PaymentGatewayApp = paymentTokenConfig.PaymentGatewayApp,
                    FlightEngine = engine,
                    AirlineIATACode = bookingQuote.FlightItinerary.Starting.AirlineCode,
                    AffiliateId = paymentTokenConfig.AffiliateId,
                    AffiliateSiteId = paymentTokenConfig.AffiliateSiteId

                },
                QuoteInfo = new QuoteInfo
                {
                    PaxName = firtsCustomerInfo.Firstname + " " + firtsCustomerInfo.Lastname,
                    PaxEmail = customerInfo.Email,
                    Services = services,
                    PaxPhone = customerInfo.Phone,
                    DepositDateLimit = bookingQuote.QuoteExpirationDate.ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ"),
                    DateLimit = bookingQuote.QuoteExpirationDate.ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ"),
                    DocumentId = firtsCustomerInfo.IdentityDocument.ToString(),
                },
                TagManagerDataLayer = new TagManagerDataLayer
                {
                    TagManagerContainerId = paymentTokenConfig.AnalyticsId,
                    DataLayerItems = paymentTokenConfig.Analytics
                },
                ShowConfirmationPage = false,
                ContactPhone = paymentTokenConfig.ContactPhone,
                ThirdPartyCheckoutProvider = paymentTokenConfig.ThirdPartyCheckoutProvider,
            };


            return paymentTokenRequest;
        }

        private static Service ToServices(CheckoutBookingRequest bookingRequest, CreateBookingResponse responseBook, int index, SummaryResponse summaryResponse)
        {
            var flightReservationSegment = new List<FlightReservationSegment>();
            var flightReservationSegmentIntermediatePoint = new List<FlightReservationSegmentIntermediatePoint>();
            var airPassengers = new List<AirPassengers>();
            var itemLocatorDetail = responseBook.ItemLocatorDetails is not null && responseBook.ItemLocatorDetails.Count > 0 ? responseBook.ItemLocatorDetails[index] : new ItemLocatorDetails();

            var customerInfo = bookingRequest.Customer;
            var bookingQuote = bookingRequest.Quote;
            var flightItinerary = bookingQuote.FlightItinerary;
            var flightDetail = bookingRequest.FlightDetail;
            var detailReturn = flightDetail.DetailReturning.Detail;
            var detailStart = flightDetail.DetailStarting.Detail;

            var detailFareFamilyReturn = flightDetail.DetailReturning.DetailFamilyFare;
            var detailFareFamilyStart = flightDetail.DetailStarting.DetailFamilyFare;
            
            if (summaryResponse != null && summaryResponse.Items != null)
            {
                var segmentDeparture = summaryResponse.Items.Flights.First().FlightDepartureSegments.First();
                detailFareFamilyStart.FamilyFareName = segmentDeparture.FamilyFareName;
                if (bookingRequest.Quote.Rate.IsRoundtrip)
                {
                    var segmentReturning = summaryResponse.Items.Flights.Last().FlightReturningSegments.FirstOrDefault();
                    segmentReturning = segmentReturning is not null ? segmentReturning : summaryResponse.Items.Flights.Last().FlightDepartureSegments.First();
                    detailFareFamilyReturn.FamilyFareName = segmentReturning.FamilyFareName;
                }
            }

            //var upsell = bookingQuote.Upsell;
            var changeTotal = responseBook.AmountTotal - (responseBook.OldTotalAmount != 0 ? responseBook.OldTotalAmount : responseBook.AmountTotal);
            var hasUpsell = detailFareFamilyStart.FamilyFareContent is not null && detailFareFamilyStart.FamilyFareContent.Count > 0;



            flightReservationSegment.Add(new FlightReservationSegment
            {
                SegmentId = 1,
                SegmentIndex = 0,
                AirlineCode = flightItinerary.Starting.AirlineCode,
                AirlineName = flightItinerary.Starting.Airline,
                ArrivalAirportCode = flightItinerary.Starting.ArrivalName,
                //ArrivalAirportCode = flightItinerary.Starting.Arrival.Airport,
                ArrivalCity = flightItinerary.Starting.ArrivalName,
                ArrivalTime = GetFormattedDateTime(flightItinerary.Starting.Arrival.Date, flightItinerary.Starting.Arrival.Time),
                ArrivalTimeZone = 0,
                DepartAirportCode = flightItinerary.Starting.DepartureName, 
                //DepartAirportCode = flightItinerary.Starting.Departure.Airport,
                DepartCity = flightItinerary.Starting.DepartureName,
                DepartureTime = GetFormattedDateTime(flightItinerary.Starting.Departure.Date, flightItinerary.Starting.Departure.Time),
                DepartTimeZone = 0,

                FlightNumber = flightItinerary.Starting.FlightNumber,
                TicketClass = detailFareFamilyStart.FamilyFareName.ToUpper(),
            });
            if (bookingRequest.Quote.Rate.IsRoundtrip)
            {
                flightReservationSegment.Add(new FlightReservationSegment
                {
                    SegmentId = 2,
                    SegmentIndex = 0,
                    AirlineCode = flightItinerary.Returning.AirlineCode,
                    ArrivalAirportCode = flightItinerary.Returning.ArrivalName,
                    //ArrivalAirportCode = flightItinerary.Returning.Arrival.Airport,
                    ArrivalCity = flightItinerary.Returning.ArrivalName,
                    AirlineName = flightItinerary.Returning.Airline,
                    ArrivalTime = GetFormattedDateTime(flightItinerary.Returning.Arrival.Date, flightItinerary.Returning.Arrival.Time),
                    ArrivalTimeZone = 0,
                    DepartAirportCode = flightItinerary.Returning.DepartureName, 
                    //DepartAirportCode = flightItinerary.Returning.Departure.Airport,
                    DepartCity = flightItinerary.Returning.DepartureName,
                    DepartureTime = GetFormattedDateTime(flightItinerary.Returning.Departure.Date, flightItinerary.Returning.Departure.Time),
                    DepartTimeZone = 0,

                    FlightNumber = flightItinerary.Returning.FlightNumber,
                    TicketClass = detailFareFamilyReturn.FamilyFareName.ToUpper(),

                });
            }

            if (detailStart != null && detailStart.Stops > 0)
            {
                var flightSegments = detailStart.FlightSegments;
                if (flightSegments.Any())
                {
                    flightSegments.RemoveAt(flightSegments.Count - 1);
                }

                foreach (var flightSegment in flightSegments)
                {
                    var fs = flightSegment.GeneralInfo;
                    flightReservationSegmentIntermediatePoint.Add(new FlightReservationSegmentIntermediatePoint
                    {
                        SegmentId = 1,
                        LocationAirportCode = fs.Destination,
                        ArrivalCity = fs.Destination,
                        ArrivalTime = GetFormattedDateTime(DateTime.Parse(fs.ArrivalDate).ToString("yyyy-MM-dd"), fs.ArrivalTime),
                        DepartureTime = GetFormattedDateTime(DateTime.Parse(fs.DepartureDate).ToString("yyyy-MM-dd"), fs.DepartureTime),
                        FlightNumber = fs.FlightNumber,
                        TicketClass = flightItinerary.FamilyFareCode
                    });
                }
            }

            if (bookingRequest.Quote.Rate.IsRoundtrip && detailReturn != null && detailReturn.Stops > 0)
            {
                var flightSegments = detailReturn.FlightSegments;
                if (flightSegments.Any())
                {
                    flightSegments.RemoveAt(flightSegments.Count - 1);
                }

                foreach (var flightSegment in flightSegments)
                {
                    var fs = flightSegment.GeneralInfo;
                    flightReservationSegmentIntermediatePoint.Add(new FlightReservationSegmentIntermediatePoint
                    {
                        SegmentId = 2,
                        LocationAirportCode = fs.Destination,
                        ArrivalCity = fs.Destination,
                        ArrivalTime = GetFormattedDateTime(DateTime.Parse(fs.ArrivalDate).ToString("yyyy-MM-dd"), fs.ArrivalTime),
                        DepartureTime = GetFormattedDateTime(DateTime.Parse(fs.DepartureDate).ToString("yyyy-MM-dd"), fs.DepartureTime),
                        FlightNumber = fs.FlightNumber,
                        TicketClass = flightItinerary.FamilyFareCode.ToUpper()
                    });
                }
            }


            foreach (var passengerRoom in customerInfo.PassengersBooking)
            {
                foreach (var passenger in passengerRoom.Passengers)
                {
                    airPassengers.Add(new AirPassengers
                    {
                        Designation = passenger.Prefix,
                        FistName = passenger.Firstname,
                        LastName = passenger.Lastname,
                    });
                }

            }


            var Breakdown = bookingQuote.Rate.Breakdown;
            double taxBreakdown = 0;
            double totalBreakdown = 0;
            double chargeServiceBreakdown = 0;
            var listTypesPassenger = new List<int> { 0,1,4 }; // adulto, menor, infante
            foreach (var item in Breakdown)
            {
                if (listTypesPassenger.Contains(item.Type)) {
                    totalBreakdown += item.Amount;
                }
                
                // impuesto
                if (item.Type == 5)
                {
                    taxBreakdown += item.Amount;
                }
                //cargo por servicio
                /*if (item.Type == 3)
                {
                    chargeServiceBreakdown += item.Amount;
                }*/
            }

            //En dado caso de cambio de tarifa por parte back de vuelos, se vera afectado el monto del vuelo para cuadrar el desglose
            totalBreakdown += changeTotal;
            return
               new Service
               {
                   Type = "flight",
                   TotalAmount = ConvertToDecimal(totalBreakdown),
                   TaxAmount = ConvertToDecimal(taxBreakdown),
                   ServiceCharge = ConvertToDecimal(chargeServiceBreakdown),
                   HasTaxesAndOtherCharges = bookingQuote.Rate.HasTaxes,
                   HasReturnFlights = bookingRequest.Quote.Rate.IsRoundtrip,
                   FlightReservationSegment = flightReservationSegment,
                   FlightReservationSegmentIntermediatePoint = flightReservationSegmentIntermediatePoint,
                   AirPassengers = airPassengers,
                   PNR = itemLocatorDetail.RecordLocator,
                   FlightDescription = GetFlightDescription(bookingRequest.Quote.Rate.IsRoundtrip, bookingQuote, airPassengers.Count),
                   FlightRateDescription = GetFlightRateDescription(bookingRequest.Quote.Rate.IsRoundtrip, bookingQuote, airPassengers.Count),
                   FlightCarryOnLuggage = (hasUpsell) ? GetDescriptionByName(detailFareFamilyStart.FamilyFareContent, 3) : "",
                   FlightCheckedLuggage = (hasUpsell) ? GetDescriptionByName(detailFareFamilyStart.FamilyFareContent, 1) : "",
                   FlightExtraInfo = (hasUpsell) ? GetDescriptionByName(detailFareFamilyStart.FamilyFareContent, 4) : "",
                   FlightExtraLuggage = (hasUpsell) ? GetDescriptionByName(detailFareFamilyStart.FamilyFareContent, 5) : "",
                   ShowLuggageInfo = false
               };

        }


        public static string GetFormattedDateTime(string fecha, string hora)
        {
            string fechaHoraString = fecha + "T" + hora + ":00.000Z";
            return fechaHoraString;
        }

        public static string GetFlightDescription(bool IsRoundtrip, QuoteApiResponse bookingQuote, int totalPaxes)
        {
            var flightType = IsRoundtrip ? "ida y regreso" : "ida";
            var flightPeople = totalPaxes == 1 ? "persona" : "personas";
            var text = $"Vuelo {flightType} {bookingQuote.ExtraInfoFlight.StartingFrom} - {bookingQuote.ExtraInfoFlight.ReturningFrom} para {totalPaxes} {flightPeople}";
            return text;
        }

        public static string GetFlightRateDescription(bool IsRoundtrip, QuoteApiResponse bookingQuote, int totalPaxes)
        {
            var flightType = IsRoundtrip ? "ida y regreso" : "ida";
            var flightPeople = totalPaxes == 1 ? "persona" : "personas";
            var text = $"Vuelo {flightType} {bookingQuote.ExtraInfoFlight.StartingFrom} - {bookingQuote.ExtraInfoFlight.ReturningFrom} para {totalPaxes} {flightPeople}";
            return text;
        }

        public static string GetDescriptionByName(List<FareContent> content, int category)
        {
            FareContent item = content.FirstOrDefault(c => c.Category == category);
            return item?.Description ?? "";
        }


        private static decimal ConvertToDecimal(double amount)
        {
            return decimal.Round((decimal)amount, 0, MidpointRounding.AwayFromZero);
        }
    }
}
