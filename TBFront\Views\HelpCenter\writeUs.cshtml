﻿@using TBFront.Helpers
@using Microsoft.Extensions.Options
@using TBFront.Models.Request;
@using TBFront.Options;
@using TBFront.Types;
@using TBFront.Models.Meta.Metatags
@inject IOptions<SettingsOptions> settingOptions
@inject IOptions<SiteOptions> siteOptions
@inject IOptions<CurrencyOptions> currencyOptions

@inject ViewHelper viewHelper
@inject ViewHelper _
@inject StaticHelper staticHelper
@{
    var resolution = _.GetImageResolution();
    var isMobile = resolution.Device == DeviceType.Mobile;
    var isRobot = _.IsRobot();
    var phones = siteOptions.Value.Phones;
    var citysUtf8 = viewHelper.GetSpecialCitiesWords();
    var metaTag = ViewData["MetaTag"] as MetaTag;
    var culture = ViewData["CultureData"] as Culture;

    ViewData["Page"] = "write-us";
    ViewData["IsRobot"] = isRobot;
    ViewData["isMobile"] = isMobile;
    ViewData["Resolution"] = resolution;

}

@await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.Code}/_Header.cshtml", new ViewDataDictionary(ViewData) { { "navs", true }, { "login", isRobot } })


<div class="container-fluid h-groups py-5 mb-5">
    <div class="container">
        <h1 class="text-white font-50 f-p-semibold mb-0">@_.Localizer("writeUsTitle")</h1>
    </div>
</div>

<div class="container c-groups pb-3">
    <div class="row">
        <div class="col-12 col-md-6 mb-3">
            <contact-form></contact-form>
        </div>
        <div class="col-12 col-md-6 mb-3">
            <div class="c-form p-4 mb-3 border rounded">
                <h3 class="font-28 f-p-semibold">@_.Localizer("writeUsMessageTitle")</h3>
                <p>@_.Localizer("writeUsMessageSubtitle")</p>
                <div class="col-12">
                    <div class="c-social mt-4">
                        <a href="http://wa.me/************" target="_blank" class="d-flex my-3 link-chats">
                            <span class="icon icon-whatsapp d-block p-1 ps-3 mb-1"></span><p class="m-0 a-link-1">@_.Localizer("sendMessageBy", "Whatsapp")</p>
                        </a>
                        <a href="https://m.me/128565927176678/" target="_blank" class="d-flex my-2">
                            <span class="icon icon-messenger d-block p-1 ps-3 mb-1"></span><p class="m-0 a-link-1">@_.Localizer("sendMessageBy", "Messenger")</p>
                        </a>
                    </div>
                </div>
            </div>
            <div class="c-form p-4 border rounded">
                <h3 class="font-28 f-p-semibold">@_.Localizer("talkAsessorTitle")</h3>
                <p>@_.Localizer("talkAsessorSubtitle")</p>

                <h4>Marcanos desde:</h4>
                <div class="contact-list d-flex flex-column flex-xl-row flex-wrap justify-content-between gap-4">
                    @foreach (var phone in phones)
                    {
                        <div class="contact-list-item py-1 border-bottom flex-grow-0">
                            <p class="mb-0 d-flex justify-content-between">
                                <span class="fw-bold">@(citysUtf8.ContainsKey(phone.City) ? citysUtf8[phone.City] : phone.City): </span> <a href="tel:@(phone.Phone.Replace(" ", ""))">
                                    @(phone.Phone)
                                </a>
                            </p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<div>
    @await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.Code}/_Footer.cshtml", new ViewDataDictionary(ViewData) { { "newsletter", false } })
</div>


@section Meta {
    @await Html.PartialAsync("_MetaMain", new ViewDataDictionary(ViewData) { { "Meta", ViewData["MetaTag"] } })
}

@section Preload {
    <link rel="preconnect" href="@settingOptions.Value.CloudCdn">
    <link rel="preload" href="@staticHelper.GetVersion($"/{settingOptions.Value.SiteName}/css/write-us.css", settingOptions.Value.Assets)" as="style" />
}

@section Css {
    <link type="text/css" rel="stylesheet" href="@staticHelper.GetVersion($"/{settingOptions.Value.SiteName}/css/write-us.css", settingOptions.Value.Assets)">
}

@section ScriptsPriority {
}

@section Scripts {
    <script>
        window.__pt.cultureData = @Json.Serialize((ViewData["CultureData"]));
        window.__pt.exchange = @Json.Serialize((ViewData["Exchange"]));
        window.__pt.alternates = @Json.Serialize((ViewData["Alternates"]));
        window.__pt.currency = @Json.Serialize((ViewData["CurrencyData"]));
        window.__pt.currencies = @Json.Serialize((currencyOptions.Value.Currencies));
    </script>

    <script src="@staticHelper.GetVersion("/js/vue/app.js", settingOptions.Value.Assets)"></script>
}