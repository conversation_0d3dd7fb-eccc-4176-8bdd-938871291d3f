.alert-custom {
    display: table;
    width: 100%;
    padding: 1rem;
    border-radius: 4px;
    border: 1px solid #ccc;
    border-left: 0.5rem solid #ccc;
    margin-bottom: 1rem;
    background: #fff;

    &.alert-content {
        display: table-cell;
        vertical-align: top;
    }

    &.alert-info {
        border-left: 0.5rem solid #4FC3F4;
    }

    .font-icons {
        color: #4FC3F4;
    }
}

.on_error_login {
    border-radius: 5px;
    font-size: 13px;
    padding: 5px 2px;
    color: #F62F0C;
}

.input-group .font-icons{
    font-size: 1.25rem;
    color: $color-primary;
}

.icon-google, .icon-fb {
    float: left;
    margin-right: 5px;
}

#modal-login-form {

    .icon-image {
        display: inline-block;
        background-repeat: no-repeat;
        background-size: 100%;
        width: 100%;
        min-height: 250px;
    }

    .icon-image-gift {
        background-image: url(https://s3.amazonaws.com/prod-single-spa.pricetravel.com.mx/assets/1.7.17/img/icon-image-gift-1.gif);
    }

    .icon-image-congrat {
        background-image: url(https://s3.amazonaws.com/prod-single-spa.pricetravel.com.mx/assets/1.7.17/img/icon-image-congrat.gif);
    }
}

