﻿.hotel-rooms-table {

    table {
        width: 100%;
        table-layout: fixed;
        border-collapse: collapse
    }

    table tr td {
        vertical-align: top
    }

    .room-mealplan small,
    pth-room-amenities small {
        display: none
    }

    .badge-button:hover,
    .badge-button:focus {
        text-decoration: underline
    }

    .room-table-unavailable .alert {
        display: block
    }

    .room-table-unavailable .alert .alert__icon,
    .room-table-unavailable .alert .alert-icon {
        display: none
    }

    .mealplan-list-item {
        width: 90%
    }

    .room-table {
        border: 1px solid #e0e0e0;
        background-color: #fff;
        margin-bottom: calc(.8875rem + .15vw)
    }

    @media (min-width: 1200px) {
        .room-table {
            margin-bottom: 1rem;
        }
    }

    .room-table-group {
        margin-bottom: 1em;
    }

    .room-table-row,
    .room-detail-row {
        background: #fff;
        margin-right: 10px;
        padding: 5px
    }

    pth-room-detail {
        display: block
    }

    .room-detail-row {
        flex: none
    }

    .room-table-aside {
        border-bottom: 1px dashed #e0e0e0
    }

    .room-table-row-mealPlan {
        border-bottom: 1px dashed #e0e0e0
    }

    .room-table-row-mealPlan:last-child {
        border-bottom: none
    }

    .room-table-header {
        display: none
    }

    .room-table-main {
        display: inline-block;
        height: auto
    }

    .room-table-mealplan {
        display: inline-block
    }

    .disccount-icon {
        display: inline-block;
        background-color: #489A00;
        padding: 0.25rem 0.25rem 0.25rem 0;
        position: relative;
        margin-left: 0.75rem;
        height: 20px;
        float: right;
        max-width: 30px;
    }

    .disccount-icon:before {
        position: absolute;
        content: "";
        display: inline-block;
        right: 100%;
        top: 0;
        bottom: 0;
        width: 0;
        height: 0;
        border-top: 10px solid transparent;
        border-right: 10px solid #489A00;
        border-bottom: 10px solid transparent
    }

    .disccount-icon .disccount-value {
        color: #fff;
        font-weight: 500;
        margin-bottom: 0;
        line-height: 1;
        font-size: 12px
    }

    .room-table-aside {
        width: 100%;
        display: block
    }

    .room-table-features {
        width: 100%;
        display: flex;
        flex-wrap: wrap
    }

    .room-table-features .room-view,
    .room-table-features .room-beds,
    .room-table-features .room-capacity,
    .room-table-features pth-room-amenities {
        flex-grow: 1;
        min-width: 40%;
        max-width: 47%;
        margin-bottom: 5px
    }

    .room-table-price,
    .room-table-actions {
        display: inline-block;
        float: left;
        vertical-align: bottom
    }

    .room-table-message {
        display: block;
        width: 100%;
        vertical-align: middle;
    }

    .room-table-price {
        width: 100%
    }

    .room-table-actions,
    .room-table-mealplan {
        width: 100%
    }

    .room-table-unavailable {
        width: 100%;
        display: block;
        max-height: 150px
    }

    .room-table-actions {
        float: left
    }

    .room-table-row-mealPlan {
        display: inline-block;
        width: 100%
    }

    .room-beds,
    .room-capacity,
    .room-view,
    .room-mealplan,
    .hotel-room-td-price,
    pth-room-amenities,
    pth-sales-advisory,
    pth-room-rate {
        margin-bottom: 0px
    }

    .mealplan-list {
        display: flex;
        width: 100%;
        overflow-x: auto;
        margin-bottom: 0px;
        padding: 10px
    }

    .mealplan-list-item {
        border: 1px solid #e0e0e0
    }

    @media (min-width: 992px) {
        .mealplan-list {
            display: block;
            width: 100%;
            overflow-x: none
        }

        .mealplan-list-item {
            width: 100%;
            height: auto;
            border-top: none;
            border-right: none;
            border-left: none;
            border-bottom: 1px solid #e0e0e0
        }

        .mealplan-list-item:last-child {
            border-bottom: none
        }
    }

    .room-table-information,
    .room-table-features,
    .room-table-price,
    .room-table-actions {
        padding: calc(.8875rem + .15vw)
    }

    @media (min-width: 1200px) {

        .room-table-information,
        .room-table-features,
        .room-table-price,
        .room-table-actions {
            padding: 1rem
        }
    }

.room-table-message {
    display: table-cell
}

.room-table-features,
.room-table-header-features {
    width: 25%
}

.room-table-unavailable {
    width: 100%;
    height: auto
}

.room-table-aside,
.room-table-header-aside {
    width: 30%
}

.room-table-main,
.room-table-header-main {
    width: 50%
}

.room-table-mealplan,
.room-table-actions {
    width: 50%
}

.room-table-actions {
    float: inherit
}

pth-room-detail {
    display: block
}

.room-table-main {
    display: inline-flex;
    padding: 0px;
    height: auto
}

.room-beds,
.room-capacity,
.room-view,
.room-mealplan,
.hotel-room-td-price,
pth-room-amenities,
pth-sales-advisory,
pth-room-rate {
    margin-bottom: 20px
}

.pth-mealplan-description {
    float: left;
    width: 50%
}

}

@media (min-width: 576px) {
    .badge-button {
        max-width: 170px
    }

    .room-table {
        margin-bottom: 0
    }

    .room-table-features,
    .room-table-price,
    .room-table-actions,
    .room-table-unavailable,
    .room-table-mealplan {
        display: table-cell;
        float: none;
        vertical-align: top
    }

    .room-table-features,
    .room-table-price,
    .room-table-unavailable {
        border-right: 1px solid #e0e0e0
    }

    .room-table-features,
    .room-table-price,
    .room-table-actions,
    .room-table-unavailable {
        width: 33.33%
    }

    .mealplan-list {
        display: block;
        width: 100%;
        overflow-x: auto;
        margin-bottom: 10px;
        padding: 10px
    }
}

@media (min-width: 768px) {
    .room-table-features .room-beds {
        max-width: 100%
    }

    .room-table-header {
        display: table;
        background: #E5E5E5;
        border-top: 1px solid #e0e0e0;
        border-left: 1px solid #e0e0e0;
        border-right: 1px solid #e0e0e0
    }

    .room-table-header-aside tr td,
    .room-table-header-main tr td {
        padding: .5rem;
        border-right: 1px solid #e0e0e0
    }

    .room-table-header-aside tr td:last-child,
    .room-table-header-main tr td:last-child {
        border-right: none
    }

    .room-table-header-main tr td:first-child {
        width: 50%
    }

    .room-table-header-main tr td:last-child {
        width: 50%;
        text-align: left
    }

    .room-table-aside,
    .room-table-main,
    .room-table-features,
    .room-table-header-aside,
    .room-table-header-main,
    .room-table-header-features {
        display: table-cell
    }

    .room-table-aside,
    .room-table-header-aside {
        width: 30%;
        border-right: 1px solid #e0e0e0
    }

    .room-table-main,
    .room-table-header-main {
        width: 56%
    }

    .room-table-features,
    .room-table-header-features {
        width: 30%
    }

    .room-table-features,
    .room-table-price,
    .room-table-actions,
    .room-table-unavailable {
        height: auto
    }
}

ul.list-amenities {
    display: none
}

ul.room-capacity-guest {
    display: flex
}

.room-capacity-guest i {
    color: #5C469C
}

.room-promo i {
    color: #489A00
}

.list-amenities li {
    width: 46px;
    height: 45px;
    border: 1px solid #BEB8B8;
    box-sizing: border-box;
    border-radius: 4px;
    display: inline-block;
    margin-bottom: 5px;
    text-align: center;
    padding: 5px
}

.hotel-room-td-price {
    text-align: left
}

.room-promo li {
    margin-bottom: 0px
}

.room-promo span {
    display: inline-grid
}

pth-sales-advisory,
pth-room-amenities {
    float: left
}

pth-room-amenities {
    min-width: 100% !important;
    margin-bottom: 0
}

.form-check__label small {
    display: block
}

.hotel-room-td-message {
    text-align: center;
    line-height: 1em;
    padding: 1rem
}

@media (min-width: 768px) {
    .mealplan-list-item {
        width: 290px;
        max-height: 480px;
        border-top: none;
        border-right: none;
        border-left: none;
        border-bottom: 1px solid #e0e0e0;
        margin-bottom: 10px
    }

    .mealplan-list-item {
        width: 100%;
        margin-right: 0
    }
}

@media (min-width: 992px) {

    .mealplan-list-item {
        width: 100%
    }

    .room-promo i {
        width: 40px;
        height: 40px
    }

    .room-mealplan small {
        display: block
    }
}

.room-promo li {
    display: inline-flex;
    width: 100%
}

.product-price {
    margin-bottom: 1rem
}

.room-deal button {
    margin-left: 0.5rem
}

.room-table-header-features {
    padding: 0.5rem
}

.room-deal .btn-link {
    height: 20px
}

.hotel-room-td-action {
    text-align: center
}

.hotel-room-td-action .btn-primary {
    width: 100%
}

pth-room-action span.msi-note {
    font-size: 14px;
    color: #fd7e14;
    font-weight: 500;
    display: inline
}

pth-room-action .ptw-icon::before {
    font-family: "md-icon" !important;
    font-style: normal !important;
    font-weight: normal !important;
    font-variant: normal !important;
    text-transform: none !important
}

.room-payform-select {
    text-align: left;
    display: inline-flex;
    flex-wrap: wrap;
    margin-bottom: 10px
}

.room-payform-select .button--link,
.room-payform-select .btn-link {
    font-size: .85rem;
    margin-left: 1.75rem
}

.room-payform-select .form-check_label {
    line-height: initial;
    font-size: 14px
}

.room-payform-select .form-check {
    width: 100%;
    margin-right: 10px;
    margin-bottom: 5px
}

.room-payform-select__title {
    display: block;
    margin-bottom: .5rem
}

@media (min-width: 768px) {
    .hotel-room-td-action .btn-primary {
        width: 100%
    }
}

.room-photo-information {
    display: block;
    width: 100%
}

.room-photo-information::after {
    display: block;
    clear: both;
    content: ""
}

.room-photo-information .room-photo-information-right,
.room-photo-information .room-photo-information-left {
    display: inline-block;
    float: left
}

.room-photo-information .room-photo-information-right {
    width: 60%;
    font-size: .85rem;
    padding-left: .25rem;
    clear: both
}

.room-photo-information .room-photo-information-left {
    width: 40%
}

.room-title {
    font-size: calc(.9625rem + 1.05vw);
    margin-bottom: .5rem
}

@media (min-width: 1200px) {
    .room-title {
        font-size: 1.75rem
    }
}

.list-dataBold {
    padding-left: 0;
    list-style: none;
    margin-bottom: 0
}

.list-dataBold:first-child {
    max-resolution: 10px;
}

.list-dataBold-label {
    font-weight: 500
}

.room-kids-free strong {
    color: #489A00
}

@media (min-width: 768px) {

    .room-photo-information .room-photo-information-right,
    .room-photo-information .room-photo-information-left {
        width: auto
    }

    .room-photo-information .room-photo-information-right {
        padding-left: .5rem
    }
}

@media (min-width: 992px) {
    .room-photo-information .room-photo-information-right {
        padding-left: 0;
        padding-top: .5rem
    }

    .room-photo-information .room-photo-information-right,
    .room-photo-information .room-photo-information-left {
        width: 100%
    }

    .room-title {
        font-size: calc(.9125rem + .45vw)
    }
}

@media (min-width: 992px) and (min-width: 1200px) {
    .room-title {
        font-size: 1.25rem
    }
}

.hotel-room-td-message {
    text-align: left;
    line-height: 1em
}

.hotel-room-td-message h5 {
    color: #c0392b;
    line-height: 1.25;
    margin-bottom: 5px
}

.hotel-room-spinner {
    text-align: center;
    padding: 1.5em
}

.hotel-room-td-price {
    text-align: left
}

.product-price {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap
}

.product-price .product-price-small {
    width: 100%
}

@media (min-width: 992px) {
    .hotel-room-td-price {
        text-align: left
    }
}

.product-price {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    flex-direction: row
}

.product-price-final {
    font-size: calc(.9375rem + .75vw);
    line-height: 1;
    margin-bottom: 0
}

@media (min-width: 1200px) {
    .product-price-final {
        font-size: 1.5rem
    }
}

.product-payplan {
    color: #fd7e14;
    font-weight: 500;
    margin-top: .5rem;
    margin-bottom: .5rem
}

.itemTagDiscount--red {
    color: #489A00;
    font-weight: 500
}

.note-reservation {
    display: block;
    margin-top: 0.5rem
}

#map {
    width: 100%;
    height: 300px
}

.hotel-map {
    position: relative
}

.l-page-container wc-hotel-gallery > div > .page-container,
.l-page-container wc-hotel-gallery > div > .l-page-container,
.l-page-container wc-hotel-gallery > div > .l-page-fullwidth,
.l-page-container wc-hotel-gallery > div > .collapse-information {
    padding-left: 0;
    padding-right: 0
}

.booker-tab,
.nav {
    margin-bottom: 0
}

.zeroInterest-title sup,
.banner-link,
.table-title-left,
.booker-navigation,
.booker-desktop {
    display: none
}

.booker-navigation {
    display: none !important
}

.pt-payment-grid-item {
    padding-top: calc(.8875rem + .15vw);
    padding-bottom: calc(.8875rem + .15vw);
    border-bottom: 1px solid #ccc
}

@media (min-width: 1200px) {
    .pt-payment-grid-item {
        padding-top: 1rem
    }
}

@media (min-width: 1200px) {
    .pt-payment-grid-item {
        padding-bottom: 1rem
    }
}

.pt-payment-grid-item:last-child {
    border-bottom: none
}

.pt-payment-block {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-justify-content: space-between;
    -moz-justify-content: space-between;
    -ms-justify-content: space-between;
    justify-content: space-between;
    -ms-flex-pack: space-between;
    -webkit-align-items: center;
    -moz-align-items: center;
    -ms-align-items: center;
    -ms-flex-align: center;
    align-items: center
}

.pt-payment-block-name h3 {
    display: none
}

.pt-payment-title h4,
.pt-payment-title h5 {
    font-size: calc(.9125rem + .45vw);
    line-height: 1.25
}

@media (min-width: 1200px) {

    .pt-payment-title h4,
    .pt-payment-title h5 {
        font-size: 1.25rem
    }
}

.pt-payment-block-data {
    line-height: 1.25;
    text-align: right
}

.pt-payment-block-dataTitle {
    display: block;
    font-weight: 500;
    font-size: calc(.8875rem + .15vw)
}

@media (min-width: 1200px) {
    .pt-payment-block-dataTitle {
        font-size: 1rem
    }
}

.pt-payment-block-logo {
    line-height: 1
}

.pt-payment-row {
    margin-bottom: calc(.9875rem + 1.35vw)
}

@media (min-width: 1200px) {
    .pt-payment-row {
        margin-bottom: 2rem
    }
}

.booker-mobile,
.booker-desktop {
    background-color: #fff;
    border-bottom: 1px solid #ccc;
}


.booker-collapsed-title,
.booker-collapsed-content p {
    margin-bottom: 0;
}

.booker-collapsed-title {
    white-space: normal;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    width: 95%;
}

.booker-collapsed-content {
    line-height: 1;
}

.hotel-heading {
    -webkit-align-items: flex-start;
    -moz-align-items: flex-start;
    -ms-align-items: flex-start;
    -ms-flex-align: flex-start;
    align-items: flex-start;
    -webkit-justify-content: space-between;
    -moz-justify-content: space-between;
    -ms-justify-content: space-between;
    justify-content: space-between;
    -ms-flex-pack: space-between
}

@media (min-width: 992px) {
    .hotel-heading {
        display: -webkit-box;
        display: -moz-box;
        display: -ms-flexbox;
        display: -webkit-flex;
        display: flex
    }
}

@media (min-width: 768px) {
    .hotel-heading {
        display: -webkit-box;
        display: -moz-box;
        display: -ms-flexbox;
        display: -webkit-flex;
        display: flex
    }
}

.hotel-heading-price {
    text-align: right;
    padding-right: .5rem
}

.hotel-heading-price * {
    margin-bottom: 0;
    white-space: nowrap;
    line-height: 1.25
}

.hotel-heading-price *:first-child,
.hotel-heading-price *:last-child {
    font-size: .85rem
}

.hotel-heading-price *:last-child {
    white-space: inherit
}

.price-message {
    border: solid 1px #E5E5E5;
    border-radius: 8px;
    padding: 0.5rem
}

.price-message .hotel-heading-price-current {
    font-size: 1.5rem !important;
    color: #EA0074
}

.card--center {
    text-align: center
}

.card .card-body ul {
    margin-top: calc(.8875rem + .15vw)
}

@media (min-width: 1200px) {
    .card .card-body ul {
        margin-top: 1rem
    }
}


.hotel-rooms-table-title {
    text-align: center
}


@media (min-width: 768px) {
    .hotel-rooms-table-title {
        text-align: left;
        display: -webkit-box;
        display: -moz-box;
        display: -ms-flexbox;
        display: -webkit-flex;
        display: flex;
        -webkit-align-items: center;
        -moz-align-items: center;
        -ms-align-items: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-justify-content: space-between;
        -moz-justify-content: space-between;
        -ms-justify-content: space-between;
        justify-content: space-between;
        -ms-flex-pack: space-between
    }

    .table-title-left {
        white-space: normal;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        width: 80%
    }

    .list-features {
        display: block
    }

    .list-features::after {
        display: block;
        clear: both;
        content: ""
    }

    .list-features li {
        float: left;
        width: 50%;
        padding-right: calc(.8875rem + .15vw)
    }
}

@media (min-width: 768px) and (min-width: 1200px) {
    .list-features li {
        padding-right: 1rem
    }
}

@media (min-width: 768px) {
    .media-columns {
        display: -webkit-box;
        display: -moz-box;
        display: -ms-flexbox;
        display: -webkit-flex;
        display: flex;
        -webkit-align-items: flex-start;
        -moz-align-items: flex-start;
        -ms-align-items: flex-start;
        -ms-flex-align: flex-start;
        align-items: flex-start
    }

    .media-columns .media,
    .media-columns .message-comunication {
        flex: 1 1 0
    }
}

@media (min-width: 992px) {

    .hotel-heading-price *:first-child,
    .hotel-heading-price *:last-child {
        font-size: calc(.8875rem + .15vw)
    }
}

@media (min-width: 992px) and (min-width: 1200px) {

    .hotel-heading-price *:first-child,
    .hotel-heading-price *:last-child {
        font-size: 1rem
    }
}

@media (min-width: 992px) {
    .list-features li {
        width: 33.33%
    }
}

.room-photo-information .room-photo-information-left {
    width: 100%
}

@media (min-width: 768px) {

    .room-table-features,
    .room-table-price,
    .room-table-actions {
        height: 339px
    }
}

.form-check__label small {
    display: block
}

.hotel-room-td-message {
    text-align: center;
    line-height: 1em;
    padding: 1rem
}

@media ($phone) {
    .list-unstyled p{
        margin-bottom: 0;
    }
    .list-unstyled li{
        width: 50%;
        display: inline-block;
    }
}