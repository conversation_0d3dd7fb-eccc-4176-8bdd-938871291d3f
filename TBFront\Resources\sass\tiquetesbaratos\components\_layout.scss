﻿@import '../_variables.scss';

body *{
	margin: 0;
	font-family: 'Roboto', sans-serif;
	font-weight: 400;
	color: #333132;
	font-family: Roboto-Regular;

	.d-flex-important {
		display: flex !important;
	}
}

h1, h2, h3, h4, h5, h6 {
	font-family: Poppins-semibold;
}

.text-gray-muted {
	color: $color-gray-muted;
}
.text-gray-dark {
	color: $color-gray-dark;
}
.text-yellow {
	color: $yellow-600;
}
.text-secondary{
	color: $color-primary !important;
}
.svg_line {
	background-image: url('https://static.cdnpth.com/img/hr-brand.svg');
	height: 6px;
	background-size: cover;
	background-position: center;
	width: 100%;

	&.svg_line_footer {
		height: 3px;
	}
}
.font-poppins-semibold {
	font-family: Poppins-semibold;
}
header {
	.hr-brand {
		display: block;
		background-image: url('https://stage-static.cdnpth.com/img/hr-brand.svg');
		background-size: 100% 100%;
		background-repeat: no-repeat;
		background-position: bottom;
		width: 100%;
		height: 6px;

		&.hr-brand--footer {
			height: 2px;
		}
	}

	.header-page {
		padding: 25px 0;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.brand-logo {
		width: 202px;
	}

	header + svg {
		width: 100%;
		height: 6px;
		display: block;
	}

	.navbar {
		padding-top: 16px;
		padding-bottom: 16px;
	}

	.avatar-user {
		width: 30px;
		height: 30px;
		border-radius: 50%;
		background-color: $color-primary;
		color: white;
		overflow: hidden;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 22px;
		margin: 0 5px;
		background-position: center center;
		background-size: cover;
		background-repeat: no-repeat;
		background-color: $color-secundary-hover;
	}

	.navbar-light .navbar-nav .nav-link.active {
		background-color: $color-primary;
		border-radius: 5px;
		color: $white !important;
	}

	.nav-login {
		.nav-link {
			font-weight: bold;
		}

		.dropdown-item {
			transition: 1s;

			&:hover {
				transition: 1s;
				color: $color-primary-hover;
				background-color: $color-primary-light;
			}
		}
	}

	.color-pink, .text-pink {
		color: $color-secundary;
	}

	.text-shadow {
		text-shadow: 1px 1px 2px black;
	}

	@media($phone) {
		.footer-group-heading {
			border-bottom: 1px solid #c2c2c2;
			padding: 10px 5px 10px 5px;
			cursor: pointer;

			&:hover {
				background-color: $color-primary-light;
			}
		}

		.navbar-tb {
			display: none;
		}

		footer .icons-expand-more {
			position: absolute;
			right: 18px;
		}

		.phonenumber {
			font-size: 15px;
			padding: 25px 9px;
		}
	}

	@media (min-width: 768px) and (max-width: 1199px) {

		.navbar-tb__menu-contact {
			display: none;
		}
	}

	@media($desktop-lg) {

		.navbar-tb__menu-contact-m {
			display: none;
		}
	}

	@media($desktop) {
		.navbar-expand-lg .navbar-nav .nav-link {
			padding-left: 1rem;
			padding-right: 1rem;
		}

		footer .icons-expand-more {
			display: none;
		}
	}

	.coupon-link {
		font-size: 14px;
		color: #003180;
		text-decoration: underline;
	}

	.coupon-color {
		color: #0026E5;
	}

	.coupon-title-color {
		color: #003180;
	}

	#form-login-img {

		.icon-image {
			display: inline-block;
			background-repeat: no-repeat;
			background-size: 100%;
			width: 100%;
			min-height: 250px;
		}

		.icon-image-gift {
			background-image: url(https://s3.amazonaws.com/prod-single-spa.pricetravel.com.mx/assets/1.7.17/img/icon-image-gift-1.gif);
		}

		.icon-image-congrat {
			background-image: url(https://s3.amazonaws.com/prod-single-spa.pricetravel.com.mx/assets/1.7.17/img/icon-image-congrat.gif);
		}
	}

	img.logoPT {
		width: 250px;
	}

	@media ($phone) {
		img.logoPT {
			width: 160px;
		}

		.phonenumber .alert .container {
			padding: 0;
		}

		.phonenumber {
			padding: 12px !important;
		}
	}

	@media #{$tablet} {
		img.logoPT {
			width: 160px;
		}
	}

	.font-icons {
		font-size: 24px;
	}

	.social-icons {
		position: relative;

		.font-icons {
			font-size: 24px;
		}
	}

	.social-icons a:hover {
		text-decoration: none;
	}

	a span.icons-messenger:hover {
		background: url(/assets-tb/img/icon_social/icons-messenger-hover.svg);
		background-repeat: no-repeat;
	}

	.social-icons span:nth-child(1) {
		font-size: 22px;
	}
}
footer {
	.social-icons {
		position: relative;

		.font-icons {
			font-size: 24px;
		}

		a {
			.icons-messenger {
				&:hover {
					background: url(/assets-tb/img/icon_social/icons-messenger-hover.svg);
					background-repeat: no-repeat;
				}
			}

			&:hover {
				text-decoration: none;
			}
		}

		span:nth-child(1) {
			font-size: 22px;
		}
	}
}
.text-success{
	color: var(--text-semantic-text-success)!important;
}
.disccount-icon {
	background-color: var(--text-semantic-text-success);
	color: $white;
	display: flex !important;
	justify-content: center;
	align-items: center;
	font-size: 12px;
	position: relative;
	padding: 0px 4px;
	min-width: 35px;

	&.disccount-icon-right {
		border-radius: 4px 0px 0px 4px;
		margin-right: 18px !important;

		&::after {
			height: 100%;
			position: absolute;
			content: "";
			left: 100%;
			border-top: 10px solid transparent;
			border-left: 10px solid var(--text-semantic-text-success);
			border-bottom: 10px solid transparent;
		}
	}

	&.disccount-icon-left {
		border-radius: 0px 4px 4px 0px;
		margin-left: 15px !important;

		&::after {
			height: 100%;
			position: absolute;
			content: "";
			left: 100%;
			border-top: 10px solid transparent;
			border-right: 10px solid var(--text-semantic-text-success);
			border-bottom: 10px solid transparent;
		}
	}
}
