﻿using TBFront.Models.Common;
using TBFront.Models.Flight.Quote;

namespace TBFront.Models.Flight.Upsell
{
    public class FlightUpsellRequest
    {
        public Context Context { get; set; }
        public bool IsPackage { get; set; }
        public string Currency { get; set; }
        public bool ShowDetailAmounts { get; set; }
        public bool ShowRevenueByLeg { get; set; }
        public string TaskID { get; set; }
        public Dictionary<string, Quote.Flight> Flights { get; set; }
        public Dictionary<string, FareLeg> Fares { get; set; }
    }

}
