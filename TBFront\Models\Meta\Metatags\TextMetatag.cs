﻿using TBFront.Models.Meta.Schema;

namespace TBFront.Models.Meta.Metatags
{
    public class TextMetatag
    {
        public string? AllIncluide { get; set; }
        public string? Stars { get; set; }
        public string? MealPlan { get; set; }
        public string? Amenities { get; set; }
        public string? Interest { get; set; }
        public string? RoomView { get; set; }
        public string? Zones { get; set; }
        public string? Title { get; set; }
        public string? Description { get; set; }
        public string? Subtitle { get; set; }



        public TextMetatag()
        {
            AllIncluide = "";
            Stars = "";
            MealPlan = "";
            Amenities = "";
            Interest = "";
            RoomView = "";
            Zones = "";
            Subtitle = "";
        }
    }


}
