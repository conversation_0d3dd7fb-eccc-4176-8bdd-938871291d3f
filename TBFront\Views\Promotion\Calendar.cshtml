﻿@using TBFront.Helpers
@using Microsoft.Extensions.Options
@using TBFront.Models.Destination.Response;
@using TBFront.Models.Request;
@using TBFront.Options;
@using TBFront.Types;
@using TBFront.Models.Meta.Metatags
@inject IOptions<SettingsOptions> settingOptions
@inject IOptions<CurrencyOptions> currencyOptions

@inject ViewHelper _
@inject StaticHelper staticHelper
@{
    var resolution = _.GetImageResolution();
    var isMobile = resolution.Device == DeviceType.Mobile;
    var isRobot = _.IsRobot();
    var request = ViewData["request"] as CalendarPromotionsRequest;

    ViewData["Page"] = "promotion";

    ViewData["IsRobot"] = isRobot;
    ViewData["isMobile"] = isMobile;
    ViewData["Resolution"] = resolution;

}

@await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.Code}/_Header.cshtml", new ViewDataDictionary(ViewData) { { "navs", true }, { "login", isRobot } })
@*     <section id="imgpromo" class="c-box-promo c-box-promo-landing position-relative container-fluid pb-4 c-box-img-promo">
        <div class="container cb-in position-relative px-0">
            <div class="col-12 p-0 ml-auto text-left mb-3">
                <h1 class="font-poppins-semibold pt-3 ">
                    Vuelos de<br />
                    Bogotá a Barranquilla
                </h1>
            </div>
            <div class="col-12 col-lg-3 p-0 s-nav-md mt-0">
                <a class=" a-link py-3 py-md-3 f-p-medium font-14 ">Busca otro destino</a>
            </div>
        </div>
    </section>
 *@


<promotions-calendar></promotions-calendar>

<div>
    @await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.Code}/_Footer.cshtml", new ViewDataDictionary(ViewData) { { "newsletter", false } })
</div>


@section Meta {
    @await Html.PartialAsync("_MetaMain", new ViewDataDictionary(ViewData) { { "Meta", ViewData["MetaTag"] }})
    <meta name="robots" content="noindex,nofollow" />
    <meta name="googlebot" content="noindex,nofollow" />
}

@section Preload {
    <link rel="preconnect" href="@settingOptions.Value.CloudCdn">
    <link rel="preload" href="@staticHelper.GetVersion($"/{settingOptions.Value.SiteName}/css/promotion.css", settingOptions.Value.Assets)" as="style" />
}

@section Css {
    <link type="text/css" rel="stylesheet"
          href="@staticHelper.GetVersion($"/{settingOptions.Value.SiteName}/css/promotion.css", settingOptions.Value.Assets)">
    <style>
        body.cloak {
            display: none !important;
        }
    </style>
}

@section Scripts {
    <script>
        window.__pt = window.__pt || {};
        window.__pt.data = @Json.Serialize(request)
        window.__pt.places_info = window.__pt.places_info || {};
        window.__pt.resolution = window.__pt.resolution || @Json.Serialize(resolution);
        window.__pt.cultureData = @Json.Serialize((ViewData["CultureData"]));
        window.__pt.exchange = @Json.Serialize((ViewData["Exchange"]));
        window.__pt.alternates = @Json.Serialize((ViewData["Alternates"]));
        window.__pt.currency = @Json.Serialize((ViewData["CurrencyData"]));
        window.__pt.currencies = @Json.Serialize((currencyOptions.Value.Currencies));
    </script>

    <script src="@staticHelper.GetVersion("/js/vue/app.js", settingOptions.Value.Assets)"></script>
}