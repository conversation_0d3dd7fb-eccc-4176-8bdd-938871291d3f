@import './app', './base/_variables', './_variables.scss', 'base/_core', 'components/footer', 'components/header';

.h-groups {
    background-color: #0461b2;
}

.link-phone {
    text-decoration: none;
    color: #333132;

    &:hover {
        color: #0d98dc;
    }
}

.position-tn-2 {
    position: relative;
    top: -1px;
}

.link-chats {
    text-decoration: none;
    &:hover {
        text-decoration: none;
    }
}

.contact-list-item {
    flex: 1 1 47%;
}

.bg-blue {
    background-color: #2196f3;
}

@media (max-width: 768px) {
    .font-mobile-12 {
      font-size: 13px !important;
    }
  }