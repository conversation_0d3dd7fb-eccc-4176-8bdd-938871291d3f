﻿@using Microsoft.Extensions.Options
@using TBFront.Models.Meta.Metatags;
@using TBFront.Options;
@inject IOptions<SettingsOptions> settingOptions
@{
    var meta = ViewData["Meta"] as MetaTag;
    var culture = ViewData["CultureData"] as Culture;
}

<title>@(meta.Title)@(meta.Separator)@(meta.SiteTitle)</title>

@{
    Uri uri = new Uri(meta.Url);

    string newUrl = $"{uri.Scheme}://{uri.Host}/es-co{uri.AbsolutePath}{uri.Query}";

    <link rel="canonical" href="@(newUrl)">
}

@foreach (var item in meta.Alternate)
{
    <link rel="alternate" href="@item.Url" hreflang="@item.Culture" />
}

<meta name="description" content="@(meta.Description)">
<meta name="author" content="@(meta.Author)">
<meta property="og:locale" content="@(meta.Lang)">
<meta property="og:type" content="website">
<meta property="og:title" content="@(meta.Title)">
<meta property="og:description" content="@(meta.Description)">
<meta property="og:url" content="@(meta.Url)">
<meta property="og:image" content="@(meta.Img)">
<meta property="og:site_name" content="@(meta.Author)">
<meta property="og:image:type" content="image/jpg">
<meta property="og:type" content="website">
<meta name="dcterms.Identifier" content="@(meta.Url)">
<meta name="dcterms.Format" content="text/html">
<meta name="dcterms.Relation" content="@(meta.Author)">
<meta name="dcterms.Language" content="@(meta.Lang)">
<meta name="dcterms.Publisher" content="@(meta.Author)">
<meta name="dcterms.Type" content="text/html">
<meta name="dcterms.Coverage" content="@(meta.Root)">
<meta name="dcterms.Title" content="@(meta.Title)">
<meta name="dcterms.Description" content="@(meta.Description)">
<meta name="dcterms.Creator" content="@(meta.Author)">
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:description" content="@(meta.Description)">
<meta name="twitter:title" content="@(meta.Title)">
<meta name="twitter:site" content="@(meta.Author)">
<meta name="twitter:image" content="@(meta.Img)">
<meta name="twitter:creator" content="@(meta.Author)">



@if (settingOptions.Value.Production)
{
    <meta name="robots" content="index,follow, max-snippet:-1; max-image-preview:large" />
    <meta name="googlebot" content="index,follow" />
}
else
{
    <meta name="robots" content="noindex,nofollow, max-snippet:-1; max-image-preview:large" />
    <meta name="googlebot" content="noindex,nofollow" />
}


<script type="application/ld+json">
    @Json.Serialize(meta.SchemaMain)
</script>

<script type="application/ld+json">
    @Json.Serialize(meta.MobileApp)
</script>

