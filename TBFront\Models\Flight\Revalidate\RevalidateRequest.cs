﻿using TBFront.Models.Common;
using TBFront.Models.Flight.Quote;

namespace TBFront.Models.Flight.Revalidate
{
    public class RevalidateRequest
    {
        public Dictionary<string, Quote.Flight> Flights { get; set; }
        public Dictionary<string, Models.Flight.Quote.FareLeg> Fares { get; set; }
        public bool IsPackage { get; set; }
        public string Currency { get; set; }
        public string TaskID { get; set; }
        public bool ShowDetailAmounts { get; set; }
        public bool ShowRevenueByLeg { get; set; }
        public bool Requote { get; set; }
        public Context Context { get; set; }
        public int? NegotiatedFareId { get; set; }
        public RevalidateRequest()
        {
            Context = new Context();
        }
        
    }
}
