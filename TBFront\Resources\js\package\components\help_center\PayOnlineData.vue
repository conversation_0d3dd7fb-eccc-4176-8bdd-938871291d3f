<template>
    <div>

        <div class="row pt-4 px-checkout-desktop" v-if="payment.status == 'error-pga' || payment.status == 'error' ">
            <div class="col-12">
                <div class="col-12">
                    <h1 class="font-28 f-r-medium my-0 d-center mt-5 ">{{__("payOnline.error")}}</h1>
                </div>
            </div>

            <div class="col-12 col-md-12 mt-5 d-center">
                <a :href="goBack" class="btn btn-primary py-3">
                    {{__("payOnline.go_back")}}
                </a>
            </div>
        </div>

        <div class="row pt-4 px-checkout-desktop" v-if="payment.status == 'not-found'">
            <div class="col-12">
                <div class="col-12">
                    <h1 class="font-28 f-r-medium my-0 d-center mt-5 ">{{__("payOnline.not_found")}}</h1>
                    <p class="font-14 mt-4">{{__("payOnline.description_not_found")}}  <span class="font-bold m-1" v-html="__('payOnline.tel')"></span></p>

                </div>
            </div>

            <div class="col-12 col-md-12 mt-5 d-center">
                <a :href="goBack" class="btn btn-primary py-3">
                    {{__("payOnline.go_back")}}
                </a>
            </div>
        </div>


        <div class="row pt-4 px-checkout-desktop" v-if="payment.status == 'cancelled'">
            <div class="col-12">
                <h1 class="font-28 d-center f-r-medium my-0">{{__("payOnline.cancelled")}}</h1>
                <p class="font-14  mt-4">{{__("payOnline.description_cancelled")}} <span class="font-bold  m-1" v-html="__('payOnline.tel')"></span></p>
            </div>

            <div class="col-12 col-md-12 mt-5 d-center">
                <a :href="goBack" class="btn btn-primary py-3">
                    {{__("payOnline.go_back")}}
                </a>
            </div>
        </div>


        <div class="row py-4 px-checkout-desktop" v-if="payment.status == 'ok'">
            <div class="col-12">
                <h1 class="font-28 f-r-medium my-0">{{__("payOnline.success")}}</h1>
                <p class="font-14">{{__("payOnline.description", [ payment.client.name ])}} <span class="font-bold" v-html="__('payOnline.tel')"></span></p>
            </div>

            <div class="col-12 col-md-12 pr-md-0 c-payments-checkout">

                <div class="c-pay-bank-info">
                    <div class="c-bank-voucher p-3 p-md-4 border-radius-10">
                        <div class="row">
                            <div class="col-4">
                                <span class="float-left font-roboto-medium">{{__("payOnline.code")}}:</span>
                            </div>
                            <div class="col-8">
                                <span class="float-right font-roboto-medium"> {{payment.client.id}} </span>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <hr>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-4">
                                <span class="float-left font-roboto-medium">{{__("payOnline.owner_name")}}:</span>
                            </div>
                            <div class="col-8 pl-0">
                                <span class="float-right font-roboto-medium"> {{payment.client.name}} {{payment.client.lastname}} </span>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <hr>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-4">
                                <span class="float-left font-roboto-medium">{{__("payOnline.owner_email")}}:</span>
                            </div>
                            <div class="col-8 pl-0">
                                <span class="float-right font-roboto-medium"> {{payment.client.email}}</span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <hr>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-4">
                                <span class="float-left font-roboto-medium">{{__("payOnline.total_amount")}}:</span>
                            </div>
                            <div class="col-5 pl-0">
                                <span class="float-right font-roboto-medium"> {{  $filters.currency(payment.client.totalAmount,  payment.client.currency) }}  </span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <hr>
                            </div>
                        </div>

                        <div class="row" v-if="false">
                            <div class="col-4">
                                <span class="float-left font-roboto-medium">{{__("payOnline.paid_amount")}}:</span>
                            </div>
                            <div class="col-5 pl-0">
                                <span class="float-right font-roboto-medium"> {{  $filters.currency(payment.client.serviceAmountPaid,  payment.client.currency) }}  </span>
                            </div>
                        </div>

                        <div class="row" v-if="false">
                            <div class="col-4">
                                <span class="float-left font-roboto-medium">{{__("payOnline.balance_amount")}}:</span>
                            </div>
                            <div class="col-5 pl-0">
                                <span class="float-right font-roboto-medium"> {{  $filters.currency(payment.client.totalAmount - payment.client.serviceAmountPaid,  payment.client.currency) }}  </span>
                            </div>
                        </div>
                    </div>

                    <div class="row pt-3">

                        <div class="col-12 col-md-6 mb-3">
                        </div>

                        <div class="col-12 col-md-3 mb-3">
                            <a :href="goBack" class="btn btn-outline-danger w-100">
                                {{__("payOnline.go_back")}}
                            </a>
                        </div>

                        <div class="col-12 col-md-3 mb-3" v-if="(payment.client.totalAmount - payment.client.serviceAmountPaid) > 0">
                            <a :href="payment.urlRedirect" target="_blank" class="btn btn-success text-white w-100">
                                {{__("payOnline.create_link")}}
                            </a>
                        </div>

                        <div class="col-12 col-md-3 mb-3" v-if="(payment.client.totalAmount - payment.client.serviceAmountPaid) <= 0">

                            <button type="button" class="btn btn-light   text-dark w-100" disabled>{{__("payOnline.create_link")}}</button>
                        </div>



                    </div>
                </div>
            </div>
        </div>
    </div>
</template>


<script>
    import { storeToRefs } from 'pinia';
    import { usePaymentOnlineStore } from '../../stores/payment-online';
    import { Generic } from '../../../utils/analytics/generics';
    
    export default {
        setup() {
            const paymentOnlineStore = usePaymentOnlineStore();
            const { payment } = storeToRefs(paymentOnlineStore);
            return { payment }
        },
        mounted() {
            this.initialize();
        },
        computed: {
            goBack() {
                return document.referrer.length ? document.referrer : "/";
            }
        },
        methods: {
            initialize() {
                let client = this.payment.client || { id: this.payment.request.code };
                Generic.paymentOnline(client.id, this.payment.status, client.type);
            },
            goLink() {
                location.href = this.payment.urlRedirect;
            }
        }
    }
</script>