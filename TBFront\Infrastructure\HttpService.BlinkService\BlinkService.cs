using System.Globalization;
using System.Net.Mime;
using System.Text;
using System.Text.Json;
using TBFront.Infrastructure.HttpService.BlacklistBookingService.Dtos;
using TBFront.Interfaces;
using TBFront.Models.Flight.BLink.Request;
using TBFront.Models.Flight.BLink.Response;

namespace TBFront.Infrastructure.HttpService.BlacklistBookingService
{
    public class BlinkService : IBlinkService
    {
        private readonly HttpClient _httpClient;

        private readonly BlacklistBookingConfiguration _configuration;
        private readonly static JsonSerializerOptions _jsonSerializerOptions = new() { PropertyNameCaseInsensitive = true };

        public BlinkService(HttpClient httpClient, BlacklistBookingConfiguration configuration)
        {
            _httpClient = httpClient;
            _configuration = configuration;
            _httpClient.DefaultRequestHeaders.Add("Accept", MediaTypeNames.Application.Json);
            _httpClient.BaseAddress = new Uri(_configuration.Uri);
        }




        public async Task<BLinkBookResponse> QueryAsync(BLinkRequest request , CancellationToken ct)
        {

            var formData = GetFormData(request);
            var uriService = $"{_configuration.BLinkUrl}";
            var httpResponseMessage = await _httpClient.PostAsync(uriService, new FormUrlEncodedContent(formData), ct);
            using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync(ct);
            var response = await JsonSerializer.DeserializeAsync<BLinkBookResponse>(contentStream, _jsonSerializerOptions, ct);

            return response;
        }

        private static string GetAuthBasic(string username, string password)
        {
            var svcCredentials = Convert.ToBase64String(ASCIIEncoding.ASCII.GetBytes(username + ":" + password));
            return $"{svcCredentials}";
        }

        private static List<KeyValuePair<string, string>> GetFormData(BLinkRequest request)
        {

            var payload = JsonSerializer.Serialize(request.PayLoad);

            var formData = new List<KeyValuePair<string, string>> {
                { new KeyValuePair<string, string>("KeyValidation", request.KeyValidation) },
                { new KeyValuePair<string, string>("ChannelId", request.ChannelId.ToString()) },
                { new KeyValuePair<string, string>("TotalCost", request.TotalCost.ToString("R",CultureInfo.InvariantCulture)) },
                { new KeyValuePair<string, string>("TotalAmount",  request.TotalAmount.ToString("R",CultureInfo.InvariantCulture)) },
                { new KeyValuePair<string, string>("OrganizationId", request.OrganizationId.ToString()) },
                { new KeyValuePair<string, string>("PayLoad", payload ) },
                { new KeyValuePair<string, string>("ServiceType", request.ServiceType.ToString() ) },
            };

            return formData;
        }

    }

}
