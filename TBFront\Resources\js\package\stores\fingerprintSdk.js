import { defineStore } from 'pinia';
import FingerprintJS from '@fingerprintjs/fingerprintjs-pro';
import { getItem, setItem } from './indexedDBHelper';
import { getBrowserFingerprint } from '../../utils/helpers/fingerprint';

const DB_NAME = 'fingerprintDb';
const STORE_NAME = 'visitorData';
const CACHE_DURATION = 365 * 24 * 60 * 60 * 1000;


export const useFingerprintStore = defineStore('fingerprintSdk', {
  state: () => ({
    visitorId: null,
    fpPromise: null, 
  }),
  actions: {
    
    initializeFingerprintAgent() {
      
      if (!this.fpPromise) {
        this.fpPromise= FingerprintJS.load({
          apiKey: window.__pt.settings.site.fingerPrintkey,
          endpoint: [
            window.__pt.settings.site.fingerPrintURL,
            FingerprintJS.defaultEndpoint
          ],
          scriptUrlPattern: [
            window.__pt.settings.site.fingerPrintURL+"/web/v<version>/<apiKey>/loader_v<loaderVersion>.js",
            FingerprintJS.defaultScriptUrlPattern
          ]
        })
      }
    },

    async fetchVisitorId() {
      try {
       
       
        const cachedData = await getItem(DB_NAME, STORE_NAME, 'visitorId');
        if( window.__pt.settings.site.fingerPrintkeyEnableSdk){
          this.initializeFingerprintAgent();
          if (cachedData && Date.now() - cachedData.timestamp < CACHE_DURATION) {
            this.visitorId = cachedData.value.value;
          } else {    
            const fp = await this.fpPromise;
            const result = await fp.get();
            this.visitorId = result?.visitorId || null;
            if (this.visitorId) {
              await setItem(DB_NAME, STORE_NAME, 'visitorId', {
                value: this.visitorId,
                timestamp: Date.now(),
              });
            }
          }
        }else {
          let fingerprintHash = await getBrowserFingerprint();
          this.visitorId = fingerprintHash;
        
        }
      } catch (error) {
        this.visitorId = undefined;
        console.error('Error fetching visitor ID:', error);
      }
    },
  },
});
