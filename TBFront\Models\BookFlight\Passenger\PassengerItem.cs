﻿

namespace TBFront.Models.BookFlight.Passenger
{
    public class PassengerItem
    {
        public string Title { get; set; }
        public int Type { get; set; }
        public string Names { get; set; }
        public string LastNames { get; set; }
        public int PassengerIndex { get; set; }
        public int PassengerNumber { get; set; }
        public bool RequireAdditionalUsaInformation { get; set; }
        public int Sex { get; set; }
        public int BirthDateDay { get; set; }
        public int BirthDateMonth { get; set; }
        public int BirthDateYear { get; set; }
        public string Nationality { get; set; }
        public int CustomerIdentityDocumentType { get; set; }
        public string CustomerDocumentNumber { get; set; }
        public string FrequentFlyerNumber { get; set; }
        public string FrequentFlyerProgram { get; set; }
        public object SpecialAssist { get; set; } //<- preguntar por modelo
        public object CustomerId { get; set; } //<- preguntar por modelo
        public object ExtraServicesCodes { get; set; } //<- preguntar por modelo
        public EmergencyContact EmergencyContact { get; set; }

        public PassengerItem()
        {
            EmergencyContact= new EmergencyContact();
        }
    }


    public class PassengerBooking
    {
        public int Type { get; set; }
        public int Quantity { get; set; }
        public int Age { get; set; }
    }
}
