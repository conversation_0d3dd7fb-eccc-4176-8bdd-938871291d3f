import {BaseModel} from "./baseModel";
export class ViewedExperimentModel extends BaseModel{
    constructor(args) {
        super()
        const defaults = {
            ExperimentId: { required: true, default: "", type: "string" },
            VariationId: { required: true, default: "", type: "string" },
            EventDestination: { required: false, default: "", type: "string" },
        };
        this.map(defaults, args)
    }
}