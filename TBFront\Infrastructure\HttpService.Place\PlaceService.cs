﻿using System.Net.Mime;
using System.Text.Json;
using TBFront.Infrastructure.HttpService.Place.Dtos;
using TBFront.Interfaces;
using TBFront.Models.Places.Request;
using TBFront.Models.Places.Response;

namespace TBFront.Infrastructure.HttpService.Place
{
    public class PlaceService : IQueryHandlerAsync<PlaceRequest, PlaceResponse>
    {

        private readonly HttpClient _httpClient;
        private readonly PlaceConfiguration _configuration;
        private readonly ICacheService _cacheService;
        private readonly static JsonSerializerOptions _jsonSerializerOptions = new() { PropertyNameCaseInsensitive = true };
        public PlaceService(HttpClient httpClient, PlaceConfiguration configuration, ICacheService cacheService)
        {
            _cacheService = cacheService;
            _configuration = configuration;
            _httpClient = httpClient;
            _httpClient.DefaultRequestHeaders.Add("Accept", MediaTypeNames.Application.Json);
            _httpClient.BaseAddress = new Uri(_configuration.Uri);
        }



        public async Task<PlaceResponse> QueryAsync(PlaceRequest request, CancellationToken ct)
        {

            var key = $"place-{request.Code}-{request.Culture}";
            var placeResponse = await _cacheService.GetCache<PlaceResponse>(key, ct);

            if (placeResponse != null && placeResponse.Code != null)
            {
                return placeResponse;
            }

            var queryParameters = $"?&type={request.Type}&organizationId={1}&propertyId={1}&culture={request.Culture}&code={request.Code}";

            var url = _configuration.ContentPath + queryParameters;
            var httpResponseMessage = await _httpClient.GetAsync(url, ct);

            if (httpResponseMessage.IsSuccessStatusCode)
            {
                var placeResponses = await httpResponseMessage.Content.ReadFromJsonAsync<List<PlaceResponse>>(_jsonSerializerOptions, ct);

                if (placeResponses is not null && placeResponses.Count > 0)
                {
                    placeResponse = placeResponses.First();
                    _cacheService.SetCache(key, placeResponse);
                }
                
            }


            return placeResponse;
        }
    }
}
