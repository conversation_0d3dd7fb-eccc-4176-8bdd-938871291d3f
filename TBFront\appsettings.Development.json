{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "System.Net.Http.HttpClient": "Warning"}}, "RedisCache": {"Configuration": {"Host": "flights-front-prod.fjxj7c.ng.0001.use1.cache.amazonaws.com", "HostContent": "flights-front-prod.fjxj7c.ng.0001.use1.cache.amazonaws.com", "Port": 6379, "Expiration": 30, "ConnectTimeout": 3000, "RetryTimeout": 3000, "Active": false}}, "PlaceAirportConfigurations": {"Production": true}, "HttpFlightQuoteServiceConfigurations": {"UriBase": "https://flight-api-quote.pricetravel.com", "UriApiPTH": "https://flight-booking-client.pricetravel.com/flights", "UriTravelItinerary": "/quote", "UriUpsell": "/upsell", "UriRevalidate": "/revalidate", "UriCreateBooking": "/bookings/createbooking", "User": "api_tiquetes<PERSON>tos", "Password": "d1dlmxg0v7", "UriSummary": "/bookings/summary", "UserSummary": "api_tiquetes<PERSON>tos", "PasswordSummary": "api_tiquetes<PERSON>tos", "SiteConfig": "tb"}, "HttpFlightContentServiceConfiguration": {"Uri": "https://fcs-api.pricetravel.com", "ContentPath": "/familyfare"}, "HttpPlaceServiceConfiguration": {"Uri": "https://search-places.pricetravel.com", "ContentPath": "search/place"}, "HttpAPIFrontServiceConfiguration": {"Uri": "https://www.tiquetesbaratos.com/", "CollectionPath": "/api-hotel/getCollection", "HotelDetailPath": "/api-hotel/HotelDetail/HotelContent"}, "HttpDestinationServiceConfiguration": {"Uri": "https://prod-static-vtb.cdnpth.com", "OriginPath": "tb-quotes/quotes-origin.json", "DestinationPath": "tb-quotes/quotes-destination.json"}, "HttpBlacklistServiceConfiguration": {"Uri": "https://prod-static-vtb.cdnpth.com", "SettingsPath": "tb-quotes/blacklist/settings.json"}, "HttpPaymentGatewayConfiguration": {"Uri": "http://api-pagos.pricetravel.com", "PathPayment": "/api/v1/Tokens/GeneratePaymentToken", "PathPaymentGetClientInfo": "/api/v1/Payment/GetClientTokenRequestInfo", "PathSearchPaymentGatewayConfiguration": "/api/v1/Configurations/SearchPaymentGatewayConfiguration", "PaymentGatewayApp": 2, "CheckoutProvider": [35], "thirdPartyCheckoutProvider": [2, 5, 6, 7], "Is3DSecureProcessingEnabled": false}, "AuthConfiguration": {"AuthUrl": "https://auth.pricetravel.com/v2/token", "AuthPath": "/v2/token", "ClientId": "80H2NpqI8cKWEP73R0PSCGbT7jHbYpeW", "ClientSecret": "kITzYF40d4UnEmegMFFmqfJpqviOOgrO1jTNp0J7p1kTPF2ZOOhwJfwAoiDD7xF3", "GrantType": "client_credential", "Organization": "PTH"}, "ItineraryConfiguration": {"Uri": "https://bookingservices.pricetravelinternal.com", "PathItinerary": "/bookings/itinerary", "UrlBookingServiceItinerary": "https://itineraryservices.pricetravelinternal.com/graphql", "BLinkUrl": "https://blinkservices.pricetravelinternal.com/Booking"}, "FlightFacadeConfiguration": {"Production": true}, "HttpCdnServiceConfiguration": {"Uri": "https://prod-static-vtb.cdnpth.com", "ExchangeRate": "/assets/exchange/rates.json", "LegalContent": "/assets/legal-content", "TabContent": "/assets/general-content", "SeoContent": "/assets/seo-content"}, "GrowthBookConfiguration": {"ClientSdk": "sdk-eHYLVCtxKFNETWRW", "ServerSdk": "sdk-eHYLVCtxKFNETWRW", "FeaturesUrl": "https://cdn.growthbook.io/api/features/", "ServerExperiments": [{"Code": "external-fee-tb", "Feature": "external-fee-tb", "CookieVariation": "external-fee-tb-cookie", "Active": true, "SdkKey": "sdk-eHYLVCtxKFNETWRW", "Environment": "production", "Config": [{"Code": "variation_a_default", "IsExperiment": false, "Value": "", "CookieValue": "0"}, {"Code": "variation_b", "IsExperiment": true, "Value": "", "CookieValue": "1"}]}]}, "Settings": {"AppName": "TiquetesBaratos", "SiteName": "tique<PERSON><PERSON><PERSON>", "Production": false, "Code": "TB", "Sufix": "", "Assets": "/assets-tb", "SiteUrl": "https://localhost:7030", "Site": "https://www.tiquetesbaratos.com", "Version": 2, "CheckoutUrl": "/vuelos/checkout", "ListUrl": "/vuelos/resultados", "RedirectToPath": "/vuelos/voucher", "QuoteCheckoutStepOne": "/vuelos/api-tb/quote", "VoucherCheckout": "/vuelos/api-tb/get-booking", "RevalidateCheckout": "/vuelos/api-tb/revalidate", "EmailCheckouttUrl": "/vuelos/api-tb/booking-mail", "NationalValitaion": "/vuelos/api-tb/national-validation", "BookingCheckout": "/vuelos/api-tb/createbooking", "UrlBookingService": "https://bookingservices.pricetravelinternal.com/bookings/itinerary", "UrlAuthToken": "https://auth.pricetravel.com/v2/token", "MailUrl": "https://api-mailing.pricetravel.com.mx", "MailIsEnable": true, "MailIsLanguage": 5, "RetryCheckout": 1, "RetryCheckoutAllowed": ["A task was canceled", "Max 30 attempts"], "ClientId": "80H2NpqI8cKWEP73R0PSCGbT7jHbYpeW", "ClientSecret": "kITzYF40d4UnEmegMFFmqfJpqviOOgrO1jTNp0J7p1kTPF2ZOOhwJfwAoiDD7xF3", "HashKey": "E546C8DF278CD5931069B522E695D4E2", "CheckoutHashKey": "bPeShVmYq3t6w9y$B&E)H@McQfTjWnZr", "Domain": "viajes.tiquetesbaratos.com", "CloudCdn": "", "Organization": 1, "Property": 1, "Channel": 912, "OrganizationId": 1, "OrganizationContent": 1, "Language": "es", "Culture": "es-co", "CultureApp": "es", "Cultures": ["es-co"], "CulturesAllowed": "es|en", "Country": "CO", "Currency": "COP", "CurrencySymbol": "COP$ ", "GTM": "GTM-TZGSTL2D", "Login": true, "ShowConsoleLogs": false, "ShowServerLogs": true, "GoogleMapsApiKey": "AIzaSyAF8k5IlfxWMXaiVbSYzBcokiIrRIe3YOs", "RecaptchaKey": "6Lfl5-8eAAAAAECHbOBAyrnsbQah1X5kthYKYyvq", "AlgoliaId": "EB23C5I1ND", "AlgoliaKey": "********************************", "FingerPrintkey": "yNXEzF46yWCkOa24HPJB", "FingerPrintURL": "https://fingerprint.tiquetesbaratos.com", "FingerPrintkeyEnableSdk": false, "TimeToShowRequoteModal": 20, "ContactPhone": "601 743 66 20", "BreakdownList": [0, 5, 1, 4], "AffiliateId": 0, "AffiliateSiteId": 0, "ApiKeySift": "", "Retry": 3, "RetryTimeOut": 5, "RangeTotalAmount": 20000, "RangeTotalAmountCreateBooking": 2500, "ExternalFee": 2000, "RevalidateTimeOut": 300, "HoursDepositLimit": 3, "ApiKeySendGrid": "*********************************************************************", "ContactEmailFrom": "<EMAIL>", "ContactEmailFromName": "TiquetesBaratos", "GroupsEmailFrom": "<EMAIL>", "GroupsEmailFromName": "RESERVA DE GRUPO", "ContactMeConfiguration": {"Kerberus": "http://10.20.200.51", "Path": "/callback.php", "EmailTo": "<EMAIL>", "EmailFrom": "<EMAIL>", "AvailableTime": [{"Title": "openning_hours_weekdays", "Days": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"], "Hours": {"Start": "06:00", "End": "23:50"}}, {"Title": "openning_hours_weekends_holidays", "Days": ["Saturday", "Sunday"], "Hours": {"Start": "07:00", "End": "23:50"}}]}, "ChannelConfig": [{"Source": "flights", "Mobile": 912, "Desktop": 912, "ChannelGroupId": 427}], "Booker": {"Adults": {"Min": 1, "Max": 9, "Default": 2, "DefaultRoomAdded": 1}, "Kids": {"Min": 0, "Max": 9, "Default": 0, "DefaultRoomAdded": 0}, "KidsAge": {"Min": 2, "Max": 11}, "ServiceUrl": "https://www.tiquetesbaratos.com", "ServicePath": "/api-hotel/Algolia/Flight", "AlgoliaSiteName": "TB", "AutocompleteItems": 5, "PlaceTypes": "3,6,7,11,12", "HistoryStorageKey": "tb.history_booker_prod", "HistoryResearchStorageKey": "tb.history_researchs_prod"}, "ApiFlights": {"Domain": "https://www.tiquetesbaratos.com", "Path": "/api-flight/api/Flight/", "PathSearch": "search", "PathMatrix": "matrix", "PathPromotions": "calendar", "PathFilterSearch": "filtersearch", "PathFlightDetail": "detail", "PathFlightFamilyFare": "familyFareDetailsbyleg", "PathRate": "rate", "PathUpsell": "upsell", "PathCheckout": "/api-flight/api/checkout/flight/createbooking", "SiteConfig": "tb", "PaxConfig": 1, "PathLuggage": "luggage"}, "ApiB2C": {"Uri": "https://www.tiquetesbaratos.com", "PathPaymentOptions": "/api-hotel/paymentsOptions"}, "SEOSettings": {"PickupCode": "BOG", "PickupId": 54198, "PickupCodeAlternative": "MDE", "PickupIdAlternative": 167960}, "FormsConfiguration": {"PathContact": "/vuelos/api-tb/forms/contact", "PathGroups": "/vuelos/api-tb/forms/groups", "PathCall": "/vuelos/api-tb/forms/call"}, "ImageResolutions": {"desktop": {"list": {"height": "192", "width": "252", "HeightB": "227", "WidthB": "255"}, "bannerMain": {"height": "367", "width": "612"}, "bannerSecondary": {"height": "169", "width": "255"}, "rooms": {"height": "197", "width": "271"}, "modal": {"height": "354", "width": "428"}, "header": {"height": "29", "width": "195"}}, "mobile": {"list": {"height": "185", "width": "382", "HeightB": "209", "WidthB": "131"}, "bannerMain": {"height": "250", "width": "330"}, "bannerSecondary": {"height": "169", "width": "255"}, "rooms": {"height": "133", "width": "396"}, "modal": {"height": "110", "width": "338"}, "header": {"height": "21", "width": "141"}}}, "QuoteConfiguration": {"International": {"IsMatrixActive": true, "QuoteConfigurations": [{"ConfigName": "all", "Engine": [], "IsActive": true}]}, "National": {"IsMatrixActive": true, "priorityOrder": ["AV", "LA", "JA", "J6", "P5", "VE", "9R"], "QuoteConfigurations": [{"ConfigName": "kiu", "Engine": [17], "IsActive": true}, {"ConfigName": "avianca", "Engine": [19, 1], "CarrierCode": ["AV"], "IsActive": true}, {"ConfigName": "wingo", "Engine": [14], "IsActive": true}, {"ConfigName": "latam", "Engine": [21], "IsActive": true}, {"ConfigName": "jetSmart", "Engine": [22], "IsActive": true}]}}, "AirlineConfiguration": {"National": [{"AirlineCode": ["LA"], "airlineName": "Latam", "HiddenEmptyFamily": false, "SearchArrival": true, "Adjustable": false, "Families": [{"Name": "Basic", "FamilyFareCode": ["BASIC"], "IsAvailable": true}, {"Name": "Light", "FamilyFareCode": ["LIGHT"], "IsAvailable": true}, {"Name": "Full", "FamilyFareCode": ["FULL"], "IsAvailable": true}, {"Name": "Premium", "FamilyFareCode": ["PREMIUM ECONOMY", "PREMIUM ECONOMY FULL"], "IsAvailable": true}]}, {"AirlineCode": ["AV"], "airlineName": "Avianca", "HiddenEmptyFamily": false, "SearchArrival": true, "Adjustable": false, "Families": [{"Name": "Basic", "FamilyFareCode": ["XS", "BASIC"], "ValidatingCarrier": "AV", "IsAvailable": true}, {"Name": "Classic", "FamilyFareCode": ["ClASSIC"], "ValidatingCarrier": "AV", "IsAvailable": true}, {"Name": "Flex", "FamilyFareCode": ["FLEX"], "ValidatingCarrier": "AV", "IsAvailable": true}]}, {"AirlineCode": ["JA", "J6"], "airlineName": "jetSmart", "HiddenEmptyFamily": false, "SearchArrival": true, "Adjustable": false, "Families": [{"Name": "Básico", "FamilyFareCode": ["LowestFare"], "IsAvailable": true}, {"Name": "Travel", "FamilyFareCode": ["BNG3"], "IsAvailable": true}, {"Name": "Standard", "FamilyFareCode": ["BNG1"], "IsAvailable": true}, {"Name": "Premium", "FamilyFareCode": ["BNG2"], "IsAvailable": true}]}, {"AirlineCode": ["P5"], "airlineName": "<PERSON><PERSON>", "HiddenEmptyFamily": false, "SearchArrival": true, "Adjustable": false, "Families": [{"Name": "Basic", "FamilyFareCode": ["BASIC"], "IsAvailable": true}, {"Name": "Standard", "FamilyFareCode": ["STANDARD"], "IsAvailable": true}, {"Name": "Plus", "FamilyFareCode": ["PLUS"], "IsAvailable": true}, {"Name": "Extra", "FamilyFareCode": ["EXTRA"], "IsAvailable": true}]}, {"AirlineCode": ["9R"], "airlineName": "Satena", "HiddenEmptyFamily": true, "SearchArrival": false, "Adjustable": false, "Families": [{"Name": "Promo", "FamilyFareCode": ["PR"], "IsAvailable": true}, {"Name": "Basic", "FamilyFareCode": ["BA"], "IsAvailable": true}, {"Name": "Económica", "FamilyFareCode": ["EC"], "IsAvailable": true}, {"Name": "<PERSON><PERSON><PERSON>", "FamilyFareCode": ["FL"], "IsAvailable": true}, {"Name": "Plus", "FamilyFareCode": ["PL"], "IsAvailable": true}]}, {"AirlineCode": ["VE"], "AirlineName": "Clic Air", "HiddenEmptyFamily": true, "SearchArrival": false, "Adjustable": false, "Families": [{"Name": "Ligera", "FamilyFareCode": ["EP"], "IsAvailable": true}, {"Name": "Economica", "FamilyFareCode": ["EC"], "IsAvailable": true}, {"Name": "Preferencial", "FamilyFareCode": ["PR"], "IsAvailable": true}]}], "International": [{"AirlineCode": ["LA"], "airlineName": "Latam", "HiddenEmptyFamily": false, "SearchArrival": true, "Families": [{"Name": "Basic", "FamilyFareCode": ["BASIC"], "IsAvailable": true}, {"Name": "Light", "FamilyFareCode": ["LIGHT"], "IsAvailable": true}, {"Name": "Full", "FamilyFareCode": ["FULL"], "IsAvailable": true}, {"Name": "Premium", "FamilyFareCode": ["PREMIUM ECONOMY"], "IsAvailable": true}]}, {"AirlineCode": ["AM"], "airlineName": "Aeromexico", "HiddenEmptyFamily": false, "SearchArrival": true, "Families": [{"Name": "Basica", "FamilyFareCode": ["BASICA"], "IsAvailable": true}, {"Name": "Light", "FamilyFareCode": ["LIGHT"], "IsAvailable": true}, {"Name": "Clasica", "FamilyFareCode": ["CLASICA"], "IsAvailable": true}, {"Name": "Premium", "FamilyFareCode": ["PREMIUM ECONOMY"], "IsAvailable": true}]}, {"AirlineCode": ["DL"], "airlineName": "Delta Air Lines", "HiddenEmptyFamily": false, "SearchArrival": true, "Families": [{"Name": "Basica", "FamilyFareCode": ["BASICA"], "IsAvailable": true}, {"Name": "BASIC ECONOMY", "FamilyFareCode": ["BASIC ECONOMY"], "IsAvailable": true}, {"Name": "Clasica", "FamilyFareCode": ["CLASICA"], "IsAvailable": true}, {"Name": "MAIN CABIN", "FamilyFareCode": ["MAIN CABIN"], "IsAvailable": true}]}, {"AirlineCode": ["UA"], "airlineName": "United Airlines", "HiddenEmptyFamily": false, "SearchArrival": true, "Families": [{"Name": "Basica", "FamilyFareCode": ["BASICA"], "IsAvailable": true}, {"Name": "BASIC ECONOMY", "FamilyFareCode": ["BASIC ECONOMY"], "IsAvailable": true}, {"Name": "Clasica", "FamilyFareCode": ["CLASICA"], "IsAvailable": true}, {"Name": "ECONOMY", "FamilyFareCode": ["ECONOMY"], "IsAvailable": true}]}, {"AirlineCode": ["AC"], "airlineName": "Air Canada", "HiddenEmptyFamily": false, "SearchArrival": true, "Families": [{"Name": "Basica", "FamilyFareCode": ["BASIC"], "IsAvailable": true}, {"Name": "ECONOMY", "FamilyFareCode": ["ECONOMY"], "IsAvailable": true}, {"Name": "Clasica", "FamilyFareCode": ["CLASICA"], "IsAvailable": true}, {"Name": "BUSINESS LOWEST", "FamilyFareCode": ["BUSINESS LOWEST"], "IsAvailable": true}, {"Name": "STANDARD", "FamilyFareCode": ["STANDARD"], "IsAvailable": true}]}, {"AirlineCode": ["AA"], "airlineName": "American Airlines", "HiddenEmptyFamily": false, "SearchArrival": true, "Families": [{"Name": "Basica", "FamilyFareCode": ["BASIC"], "IsAvailable": true}, {"Name": "Basic Economy", "FamilyFareCode": ["Basic Economy"], "IsAvailable": true}, {"Name": "Clasica", "FamilyFareCode": ["CLASICA"], "IsAvailable": true}, {"Name": "BUSINESS LOWEST", "FamilyFareCode": ["BUSINESS LOWEST"], "IsAvailable": true}, {"Name": "STANDARD", "FamilyFareCode": ["STANDARD"], "IsAvailable": true}]}, {"AirlineCode": ["AV"], "airlineName": "Avianca", "HiddenEmptyFamily": false, "SearchArrival": true, "Families": [{"Name": "Basic", "FamilyFareCode": ["XS", "BASIC"], "ValidatingCarrier": "AV", "IsAvailable": true}, {"Name": "Classic", "FamilyFareCode": ["ClASSIC"], "ValidatingCarrier": "AV", "IsAvailable": true}, {"Name": "Flex", "FamilyFareCode": ["FLEX"], "ValidatingCarrier": "AV", "IsAvailable": true}]}, {"AirlineCode": ["JA", "J6"], "airlineName": "jetSmart", "HiddenEmptyFamily": false, "SearchArrival": true, "Families": [{"Name": "Básico", "FamilyFareCode": ["LowestFare"], "IsAvailable": true}, {"Name": "Standard", "FamilyFareCode": ["BNG1"], "IsAvailable": true}, {"Name": "Premium", "FamilyFareCode": ["BNG2"], "IsAvailable": true}]}, {"AirlineCode": ["P5"], "airlineName": "<PERSON><PERSON>", "HiddenEmptyFamily": false, "SearchArrival": true, "Families": [{"Name": "Basic", "FamilyFareCode": ["BASIC"], "IsAvailable": true}, {"Name": "Standard", "FamilyFareCode": ["STANDARD"], "IsAvailable": true}, {"Name": "Plus", "FamilyFareCode": ["PLUS"], "IsAvailable": true}, {"Name": "Extra", "FamilyFareCode": ["EXTRA"], "IsAvailable": true}]}, {"AirlineCode": ["9R"], "airlineName": "Satena", "HiddenEmptyFamily": true, "SearchArrival": true, "Families": [{"Name": "Promo", "FamilyFareCode": ["PR"], "IsAvailable": true}, {"Name": "Basic", "FamilyFareCode": ["BA"], "IsAvailable": true}, {"Name": "Económica", "FamilyFareCode": ["EC"], "IsAvailable": true}, {"Name": "<PERSON><PERSON><PERSON>", "FamilyFareCode": ["FL"], "IsAvailable": true}, {"Name": "Plus", "FamilyFareCode": ["PL"], "IsAvailable": true}]}, {"AirlineCode": ["VE"], "AirlineName": "Clic Air", "HiddenEmptyFamily": true, "SearchArrival": true, "Families": [{"Name": "Ligera", "FamilyFareCode": ["EP"], "IsAvailable": true}, {"Name": "Economica", "FamilyFareCode": ["EC"], "IsAvailable": true}, {"Name": "Preferencial", "FamilyFareCode": ["PR"], "IsAvailable": true}]}, {"AirlineCode": ["AF"], "AirlineName": "Air France", "HiddenEmptyFamily": true, "SearchArrival": true, "Families": [{"Name": "ECONOMY LIGHT", "FamilyFareCode": ["ECONOMY LIGHT"], "IsAvailable": true}, {"Name": "ECONOMY STANDARD", "FamilyFareCode": ["ECONOMY STANDARD"], "IsAvailable": true}, {"Name": "ECONOMY FLEX", "FamilyFareCode": ["ECONOMY FLEX"], "IsAvailable": true}]}, {"AirlineCode": ["IB"], "AirlineName": "Iberia", "HiddenEmptyFamily": true, "SearchArrival": true, "Families": [{"Name": "BASIC", "FamilyFareCode": ["BASIC"], "IsAvailable": true}, {"Name": "OPTIMA", "FamilyFareCode": ["OPTIMA"], "IsAvailable": true}, {"Name": "COMFORT", "FamilyFareCode": ["COMFORT"], "IsAvailable": true}]}, {"AirlineCode": ["KL"], "AirlineName": "KLM", "HiddenEmptyFamily": true, "SearchArrival": true, "Families": [{"Name": "ECONOMY LIGHT", "FamilyFareCode": ["ECONOMY LIGHT"], "IsAvailable": true}, {"Name": "ECONOMY STANDARD", "FamilyFareCode": ["ECONOMY STANDARD"], "IsAvailable": true}, {"Name": "ECONOMY FLEX", "FamilyFareCode": ["ECONOMY FLEX"], "IsAvailable": true}]}, {"AirlineCode": ["LH"], "AirlineName": "Lufthansa", "HiddenEmptyFamily": true, "SearchArrival": true, "Families": [{"Name": "ECONOMY LIGHT", "FamilyFareCode": ["ECONOMY LIGHT"], "IsAvailable": true}, {"Name": "ECONOMY STANDARD", "FamilyFareCode": ["ECONOMY STANDARD"], "IsAvailable": true}, {"Name": "CORPORATE ECONOMY BASE", "FamilyFareCode": ["CORPORATE ECONOMY BASE"], "IsAvailable": true}]}, {"AirlineCode": ["EK"], "AirlineName": "Emirates", "HiddenEmptyFamily": true, "SearchArrival": true, "Families": [{"Name": "OTA", "FamilyFareCode": ["OTA"], "IsAvailable": true}]}, {"AirlineCode": ["UX"], "AirlineName": "Air Europa", "HiddenEmptyFamily": true, "SearchArrival": true, "Families": [{"Name": "LITE", "FamilyFareCode": ["LITE"], "IsAvailable": true}, {"Name": "STANDARD", "FamilyFareCode": ["STANDARD"], "IsAvailable": true}, {"Name": "FLEX", "FamilyFareCode": ["FLEX"], "IsAvailable": true}]}, {"AirlineCode": ["TP"], "AirlineName": "TAP Air Portugal", "HiddenEmptyFamily": true, "SearchArrival": true, "Families": [{"Name": "LITE", "FamilyFareCode": ["LITE"], "IsAvailable": true}, {"Name": "STANDARD", "FamilyFareCode": ["STANDARD"], "IsAvailable": true}, {"Name": "FLEX", "FamilyFareCode": ["FLEX"], "IsAvailable": true}]}]}, "FlightValidationStops": {"National": {"Routes": [{"Origin": "BOGMDE", "AirlineStops": [{"Airline": "LA", "Stops": 0}, {"Airline": "AV", "Stops": 0}]}, {"Origin": "BOGCLO", "AirlineStops": [{"Airline": "LA", "Stops": 0}, {"Airline": "AV", "Stops": 0}]}, {"Origin": "BOGPEI", "AirlineStops": [{"Airline": "LA", "Stops": 0}, {"Airline": "AV", "Stops": 0}]}, {"Origin": "CLOCTG", "AirlineStops": [{"Airline": "LA", "Stops": 1}, {"Airline": "AV", "Stops": 1}]}, {"Origin": "BGATCO", "AirlineStops": [{"Airline": "LA", "Stops": 2}, {"Airline": "AV", "Stops": 2}]}], "Default": {"Value": 1}}}, "IataCodes": [{"Iata": "AV", "Engine": 19}, {"Iata": "LA", "Engine": 21}, {"Iata": "P5", "Engine": 14}, {"Iata": "9R", "Engine": 17}, {"Iata": "VE", "Engine": 17}, {"Iata": "JA", "Engine": 22}, {"Iata": "J6", "Engine": 22}, {"Iata": "AA", "Engine": 27}, {"Iata": "P5", "Engine": 14}, {"Iata": "CM", "Engine": 20}, {"Iata": "VB", "Engine": 6}, {"Iata": "Y4", "Engine": 11}], "InternalApi": {"CurrencyChangePath": "/vuelos/api-tb/change-currency"}, "EmailDomains": ["gmail.com", "hotmail.com", "outlook.com", "yahoo.com"]}, "Site": {"StaticPhoneNumbers": {"PrimaryPhone": "6017436620", "PrimaryPhoneFormat": "************", "SecondaryPhone": "015717442074", "SecondaryPhoneFotmat": "+57 (1) 7442074", "RestOfWorld": "01529988812485", "RestOfWorldFormat": "+52 (*************", "UsaPhone": "018552468484", "UsaPhoneFormat": "(*************"}, "Phones": [{"City": "Bogota", "Phone": "************"}, {"City": "S. <PERSON>", "Phone": "************"}, {"City": "Cali", "Phone": "************"}, {"City": "Cartagena", "Phone": "************"}, {"City": "Medellin", "Phone": "************"}, {"City": "Valledupar", "Phone": "************"}, {"City": "<PERSON><PERSON><PERSON><PERSON>", "Phone": "************"}, {"City": "Cucuta", "Phone": "************"}, {"City": "Pasto", "Phone": "************"}, {"City": "Man<PERSON><PERSON>", "Phone": "************"}, {"City": "<PERSON>", "Phone": "************"}, {"City": "Bucaramanga", "Phone": "************"}, {"City": "Armenia", "Phone": "************"}], "airlinesCheckIn": [{"name": "Avianca", "img": "https://img.cdnpth.com/media/images/airlines-logos/AV.svg", "url": {"es": "https://checkinnew.avianca.com/Check-In?lang=ES", "en": "https://checkinnew.avianca.com/Check-In?lang=EN"}}, {"name": "American Airlines", "img": "https://img.cdnpth.com/media/images/airlines-logos/AA.svg", "url": {"es": "https://www.aa.com/reservation/findReservationAccess.do?locale=es_CO", "en": "https://www.aa.com/reservation/findReservationAccess.do?locale=en_US"}}, {"name": "LATAM", "img": "https://img.cdnpth.com/media/images/airlines-logos/LA.svg", "url": {"es": "https://www.latamairlines.com/mx/es/check-in", "en": "https://www.latamairlines.com/mx/en/check-in"}}, {"name": "United", "img": "https://img.cdnpth.com/media/images/airlines-logos/UA.svg", "url": {"es": "https://www.united.com/es/co/checkin", "en": "https://www.united.com/en/co/checkin"}}, {"name": "Copa Airlines", "img": "https://img.cdnpth.com/media/images/airlines-logos/CM.svg", "url": {"es": "https://checkin.copaair.com/", "en": "https://checkin.copaair.com/"}}, {"name": "Iberia", "img": "https://img.cdnpth.com/media/images/airlines-logos/IB.svg", "url": {"es": "https://www.iberia.com/co/autocheckin-online/?language=es&market=co#!/ibcose", "en": "https://www.iberia.com/co/autocheckin-online/?language=en&market=co#!/ibcose"}}, {"name": "AirFrance", "img": "https://img.cdnpth.com/media/images/airlines-logos/AF.svg", "url": {"es": "https://wwws.airfrance.com.co/check-in", "en": "https://wwws.airfrance.us/check-in"}}, {"name": "AeroMexico", "img": "https://img.cdnpth.com/media/images/airlines-logos/AM.svg", "url": {"es": "https://aeromexico.com/es-es/check-in", "en": "https://www.aeromexico.com/en-us/check-in"}}, {"name": "JetBlue", "img": "https://img.cdnpth.com/media/images/airlines-logos/B6.svg", "url": {"es": "https://checkin.jetblue.com/checkin/", "en": "https://checkin.jetblue.com/checkin/"}}, {"name": "AirCanada", "img": "https://img.cdnpth.com/media/images/airlines-logos/AC.svg", "url": {"es": "https://www.aircanada.com/home/<USER>/es/aco/checkin", "en": "https://www.aircanada.com/home/<USER>/en/aco/checkin"}}, {"name": "Aerolineas Argentinas", "img": "https://img.cdnpth.com/media/images/airlines-logos/AR.svg", "url": {"es": "https://www.aerolineas.com.ar/es-eu/?activeTab=checkIn", "en": "https://www.aerolineas.com.ar/en-eu/?activeTab=checkIn"}}, {"name": "Delta", "img": "https://img.cdnpth.com/media/images/airlines-logos/DL.svg", "url": {"es": "https://es.delta.com/my-trips/search", "en": "https://www.delta.com/my-trips/search"}}, {"name": "Lufthansa", "img": "https://img.cdnpth.com/media/images/airlines-logos/LH.svg", "url": {"es": "https://www.lufthansa.com/co/es/online-check-in", "en": "https://www.lufthansa.com/co/en/online-check-in"}}, {"name": "British Airways", "img": "https://img.cdnpth.com/media/images/airlines-logos/BA.svg", "url": {"es": "https://www.britishairways.com/travel/olcilandingpageauthreq/public/es_co", "en": "https://www.britishairways.com/travel/olcilandingpageauthreq/public/en_co"}}, {"name": "Jet Smart", "img": "https://img.cdnpth.com/media/images/airlines-logos/JA.svg", "url": {"es": "https://jetsmart.com/co/es/minisitios/checkin/home", "en": "https://jetsmart.com/us/en/minisitios/checkin/home"}}, {"name": "<PERSON><PERSON>", "img": "https://img.cdnpth.com/media/images/airlines-logos/P5.svg", "url": {"es": "https://reserva.wingo.com/#/admin/login/es/manage", "en": "https://reserva.wingo.com/#/admin/login/en/manage"}}, {"name": "Clic Air", "img": "/assets-tb/img/tiquetesbaratos/logos/logo-clic.svg", "url": {"es": "https://clicair.co/web-check-in", "en": "https://clicair.co/web-check-in"}}, {"name": "Satena", "img": "https://img.cdnpth.com/media/images/airlines-logos/9R.svg", "url": {"es": "https://wc2-9r.kiusys.net/", "en": "https://wc2-9r.kiusys.net/"}}, {"name": "Viva Aerobus", "img": "https://img.cdnpth.com/media/images/airlines-logos/VB.svg", "url": {"es": "https://www.vivaaerobus.com/es-mx/manage/find-booking", "en": "https://www.vivaaerobus.com/en-us/manage/find-booking"}}, {"name": "<PERSON><PERSON>", "img": "https://img.cdnpth.com/media/images/airlines-logos/Y4.svg", "url": {"es": "https://www.volaris.com/mytrips", "en": "https://www.volaris.com/mytrips"}}, {"name": "Air Europa", "img": "https://img.cdnpth.com/media/images/airlines-logos/UX.svg", "url": {"es": "https://www.aireuropa.com/es/es/mytrips/checkin", "en": "https://www.aireuropa.com/us/en/mytrips/checkin"}}, {"name": "Turkish Airlines", "img": "https://img.cdnpth.com/media/images/airlines-logos/TK.svg", "url": {"es": "https://www.turkishairlines.com/es-mx/flights/manage-booking", "en": "https://www.turkishairlines.com/en-us/flights/manage-booking"}}, {"name": "Plus Ultra", "img": "https://img.cdnpth.com/media/images/airlines-logos/PU.svg", "url": {"es": "https://www.plusultra.com/es-es/check-in/login/", "en": "https://www.plusultra.com/es-es/check-in/login/"}}, {"name": "Emirates", "img": "https://img.cdnpth.com/media/images/airlines-logos/EK.svg", "url": {"es": "https://www.emirates.com/mx/spanish/manage-booking/online-check-in/", "en": "https://www.emirates.com/mx/english/manage-booking/online-check-in/"}}, {"name": "KLM", "img": "https://img.cdnpth.com/media/images/airlines-logos/KL.svg", "url": {"es": "https://www.klm.com.mx/check-in", "en": "https://www.klm.com/check-in"}}, {"name": "Spirit Air Lines", "img": "https://img.cdnpth.com/media/images/airlines-logos/NK.svg", "url": {"es": "https://www.spirit.com/home-check-in", "en": "https://www.spirit.com/home-check-in"}}, {"name": "Alaska Airlines", "img": "https://img.cdnpth.com/media/images/airlines-logos/AS.svg", "url": {"es": "https://webselfservice.alaskaair.com/checkinweb/default.aspx", "en": "https://webselfservice.alaskaair.com/checkinweb/default.aspx"}}, {"name": "Sky Airlines", "img": "https://img.cdnpth.com/media/images/airlines-logos/H2.svg", "url": {"es": "https://check-in.skyairline.com/es/chile", "en": "https://check-in.skyairline.com/en/chile/"}}, {"name": "<PERSON><PERSON>", "img": "https://img.cdnpth.com/media/images/airlines-logos/G3.svg", "url": {"es": "https://b2c.voegol.com.br/check-in/", "en": "https://b2c.voegol.com.br/check-in/"}}, {"name": "Westjet", "img": "https://img.cdnpth.com/media/images/airlines-logos/WS.svg", "url": {"es": "https://checkin.westjet.com/index.html#/", "en": "https://checkin.westjet.com/index.html#/"}}, {"name": "All Nippon Airways Co.", "img": "https://img.cdnpth.com/media/images/airlines-logos/NH.svg", "url": {"es": "https://www.ana.co.jp/es/mx/travel-information/online-check-in/", "en": "https://www.ana.co.jp/en/us/travel-information/online-check-in/"}}, {"name": "<PERSON><PERSON>ing", "img": "https://img.cdnpth.com/media/images/airlines-logos/VY.svg", "url": {"es": "https://tickets.vueling.com/checkin", "en": "https://tickets.vueling.com/checkin"}}, {"name": "ITA Airways", "img": "https://img.cdnpth.com/media/images/airlines-logos/AZ.svg", "url": {"es": "https://www.ita-airways.com/es_es/check-in-search.html", "en": "https://www.ita-airways.com/en_en/check-in-search.html"}}], "Configuration": [{"Id": "https://www.tiquetesbaratos.com", "Channel": [{"Code": 1, "ChannelId": 912, "ChannelGroupId": 427, "Device": "Desktop"}, {"Code": 3220, "ChannelId": 3220, "ChannelGroupId": 427, "Device": "Mobile"}, {"Code": 3221, "ChannelId": 3221, "ChannelGroupId": 427, "Device": "Mobile"}, {"Code": 3940, "ChannelId": 3940, "ChannelGroupId": 427, "Device": "Mobile"}, {"Code": 3941, "ChannelId": 3941, "ChannelGroupId": 427, "Device": "Mobile"}], "ApiVersion": "2.1", "Currency": "COP", "Language": "1", "SourceMarket": "CO", "LanguageCode": "ES", "Culture": "es-co", "OrganizationId": "1", "OrganizationContent": "1", "SiteId": 1, "Source": "SPA-Hotel-Checkout", "ExternalProvider": [1, 4, 65, 97, 100], "ExcludeHotelCollectContracts": true, "ApplyCountryRestriction": true, "ResponseTimeout": 5670, "IsTaxAdjustmentEnabled": true}], "algoliaFlightIndex": "tb_searchvuelos"}, "AbTesting": {"Experiments": []}}