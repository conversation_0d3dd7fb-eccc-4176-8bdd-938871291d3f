import { oneWay, placeType, roundTrip } from "../../constants";
import { __ } from "./translate";

/**
 * @name getCookie
 * @description Get cookie value by name
 * 
 * @param {string} name required cookie name
 * 
 * @returns {string} cookie value
 */

export const getFlightTypeByMode = (mode) => {
	if (mode == 0) {
		return oneWay;
	} else if (mode == 1) {
		return roundTrip;
	}
};

export const getTripModeByFlightType = (flightType) => {
	if (flightType === oneWay) {
		return 0;
	} else if (flightType === roundTrip) {
		return 1;
	}
};

export const getAirportType = (type) => {
	return _.findKey(placeType, (value) => value == type);
};

export const getAirportTypeAlgolia = (type) => {
	let algoliaType = '';
	switch (type) {
		case 'startingAirport':
			algoliaType = 'from';
			break;
		case 'returningAirport':
			algoliaType = 'to';
			break;
		default:
			algoliaType = '';
			break;
	}

	return algoliaType;
}

export const getFareByLeg = (fares, fareByLegIds) => {
	let fareByLegSelected = {};

	for (const legId in fareByLegIds) {

		let leg = fares[legId];
		let fareId = fareByLegIds[legId]

		let fareSelect = leg[fareId];
		fareByLegSelected[legId] = fareSelect;

	}


	return fareByLegSelected;
}

export const getAlgoliaIndexName = (type) => {
	return type === startingAirport ? window.__pt.settings.site.algoliaIndexPackagesOrigin : window.__pt.settings.site.algoliaIndexPackagesDestination
};

export const sleep = (ms) => {
	return new Promise(resolve => setTimeout(resolve, ms));
};

export const conteinsItem = (arr, text) => {
// peticiones internel server truenan el bloque
	try {
		for (var i = 0; i < arr.length; i++) {
			if (text.indexOf(arr[i]) >= 0) {
				return true;
				break;
			}
		}
		return false;
	} catch (error) {
		return false
	}

}