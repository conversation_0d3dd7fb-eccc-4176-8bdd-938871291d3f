import { setDatalayer, UTILS } from "./main"

export default class Generics {


    constructor() {
        this.settings = window.__pt.settings.site;
        this.request = window.__pt.data || {};
    }

    paymentOnline(id, message, type = "Error") {
        let event = {
            eventAction: UTILS.actions.payment_online,
            eventCategory: `${message} - ${type}`,
            eventLabel: id,
            event: UTILS.events.gtmEvent,
            eventName: UTILS.events.gtmEvent,
            eventExtra:'',
        }
        setDatalayer(event);
    }

    callMe() {
        let event = {
            eventAction: UTILS.actions.call_me,
            eventCategory: UTILS.categories.header,
            eventLabel: "click menu",
            event: UTILS.events.gtmEvent,
            eventName: UTILS.events.gtmEvent,
            eventExtra: '',
        }
        setDatalayer(event);
    }

    phones() {
        let event = {
            eventAction: UTILS.actions.phones,
            eventCategory: UTILS.categories.header,
            eventLabel: "open",
            event: UTILS.events.gtmEvent,
            eventName: UTILS.events.gtmEvent,
            eventExtra: '',
        }
        setDatalayer(event);
    }

    newsLetter(email) {
        let event = {
            newsletterUser: email,
            event: UTILS.events.news_letter_subscription,
        }
        setDatalayer(event);
    }

    groups(groupType, route, adults, kids) {
        const totalTravelers = parseInt(adults) + parseInt(kids);
        let event = {
            tipogrupo: groupType,
            ruta: route,
            adultos: `${adults}`,
            ninos: `${kids}`,
            viajeros: `${totalTravelers}`,
            transactionProducts: [
                {
                    name: "CLO - BOG",
                    sku: "2013-06-10",
                    category: "LTMB-HM-1",
                    quantity: "1"
                }
            ]
        }
        setDatalayer(event);
    }

    consultReservation() {
        let event = {
            eventAction: UTILS.actions.hoteles_paquetes,
            eventCategory: UTILS.categories.consulta_reserva,
            eventLabel: "localizador",
            event: UTILS.events.gtmEvent,
            eventName: UTILS.events.gtmEvent,
            eventExtra: '',
        }
        setDatalayer(event);
    }

}

export const Generic = new Generics();