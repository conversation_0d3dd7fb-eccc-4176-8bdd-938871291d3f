<template>
    <div class="col-12 col-md-6">
        <h1 class="fw-bold mt-3 mt-md-0 mb-3 mb-md-4">{{__('checkReservation.title')}}</h1>
		<div>
			<p class="font-16">{{__('checkReservation.codeReservationPackages')}}</p>
			<div class="col-12">
				{{meta}}
				<Form @submit="handleFormSubmit" method="get" action="https://viajes.tiquetesbaratos.com/ayuda/tiquetes-baratos/iniciar-sesion" ref="reservation" tag="form" id="reservation">

					<Field :validateOnInput="true" v-model="masterLocator" :name="`masterLocator`" rules="required"
						   :ref="`masterLocator`" v-slot="{ field, errors, errorMessage }">

						<label class="px-1">{{__('checkReservation.title3')}}</label>
						<input type="tel" id="masterLocator" name="masterLocator"
							   class="form-control" :class="{ 'is-invalid': errors && errors.length }"
							   v-bind="field" placeholder="Ej: 123455687" />
						<span v-if="errors[0]" class="invalid-feedback">{{ errors[0] }}</span>
					</Field>

					<Field :validateOnInput="true" v-model="mail" :name="`email`" rules="required|email"
						   :ref="`email`" v-slot="{ field, errors, errorMessage }">

						<label class="px-1 mt-4">{{__('checkReservation.title4')}}</label>
						<input type="email" id="email" name="email"
							   class="form-control" :class="{ 'is-invalid': errors && errors.length }"
							   v-bind="field" placeholder="Ej. <EMAIL>" />
						<span v-if="errors[0]" class="invalid-feedback">{{ errors[0] }}</span>
					</Field>
                    <div class="d-flex flex-column flex-md-row justify-content-between mt-3">
                        <div class="cap">
                            <div class="g-recaptcha-grupos">
                                <div class="g-recaptcha" :class="{'border-error': recaptchaToken === '' && submitCount > 0}" id="recaptcha-check-reservation" :data-sitekey="config.recaptchaKey"></div>
                            </div>
                            <p class="invalid-feedback text-left d-block mb-0" v-if="recaptchaToken === '' && submitCount > 0">
                                {{__('errors.recaptcha_error')}}
                            </p>
                        </div>
                        <div class="pt-4">
                            <button class="btn btn-blue btn-confirmation px-5 ml-auto py-10">{{__('checkReservation.btnReservation2')}}</button>
                        </div>
                    </div>
				</Form>
			</div>
		</div>
    </div>
</template>

<script>
	import { __ } from '../../../utils/helpers/translate';
	import { Form, Field, useForm } from 'vee-validate';
    import { Generic } from '../../../utils/analytics/generics.js'
    import {getWgetIdCaptcha, submitFormRc} from "../../../utils/helpers/recaptcha";

    const { meta } = useForm();

	export default {
		data() {
			return {
                config: window.__pt.settings.site || {},
				mail: "",
                recaptchaToken: "",
                masterLocator: "",
                submitCount: 0
			}
		},
		computed: {
			isValid() {
				return meta._value.valid;
			}
		},
        components: {
			Field,
			Form
		},
		mounted() {
		},
        methods: {
			handleFormSubmit(value) {
                this.submitCount += 1
                this.recaptchaToken = this.validateCaptcha()
                if(this.recaptchaToken !== '' && this.submitCount > 0){
                    Generic.consultReservation();
                    const form = document.getElementById('reservation');
                    submitFormRc(form)
                    //form.submit();
                }
			},
            validateCaptcha(){
                const wgetId = getWgetIdCaptcha("#recaptcha-check-reservation")
                const recaptchaRes = grecaptcha.getResponse(wgetId);
                if(recaptchaRes !== "") {
                    setTimeout(()=>{
                        grecaptcha.reset(wgetId)
                    }, 2000)
                }
                return recaptchaRes;
            }
		}
	}
</script>