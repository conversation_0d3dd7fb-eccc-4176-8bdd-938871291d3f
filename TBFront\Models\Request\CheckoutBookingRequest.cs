﻿using TBFront.Models.Flight.Revalidate;
using TBFront.Models.Response;

namespace TBFront.Models.Request
{
    public class CheckoutBookingRequest
    {
        public QuoteApiResponse? Quote { get; set; }
        public Customer? Customer { get; set; } 
        public RevalidateRootResponse Revalidate { get; set; } = new RevalidateRootResponse();
        public string? MasterLocatorID { get; set; }
        public string? KeyValidation { get; set; }
        public FlightDetail? FlightDetail { get; set; }
        public string? ClientIp { get; set; }
        public Dictionary<int, RevalidateOptions>? RevalidateOptions { get; set; } = new Dictionary<int, RevalidateOptions>();
        public bool IsMultipleTicket { get; set; }
        public string? FingerprintHash { get; set; }

    }

    public class RevalidateOptions {
        public string? QuoteTaskID { get; set; }
        public string? FareKey { get; set; }
        public double? ta { get; set; }
    }


    public class FlightDetail
    {
        public RouteFlightDetail? DetailReturning { get; set; }
        public RouteFlightDetail DetailStarting { get; set; }
    }

    public class RouteFlightDetail
    {
        public Detail? Detail { get; set; } = new Detail();
        public DetailFamilyFare DetailFamilyFare { get; set; } = new DetailFamilyFare();
    }

    public class FlightSegment
    {
        public GeneralInfo GeneralInfo { get; set; } = new GeneralInfo();
        public List<StopInfo> StopInfo { get; set; } = new List<StopInfo>();
    }

    public class GeneralInfo
    {
        public string AirlineLogoUri { get; set; } = string.Empty;
        public string AirlineName { get; set; } = string.Empty;
        public string AirlineCode { get; set; } = string.Empty;
        public string FlightNumber { get; set; } = string.Empty;
        public string DepartureTime { get; set; } = string.Empty;
        public string DepartureDate { get; set; } = string.Empty;
        public string ArrivalTime { get; set; } = string.Empty;
        public string ArrivalDate { get; set; } = string.Empty;
        public string Origin { get; set; } = string.Empty;
        public string Destination { get; set; } = string.Empty;
        public string FlightDuration { get; set; } = string.Empty;

        public string DepartureAirportCode { get; set; } = string.Empty;
        public string DepartureAirportName { get; set; } = string.Empty;
        public string DepartureCity { get; set; } = string.Empty;
        public string ArrivalAirportCode { get; set; } = string.Empty;
        public string ArrivalAirportName { get; set; } = string.Empty;
        public string ArrivalCity { get; set; } = string.Empty;
        public string Operator { get; set; } = string.Empty;
        public string AirEquipType { get; set; } = string.Empty;
        public string? TerminarArrival { get; set; } = string.Empty;
        public string? TerminalDeparture { get; set; } = string.Empty;
        public string DepartureAirportInformation { get; set; } = string.Empty;
        public string ArrivalAirportInformation { get; set; } = string.Empty;
        public bool ReCheckingRequired { get; set; } = false;
    }

    public class StopInfo
    {
        public string Duration { get; set; } = string.Empty;
        public string Destination { get; set; } = string.Empty;
    }

    public class DetailFamilyFare
    {

        public List<FareContent> FamilyFareContent { get; set; } = new List<FareContent>();

        public string FamilyFareName { get; set; } = string.Empty;
    }

    public class FareContent
    {
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public int Category { get; set; } = 0;
    }

    public class Detail
    {
        public string FlightDuration { get; set; } = string.Empty;
        public int Stops { get; set; }
        public List<FlightSegment> FlightSegments { get; set; } = new List<FlightSegment>();
    }

}
