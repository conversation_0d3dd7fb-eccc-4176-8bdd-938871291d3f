@import './app', './base/_variables', './_variables.scss', 'base/_core', 'components/footer', 'components/header';

.h-groups {
    background-color: #0461b2;
}

.custom-select {
    display: inline-block;
    width: 100%;
    height: calc(1.5em + .75rem + 2px);
    padding: .375rem 1.75rem .375rem .75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    vertical-align: middle;
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E") no-repeat right .75rem center/8px 10px;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: .25rem;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.c-forms {
    .form-group {
        margin-bottom: 1rem;

        .form-control {
            height: 45px;
        }

        .label-xs {
            color: var(--text-strong);
            left: 10px;
            position: absolute;
            top: -9px;
            z-index: 1;

            &:before {
                content: "";
                background-color: var(--bg-base);
                position: absolute;
                left: 0;
                right: 0;
                height: 8px;
                top: 7px;
                z-index: -1;
            }
        }
    }
}

@media (max-width: 767px) {
    .c-vg-form {
        padding: 25px 15px;
    }
}