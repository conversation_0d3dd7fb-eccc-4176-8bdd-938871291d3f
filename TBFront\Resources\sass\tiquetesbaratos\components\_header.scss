/* ------------------------------------------ HEADER ------------------------------------------ */
/*- GENERALES -*/
#header {
    i {
        display: inline-flex;
        font-size: 1.25rem;
    }

    menu {
        margin-top: 0;
        margin-bottom: 0;
        padding-left: 0;
        list-style: none;
    }

    .logged__avatar {
        width: 28px;
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, var(--bg-secondary-level2) 0%, var(--bg-primary-level2) 100%);
        border: 1px solid var(--border-secondary-subtle);
        border-radius: 50%;
        font-size: 16px;
        font-weight: 600;
        color: var(--text-secondary);
    }
}

/*- BASE MOBILE -*/
#header {
    position: relative;
    background-color: var(--bg-base);
    box-shadow: var(--shadow-100);

    .container {
        padding-left: 0;
        padding-right: 0;

        // TOP ROW
        .header__top {
            padding: 12px 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .header__logo {
                img {
                    width: 60px;
                    height: auto;
                }
            }

            .menu__btn {
                width: 48px;
                height: 48px;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 8px;

                i {
                    color: var(--text-strong);
                    &.icons-menu, &.icons-person {
                        font-size: 1.5rem;
                    }
                }

                &.active {
                    background-color: var(--bg-level2);
                }
                &--logged {
                    padding: 0;
                    border: none;
                    
                    .logged__avatar {
                        width: 40px;
                        height: 40px;
                    }
                }
            }
            .menu__window {
                position: fixed;
                z-index: 40;
                top: 0;
                left: 0;
                bottom: 0;
                right: 0;
                width: 100%;
                height: 100%;
                background-color: var(--bg-base);
                transition: transform 300ms ease-in-out;
                
                &.show {
                    transform: translateX(0);
                    overflow-y: auto;
                }
                &--left {
                    transform: translateX(-100%);
                }
                &--right {
                    transform: translateX(100%);
                }

                .menu__closeBtn {
                    padding: 16px;
                    i {
                        font-size: 1.5rem;
                    }
                }
                .menu__session {
                    position: relative;
                    padding: 48px 16px 32px;
                    margin-bottom: 24px;
                    background: -moz-linear-gradient(255deg, rgba(245, 245, 247, 0) 0%, var(--bg-secondary-level1) 100%);
                    background: -webkit-linear-gradient(255deg, rgba(245, 245, 247, 0) 0%, var(--bg-secondary-level1) 100%);
                    background: linear-gradient(255deg, rgba(245, 245, 247, 0) 0%, var(--bg-secondary-level1) 100%);

                    .menu__closeBtn {
                        position: absolute;
                        top: 0;
                        right: 0;
                    }
                    .session__title {
                        font: var(--title-sm);
                        display: flex;
                        align-items: center;
                        gap: 8px;
                        margin-bottom: 12px;
                    }
                    &--logged {
                        padding: 12px 16px;
                        display: flex;
                        align-items: center;
                        justify-content: space-between;

                        .logged__avatar {
                            flex-shrink: 0;
                            width: 48px;
                            height: 48px;
                        }
                        .session__title {
                            margin-bottom: 0;
                        }
                        .menu__closeBtn {
                            position: static;
                            padding: 12px;
                        }
                    }
                }
                .menu__navigation {
                    a, button {
                        padding: 16px 12px;
                        width: 100%;
                        display: flex;
                        align-items: center;
                        gap: 8px;
                        border-left: 4px solid transparent;
                        color: var(--text-strong);

                        &.tab {
                            font-weight: 500;
                        }
                        &.current {
                            background-color: var(--bg-level2);
                            border-color: var(--border-primary);
                            color: var(--text-main);
                            i {
                                color: var(--text-primary);
                            }
                        }
                    }
                }

                &--modal {
                    .modal__container {
                        max-height: 100%;
                        display: flex;
                        flex-direction: column;
                        overflow: hidden;

                        .modal__header {
                            padding: 20px 16px;
                            width: 100%;
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                            border-bottom: 1px solid var(--border-subtle);
                            color: var(--text-main);
                            
                            h3 {
                                font: var(--title-xs);
                                margin-bottom: 0;
                                display: flex;
                                align-items: center;
                                gap: 8px;
                            }
                            i {
                                font-size: 1.5rem;
                            }
                        }
                        .modal__menu {
                            flex-grow: 1;
                            overflow-y: auto;
        
                            .menu__link {
                                padding: 16px;
                                width: 100%;
                                display: flex;
                                align-items: center;
                                justify-content: space-between;
                                color: var(--text-strong);
                            }
                            .menu__callBtn {
                                width: 100%;
                                padding: 12px;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                gap: 4px;
                                background-color: var(--bg-primary-subtle);
                                font-weight: 500;
                                color: var(--text-link);
                            }
                        }
                    }

                }
            }

            .header__buttons {
                align-items: stretch;
                gap: 12px;

                .header__btn {
                    min-height: 100%;
                    padding: 8px;
                    font: var(--body-sm);
                    color: var(--text-main);
                    display: flex;
                    align-items: center;
                    gap: 4px;
                    border: 1px solid transparent;
                    border-radius: 8px;
                    transition: all 300ms ease-in-out;

                    i {
                        color: var(--text-strong);
                    }

                    &--login {
                        padding: 8px 12px;
                        gap: 8px;
                        border: 1px solid var(--border-strong);

                        &.active {
                            border-color: transparent;
                        }
                    }
                    &.active {
                        background-color: var(--bg-level2);
                        border-radius: 8px 8px 0 0;
                        box-shadow: var(--shadow-300);
                    }
                }
                .header__dropdown {
                    position: absolute;
                    z-index: 20;
                    right: 0;
                    top: 100%;
                    opacity: 0;
                    transform: scaleY(0);
                    transform-origin: top center;
                    transition: transform 300ms ease-in, opacity 300ms ease-in;
                    
                    &.show{
                        transform: scaleY(1);
                        opacity: 1;
                    }
                    .dropdown__container {
                        background-color: var(--bg-base);
                        box-shadow: var(--shadow-300);
                        border-radius: 8px 0 8px 8px;
                        width: max-content;
                        overflow: hidden;

                        .dropdown__item--login {
                            padding: 24px 16px;
                            width: 300px;
                            background: -moz-linear-gradient(255deg, rgba(245, 245, 247, 0) 0%, var(--bg-secondary-level1) 100%);
                            background: -webkit-linear-gradient(255deg, rgba(245, 245, 247, 0) 0%, var(--bg-secondary-level1) 100%);
                            background: linear-gradient(255deg, rgba(245, 245, 247, 0) 0%, var(--bg-secondary-level1) 100%);
                        }
                        .dropdown__link {
                            width: 100%;
                            display: flex;
                            align-items: center;
                            flex-wrap: wrap;
                            gap: 8px;
                            padding: 12px 8px;
                            font: var(--body-sm);
                            color: var(--text-strong);
                            transition: background 300ms ease-out;
                            &:hover {
                                background-color: var(--bg-level2);
                            }
                        }
                        &--phones {
                            display: grid;
                            grid-template-columns: 1fr 1fr;
                            border-radius: 8px 0 8px 8px;
                            
                            .phones__halfwidth {
                                background-color: var(--bg-level2);

                                .dropdown__link {
                                    justify-content: center;
                                    
                                    &:hover {
                                        background-color: var(--bg-level3);
                                    }
                                }

                                &:nth-of-type(even) {
                                    grid-column: 1 / 2;
                                }
                                &:nth-of-type(odd) {
                                    grid-column: 2 / 3;
                                }
                            }
                            .phones__fullwidth {
                                grid-column: 1 / 3;
                                .phones__contactAgentBtn {
                                    width: 100%;
                                    background-color: var(--bg-primary-subtle);
                                    color: var(--text-primary);
                                    font: var(--body-sm);
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    gap: 0.25rem;
                                    padding: 0.5rem;
                                    border-radius: 0 0 0.5rem 0.5rem;
                                    transition: background 300ms ease-in-out;

                                    i {
                                        font-size: 1.5rem;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        // BOTTOM ROW
        .header__bottom {
            position: relative;

            .bottom__container {
                padding: 0 16px;
                display: flex;
                overflow-x: auto;
                overflow-y: hidden;

                .bottom__tab {
                    white-space: nowrap;

                    a {
                        padding: 8px 16px;
                        display: flex;
                        align-items: center;
                        gap: 8px;
                        font: var(--body-sm-bold);
                        color: var(--text-strong);
                        border-bottom: 3px solid transparent;
                        transition: color 300ms ease-in-out;
                        position: relative;

                        &:before {
                            position: absolute;
                            bottom: -3px;
                            left: 0;
                            display: block;
                            height: 3px;
                            width: 100%;
                            transform: scaleX(0);
                            transform-origin: left;
                            content: "";
                            background-color:var(--border-strong);
                            transition: transform 350ms ease-in;
                        }
                        &.current {
                            color: var(--text-main);
                            i {
                                color: var(--text-primary);
                            }
                            &:before {
                                background-color:var(--border-primary);
                                transform: scaleX(1);
                            }
                        }
                    }
                }
            }
            .bottom__mask {
                position: absolute;
                height: 100%;
                top: 0;
                width: 85px;
                z-index: 1;

                &--left {
                    left: 0;
                    background: -moz-linear-gradient(90deg, rgba(255,255,255,1) 0%, rgba(255,255,255,0) 100%);
                    background: -webkit-linear-gradient(90deg, rgba(255,255,255,1) 0%, rgba(255,255,255,0) 100%);
                    background: linear-gradient(90deg, rgba(255,255,255,1) 0%, rgba(255,255,255,0) 100%);
                }
                &--right {
                    right: 0;
                    background: -moz-linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,1) 100%);
                    background: -webkit-linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,1) 100%);
                    background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,1) 100%);
                }
            }
        }
    }
}

/*- MEDIA QUERIES -*/
@media (min-width: 768px) {
    #header {
        .container {
            // TOP ROW
            .header__top {
                padding: 16px 0;
    
                .header__logo {
                    img {
                        width: 68px;
                    }
                }
            }
            // BOTTOM ROW
            .header__bottom {
                .bottom__container {
                    padding: 0;
                    gap: 16px;
                }
            }
        }
    }
}
@media (min-width: 992px) {
    #header {
        .container {
            // TOP ROW
            .header__top {
                .header__dropdown {
                    &--fullwidth {
                        width: 100%;
                        .dropdown__container {
                            width: 100% !important;
                            border-radius: 0 0 8px 8px !important;
                        }
                    }
                }
            }
        }
    }
}
// Cursor Interactions
@media (pointer: fine) {
    #header {
        .container {
            // TOP ROW
            .header__top {
                .header__buttons {
                    .header__btn {
                        &:hover {
                            border-color: var(--border-subtle);
                        }
                        &--login {
                            &:hover {
                                border-color: var(--border-strong-hover);
                            }
                        }
                    }
                    .phones__contactAgentBtn {
                        &:hover {
                            background-color: var(--bg-primary-level1) !important;
                        }
                    }
                }
            }
            // BOTOM ROW
            .header__bottom {
                .bottom__container {
                    .bottom__tab {
                        a:hover {
                            color: var(--text-main);
                            &:before {
                                transform: scaleX(1);
                            }
                        }
                    }
                }
            }
        }
    }
}
/* ------------------------------------------ HEADER ------------------------------------------ */

/* ------------------------------------------ MODAL: Lang & Currency ------------------------------------------ */
#modal_langcurr {
    .modal-title {
        font: var(--title-xs);
    }
    legend {
        margin-bottom: 16px;
        font: var(--title-xxs);
    }

    .modal__container {
        display: grid;
        grid-template-columns: 1fr;
        
        input[type="radio"]:checked {
            + .option {
                background: var(--bg-level2);

                i.icons-check-circle {
                    display: block;
                }
                .option__value {
                    i.option__mark {
                        display: none;
                    }
                }
            }
        }
        .option {
            margin: 0 -1rem;
            padding: 8px 12px;
            width: calc(100% + 2rem);
            height: 48px;
            display: grid;
            grid-template-columns: 28px 1fr auto;
            align-items: center;
            column-gap: 8px;
            background: var(--bg-base);
            font: var(--body-xs);
            color: var(--text-main);
            transition: background 150ms ease-out, box-shadow 150ms ease-out;

            &__flag {
                border-radius: 50%;
                object-fit: cover;
            }
            &__code {
                color: var(--text-subtle);
            }
            i.icons-check-circle {
                display: none;
                grid-column: 3 / 4;
                font-size: 24px;
                color: var(--text-success);
            }
            &__value {
                grid-column: 2 / 3;
                grid-row: 1 / 2;
                display: flex;
                justify-content: space-between;
            }
            &__mark {
                font-size: 0.75rem;
                font-weight: 400;
                color: var(--text-subtle);
            }
        }
    }
    
    @media (width < 768px) {
        padding: 0;

        .modal-dialog {
            margin: 0;
            max-height: 100%;
            max-width: none;

            .modal-content {
                max-height: 100vh;
                border-radius: 0;
            }
        }
    }
    @media (768px <= width) {
        .modal-dialog {
            max-width: 736px;
        }
        .modal-header, .modal-body {
            padding: 20px 32px;
        }
        .modal-title {
            font: var(--title-md);
        }
        legend {
            font: var(--title-xs);
        }

        .modal__container {
            grid-template-columns: repeat(3, 1fr);
            column-gap: 48px;
            row-gap: 12px;

            .option {
                margin: 0;
                width: 100%;
                height: 56px;
                grid-template-columns: auto 1fr 28px;
                border: 1px solid var(--border-subtle);
                border-radius: 8px;
                cursor: pointer;

                i.icons-check-circle {
                    grid-column: 1 / 2;
                    grid-row: 1 / 2;
                }
                &__flag, &__code {
                    grid-column: 3 / 4;
                    grid-row: 1 / 2;
                }
                &__value {
                    grid-column: 2 / 3;
                    grid-row: 1 / 2;
                    display: block;
                }

                &:hover {
                    background-color: var(--bg-level2);
                    box-shadow: var(--shadow-300);
                }
            }
        }
        .btnPrimary {
            max-width: 224px;
        }
    }
    @media (1280px <= width) {
        .modal-dialog {
            max-width: 1038px;
        }
        .modal__container {
            grid-template-columns: repeat(4, 1fr);
        }
    }
}
/* ------------------------------------------ MODAL: Lang & Currency ------------------------------------------ */

/* ------------------------------------------ MODALES: TB ------------------------------------------ */
.bg-modal-call {
    background-image: url(/assets/img/tiquetesbaratos/img-modal-call.jpg);
    background-repeat: no-repeat;
    background-size: cover;
}
.bg-modal-call .cac-2 {
    background: var(--bg-base);
    background: linear-gradient(180deg,hsla(0,0%,100%,0),#fff 79%);
    height: 150px;
}
.bg-modal-call .c-animated-call {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 99;
}
.invalid-feedback {
    display: none;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 80%;
    color: var(--text-error);
}
.bg-modal-call .cac-1 {
    background: var(--bg-base);
    border-radius: 12px 12px 0 0;
    height: 90px;
    margin: auto;
    width: 160px;
}
.bg-modal-call .c-animated-call img {
    bottom: -10px;
    display: block;
    left: 0;
    margin: auto;
    position: absolute;
    right: 0;
}
.bg-recapcha {
    background-color: var(--bg-level2);
    border-radius: 12px;
}
.navbar-tb .icons-label-right{
    font-size: 20px;
    vertical-align: middle;
}
/* ------------------------------------------ MODALES: TB ------------------------------------------ */