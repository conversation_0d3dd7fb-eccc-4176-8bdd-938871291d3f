<template>
    <div>
        <div class="row">
            <div class="col-12">

                <div class=" mb-4 text-center">
                    <span class="icons-font  icons-exclamation-triangle align-middle display-3 text-warning mt-4 mx-auto"></span>
                    <div class="">
                        <div class="row">
                            <div class="col-12 text-center h4 mt-4">
                                <p v-if="message == 'RATE_NOT_FOUND'" v-html="__('messages.RATE_NOT_FOUND')"> </p>
                                <p v-if="message == 'ROOM_NOT_FOUND'" v-html="__('messages.ROOM_NOT_FOUND')"> </p>
                                <p v-if="message == 'HOTEL_NOT_FOUND'"> {{__("messages.HOTEL_NOT_FOUND")}} </p>
                                <p v-if="message == 'VOUCHER_NOT_FOUND'"> {{__("messages.VOUCHER_NOT_FOUND")}} </p>
                                <p v-if="message == 'ERROR_DEFAULT'"> {{__("messages.ERROR_DEFAULT")}} </p>

                            </div>
                        </div>
                        <div class="row justify-content-md-center">
                            <div class="col-12 col-md-8 ">
                                <p>{{__("messages.error_description")}}</p>
                                <ul class="bullet text-left">
                                    <li class="mb-3">
                                        {{__("messages.error_list_one")}}
                                    </li>
                                    <li class="mb-3">
                                        <strong>{{__("messages.error_list_two_bold")}}:</strong> {{__("messages.error_list_two")}}
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12 my-5"><a :href="__('urls.url_go_home')" class="font-weight-bold btn-lg btn btn-primary">{{__("messages.go_home")}}</a></div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</template>

<script>




    export default {
        props: {
            quote: {
                message: ""
            }
        },
        data() {
            return {
                message: ""
            }
        },
        mounted() {
            this.message = this.getErrorName(this.quote.message)
        },
        methods: {

            getErrorName(message) {
                let error = "UNKNOWN_ERROR"
                switch (message) {
                    case "RATE_NOT_FOUND":
                        error = "RATE_NOT_FOUND";
                        break;
                    case "ROOM_NOT_FOUND":
                        error = "ROOM_NOT_FOUND";
                        break;
                    case "HOTEL_NOT_FOUND":
                        error = "HOTEL_NOT_FOUND";
                        break;
                    case "VOUCHER_NOT_FOUND":
                        error = "VOUCHER_NOT_FOUND";
                        break;
                    default:
                        error = "ERROR_DEFAULT";
                        break;
                }
                return error;
            }

        },
        components: {
        }
    }
</script>
