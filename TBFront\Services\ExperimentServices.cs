﻿using Microsoft.Extensions.Options;
using TBFront.Models.AB;
using TBFront.Options;

namespace TBFront.Services
{
    public class ExperimentServices
    {
        public ExperimentsOptions _options;
        private IHttpContextAccessor _httpContextAccessor;
        public ExperimentServices(IOptions<ExperimentsOptions> options, IHttpContextAccessor httpContextAccessor)
        {
            _options = options.Value;
            _httpContextAccessor = httpContextAccessor;
        }


        public ExperimentConfig Get(string experimentName)
        {
            var experiment = new ExperimentConfig();
            var experimentSelected = _options.Experiments.Find(x => x.Code == experimentName);

            if (experimentSelected != null && experimentSelected.Active)
            {
                experiment = SegmentExperiment(experimentSelected);
            }

            return experiment;
        }

        private ExperimentConfig SegmentExperiment(Experiment experiment)
        {
            var cookie = _httpContextAccessor.HttpContext.Request.Cookies[experiment.CookieName];
            var valid = IsValidValues(experiment.Config, cookie);
            
            if (cookie == null || !valid)
            {
                cookie = CreateSegment(experiment);
            }

            var segment = GetExperiment(cookie, experiment.Config);
            return segment;
        }

        private string CreateSegment(Experiment experiment)
        {
            var testA = experiment.Config.FirstOrDefault();
            var testB = experiment.Config.LastOrDefault();

            var random = new Random();
            var percentage = random.Next(0, 100);

            var valueCookie = testA.CookieValue;

            if (percentage <= experiment.Percentage)
            {
                valueCookie = testB.CookieValue;
            }

            var cookieOptions = new CookieOptions
            {
                Expires = DateTimeOffset.UtcNow.AddDays(experiment.Duration),
            };
            _httpContextAccessor.HttpContext.Response.Cookies.Append(experiment.CookieName, valueCookie, cookieOptions);

            return valueCookie;
        }

        private ExperimentConfig GetExperiment(string cookieValue, List<ExperimentConfig> experiment)
        {
            var segment = experiment.Find(x => x.CookieValue == cookieValue);
            return segment;
        }

        private bool IsValidValues( List<ExperimentConfig> experiment, string cookieValue = "")
        {
            return experiment.Any(x => x.CookieValue == cookieValue);
        }

    }
}
