@import './app', './base/_variables', './_variables.scss', 'base/_core', 'components/footer', 'components/header', 'components/_loading', './variables_custom';

@import 'intl-tel-input/build/css/intlTelInput.css';
@import "../../../node_modules/bootstrap/scss/_tooltip";


:root {
    --iti-path-flags-1x: url(/assets-tb/img/flags/flags.png?1952df896c6a79bb730cdfb69adf4fa0);
    --iti-path-flags-2x: url(/assets-tb/img/flags/<EMAIL>?276410ffdcdd2ff8dca2477e71874903);
    --iti-path-globe-1x: url(/assets-tb/img/flags/globe.png?405a087a9bc2c9a618e05955b4f880c7);
    --iti-path-globe-2x: url(/assets-tb/img/flags/<EMAIL>?401ac80960f5164ada6398486cac8c59);
}

.container-default-loading {
    min-height: 900px;
}

.ck-footer {
    background-color: #f1f5f9;
}

.color-cellphone {
    color: #333132 !important;
}

.ch-info-2 {
    background-color: #fff6d2;
}

.c-checkout {
    h1 {
        text-shadow: none;
    }
}

.c-summary {
    border: var(--border-width-sm) solid var(--border-subtle);
    border-radius: var(--border-radius-xs);

    .m-tooltip {
        .c-line-schedule {
            border-bottom: var(--border-width-sm) dashed var(--border-strong);
            top: 24px;
            position: relative;
            width: 100%;

            .i-circle {
                left: -2px;
                height: 10px;
                position: absolute;
                top: -5px;
                width: 10px;
                border: var(--border-width-lg) solid var(--border-subtle);
                border-radius: 50px;
                margin: auto;
            }

            .icon {
                font-size: medium !important;
                position: absolute;
                left: auto;
                right: -5px;
                top: -8px;
                width: auto;
            }
        }
    }
}

.c-forms {
    background-color: var(--bg-level1);
    border: var(--border-width-sm) solid var(--border-subtle);
    border-radius: var(--border-radius-xs);

    .form-group {
        .label-xs {
            color: var(--text-strong);
            left: 10px;
            position: absolute;
            top: -9px;
            z-index: 1;

            &:before {
                content: "";
                background-color: var(--bg-base);
                position: absolute;
                left: 0;
                right: 0;
                height: 8px;
                top: 7px;
                z-index: -1;
            }
        }
    }
}

input[type=checkbox], input[type=radio] {
    box-sizing: border-box;
    padding: 0;
}

.custom-control-input {
    z-index: -1;
    opacity: 0;
}

.c-detail-info {
    background-color: var(--bg-level1);
    border: var(--border-width-sm) solid var(--border-subtle);
    border-radius: var(--border-radius-xs);
}

.c-info-offer-1 {
    background-color: var(--bg-success-subtle);
    border: var(--border-width-sm) solid var(--border-success);
    border-radius: var(--border-radius-xs);

    .label-green {
        background-color: var(--bg-success);
        display: inline-block;
    }
}

.pax-title {
    color: var(--text-strong);
    font: var(--title-xxs);
    padding: var(--space-12);  
    background: var(--bg-level2); 
    margin-bottom: var(--space-16); 
    border-radius: var(--border-radius-xs) var(--border-radius-xs) 0 0 ;  

}

.c-payments-checkout {
    .form-control {
        height: 50px;
    }
}

.m-tooltip {
    background-color: var(--bg-base);
    border-radius: var(--border-radius-xs);
    left: 32px;
    font-size: 14px;
    position: absolute;
    top: 45px;
    width: 250px;
    z-index: 9;
}

.custom-select {
    display: inline-block;
    width: 100%;
    height: calc(1.5em + .75rem + 2px);
    padding: .375rem 1.75rem .375rem .75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    vertical-align: middle;
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E") no-repeat right .75rem center/8px 10px;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: .25rem;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.form-control {
    display: block;
    width: 100%;
    height: calc(1.5em + .75rem + 2px);
    padding: .375rem .75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: .25rem;
    transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
}


.form-control:focus {
    color: var(--text-strong);
    background-color: var(--bg-base);
    border: var(--border-width-md) solid var(--border-info);
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(57, 96, 213, 0.15);
}

.bg-yellow-05 {
    background-color: #fffbeb;
}

.form-group .iti--show-flags {
    width: 100%;
}


.container-loading {
    border: 1px solid #2196f3;
    border-radius: 10px;
    bottom: 0;
    height: 340px;
    left: 0;
    margin: auto;
    position: fixed;
    right: 0;
    top: 0;
    width: 450px
}

@media(max-width: 767px) {
    .container-loading {
        height: 325px;
        width: 95%
    }
}

.c-loader {
    border: 16px solid #f3f3f3;
    border-top: 16px solid #2196f3;
    border-radius: 50%;
    width: 120px;
    height: 120px;
    animation: e 4s linear infinite;
    position: fixed;
    top: -60px;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    z-index: 999
}

@media(max-width: 767px) {
    .c-loader {
        top: -70px
    }
}

.icon-loader {
    width: 120px;
    height: 120px;
    position: fixed;
    top: -60px;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    z-index: 999
}

@media(max-width: 767px) {
    .icon-loader {
        top: -70px
    }
}

.icon-loader:before {
    color: #2196f3;
    content: "";
    display: block;
    position: absolute;
    top: 54%;
    left: 53%;
    transform: translate(-50%, -60%);
    opacity: 0;
    animation: f 4s ease-in-out infinite;
    font-family: icomoon;
    font-size: 50px
}

@keyframes e {
    0% {
        transform: rotate(0deg)
    }

    to {
        transform: rotate(1turn)
    }
}

@keyframes f {

    0%,
    to {
        opacity: 0
    }

    50% {
        opacity: 1
    }
}

.accordion-button {
    background-color: $background-button-accordion;

    &:not(.collapsed) {
        background-color: $background-button-accordion;
    }
}

.color-yellow{
    color: rgb(239, 188, 56);
}
/** Modal detalles **/
.line-steps {
    position: relative;
    width: 5%;
    display: table-cell;
    height: 100%;
}

.circle-top1 {
    border: 2px solid #c4c4c4;
    border-radius: 50px;
    height: 10px;
    margin: auto;
    width: 10px;
    z-index: 1;
    position: absolute;
    top: 5px;
    left: 6px;
    display: block;
    background-color: #fff;
}

.line1 {
    bottom: -9px;
    left: 0;
    top: 20px;
    border: 1px dashed #c4c4c4;
    border-radius: 50px;
    margin: auto;
    position: absolute;
    right: 0;
    width: 1px;
    z-index: 0;
}
.int-xs {
    font-size: 14px !important;
}
 p {
    color: #0e213a !important;
}
.info-travel {
    p {
        display: inline-block;
    }
    .p01 {
        position: relative;
        right: -4px;
    }
}
.c-view-detail {
    color: $color_12;

    .gray {
        color: $color_13;
    }

    .circle-top {
        border: 2px solid #c4c4c4;
        border-radius: 50px;
        height: 10px;
        left: 0;
        margin: auto;
        position: absolute;
        top: 0;
        right: 0;
        width: 10px;
        z-index: 1;
    }

    .circle-bottom {
        border: 2px solid #c4c4c4;
        border-radius: 50px;
        bottom: 25px;
        height: 10px;
        left: 0;
        margin: auto;
        position: absolute;
        right: 0;
        width: 10px;
        z-index: 1;
    }

    .line {
        border: 1px dashed #c4c4c4;
        border-radius: 50px;
        bottom: 25px;
        left: 0;
        margin: auto;
        position: absolute;
        right: 0;
        top: 0;
        width: 1px;
        z-index: 0;
    }

    p {
        font-size: 14px;
    }
}
.c-capsule-light {
    background-color: #f0f9ff;
    .icon {
        top: -1px;
    }
}
.content-info {
    position: relative;
    display: inline-table;
    width: 100%;
    height: auto;
}
.email-input-wrapper {
    position: relative;
}

.input-wrapper {
    position: relative;
}

.input-wrapper input.form-control {
    margin-bottom: 0 !important;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

.suggestions-list {
    position: absolute;
    top: calc(100% - 1px);
    left: 0;
    right: 0;
    z-index: 1050;
    background: #fff;
    border: 1px solid #ccc;
    border-top: none;
    list-style: none;
    margin: 0;
    padding: 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    max-height: 200px;
    overflow-y: auto;
}

.suggestions-list li {
    padding: 8px 12px;
    cursor: pointer;
    font-size: 14px;
}

.suggestions-list li.active,
.suggestions-list li:hover {
    background-color: #f0f0f0;
}