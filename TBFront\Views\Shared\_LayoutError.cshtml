﻿@using Microsoft.Extensions.Options
@using TBFront.Options
@using TBFront.Helpers

@inject IOptions<SettingsOptions> settingOptions
@inject ViewHelper viewHelper
@inject StaticHelper staticHelper


@{
    var language = settingOptions.Value.Language;
    var isRobot = viewHelper.IsRobot();
}


<!DOCTYPE html>
<html lang="@language">
<head>
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">

    @await RenderSectionAsync("Meta", required: false)

    <meta http-equiv="content-language" content="@language" />
    <meta name="language" content="@language" />

    <script>
        (function(w, d, s, l, i) {
            w[l] = w[l] || [];
            w[l].push({
                'gtm.start': new Date().getTime(),
                event: 'gtm.js'
            });
            var base = '//www.googletagmanager.com/gtm.js?id=';
            var f = d.getElementsByTagName(s)[0],
                j = d.createElement(s),
                dl = l != 'dataLayer' ? '&l=' + l : '';
            j.async = true;
            j.src = dl === '' ? base + i : base + i + dl;
            f.parentNode.insertBefore(j, f);
        })(window, document, 'script', 'dataLayer', '@settingOptions.Value.GTM');
        var dataLayer = [];
    </script>



    @await RenderSectionAsync("Css", required: false)


</head>
<body>

    @RenderBody()


    @await RenderSectionAsync("Scripts", required: false)

</body>
</html>