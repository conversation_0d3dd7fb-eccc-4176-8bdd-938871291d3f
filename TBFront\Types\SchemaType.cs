﻿namespace TBFront.Types
{
    static class SchemaType
    {
        public const string Context = "https://schema.org";
        public const string MobileApplication = "MobileApplication";
        public const string ShoppingApplication = "ShoppingApplication";
        public const string AggregateRating = "AggregateRating";
        public const string BreadcrumbList = "BreadcrumbList";
        public const string Offer = "Offer";
        public const string Place = "Place";
        public const string Organization = "Organization";
        public const string ContactPoint = "ContactPoint";
        public const string GeoCoordinates = "GeoCoordinates";
        public const string PostalAddress = "PostalAddress";
        public const string ItemList = "ItemList";
        public const string ListItem = "ListItem";
        public const string Product = "Product";
        public const string Hotel = "Hotel";
        public const string Review = "Review";
        public const string Rating = "Rating";
        public const string Person = "Person";
        public const string Annom = "Anónimo";
        public const string Author = "Author";
        public const string TypeFAQ = "FAQPage";
        public const string TypeAns = "Answer";
        public const string TypeQs = "Question";
        public const string ItemCondition = "https://schema.org/UsedCondition";
        public const string Availability = "https://schema.org/InStock";
        public const string List = "List";
        public const string CanonicalFull = "CanonicalFull";
        public const string CanonicalDestination = "CanonicalDestination";


    }
}
