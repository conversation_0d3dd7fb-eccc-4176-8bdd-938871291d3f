﻿using TBFront.Interfaces;
using TBFront.Infrastructure.HttpService.Place.Dtos;
using TBFront.Models.Places.Request;
using TBFront.Models.Places.Response;

namespace TBFront.Infrastructure.HttpService.Place
{
    public static class PlaceServiceRegister
    {
        public static void AddPlaceServiceRegister(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddHttpClient<PlaceService>("");

            services.AddSingleton(s => configuration.GetSection("HttpPlaceServiceConfiguration").Get<PlaceConfiguration>());

            services.AddSingleton<IQueryHandlerAsync<PlaceRequest, PlaceResponse>, PlaceService>();

        }
    }
}
