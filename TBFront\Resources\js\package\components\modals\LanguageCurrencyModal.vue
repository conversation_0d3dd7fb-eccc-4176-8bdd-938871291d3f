<template>
  <div class="modal fade" id="modal_langcurr" tabindex="-1" aria-labelledby="exampleModalDetail01" aria-hidden="true">
      <div class="modal-dialog modal-dialog-scrollable modal-lg">
          <div class="modal-content px-2">
              <div class="modal-header">
                  <p class="font-24 font-poppins-medium mb-0">
                  <h2 class="modal-title">
                      {{ __('culture.title') }}
                  </h2>
                  </p>
                  <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
              </div>
              <div class="modal-body pt-0 px-4 c-view-detail">
                  <form @submit.prevent="onSubmitSettingsForm" id="settingsForm" novalidate class="modal-body">
                      <fieldset>
                          <legend>{{ __('culture.language') }}</legend>
                          <div class="modal__container">
                              <template v-for="(alternate, index) in alternates" :key="index">
                                  <input type="radio"
                                      @change="onChangeSettings('language', `${siteConfig.siteUrl}/${alternate.urlPath}`)"
                                      name="choose-language" class="d-none"
                                      :value="`${siteConfig.siteUrl}/${alternate.urlPath}`"
                                      :id="`${siteConfig.siteUrl}/${alternate.urlPath}`"
                                      :checked="alternate.name == culture?.name" />
                                  <label :for="`${siteConfig.siteUrl}/${alternate.urlPath}`" class="option"
                                      :class="{ 'option--selected': alternate.name === culture?.name }">
                                      <img class="option__flag" width="28" height="28"
                                          :src="`/assets-tb/img/header/${alternate.culture}.svg`"
                                          alt="Language flag" />
                                      <i class="icon-check-circle"></i>
                                      <span class="option__value">
                                          <span>{{ alternate.name }}</span>
                                          <i v-if="alternate.name == culture.name" class="option__mark">
                                            <br /> 
                                              {{ __('culture.active') }}
                                          </i>
                                      </span>
                                  </label>
                              </template>
                          </div>
                      </fieldset>

                      <fieldset class="mt-32">
                          <legend>{{ __('culture.currency') }}</legend>
                          <p class="d-flex align-items-center g-8 font-14">
                              <i class="icons-info font-24 font-blue d-none d-md-inline"></i>
                              {{ __('culture.price_warning') }}
                          </p>
                          <div class="modal__container">
                              <template v-for="currency in currencies" :key="currency.currencyCode">
                                  <input type="radio"
                                      @click="onChangeSettings('currency', `${siteConfig.internalAPI?.currencyChangePath}/${currency.currencyCode.toLowerCase()}`)"
                                      name="choose-currency" class="d-none"
                                      :value="`${siteConfig.internalApi?.currencyChangePath}/${currency.currencyCode.toLowerCase()}`"
                                      :id="`${siteConfig.internalApi?.currencyChangePath}/${currency.currencyCode.toLowerCase()}`"
                                      :checked="currency.currencyCode == currencyConfig.currencyCode" />
                                  <label
                                      :for="`${siteConfig.internalApi?.currencyChangePath}/${currency.currencyCode.toLowerCase()}`"
                                      class="option">
                                      <span class="option__code">{{ currency.currencyCode }}</span>
                                      <i class="icon-check-circle"></i>
                                      <span class="option__value">
                                          <span>{{ currency.name }}</span>
                                          <i v-if="currency.currencyCode == currencyConfig?.currencyCode"
                                              class="option__mark">
                                              <br /> 
                                              {{ __('culture.active') }}
                                          </i>
                                      </span>
                                  </label>
                              </template>
                          </div>
                      </fieldset>
                  </form>
              </div>
              <div class="modal-footer">
                  <button type="submit" form="settingsForm" class="btnPrimary w-100">{{ __('culture.apply')
                      }}</button>
              </div>
          </div>
      </div>
  </div>
</template>

  <script>
        import { h, reactive, toRefs } from 'vue';
  
        const culture = reactive(window.__pt.cultureData || {});
        const ptData = reactive(window.__pt.alternates || { alternates: [] });
        const siteConfig = reactive(window.__pt.settings.site || {});
        const currencies = reactive(window.__pt.currencies || []);
        const currencyConfig = reactive(window.__pt.currency || {});
        const { alternates } = toRefs(ptData);

        const settingsForm = reactive({
            redirect: "",
            language: "",
            currency: "",
            url: ""
        });

      export default {
          setup() {
            const onChangeSettings = (property, value) => {
                settingsForm[property] = value;

                const mapping = {
                    language: "redirect",
                    currency: "url"
                };

                if (mapping[property]) {
                    settingsForm[mapping[property]] = value;
                }
            },
                onSubmitSettingsForm = () => {
                    const currencyChange = settingsForm.url?.length;
                    const redirectChange = settingsForm.redirect?.length;

                    if (currencyChange || redirectChange) {
                        const query = location.search;
                        const actionUrl = currencyChange
                            ? `${settingsForm.url}${query}`
                            : `${settingsForm.redirect}${query}`;

                        const formElement = document.createElement("form");
                        formElement.setAttribute("method", "GET");
                        formElement.setAttribute("action", actionUrl);

                        if (redirectChange && !currencyChange) {
                            const queryParams = new URLSearchParams(location.search);
                            queryParams.forEach((value, key) => {
                                const hiddenField = document.createElement("input");
                                hiddenField.setAttribute("type", "hidden");
                                hiddenField.setAttribute("name", key);
                                hiddenField.setAttribute("value", value);
                                formElement.appendChild(hiddenField);
                            });
                        }

                        if (redirectChange && currencyChange) {
                            const hiddenField = document.createElement("input");
                            hiddenField.setAttribute("type", "hidden");
                            hiddenField.setAttribute("name", "redirect");
                            hiddenField.setAttribute("value", `${settingsForm.redirect}${query}`);
                            formElement.appendChild(hiddenField);
                        }

                        document.body.appendChild(formElement);
                        formElement.submit();
                    }

                    setTimeout(() => {
                        const modalElement = document.getElementById('modal_langcurr');
                        const modal = bootstrap.Modal.getInstance(modalElement); 
                        if (modal) {
                            modal.dispose();
                        }

                        
                        const newModal = new bootstrap.Modal(modalElement);
                        newModal.hide();

                        
                        modalElement.classList.remove('show');
                        modalElement.style.display = 'none';

                        
                        const backdrop = document.querySelector('.modal-backdrop');
                        if (backdrop) {
                            backdrop.parentNode.removeChild(backdrop);
                        }
                    }, 100);
                }
            const { alternates } = toRefs(ptData);
            return {
                culture,
                alternates,
                siteConfig,
                currencies,
                currencyConfig,
                settingsForm,
                onChangeSettings,
                onSubmitSettingsForm
              };
          }
        };
  </script>
  
  <style lang="scss" scoped>
      #modal_langcurr {
          .modal-title {
              font: 600 1.125rem/120% 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, system-ui;
          }

          legend {
              margin-bottom: 16px;
              font: 600 1rem/120% -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto',Arial, system-ui;
          }

          .modal__container {
              display: grid;
              grid-template-columns: 1fr;

              input[type="radio"]:checked {
                  + .option {
                      background: #F5F5F7;

                      i.icon-check-circle {
                          display: block;
                      }

                      .option__value {
                          i.option__mark {
                              display: none;
                          }
                      }
                  }
              }

              .option {
                  margin: 0 -1rem;
                  padding: 8px 12px;
                  width: calc(100% + 2rem);
                  height: 48px;
                  display: grid;
                  grid-template-columns: 28px 1fr auto;
                  align-items: center;
                  column-gap: 8px;
                  background: #fff;
                  font: 400 0.75rem/150% -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto',Arial, system-ui;
                  color: #18161C;
                  transition: background 150ms ease-out, box-shadow 150ms ease-out;

                  &__flag {
                      border-radius: 50%;
                      object-fit: cover;
                  }

                  &__code {
                      color: #696770;
                  }

                  i.icon-check-circle {
                      display: none;
                      grid-column: 3 / 4;
                      font-size: 24px;
                      color: #1C8207;
                  }

                  &__value {
                      grid-column: 2 / 3;
                      grid-row: 1 / 2;
                      display: flex;
                      justify-content: space-between;
                  }

                  &__mark {
                      font-size: 0.75rem;
                      font-weight: 400;
                      color: #696770;
                  }
              }
          }

          @media (width < 768px) {
              padding: 0;
              height: 100dvh;

              .modal-dialog {
                  margin: 0;
                  max-height: 100%;
                  max-width: none;

                  .modal-content {
                      max-height: 100vh;
                      border-radius: 0;
                  }
              }
          }

          @media (768px <= width) {
              .modal-dialog {
                  max-width: 736px;
              }

              .modal-header, .modal-body {
                  padding: 20px 32px;
              }

              .modal-title {
                  font: 600 1.5rem/120% 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, system-ui;
              }

              legend {
                  font: 600 1.125rem/120% 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, system-ui;
              }

              .modal__container {
                  grid-template-columns: repeat(3, 1fr);
                  column-gap: 48px;
                  row-gap: 12px;

                  .option {
                      margin: 0;
                      width: 100%;
                      height: 56px;
                      grid-template-columns: auto 1fr 28px;
                      border: 1px solid #E4E4E6;
                      border-radius: 8px;
                      cursor: pointer;

                      i.icon-check-circle {
                          grid-column: 1 / 2;
                          grid-row: 1 / 2;
                      }

                      &__flag, &__code {
                          grid-column: 3 / 4;
                          grid-row: 1 / 2;
                      }

                      &__value {
                          grid-column: 2 / 3;
                          grid-row: 1 / 2;
                          display: block;
                      }

                      &:hover {
                          background-color: #F5F5F7;
                          box-shadow: 0 calc(0.5px * 8) calc(1px * 8) hsla(210, 18%, 4%, 0.14), 0 calc(0.5px * 8) calc(1px * 8) hsla(210, 18%, 4%, 0.14);
                      }
                  }
              }

              .btnPrimary {
                  max-width: 224px;
              }
          }

          @media (1280px <= width) {
              .modal-dialog {
                  max-width: 1038px;
              }

              .modal__container {
                  grid-template-columns: repeat(4, 1fr);
              }
          }

          .my-32,
          .mt-32 {
              margin-top: calc(0.25rem * 8);
          }

          .g-8 {
              gap: calc(0.25rem * 2);
          }

          .font-blue {
              color: #3161f2;
          }

          .btnPrimary {
              padding: 1rem;
              display: inline-flex;
              align-items: center;
              justify-content: center;
              gap: 4px;
              border-radius: 8px;
              font: 500 1rem/150% -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto',Arial, system-ui;
              line-height: 1.2em;
              letter-spacing: 0.01em;
              transition: all 150ms ease-out;
              cursor: pointer;
              min-width: 80px;
              background: #E50072;
              border: 2px solid #E50072;
              color: #fff;
              box-shadow: 0 calc(0.5px * 2) calc(1px * 2) hsla(210, 18%, 4%, 0.14), 0 calc(0.5px * 2) calc(1px * 2) hsla(210, 18%, 4%, 0.14);

              &:hover {
                  background: #800D46;
                  border-color: #800D46;
                  color: #fff;
                  box-shadow: 0 calc(0.5px * 8) calc(1px * 8) hsla(210, 18%, 4%, 0.14), 0 calc(0.5px * 8) calc(1px * 8) hsla(210, 18%, 4%, 0.14);
              }

              &--md {
                  padding: 0.75rem 1rem;
                  font: 500 0.875rem/150% -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto',Arial, system-ui;
                  letter-spacing: 0.02em;
              }

              &--xs {
                  padding: 0.5rem 0.75rem;
                  border-radius: 4px;
                  font: 500 0.875rem/150% -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto',Arial, system-ui;
                  letter-spacing: 0.02em;
              }
          }

          .modal-footer {
              align-items: center;
              border-bottom-left-radius: calc(.3rem - 1px);
              border-bottom-right-radius: calc(.3rem - 1px);
              border-top: 1px solid #e4e4e6;
              display: flex;
              flex-wrap: wrap;
              justify-content: flex-end;
              padding: .75rem;
          }
      }
    </style>
    