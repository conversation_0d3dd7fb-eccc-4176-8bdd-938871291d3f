﻿

namespace TBFront.Models.Response
{
    public class CheckoutBookingResponse
    {
        public string UrlRedirect { get; set; }
        public string Status { get; set; }
        public string Message { get; set; }
        public string MessageInternal { get; set; }
        public TotalBooking Detail { get; set; } = new TotalBooking();
        public CheckoutFlightInfo FlightInfo { get; set; } = new CheckoutFlightInfo();
        public string Id { get; set; } = "";
        public int BookingId { get; set; }

    }


    public class TotalBooking
    {
        public bool ChangeAmount { get; set; }
        public double TotalAmount { get; set; }
        public double TotalAmountOld { get; set; }
    }

    public class CheckoutFlightInfo
    {
        public bool ChangeReservation { get; set; }
        public bool ChangeFamilyFare { get; set; }
        public List<CheckoutFamilyFare> FamilyFares { get; set; } = new List<CheckoutFamilyFare>();
    }

    public class CheckoutFamilyFare
    {
        public string FamilyFare { get; set; }
        public string FamilyFareOld { get; set; }
        public string Origin { get; set; }
        public string Destination { get; set; }
        public int FlightSegment { get; set; } // 0 vuelo ida, 1 vuelo regreso

    }
}
