﻿@using TBFront.Helpers
@using Microsoft.Extensions.Options
@using TBFront.Options;
@using TBFront.Types;
@using TBFront.Models.Meta.Metatags;
@using TBFront.Models.Request;

@inject IOptions<SettingsOptions> settingOptions
@inject IOptions<CurrencyOptions> currencyOptions

@inject ViewHelper viewHelper
@inject StaticHelper staticHelper


@inject ViewHelper _
@inject StaticHelper staticHelper
@{
    var resolution = _.GetImageResolution();
    var isMobile = resolution.Device == DeviceType.Mobile;
    var isRobot = _.IsRobot();

    var request = ViewData["request"] as FlightRequest;
    var metaTag = ViewData["MetaTag"] as MetaTag;
    var culture = ViewData["CultureData"] as Culture;

    ViewData["Page"] = "online-payment";

    ViewData["IsRobot"] = isRobot;
    ViewData["isMobile"] = isMobile;
    ViewData["Resolution"] = resolution;

}

@await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.Code}/_Header.cshtml", new ViewDataDictionary(ViewData) { { "navs", true }, { "login", isRobot } })

<div class="container c-pago py-3 pt-md-5">
    <div class="row">
        <div class="col-12 col-md-6 d-none d-md-block">
            <img class="w-100 h-100 rounded" src="/assets-tb/img/tiquetesbaratos/img-pago-en-linea.jpg" />
        </div>

        

        <div class="col-12 col-md-6">
            <pay-online></pay-online>

            <p class="mt-4 mb-2"></p>
            
            <div class="col-12 border px-md-3 px-4 px-lg-3 py-3 cf-pay">
                <div class="col-12 mb-4">
                    <div class="text-center font-18">
                        <span class="fw-bold">@_.Localizer("secure_payments")</span>
                        @* <span class="d-md-block d-sm-block d-lg-inline d-block ms-lg-3"><a data-bs-toggle="modal" data-bs-target="#modal-payform" class="a-link font-16">@_.Localizer("methodPaymentsLinkTitle") <span class="icon icon-chevron-right font-20"></span></a></span> *@
                    </div>                    
                </div>
                <div class="PaymentBanner__logos">
                    <img bn-lazy-src="https://3.cdnpt.com/images/bancos/logo-visa.svg" alt="visa card" width="50" height="16" src="https://3.cdnpt.com/images/bancos/logo-visa.svg">
                    <img bn-lazy-src="https://3.cdnpt.com/images/bancos/logo-amex.svg" alt="american express" width="26" height="26" src="https://3.cdnpt.com/images/bancos/logo-amex.svg">
                    <img bn-lazy-src="https://3.cdnpt.com/images/bancos/logo-mastercard.svg" alt="mastercard" width="34" height="26" src="https://3.cdnpt.com/images/bancos/logo-mastercard.svg">
                    <img bn-lazy-src="https://3.cdnpt.com/images/bancos/logo-dinersclub.svg" alt="Diners Club" width="78" height="20" src="https://3.cdnpt.com/images/bancos/logo-dinersclub.svg">
                    <img bn-lazy-src="https://3.cdnpt.com/images/bancos/logo-pse.svg" alt="Pagos Seguros en Línea" width="26" height="26" src="https://3.cdnpt.com/images/bancos/logo-pse.svg">
                    <img bn-lazy-src="https://3.cdnpt.com/images/bancos/logo-bancolombia.png" alt="Bancolombia" width="83" height="26" src="https://3.cdnpt.com/images/bancos/logo-bancolombia.png">
                    <img bn-lazy-src="https://3.cdnpt.com/images/bancos/logo-efecty.svg" alt="Efecty" width="26" height="26" src="https://3.cdnpt.com/images/bancos/logo-efecty.svg">
                    <img bn-lazy-src="https://3.cdnpt.com/images/bancos/logo-daviplata.svg" alt="Daviplata" width="70" height="26" src="https://3.cdnpt.com/images/bancos/logo-daviplata.svg">
                    <img bn-lazy-src="https://3.cdnpt.com/images/bancos/logo-nequi.svg" alt="Nequi" width="70" height="26" src="https://3.cdnpt.com/images/bancos/logo-nequi.svg">
                </div>
                <div class="text-center font-18 mt-3 fw-bold">
                    <span class="d-md-block d-sm-block d-lg-inline d-block ms-lg-3"><a data-bs-toggle="modal" data-bs-target="#modal-payform" class="a-link font-16">@_.Localizer("check_methods_pay") <span class="icon icon-chevron-right font-20"></span></a></span>
                </div>
            </div>
            <small class="lh-sm d-block mt-2">@_.Localizer("payment_option_depend")</small>

           
            
            
            

            <form id="AntiForgeryToken">
                @Html.AntiForgeryToken()
            </form>
        </div>
    </div>
</div>
<modal-check-payment-methods></modal-check-payment-methods>
<loader-page></loader-page>
<div>
    @await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.Code}/_Footer.cshtml", new ViewDataDictionary(ViewData) { { "newsletter", false } })
</div>

@section Css {
    <link type="text/css" rel="stylesheet"
          href="@staticHelper.GetVersion($"/{settingOptions.Value.SiteName}/css/payonline.css", settingOptions.Value.Assets)">
    <style>
        .PaymentBanner__logos {
            align-items: center;
            display: flex;
            flex-wrap: wrap;
            gap: 2rem;
            justify-content: center;
            row-gap: 1rem;
        }
    </style>
}


@section Meta {
    @await Html.PartialAsync("_MetaMain", new ViewDataDictionary(ViewData) { { "Meta", ViewData["MetaTag"] } })
}

@section ScriptsPriority {
}


@section Scripts {
    <script>
        window.__pt.data = @Json.Serialize(request);
         window.__pt.cultureData = @Json.Serialize((ViewData["CultureData"]));
        window.__pt.exchange = @Json.Serialize((ViewData["Exchange"]));
        window.__pt.alternates = @Json.Serialize((ViewData["Alternates"]));
        window.__pt.currency = @Json.Serialize((ViewData["CurrencyData"]));
        window.__pt.currencies = @Json.Serialize((currencyOptions.Value.Currencies));
    </script>

    <script src="@staticHelper.GetVersion("/js/vue/app.js", settingOptions.Value.Assets)"></script>
}