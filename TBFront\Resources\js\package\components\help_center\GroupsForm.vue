<template>
	<div class="col-12 col-md-12 c-vg-form border rounded mt-5">
		<p>{{__('groupsForm.mainMessage')}}</p>
		<!-- start forms 01 -->
		<form method="post">
			<!--<input type="hidden" name="********************************" value="1">-->
			<div class="c-forms py-3 mb-3">
				<div class="row">
					<div class="col-12 col-md-6 px-2 mb-3">
						<div class="form-group position-relative">
							<label class="font-12 px-1 label-xs">{{__('groupsForm.origin')}}</label>
							<input type="text" class="form-control" :class="{'is-invalid': errors.origin && submitCount > 0}" :placeholder="__('groupsForm.originPlaceholder')" v-model="origin">
							<div class="invalid-feedback text-left">
								{{__(`errors.${errors.origin}`)}}
							</div>
						</div>
					</div>
					<div class="col-12 col-md-6 px-2 mb-3">
						<div class="form-group position-relative">
							<label class="font-12 px-1 label-xs">{{__('groupsForm.destination')}}</label>
							<input type="text" class="form-control" :class="{'is-invalid': errors.destination && submitCount > 0}" :placeholder="__('groupsForm.destinationPlaceholder')" v-model="destination">
							<div class="invalid-feedback text-left">
								{{__(`errors.${errors.destination}`)}}
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-12 px-2 mb-3">
						<div class="form-group position-relative">
							<label class="font-12 px-1 label-xs">{{__('groupsForm.travelDate')}}</label>
							<input type="text" id="calendar" class="form-control open-datepicker" :class="{'is-invalid': (!getDates.start || !getDates.end) && submitCount > 0}" @change="datepicker('groups')">
							<input hidden="" type="text">
							<div class="invalid-feedback text-left" v-if="!getDates.start || !getDates.end">
								{{__(`errors.error_dates`)}}
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-6 col-md-3 px-2 mb-3 mb-md-0">
						<div class="form-group position-relative">
							<label class="font-12 px-1 label-xs">{{__('groupsForm.adults')}}</label>
							<input type="number" class="form-control" :class="{'is-invalid': errors.adults && submitCount > 0}" v-model="adults" min="1">
							<div class="invalid-feedback text-left">
								{{__(`errors.${errors.adults}`)}}
							</div>
						</div>
					</div>
					<div class="col-6 col-md-3 px-2 mb-3 mb-md-0">
						<div class="form-group position-relative">
							<label class="font-12 px-1 label-xs">{{__('groupsForm.kids')}}</label>
							<input type="number" class="form-control" :class="{'is-invalid': errors.kids && submitCount > 0}" min="0" v-model="kids">
							<div class="invalid-feedback text-left">
								{{__(`errors.${errors.kids}`)}}
							</div>
						</div>
					</div>
					<div class="col-12 col-md-6 px-2">
						<div class="form-group position-relative">
							<label class="font-12 px-1 label-xs">{{__('groupsForm.groupType')}}</label>
							<select class="form-control custom-select" :class="{'is-invalid': errors.groupType && submitCount > 0}" v-model="groupType">
								<option value="Familia" selected="selected">{{__('groupsForm.family')}}</option>
								<option value="Colegios">{{__('groupsForm.schools')}}</option>
								<option value="Universidades">{{__('groupsForm.universities')}}</option>
								<option value="Convenciones">{{__('groupsForm.conventions')}}</option>
								<option value="Artistico">{{__('groupsForm.artistic')}}</option>
								<option value="Deportivo">{{__('groupsForm.sports')}}</option>
								<option value="Otros">{{__('groupsForm.others')}}</option>
							</select>
							<div class="invalid-feedback text-left">
								{{__(`errors.${errors.groupType}`)}}
							</div>
						</div>
					</div>
					<div class="col-12 col-md-12 px-2">
						<div class="form-group position-relative">
							<label class="font-12 px-1 label-xs">{{__('groupsForm.others')}}:</label>
							<input type="text" class="form-control" :class="{'is-invalid': errors.otherGroup && submitCount > 0}" v-if="groupType === 'Otros'" v-model="otherGroup">
							<div class="invalid-feedback text-left">
								{{__(`errors.${errors.otherGroup}`)}}
							</div>
						</div>
					</div>
					<div class="col-12 col-md-4 pr-0">
						<div class="custom-control custom-checkbox py-1 d-flex">
							<input id="chek_hotel" type="checkbox" class="custom-control-input me-2" v-model="needHotel">
							<label class="custom-control-label" for="chek_hotel">
								<span class="c-text font-12 position-t-2 ml-1">
									{{__('groupsForm.needHotel')}}
								</span>
							</label>
						</div>
					</div>
					<div class="col-12 col-md-4 pr-0">
						<div class="custom-control custom-checkbox py-1 d-flex">
							<input id="chek_traslados" type="checkbox" class="custom-control-input me-2" v-model="needShuttle">
							<label class="custom-control-label" for="chek_traslados">
								<span class="c-text font-12 position-t-2 ml-1">{{__('groupsForm.needShuttle')}}</span>
							</label>
						</div>
					</div>
					<div class="col-12 col-md-4 pr-0">
						<div class="custom-control custom-checkbox py-1 d-flex">
							<input id="chek_auto" type="checkbox" class="custom-control-input me-2" v-model="needCar">
							<label class="custom-control-label" for="chek_auto">
								<span class="c-text font-12 position-t-2 ml-1">{{__('groupsForm.needCar')}}</span>
							</label>
						</div>
					</div>
					<div class="col-12">
						<hr>
					</div>
					<div class="col-12 mt-3">
						<h3>{{__('groupsForm.contact')}}</h3>
					</div>
					<div class="col-12 col-md-6 px-2 mb-3">
						<div class="form-group position-relative">
							<label class="font-12 px-1 label-xs">{{__('groupsForm.full_name')}}</label>
							<input type="text" class="form-control" :class="{'is-invalid': errors.name && submitCount > 0}" v-model="name">
							<div class="invalid-feedback text-left">
								{{__(`errors.${errors.name}`)}}
							</div>
						</div>
					</div>
					<div class="col-12 col-md-6 px-2 mb-3">
						<div class="form-group position-relative">
							<label class="font-12 px-1 label-xs">{{__('groupsForm.email')}}</label>
							<input type="email" class="form-control" :class="{'is-invalid': errors.email && submitCount > 0}" :placeholder="__('groupsForm.email_placeholder')" v-model="email">
							<div class="invalid-feedback text-left">
								{{__(`errors.${errors.email}`)}}
							</div>
						</div>
					</div>
					<div class="col-12 col-md-6 px-2 mb-3">
						<div class="form-group position-relative">
							<label class="font-12 px-1 label-xs">{{__('groupsForm.phone')}}</label>
							<input type="text" class="form-control" :class="{'is-invalid': errors.phone && submitCount > 0}" v-model="phone">
							<div class="invalid-feedback text-left">
								{{__(`errors.${errors.phone}`)}}
							</div>
						</div>
					</div>
					<div class="col-12 px-2 mb-3">
						<div class="form-group position-relative">
							<label class="font-12 px-1 label-xs">{{__('groupsForm.observations')}}</label>
							<textarea rows="4" cols="50" class="form-control h-auto" :class="{'is-invalid': errors.observations && submitCount > 0}" v-model="observations"></textarea>
						</div>
					</div>
					<div class="d-flex flex-column flex-md-row justify-content-between px-2">
						<div class="cap">
							<div class="g-recaptcha-grupos">
								<div class="g-recaptcha" :class="{'border-error': errorCaptcha}" id="recaptcha" :data-sitekey="config.recaptchaKey"></div>
							</div>
							<p class="invalid-feedback text-left d-block mb-0" v-if="errorCaptcha && submitCount > 0">
								{{__('errors.recaptcha_error')}}
							</p>
						</div>
						<div>
							<button class="btn btn-primary d-flex ms-auto px-5 py-2 mt-4" type="button" @click="submit">{{__('groupsForm.send')}}</button>
						</div>
					</div>
				</div>
			</div>
		</form>
	</div>

	<!-- Modal -->
	<div class="modal fade" id="group-form-success" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-dialog-centered">
			<div class="modal-content">
				<div class="modal-header bg-blue">
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				</div>
				<div class="modal-body d-flex flex-column justify-content-center align-items-center">
					<i v-if="submitError" class="icons icon-warning text-warning font-60"></i>
					<i v-else class="icons icon-check-circle text-success font-60"></i>
					<p>{{submitError ? __('contactForm.error') : __('contactForm.success')}}</p>
				</div>
			</div>
		</div>
	</div>
	<LoaderFullPage :show="submitting" />
</template>

<script setup>
	import { storeToRefs } from 'pinia';
	import { onMounted, ref } from 'vue';
	import { useForm } from 'vee-validate';
	import * as yup from 'yup';

	import { datepicker } from '../../../utils/helpers';
	import { apiRequestService } from '../../../utils/http';
	import { useCalendarStore } from '../../stores/calendar';
	import { methods } from '../../../constants';
	import { Generic } from '../../../utils/analytics/generics.js'
	import { Logger } from '../../../utils/helpers/logger';

	const phoneRegExp = /^((\\+[1-9]{1,4}[ \\-]*)|(\\([0-9]{2,3}\\)[ \\-]*)|([0-9]{2,4})[ \\-]*)*?[0-9]{3,4}?[ \\-]*[0-9]{3,4}?$/;
	const config = window.__pt.settings.site || {};

	const calendarStore = useCalendarStore();
	const { getDates } = storeToRefs(calendarStore);

	const { handleSubmit, errors, submitCount, defineField, resetForm } = useForm({
		validationSchema: yup.object({
			origin: yup.string().required(),
			destination: yup.string().required(),
			adults: yup.number().typeError("must be a number").integer().positive().min(1).required(),
			kids: yup.number().typeError("must be a number").integer().positive().min(0).optional(),
			groupType: yup.string().required(),
			name: yup.string().required(),
			email: yup.string().email().required(),
			phone: yup.string().required().matches(phoneRegExp, 'phone number is not valid'),
			observations: yup.string(),
			otherGroup: yup.string().when("groupType", ([groupType], schema) => {
				if (groupType === "Otros") {
					return schema.required();
				}
			})
		}),
		initialValues: {
			groupType: "Familia"
		}
	});

	const [origin] = defineField('origin');
	const [destination] = defineField('destination');
	const [adults] = defineField('adults');
	const [kids] = defineField('kids');
	const [groupType] = defineField('groupType');
	const [name] = defineField('name');
	const [email] = defineField('email');
	const [phone] = defineField('phone');
	const [observations] = defineField('observations');
	const [otherGroup] = defineField('otherGroup');

	const needHotel = ref(false);
	const needShuttle = ref(false);
	const needCar = ref(false);
	const submitError = ref(true);
	const submitting = ref(false);
	const errorCaptcha = ref(false);

	const validateCaptcha = () => {
		const recaptchaRes = grecaptcha.getResponse();
		if (!recaptchaRes) {
			errorCaptcha.value = true;
			submitError.value = true;
		} else {
			errorCaptcha.value = false;
		}

		return recaptchaRes;
	};

	const submit = handleSubmit(async values => {
		submitting.value = true;
		const params = { ...values, needHotel: needHotel.value, needShuttle: needShuttle.value, needCar: needCar.value, outboundDate: getDates.value.start, returningDate: getDates.value.end };
		const modal = new bootstrap.Modal(document.getElementById('group-form-success'), null);

		try {
			const token = validateCaptcha();
			if (true && getDates.value.start && getDates.value.end) {
				const resource = { uri: config.formsConfiguration.pathGroups, method: methods.POST };
				params.recaptchaToken = token;
				const response = await apiRequestService(resource, {}, params);

				if (response && response.data) {
					submitError.value = false;
					Generic.groups(groupType.value, origin.value + " - " + destination.value, adults.value, kids.value);
					resetForm();
				} else {
					submitError.value = true;
				}
			}
		} catch (e) {
			Logger.error(e);
			errorCaptcha.value = false;
		} finally {
			submitting.value = false;
			if (getDates.value.start && getDates.value.end) {
				modal.show();
			}
		}
	}, () => {
		validateCaptcha();
	});

	onMounted(() => {
		datepicker("groups");
	});
</script>
<style lang="scss">
	.c-forms {
		.container.show {
			left: 0 !important;
			right: 0 !important;
			background: red !important;
		}
	}

	.border-error {
		border: 1px solid #DC3545;
	}
</style>