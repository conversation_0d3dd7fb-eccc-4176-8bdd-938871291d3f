﻿using TBFront.Models.Response;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace TBFront.Models.Response
{

    public class AuthTokenResponse
    {
        public AuthTokenBodyResponse AuthenticationResult { get; set; }

        public string Message { get; set; }

        public AuthTokenResponse() {
            AuthenticationResult = new AuthTokenBodyResponse();
        }

    }
    public class AuthTokenBodyResponse
    {
        public string? Token { get; set; }
        public string? AccessToken { get; set; }
        public string? RefreshToken { get; set; }
        public int ExpireIn { get; set; }
        public string? TokenType { get; set; }

    }
}
