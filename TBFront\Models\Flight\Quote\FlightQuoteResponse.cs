﻿using System.Text.Json.Serialization;
using TBFront.Models.BookFlight;
using TBFront.Models.Common;

namespace TBFront.Models.Flight.Quote
{

    public class FlightQuoteResponse
    {
        public FlightResponse Response { get; set; }
        public bool Success { get; set; }
    }


    #region Flights
    public class Flight
    {
        public int ID { get; set; }
        public List<Segment> Segments { get; set; }
        public AirportLocation Arrival { get; set; }
        public AirportLocation Departure { get; set; }
        public string? ArrivalTerminal { get; set; } = string.Empty;
        public string? DepartureTerminal { get; set; } = string.Empty;
        public DateTime DepartureTime { get; set; }
        public DateTime ArrivalTime { get; set; }
        public IEnumerable<string> FlightNumbers { get; set; }
        public int Duration { get; set; }
        public Dictionary<string, Connection>? Connections { get; set; } = new Dictionary<string, Connection>();
        public ValidatingCarrier ValidatingCarrier { get; set; }
        public int Engine { get; set; }
        public string? ShortDepartureTime { get; set; }
        public string? PromotionalCode { get; set; } = "";
        public bool IsMultipleAirline { get; set; } = false;
    }

    public class AirportLocation
    {
        public string? Code { get; set; } = string.Empty;
        public string? Date { get; set; } = string.Empty;
        public string? Time { get; set; } = string.Empty;
        public string? AirportCode { get; set; } = string.Empty;
        public string? Terminal { get; set; } = string.Empty;
        public string? Country { get; set; } = string.Empty;
        public string? Name { get; set; } = string.Empty;

    }

    public class LocationCode
    {
        public string? Code { get; set; }
        public string? Name { get; set; } = string.Empty;
    }

    public class Connection
    {
        public LocationCode LocationCode { get; set; }
        public DateTime ArrivalTime { get; set; }
        public DateTime DepartureTime { get; set; }
        public int Duration { get; set; }
    }


    public class Equipment
    {
        public string AirEquipType { get; set; }
        public bool ChangeofGauge { get; set; }
    }

    public class Airline
    {
        public string? Code { get; set; } = string.Empty;
        public string? Name { get; set; } = string.Empty;
    }

    public class IntermediatePoint
    {
        public LocationCode LocationCode { get; set; }
        public DateTime ArrivalTime { get; set; }
        public DateTime DepartureTime { get; set; }
        public int Duration { get; set; }
    }

    public class Segment
    {
        public int ID { get; set; }
        public AirportLocation Arrival { get; set; } = new AirportLocation();
        public AirportLocation Departure { get; set; } = new AirportLocation();
        public DateTime ArrivalTime { get; set; }
        public DateTime DepartureTime { get; set; }
        public string? ArrivalTerminal { get; set; } = string.Empty;
        public string? DepartureTerminal { get; set; } = string.Empty;
        public List<IntermediatePoint>? IntermediatePoints { get; set; } = new List<IntermediatePoint>();
        public Airline OperatingAirline { get; set; } = new Airline();
        public Airline MarketingAirline { get; set; } = new Airline();
        public string? FlightNumber { get; set; }
        public int FlightSegmentIndex { get; set; }
        public List<Equipment> Equipment { get; set; } = new List<Equipment>();
        public int Duration { get; set; }
        public bool ReCheckingRequired { get; set; }
    }

    public class ValidatingCarrier
    {
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string? AirlineLogoUrl { get; set; } = string.Empty;

    }
    #endregion
    #region Recommendations
    public class Recommendation
    {
        public int ID { get; set; }
        public Dictionary<string, int> Flights { get; set; }
        public ValidatingCarrier ValidatingCarrier { get; set; }
        public int Engine { get; set; }
        public List<FareCombinable> FareCombinables { get; set; }
        public int FlightConnectionSetting { get; set; }
        public string? PromotionalCode { get; set; }
    }
    public class FamilyFare
    {
        public string Code { get; set; }
        public int Priority { get; set; }
        public string AirlineCode { get; set; }
    }

    public class FareCombinable
    {
        public Dictionary<string, int> FareByLeg { get; set; }
        public TotalItem TotalFare { get; set; }
        public TotalItem TotalCost { get; set; }
        public Dictionary<string, List<Rate>>? Revenues { get; set; } = new Dictionary<string, List<Rate>>();
        public FamilyFare FamilyFare { get; set; }
        public string FareKey { get; set; }
        public int? NegotiatedFareId { get; set; } = 0;
        public int Engine { get; set; }
        public Dictionary<string, FareCombinableLegInfo> LegInfo { get; set; }
    }
    public class LegInfo
    {
        public Dictionary<string, FareCombinableLegInfo> Leg { get; set; }

    }
    public class FareCombinableLegInfo
    {
        public int Engine { get; set; }
        public int FlightConnectionSetting { get; set; }

    }

    public class Rate
    {
        public double Amount { get; set; }
        public string Currency { get; set; }
        public int Type { get; set; }
        public string? Code { get; set; }
    }
    public class TotalItem
    {
        public double Amount { get; set; }
        public string Currency { get; set; }
    }


    #endregion

    #region FaresByLeg
    public class Fares
    {
        public Dictionary<string, string>? FareBasisPerSegment { get; set; }
        public Dictionary<string, string>? ClassOfServicePerSegment { get; set; }
        public Dictionary<string, string>? FamilyFarePerSegment { get; set; }
        public Dictionary<string, string>? ProductClassPerSegment { get; set; }
        public Dictionary<string, string>? BrandIDPerSegment { get; set; }
        public Dictionary<string, string>? BundlePerSegment { get; set; }
    }

    public class PassengerFareBreakDown
    {
        public int PassengerType { get; set; }
        public int PassengerTypeProvider { get; set; }
        public string PassengerCode { get; set; } = string.Empty;
        public int Quantity { get; set; }
        public Dictionary<string, Fares> LegsFares { get; set; }
        public ServiceCharges ServiceCharges { get; set; }
        public int Age { get; set; }
    }



    public class Charges
    {
        public double Amount { get; set; }
        public string? Currency { get; set; }
        public string? Code { get; set; }
        public int Type { get; set; }
    }


    public class FareLeg
    {
        public int ID { get; set; }
        public RevenueCharges? RevenueCharges { get; set; } = new RevenueCharges(); 
        public List<PassengerFareBreakDown> PassengerFareBreakDown { get; set; }
        public TotalItem TotalFare { get; set; }
        public TotalItem TotalCost { get; set; }
        public FamilyFare FamilyFare { get; set; }
        public FareLegInfo LegInfo { get; set; }
    }
    public class FareLegInfo
    {
        public int Engine { get; set; }
        public string? OwnerCode { get; set; } = string.Empty;
        public string? OfferItemID { get; set; } = string.Empty;
        public string? OfferID { get; set; } = string.Empty;

    }


    public class RevenueCharges
    {

        [JsonPropertyName("EXTRAFEE")]
        public List<Charges> EXTRAFEE { get; set; }

        [JsonPropertyName("COMMISSION")]
        public List<Charges> COMMISSION { get; set; }

        [JsonPropertyName("MARGIN")]
        public List<Charges> MARGIN { get; set; }

        [JsonPropertyName("EXTRASERVICES")]
        public List<Charges> EXTRASERVICES { get; set; }

        public RevenueCharges()
        {
            COMMISSION = new List<Charges>();
            EXTRAFEE = new List<Charges>();
            MARGIN = new List<Charges>();
            EXTRASERVICES = new List<Charges>();

        }
    }

    public class ServiceCharges
    {
        [JsonPropertyName("PROVIDER_BASE")]
        public List<Charges>? ProviderBase { get; set; }
        //public List<Charges> PROVIDER_BASE { get; set; }


        [JsonPropertyName("PROVIDER_EQUIV")]
        public List<Charges>? ProviderEquiv { get; set; }
        //public List<Charges> PROVIDER_EQUIV { get; set; }


        [JsonPropertyName("PROVIDER_TAX")]
        public List<Charges>? ProviderTax { get; set; }
        //public List<Charges> PROVIDER_TAX { get; set; }


        public ServiceCharges()
        {
            ProviderBase = new List<Charges>();
            ProviderEquiv = new List<Charges>();
            ProviderTax = new List<Charges>();
        }

    }

    #endregion




    public class FlightCharge
    {
        public double Amount;
        public string Currency;
    }

    public class FlightCodeCharge
    {
        public string Code;
        public FlightCharge Charge;
    }


    public class FlightFare
    {
        public int ID { get; set; }
        public Dictionary<string, List<FlightCodeCharge>>? RevenueCharges { get; set; }
        public List<PassengerFareBreakDown> PassengerFareBreakDown { get; set; }
        public string FamilyFareCode { get; set; }
        public string FareKey { get; set; }
        public FlightCharge TotalFare { get; set; }

    }
}
