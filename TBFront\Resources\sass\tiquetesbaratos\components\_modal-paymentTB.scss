.modal-payform i{
  background-size: 40px;
  background-repeat: no-repeat;
  width: 50px;
  height: 50px;
  margin-left: 1rem;
  margin-right: 1rem;
  background-position: center;

  @media screen and (max-width: 500px) {
    margin-left: 0.5rem;
    margin-right: 0.5rem;
  }
}

.logos-ba-banner {
  flex-flow: wrap;
  gap: 70px;

  @media screen and (max-width: 1024px) {
    gap: 20px;
  }

  @media screen and (max-width: 500px) {
    gap: 20px;
  }
}

.gray-black-color {
 color: #4C4C4C;
}

.content-banks-brand {
  .icon-bank-brand {
    width: 60px;
    height: 60px;
    background-position: center;
  }

  .ptw-icon-multiple-co {
    width: 130px !important;
    height: 100px !important;

    @media screen and (max-width: 769px) {
     width: 200px !important;
    }
  }

  .ptw-icon-bancolombia {
    width: 130px !important;
    height: 70px !important;
  }
}

.logos-ba-banner i {
  background-size: 45px;
  background-repeat: no-repeat;
  width: 50px;
  height: 50px;
  background-position: center;
}

.logos-ba-banner .large {
  background-size: 130px !important;
  background-repeat: no-repeat;
  width: 130px;
  height: 50px;
}

.icon-brand-visa{
  background-position-y: 11px !important;
}


@include media-breakpoint-up(md) {


}
