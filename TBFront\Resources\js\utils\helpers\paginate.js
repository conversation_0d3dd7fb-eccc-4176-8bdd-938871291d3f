
export const buildPaginator = (total, lenght, currentPage, itemsByPage) => {
    const pointBreak = 4;
    let pagination = {
        currentPage: currentPage
    };
    pagination.totalPages = Math.ceil(total / itemsByPage);
    pagination.buttons = Array.from({ length: pagination.totalPages }, (_, i) => i + 1);
    pagination.startPage = 1 + ((pagination.currentPage-1) * itemsByPage);
    pagination.endPage = (pagination.startPage + (lenght)) - 1;

    if (pagination.endPage > pagination.total) {
        pagination.endPage = pagination.total;
    }

    if (pagination.totalPages <= pointBreak) {
        pagination.render = pagination.buttons;
        return pagination;
    }

    pagination.render = [];

    if (pagination.currentPage < pointBreak - 1) {
        pagination.render = pagination.buttons.slice(0, pointBreak);
        pagination.render.push("...");
        pagination.render.push(pagination.totalPages);
    } else if (pagination.currentPage > pagination.totalPages - pointBreak) {
        pagination.render.push(pagination.buttons[0]);
        pagination.render.push("...");
        pagination.render.push(...pagination.buttons.slice(pagination.totalPages - pointBreak, pagination.totalPages));
    } else {
        pagination.render.push(pagination.buttons[0]);
        pagination.render.push("...");
        pagination.render.push(...pagination.buttons.slice(pagination.currentPage - 1, pagination.currentPage + 2));
        pagination.render.push("...");
        pagination.render.push(pagination.totalPages);
    }

    return pagination;
}
