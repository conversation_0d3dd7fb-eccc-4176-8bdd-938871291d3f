<template>
	<div :id="configInfo(airline.departure?.code?.trim() || airline.returning?.code?.trim()).airlineName+iFlight" class="row py-0 line-flights mb-3 mb-md-0">
		<div :id="iFlight" :class="['row', 'm-0','p-0','col-lg-'+(12-airline.families.length)]">
			<div :class="[ 'col-6', 'col-md-3','col-lg-5', 'l-w', 'rounded-left', 'd-flex', 'align-items-center', 'line-bottom']">
				<div class="row w-100 mx-0">
					<div class="col-4 col-lg-3 px-0 ps-lg-2">
						<p class="color-time py-2 text-center">{{ flight.departure?.time }}</p>
					</div>
					<div class="col-4 col-lg-6  px-0 px-lg-3 pe-lg-0">
						<div class="row m-auto c-line-f position-relative">
							<div class="col-12 text-center hide-md d-none d-md-block">
								<span class="font-12">{{flight.flightDuration}}</span>
							</div>
							<div class="col-12 c-line-schedule">
								<span class="i-circle bg-white hide-md d-none d-md-block"></span>
								<span class="icon icon-plane-right font-16"></span>
							</div>
						</div>
					</div>
					<div class="col-4 col-lg-3 px-0 px-lg-3">
						<p class="color-time py-2 text-center">{{flight.arrival?.time}}</p>
					</div>
				</div>
			</div>
            <!-- Detalle mobile -->
            <div :class="['col-6', 'col-md-9', 'col-lg-2', 'pl-md-0', 'l-w', 'py-1', 'py-lg-2', 'd-flex', 'align-items-center', 'line-bottom', 'hide-lg']">
                <p class="py-2 view-detail w-100 text-right pr-2">
                    <button @click="getFlightDetails({name: airline.departure?.name?.trim() || airline.returning?.name?.trim(),
							image: airline.departure?.image?.trim() || airline.returning?.image?.trim(),
							flight: flight, group: airline, fare: flight.fares[0]}, 'FLIGHT_DETAIL')"
							class="btn-none font-18 fw-bold pr-4 pr-md-0 pr-lg-4 pr-md-0 pr-lg-0 cp-auto cta-link">
                        {{ (flight.stops >= 1) ? flight.stops: '' }} {{ __(`messages.stops_${flight.stops >=2 ? 2 : flight.stops}`) }}
                    </button>
                    <!-- <span class="icon icon-expand font-20 position-t-3"></span> -->
                    <span class="icon icon-keyboard-right font-24 hide-md hide-lg position-t-5"></span>
                </p>
            </div>
			<!-- Detalle desktop y tablet -->
			<div :class="['col-6', 'col-md-6','col-lg-3', 'pl-md-0', 'l-w', 'py-1', 'py-lg-2', 'd-flex', 'align-items-center', 'line-bottom', 'hide-xs', 'hide-md']">
				<p id="viewDetail" class="py-2 view-detail w-100 text-end pe-2">
					<button @click="getFlightDetails({name: airline.departure?.name?.trim() || airline.returning?.name?.trim(),
							image: airline.departure?.image?.trim() || airline.returning?.image?.trim(),
							flight: flight, group: airline, fare: flight.fares[0]}, 'FLIGHT_DETAIL')"
							class="btn-none cta-link fw-bold pe-4 pe-md-0 pe-lg-0 cp-auto">
						{{ (flight.stops >= 1) ? flight.stops: '' }} {{ __(`messages.stops_${flight.stops >=2 ? 2 : flight.stops }`) }}
					</button>
				</p>
			</div>
            <!-- información del vuelo desktop y tablet -->
            <div class="col-lg-4 hide-xs hide-md-xs py-3 py-lg-1 d-flex align-items-center line-bottom">
                <p class="py-2 w-100 text-center ps-md-2 ps-lg-5 flight-number">{{ __("messages.flight") }} <span>{{flight.flightNumbers[0]}}</span></p>
            </div>
		</div>
		<div class="d-flex w-100 col column-list-flights p-0">
			<!--ITEMS FARE-->
			<template v-for="(fare, indexfare) in formatFares(flight.fares, airline.families)">
				<template v-if="((isCheapestFlightsApplied && fare.displayAmount === airline[type]?.cheapest) && isResponsive) || (!isResponsive || !isCheapestFlightsApplied)">
					<template v-if="!isResponsive || fare.displayAmount >0">
						<div :class="[((fare.displayAmount === airline[type]?.cheapest) ? 'bg-economic' : '' ),'w-100','h-100','px-0', 'bb-white',  'bg-'+(indexfare+1), 'bw-bottom', 'py-1']">
							<div class="row">
								<div class="col-6 col-md-10 col-sm-8 hide-desk-md hide-desk pl-3 pr-0 pm-xs">
									<span class="md-info">
										<span class="icon text-top icon-info d-inline-block ps-2 font-20 "></span>
										<button @click="getFlightDetails({name: airline.departure?.name?.trim() || airline.returning?.name?.trim(),
												image: airline.departure?.image?.trim() || airline.returning?.image?.trim(),
												flight: flight, group: airline, fare: fare}, 'FLIGHT_FAMILY')"
												class="btn-none ps-1 py-1 d-inline-block position-t-2 txt-xs cp-auto">
											{{fare.fareName}}
										</button>
									</span>
								</div>
								<div v-if="fare.fareId !== 0 && fare.displayAmount > 0" class="col-lg-12 col-6 col-md-2 col-sm-4 ps-0 ps-xs-0 px-md-0 align-self-center">
									<div class="font-12 text-center">
										<div class="radio py-md-1 position-t-4 position-relative">
											<input :value="fare" v-model="flightSelected[type+'_fare']" v-on:change="selectInputFlight(flight, airline[type].token, airline, airline.quoteTokenFQS)" class="position-relative" type="radio" :name="'flight_'+type" :id="fare.fareKey + fare.fareId + flight.id + type">
											<label :roundtrip="fare?.isRoundTrip ? 'yes': 'no'" class="font-18 position-relative" :for="fare.fareKey + fare.fareId + flight.id + type">
												<span class="d-block font-14 pe-md-0 cni-rates">
													<CurrencyDisplay :amount="fare.displayAmount" :showCurrencyCode="false" />
												</span>
												<span class="d-block font-10 color-red d-none d-md-block"></span>
											</label>
										</div>
									</div>
								</div>
								<div v-else class="col-5 col-md-12 pl-0 pl-md-3 d-flex flex-row justify-content-center align-items-center flex-wrap">
									<div class="row">
										<div class="col-5 col-md-12 p-0">
											<div class="font-14 text-center">
												<div class="radio py-md-2 position-t-4">
													<div class="c-out-off">{{__(`flightList.sold_out`)}}</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
							<!--action mobile next -->
							<div class="row px-1 hide-lg d-block btn-ida"
								 v-if="(isResponsive) && flightSelected[type+'_fare'] == fare">
								<div class="col-12 px-4">
									<button @click="actionNextNexSectionMobile()" class="btn btn-primary d-block mx-auto mb-3 mt-1 font-14 py-3 w-100 px-0">
										{{__(`flightList.select_${type}_flight`)}}
									</button>
								</div>
							</div>
							<!--end action mobile next-->
						</div>
					</template>
				</template>
			</template>

			<!--<div class="c-overlay d-none"></div>-->
		</div>
	</div>
</template>
<script>
	import { storeToRefs } from "pinia";
	import { useFlightStore } from '../../../../stores/flight';
	import { useFlightUpsellStore } from '../../../../stores/flightUpsell';
	import { useFlightRevalidateStore } from "../../../../stores/flightRevalidate";
    import { getDetail, getFamilyFare, getParamsDetailFlight, getMatrix, getFilteredList } from "../../../../services/ApiFlightFrontServices";
	import { useFlightDetailStore } from "../../../../stores/flightDetail";
	import { useFlightFamilyFareStore } from "../../../../stores/flightFamilyFare";
	import { toRaw } from 'vue';
	import { List } from '../../../../../utils/analytics/flightList.js';
	import { useUserSelectionStore } from "../../../../stores/user-selection";
	import { Logger } from "../../../../../utils/helpers/logger";
    import {
        getReturningFlights,
        getFlightsBySimpleFlightQuotes,
        actionFlights, configFilter
    } from "../../../../services/fetchListService";
    import {responsiveObserver} from "../../../../../utils/helpers/responsiveObserver";
	import CurrencyDisplay from "../../../common/CurrencyDisplay.vue";
	import { useMultiTicketStore } from '../../../../stores/multiTicket';
    import { usePromotionStore } from '../../../../stores/promotion';
    import { useFlightMatrixStore } from '../../../../stores/flightMatrix';
    import { sleep } from '../../../../../utils/helpers';

    export default {
		data() {
			return {
				configSite: window.__pt.settings.site.airlineConfiguration.national ?? [],
			}
		},
		props: {
			isRoundTrip: { type: Boolean, default: false },
			submit: Function,
			flight: { type: Object, default: {} },
			airline: { type: Object, default: {} },
			type: { type: String, default: "departure" },
			iFlight: { type: Number, default: 0 }

		},
		setup() {
			const storeFlight = useFlightStore()
			const useFlightUpsell = useFlightUpsellStore();
			const flightRevalidateStore = useFlightRevalidateStore();
			const flightDetailStore = useFlightDetailStore();
			const flightFamilyFareStore = useFlightFamilyFareStore();
			const useMultiTicket = useMultiTicketStore();
			const storeFlightMatrix = useFlightMatrixStore();
            const promotionStore = usePromotionStore();

			const { getParams, getAllQuoteTokens, getReturnQuoteTokens } = storeToRefs(storeFlight); //get
            const { setFlightResponses, resetFlightResponse } = storeFlight;
			const { getTotalAmount } = storeToRefs(flightRevalidateStore);
			const { getFlightSelected } = storeToRefs(useFlightUpsell);
			const { setFlightSelected } = useFlightUpsell; //set/actions
            const { setFlightDetailResponse, activeModalDetail, setExtraData } = flightDetailStore;
			const { setFlightFamilyFareResponse } = flightFamilyFareStore;
			const userSelectionStore = useUserSelectionStore();
			const { getFiltersApplied, isCheapestFlightsApplied } = storeToRefs(userSelectionStore);
            const { getShowDetail } = storeToRefs(flightDetailStore);
			const isResponsiveRef = responsiveObserver.getResponsiveStatus();
            const { changeFilters } = userSelectionStore;
            const { setAirlinesFilters, setStopsFilters, setIsStepTwo, setCheckOutDataStepOne } = useMultiTicket;
            const { getTripMode } = storeToRefs(promotionStore);
            const { setLoading, setFlightMatrix } = storeFlightMatrix;
            return {
				getTotalAmount,
				getParams,
				getFlightSelected,
				setFlightDetailResponse,
				setFlightFamilyFareResponse,
				setFlightSelected,
				getFiltersApplied,
				isCheapestFlightsApplied,
                activeModalDetail,
                getShowDetail,
				isResponsiveRef,
				setExtraData,
                changeFilters,
                setAirlinesFilters,
				setStopsFilters,
				setIsStepTwo,
				getAllQuoteTokens,
				getReturnQuoteTokens,
				setFlightResponses,
				resetFlightResponse,
				setCheckOutDataStepOne,
				getTripMode,
				setLoading,
                setFlightMatrix
			}
		},
		computed: {
            isResponsive() {
                return this.isResponsiveRef;
            },
			flightSelected() {
				return this.getFlightSelected;
			},
			information() {
				return this.getParams
			}
		},
		methods: {
			configInfo(code) {
				return configFilter(code) ?? {}
			},
			checkResponsive() {
				if (this.currentScreen != window.innerWidth) {
					if ((window.innerWidth < 993) || this.isMobileDevice()) {
						//if (this.flightSelected.step_action == 3) this.flightSelected.step_action = 2;
						this.isResponsive = true;
					} else {
						//if (this.flightSelected.step_action == 2) this.flightSelected.step_action = 3;
						this.isResponsive = false;
					}
					this.actionFlightSelected(this.flightSelected)
					this.currentScreen = window.innerWidth; //actualizamos la pantalla
				}
			},
            isMobileDevice() {
                // Detección de dispositivos móviles excluyendo iPad y tabletas Android
                return /Android(?!.*(Tablet|tab|SCH-I800|SGH-T849|SPH-P100|SGH-T849|SHW-M180S|SHW-M180W))|webOS|iPhone|iPod|BlackBerry|IEMobile|Opera Mini|Mobile|mobile|Touch|Windows Phone|HarmonyOS/i.test(navigator.userAgent) &&
                    !/iPad|Tablet|Macintosh/i.test(navigator.userAgent);
            },
			async getFlightDetails({ name, image, flight, group, fare }, type) {
                const modalElement = document.getElementById('modalDetailNational');
                if (this.getShowDetail) return;
                this.activeModalDetail();
				const modal = new bootstrap.Modal(modalElement);
                this.setExtraData({
                    airlineLogoUri: image,
                    airlineName: name,
                    view: this.type === 'departure' ? this.__(`messages.departure_flight`) : this.__(`messages.return_flight`)
                });
                modal.show();
				this.paramsDetail = {
                    id: flight.flightId,
                    token: group.quoteToken,
					//token: ((group.departure.token ) ? group.departure.token : group.returning.token) ,
                    flightId: 0,
                    airlineLogoUri: image,
                    airlineName: name,
                    fareId: 0,
					flightType: this.type === 'departure' ? 'starting' : 'returning',
                    familyFare: fare?.fareGroup,
                    fareKey: fare?.fareKey,
				};
				let rq = getParamsDetailFlight(this.paramsDetail);

				switch ((String(type)).toUpperCase()) {
					case "FLIGHT_DETAIL":
						let responseDetail = await getDetail(rq);
						this.setFlightDetailResponse(responseDetail);
                        List.modalDetail(group.departure?.code?.trim() || group.returning?.code?.trim(), "", "", false);
						this.setFlightFamilyFareResponse({});
						break;
					case "FLIGHT_FAMILY":
						let responseFamilyFare = await getFamilyFare(rq);
                        this.setFlightFamilyFareResponse({...responseFamilyFare,params: rq});
                        List.modalDetail(group.departure?.code?.trim() || group.returning?.code?.trim(), responseFamilyFare.familyFareName, "", true);
						this.setFlightDetailResponse({});
						break;
				}
                this.activeModalDetail();
			},
			formatFares(fares, families) {
				let mFares = [];
				for (const family of families) {
                    const searchFareGroup = (code) => fares.find(
                        f => String(f.fareGroup).toUpperCase() === String(code).toUpperCase()
                    );
                    
                    let fareExist = Array.isArray(family.familyFareCode)
                        ? family.familyFareCode.map(searchFareGroup).find(Boolean)
                        : searchFareGroup(family.familyFareCode);
					if (fareExist) {
						fareExist.fareName = family.name;
						mFares.push(fareExist)
					} else {
						mFares.push({
							fareId: 0,
							priority: 0,
							fareName: family.name,
							fareGroup: String(family.familyFareCode).toUpperCase(),
							fareKey: "",
							averageAmount: 0,
							amount: 0,
							displayAmount: 0,
							engine: 0,
							fareDetails: []
						});
					}
				}
				return mFares || []
			},
			selectInputFlightMobile(step_action = this.type === 'departure' ? 3 : 4) {
				//if (this.isResponsive) {
                let flight_selected = (this.flightSelected);
                flight_selected.step_action = step_action;
                if (step_action >= 2) {
                    let elements = document.querySelector('#mobileInfo-section');

                    this.scrollToElement("#mobileInfo-section");
                } else if (step_action <= 1) {
                    flight_selected.returning_flight = null;
                    flight_selected.returning_fare = null;

                    flight_selected.departure_flight = null;
                    flight_selected.departure_fare = null;

                    this.flightSelected.departure = null;
                    this.flightSelected.returning = null;
                    if(this.isRoundTrip) t
                }
                
				if (step_action === 3) {
                    setTimeout(()=>{
						if (this.isRoundTrip) {
							List.eventListReturning();
                            const params = {
                                departureToken: this.flightSelected.departure_token,
                                flightQuoteId: this.flightSelected.departure_fare.fareKey,
                                code: this.flightSelected.departure_flight.airline?.code
                            }
                            let configuration = configFilter(this.flightSelected.departure_flight.airline?.code) || {};
                            getReturningFlights(params, configuration, this.flightSelected.departure_flight.engine, (res)=>{
                                (Object.values(res.returning.flightList)).forEach((flightList)=>{
                                    let roundtrip_fare = null;
                                    let roundtrip_flight = null;
                                    let diff_fare = 0
                                    actionFlights(flightList.flights, {
                                        callbackFare: (fare, $indexFare, flight)=>{
                                            if(fare.isRoundTrip){
                                                const calc_dif = fare.beforeDisplayAmount - fare.displayAmount
                                                if(diff_fare === 0 || (diff_fare < calc_dif && (calc_dif < 0))){
                                                    roundtrip_fare = fare;
                                                    roundtrip_flight = flight
                                                    diff_fare = calc_dif
                                                }
                                            }
                                        }
                                    })

                                    if((roundtrip_fare && roundtrip_flight) && diff_fare < 0) List.roundtripNationalFlight(this.flightSelected.departure_fare, roundtrip_flight, roundtrip_fare)

                                })
                            })
                        }
                    }, 500)
                }
                flight_selected.loadingList = true;
				this.actionFlightSelected(flight_selected)
                this.changeFilters([]);
                this.setAirlinesFilters([]) //-> reiniciamos filtros
				this.setStopsFilters('')
                this.setIsStepTwo(true);
                setTimeout(() => {
                    flight_selected.loadingList = false;
                    this.actionFlightSelected(flight_selected)
                }, 1000); //loading ficticio
			},
            async sendData() {
                let flight_selected = (this.flightSelected);
                flight_selected.step_action = 3;
                this.scrollToElement("#mobileInfo-section");
                const modal = new bootstrap.Modal(document.getElementById('LoaderFullPage'), null);
                modal.show();
				await sleep(1000);
                List.eventListReturning();

                const params = {
                    token: this.getAllQuoteTokens.join(','),//(!getIsStepTwo.value ? getStartQuoteTokens.join(',') : getReturnQuoteTokens.join(',')),
                    filterApplied: '',
                    site: window.__pt.settings.site.apiFlights.siteConfig,
                    tripMode: this.getTripMode,
                    simpleFlightQuotes: true,
					step: true,
					DepartureToken: this.flightSelected.departure_token,
                    FlightQuoteId: this.flightSelected.departure_fare.fareKey
                };
                
                const response = await getFilteredList(params);
                this.resetFlightResponse();
                await sleep(50)
                this.changeFilters([]);
				await this.setFlightResponses(response.response);
                this.evtReturningFlights(response);
                await this.setMatrix(this.flightSelected.departure_token, this.flightSelected.departure_fare.fareKey);
                const checkoutData = {
                    summary: {
                        token: this.flightSelected.departure_token,
                        fareKey: this.flightSelected.departure_fare.fareKey
                    }
                };
				this.setCheckOutDataStepOne({ ...checkoutData });  //  ->>>>>>>>>>>>>>>>>>>

                flight_selected.loadingList = true;
                this.setIsStepTwo(true);
                flight_selected.loadingList = false;
                this.actionFlightSelected(flight_selected);
                this.setAirlinesFilters([]); //-> reiniciamos filtros
                this.setStopsFilters('');
                await sleep(1000);
				modal.hide();
			},
            async setMatrix(token, fareKey) {
                if (this.getReturnQuoteTokens.length) {
                    this.setLoading(true);
                    const response = await getMatrix({ token: (this.getReturnQuoteTokens.join(',')), step: true, departureToken: token, flightQuoteId: fareKey });//
                    this.setFlightMatrix(response);
                    this.setLoading(false);
                }
            },
            evtReturningFlights(data) {
                for (const element of data.response) {
                    (Object.values(element.returning.flightList)).forEach((flightList) => {
                        let roundtrip_fare = null;
                        let roundtrip_flight = null;
                        let diff_fare = 0
                        actionFlights(flightList.flights, {
                            callbackFare: (fare, $indexFare, flight) => {
                                if (fare.isRoundTrip) {
                                    const calc_dif = fare.beforeDisplayAmount - fare.displayAmount
                                    if (diff_fare === 0 || diff_fare < calc_dif) {
                                        roundtrip_fare = fare;
                                        roundtrip_flight = flight
                                        diff_fare = calc_dif
                                    }
                                }
                            }
                        })
                        if ((roundtrip_fare && roundtrip_flight) && diff_fare < 0) List.roundtripNationalFlight(this.flightSelected.departure_fare, roundtrip_flight, roundtrip_fare)
                    })
                }
            },
			selectInputFlight(flight, token, airline, taskIDs) {
				if (this.type !== 'departure') {
                    const dateDeparture = new Date(flight.departureTime);
                    const dateArrival = new Date(this.flightSelected.departure_flight.arrivalTime);
					if (dateArrival >= dateDeparture) {
						this.flightSelected.returning_fare = null;
						this.actionFlightSelected(this.flightSelected);
                        const modalMessages = document.getElementById('modalMessages');
                        const modal = new bootstrap.Modal(modalMessages);
                        modal.show();
                        return false;
                    }
				}
                const taskID = taskIDs.split(",");
				if (this.type === 'departure') {
					//this.flightSelected.step_action = (!this.isRoundTrip) ? 2 : this.isResponsive ? 2 : 3; //4 es para ir directo a reservar y 2 es para ir a seleccionar vuelo de regreso
					//this.flightSelected.step_action = (!this.isRoundTrip) ? 2 : 2; //4 es para ir directo a reservar y 2 es para ir a seleccionar vuelo de regreso
                    this.flightSelected.step_action = 2; //4 es para ir directo a reservar y 2 es para ir a seleccionar vuelo de regreso
					this.flightSelected.departure_flight = flight
					this.flightSelected.departure_token = token;
					this.flightSelected.departure = airline;
					this.flightSelected.departure_quoteTokenFQS = taskID[0];
                    if(this.isRoundTrip && (configFilter(this.flightSelected.departure_flight.airline?.code) || {})?.searchArrival){
                        getFlightsBySimpleFlightQuotes(this.flightSelected.departure_flight.engine, false)
                    }
				} else {
					this.flightSelected.step_action = 4;
					this.flightSelected.returning_flight = flight;
					this.flightSelected.returning_token = token;
					this.flightSelected.returning = airline;
					this.flightSelected.returning_quoteTokenFQS = taskID[1];
					if(airline?.returningQuoteToken) this.flightSelected.returningQuoteToken = airline.returningQuoteToken;
					if(airline?.outboundFlightId) this.flightSelected.outboundFlightId = airline.outboundFlightId;
					if(airline?.outputFareId) this.flightSelected.outputFareId = airline.outputFareId;
                    if(this.flightSelected.returning_fare?.isRoundTrip){
                        Logger.log("Precio anterior, oneway: ", this.flightSelected.returning_fare?.beforeDisplayAmount)
                        Logger.log(`Diferencia de rountrip vs oneway: ${this.flightSelected.returning_fare.beforeDisplayAmount} - ${this.flightSelected.returning_fare.displayAmount} = `, this.flightSelected.returning_fare.beforeDisplayAmount - this.flightSelected.returning_fare.displayAmount)
                    }
				}
				const dataGroups = {
					index: 0,
                    code: airline.departure?.code?.trim() || airline.returning?.code?.trim(),
					departure: {
						price: this.type === 'departure' ? this.flightSelected.departure_fare.amount : this.flightSelected.returning_fare.amount,
						view: this.type === 'departure' ? 'departing' : 'returning'
					}
				}
				List.flights([dataGroups], false);
				this.setBarLineBottomShow()
			},
			scrollToElement(elementId) {
				let element = document.querySelector(elementId);
				if (element) {
					element.scrollIntoView({ behavior: 'instant', block: 'start' });
				} else {
					Logger.warn('Elemento no encontrado:', elementId);
				}
			},
			actionFlightSelected(flight_selected = this.flightSelected) {
				this.setFlightSelected(flight_selected)
			},
			async actionNextNexSectionMobile() {
				if (!this.isRoundTrip || this.flightSelected?.step_action === 4) {
					this.submit();
				} else {
					await this.sendData();
				}
			},
			setBarLineBottomShow() {
				setTimeout(() => {
					this.flightSelected.barLineBottomShow = true;
					setTimeout(() => {
						this.flightSelected.barLineBottomShow = false;
                        this.flightSelected.firstClick = false;
						this.actionFlightSelected(this.flightSelected)
					}, 2000);
					this.actionFlightSelected(this.flightSelected)
				}, 100);
			}
		},
		mounted() {
			this.checkResponsive(); // check responsive INIT
			window.addEventListener('resize', this.checkResponsive); // check responsive
            window.addEventListener('orientationchange', this.checkResponsive); // check responsive
        },
		components: {
			CurrencyDisplay
		}
	}
</script>
<style>
.c-flights-list p.flight-number{
    color: #696770 !important;
}
.cta-link{
    color: #186BDF !important;
    font-weight: 500 !important;
}
.color-time{
    color: #3B3A40 !important;
    font-weight: 600 !important;
}
.cni-rates {
	.currency-display {
		white-space: nowrap;
	}
}
</style>