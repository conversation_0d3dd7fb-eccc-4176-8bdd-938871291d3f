<template>
    <div>
        <section id="app" class="c-box-promo position-relative container-fluid pb-4 calendar mb-3 mb-md-4" v-bind:style="{ backgroundImage: 'url(' + backgroundImage() + ')' }">
            <div class="container cb-in position-relative p-0 d-none d-md-block">
                <div class="col-12 p-0 ml-auto text-left">
                    <h1 class="font-poppins-semibold pt-3">{{__("promotions.titleCalendar")}}<br> {{originName}} {{__("promotions.to")}} {{destinationName}} </h1>
                </div>
                <div class="col-12 col-lg-3 p-0 s-nav-md mt-0">
                    <a class=" a-link py-3 py-md-3 f-p-medium font-14 cursor-pointer" @click="returnList">{{__("promotions.other_destination")}}</a>
                </div>
            </div>
        </section>
        <div class="container mt-2 mt-md-4">
            <div class="container cb-in position-relative p-0 d-block d-md-none">
                <div class="col-12 p-0 ml-auto text-left">
                    <h1 class="font-poppins-semibold font-20 color-primary">{{__("promotions.titleCalendar")}}<br> {{originName}} {{__("promotions.to")}} {{destinationName}} </h1>
                </div>
                <div class="col-12 col-lg-3 p-0 s-nav-md mt-0 mb-3">
                    <a class=" a-link py-3 py-md-3 f-p-medium font-14 cursor-pointer" @click="returnList">{{__("promotions.other_destination")}}</a>
                </div>
            </div>
            <ul class="nav nav-tabs mx-4">
                <li class="nav-item" @click="changeTab('roundTrip')" role="presentation" v-if="getCalendarRoundTrip != null">
                    <a class="nav-link position-relative py-3 text-center" :class="{'active': showRoundTrip}">{{__('promotions.tab1')}} <span class="d-block">{{__('promotions.from')}} <CurrencyDisplay :amount="getSmallerTaxeRoundTrip" :showCurrencyCode="true" :reduceIsoFont="false" /></span></a>
                </li>
                <li class="nav-item" @click="changeTab('oneway')" role="presentation" v-if="getCalendar != null">
                    <a class="nav-link position-relative py-3 ms-2 text-center" id="emma" :class="{'active': showOneWay}">{{__('promotions.tab2')}} <span class="d-block">{{__('promotions.from')}} <CurrencyDisplay :amount="getSmallerTaxeOneWay" :showCurrencyCode="true" :reduceIsoFont="false" /></span></a>
                </li>
            </ul>
            <div class="tab-content p-3">
                <div v-show="showRoundTrip" class="tab-pane fade " :class="{'show active': showRoundTrip}">
                    <div class="row">
                        <div class="col-12 col-md-9 d-none d-md-block mt-5 mb-5">
                            <p class="alert-message"><i class="icon icon-warning align-middle ms-2"></i>{{__('promotions.rates_per_adult')}}</p>
                        </div>
                        <div class="c-box col-12 col-md-3 d-flex flex-row flex-nowrap justify-content-center align-items-center cb-promo">
                            <div class="col-12 p-0 s-nav-md mt-0">
                                <div class="input-group mb-3">
                                    <span class="input-group-text bg-transparent border-0 rounded-0 px-1">
                                        <i class="icon icon-event font-20 text-secondary"></i>
                                    </span>
                                    <input type="text" required readonly id="calendar-checkIn-flight1" name="calendar-checkIn-flight"
                                           class="form-control input-booker border-0 shadow-none rounded-0 px-0 cursor-pointer open-datepicker form-control-plaintext"
                                           :value="$filters.date(getStartDate, 'DD MMM YYYY') + (getTripMode !== 0 ? ' - ' + $filters.date(getReturningDate, 'DD MMM YYYY') : '')" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <CalendarDatesQuotes v-if="showRoundTrip" :typeFlight="1"></CalendarDatesQuotes>
                </div>
                <div v-show="showOneWay" class="tab-pane fade" :class="{'show active': showOneWay}">
                    <div class="row">
                        <div class="col-12 col-md-10 d-none d-md-block mt-5 mb-5">
                            <p class="alert-message"><i class="icon icon-warning align-middle ms-2"></i>{{__('promotions.rates_per_adult')}}</p>
                        </div>
                        <div class="c-box col-12 col-md-2 d-flex flex-row flex-nowrap justify-content-center align-items-center cb-promo">
                            <div class="input-group mb-3 promotionsCalendar">
                                <span class="input-group-text bg-transparent border-0 rounded-0 px-1">
                                    <i class="icon icon-event font-20 text-secondary"></i>
                                </span>
                                <input type="text" required readonly id="calendar-checkIn-flight2" name="calendar-checkIn-flight"
                                       class="form-control input-booker border-0 shadow-none rounded-0 px-0 cursor-pointer open-datepicker form-control-plaintext"
                                       :value="$filters.date(getStartDepartureDate, 'DD MMM YYYY') + (getTripMode !== 0 ? ' - ' + $filters.date(getReturningDate, 'DD MMM YYYY') : '')" />
                            </div>
                        </div>
                    </div>
                    <CalendarDatesQuotes v-if="showOneWay" :typeFlight="0"></CalendarDatesQuotes>
                </div>
            </div>

            <div class="row">
                <div class="col-12 my-3 p-0">
                    <div class="banner-promo">
                        <a class="btn-hotels" :href="sendHotel()" target="_blank">
                            <div class="row">
                                <div class="col-12 col-md-6">
                                    <span class="icon-banner align-middle p-3"><i class="icono-bed-outline"></i></span>
                                    <div class="banner-promo-text align-middle ps-3">
                                        <p class="m-0">{{__("promotions.hotelsDestination")}}</p>
                                        <h3 class="m-0 font-36">{{destinationName}}</h3>
                                    </div>
                                </div>
                                <div class="col-12 col-md-6 d-none d-md-block">
                                    <div class="banner-promo-img c-box-img-ibe" v-bind:style="{ backgroundImage: 'url(' + backgroundImage() + ')' }">
                                        <span class="btn btn-primary px-5 align-middle float-end mt-5 me-3">{{__("promotions.see")}}</span>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import { storeToRefs } from 'pinia';
    import { datepicker } from '../../../utils/helpers/calendar';
    import { __ } from '../../../utils/helpers/translate';
    import { usePromotionStore } from '../../stores/promotion';
    import { getPromotionsCalendar, getParamsPromotions } from '../../services/ApiPromotionsFrontServices';
    import { hidenCloak } from '../../../utils/helpers/animates';
    import CurrencyDisplay from '../common/CurrencyDisplay.vue';

	export default {
		data() {
			return {
                showRoundTrip: false,
                showOneWay: true,
                startingDate: "",
                returningDate: "",
                origin: "",
                destination: "",
                originName: "",
                destinationName: "",
                hotelsUri: "",
                campaingToken: "",
                tipotarifa: "",
                config: window.__pt.settings.site
			}
		},
        components: {
            CurrencyDisplay
        },
        watch: {
            async getCalendarChange(val) {
                this.setIsLoading(true, this.getTripMode);
                await this.quoteFlight(this.getTripMode);
            },
        },
        setup() {
            const usePromotion = usePromotionStore();
            const { setOrigin, setDestination, setStartingDate, setReturningDate, setTripMode, setPromotionsResponse, setPromotionResponseRoundtrip, setSelected, setDestinationObjectSelected, setIsLoading, setStartingDepartureDate } = usePromotion;
            const { getTripMode, getOriginSelected, getOrigin, getDestination, getStartDate, getReturningDate, getSmallerTaxeOneWay, getSmallerTaxeRoundTrip, getCalendar, getCalendarRoundTrip, getCalendarChange, getStartDepartureDate  } = storeToRefs(usePromotion);
            return {
                setOrigin, setDestination, setStartingDate, setReturningDate, setTripMode, getTripMode,
                getOriginSelected, getOrigin, getDestination, getStartDate, getReturningDate, setPromotionsResponse,
                setPromotionResponseRoundtrip, getSmallerTaxeOneWay, getSmallerTaxeRoundTrip, getCalendar, getCalendarRoundTrip,
                setSelected, setDestinationObjectSelected, getCalendarChange, setIsLoading, setStartingDepartureDate, getStartDepartureDate
            }
        },
        async mounted() {
            hidenCloak();
            this.initialize();
            this.changeTab(this.tipotarifa);
		},
		methods: {
            async changeTab(tabe) {
                switch (tabe) {
                    case 'roundTrip':
                        this.showRoundTrip = true;
                        this.showOneWay = false;
                        this.setTripMode(1);
                        break;
                    default:
                        this.showRoundTrip = false;
                        this.showOneWay = true;
                        this.setTripMode(0);
                        break;
                }
                datepicker("promotions");
            },
            setValues() {
                const origin = {
                    code: this.origin,
                    name: this.originName,
                }

                const destination = {
                    code: this.destination,
                    name: this.destinationName,
                }
                this.setSelected(origin);
                this.setDestinationObjectSelected(destination);
                this.setOrigin(this.origin);
                this.setDestination(this.destination);
                this.setStartingDepartureDate(this.$filters.createLocalDateFromISOString(this.startingDate));
                this.setStartingDate(this.$filters.createLocalDateFromISOString(this.startingDate));
                this.setReturningDate(this.$filters.createLocalDateFromISOString(this.returningDate));
                //this.setTripMode(0);
            },
            sendHotel() {
                return `https://viajes.tiquetesbaratos.com/hoteles/${this.hotelsUri}?CampaignToken=${this.campaingToken}`;
            },
            async quoteFlight(tripMode) {
                const extraParams = {};
                const url = new URL(window.location.href);
                const values = new URLSearchParams(url.search);
                if (values.get('cache')) {
                    extraParams.ClearCache = values.get('cache');
                }
                const params = {
                    site: this.config.code,
                    origin: this.getOrigin,
                    destination: this.getDestination,
                    startDate: tripMode === 0 ? this.$filters.date(this.getStartDepartureDate, 'YYYY-MM-DD') : this.$filters.date(this.getStartDate, 'YYYY-MM-DD'),
                    returnDate: this.$filters.date(this.getReturningDate, 'YYYY-MM-DD'),
                    adults: 1,
                    tripMode: tripMode,
                }
                const rq = getParamsPromotions(params, extraParams);
                let responseDetail = await getPromotionsCalendar(rq);
                if (responseDetail.Response.Recommendations != null) {
                    if (tripMode === 0) {
                        this.setPromotionsResponse(responseDetail.Response.Recommendations, tripMode, params);
                    } else {
                        this.setPromotionResponseRoundtrip(responseDetail.Response.Recommendations, tripMode, params);
                    }
                }
                this.setIsLoading(false, tripMode);
            },
            backgroundImage() {
                return `https://tiquetesbaratos.com/front-vuelos/landings/img/${this.destination}.jpg`
            },
            returnList() {
                history.back();
            },
            parseDate(date) {
                if (date.indexOf("/") != -1) {
                    var datesSplit = date.split("/");
                    return `${datesSplit[2]}-${datesSplit[1]}-${datesSplit[0]}`;
                }
                return date;
            },
            async initialize() {
                const expresionRegular = /\((.*?)\)/;
                const queryString = window.location.search;
                const urlParams = new URLSearchParams(queryString);
                const origin = urlParams.get('origen');
                const destination = urlParams.get('destino');
                this.hotelsUri = urlParams.get('hotelsUri');
                this.campaingToken = urlParams.get('campaingToken');

                let splitOrigin = origin.split('|');
                let splitDestination = destination.split('|');

                this.origin = splitOrigin[1];
                this.destination = splitDestination[1];


                if (splitOrigin.length  == 1) {
                    this.origin = origin.match(expresionRegular)[1];
                }

                if (splitDestination.length == 1) {
                    this.destination = destination.match(expresionRegular)[1];
                }


                this.origin = this.origin.toUpperCase();
                this.destination = this.destination.toUpperCase();

                this.originName = splitOrigin[0];
                this.destinationName = splitDestination[0];

                this.startingDate = this.parseDate(urlParams.get('from'));
                this.returningDate = this.parseDate(urlParams.get('to'));
                this.tipotarifa = this.parseDate(urlParams.get('tipotarifa'));


                this.setValues();
                datepicker("promotions");

                this.quoteFlight(0);
                this.quoteFlight(1);
            }
		}
	}
</script>

<style lang="scss" scoped>
    .nav-tabs {
        background-color: #fff;
        position: relative;
        z-index: 1;
        cursor: pointer;

        .nav-item {
            margin-bottom: -1px;
        }

        .nav-link {
            border: 1px solid transparent !important;
            border-top-left-radius: .25rem !important;
            border-top-right-radius: .25rem !important;

            &.active {
                color: #495057;
                background-color: #fff;
                border-color: #dee2e6 #dee2e6 #fff !important;

                &::before {
                    content: "";
                    border-top: 5px solid #2196f3 !important;
                    left: 0;
                    position: absolute;
                    right: 0;
                    top: 0;
                    border-top-left-radius: .25rem !important;
                    border-top-right-radius: .25rem !important;
                }
            }
        }
    }

    link {
        .active {
        }
    }

    .tab-content {
        border: 1px solid #dee2e6;
        border-radius: 6px;
        position: relative;
        top: -1px;
        z-index: 0;
    }

    .input-group {
        border-bottom: 1px solid #525051;
    }

    .range-plugin {
        right: 0 !important;
        left: unset !important;
    }

    .alert-message {
        font-family: 'Roboto-Regular';
        color: #717171;
    }

    .banner-promo {
        max-width: 890px;
        width: 90%;
        background-color: #003b98;
        margin: 0 auto;
        border-radius: 12px;
        display: block;
        box-shadow: 1px 33px 16px -32px rgba(0, 0, 0, .75);
        -webkit-box-shadow: 1px 33px 16px -32px rgba(0, 0, 0, .75);
        -moz-box-shadow: 1px 33px 16px -32px rgba(0, 0, 0, .75);

        p {
            color: #fff;
        }
    }

    .banner-promo-text, .icon-banner {
        display: inline-block;
    }

    .banner-promo {
        h3 {
            color: #ffe000;
            font-weight: 700;
        }
    }

    .banner-promo-img {
        border-radius: 0 12px 12px 0;
        background-size: cover;
        display: block;
        height: 100%;
    }

    .btn-primary {
        background: #2196f3;
        border: none;
        border-radius: 4px;
        color: #fff !important;
        transition: all .2s linear;
    }

    .nav-link {
        border-color: rgba(233, 236, 239, .69) rgba(233, 236, 239, .69) rgba(222, 226, 230, .69);
        color: #003b98 !important;
        overflow: hidden;
        position: relative;

        &.active {
            font-weight: 700;
            font-family: Roboto-Medium;
        }


        span {
            display: block;
            font-size: .8rem;
            color: #003b98 !important;
        }
    }

    .btn-hotels{
        color: unset !important;
        text-decoration: none !important;
    }
</style>
