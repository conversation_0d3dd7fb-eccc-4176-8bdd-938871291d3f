import { mapToQueryString, mapPaxToUrl, searchParams } from '../../utils/helpers/queryString';
import { PackageMapper } from '../mappers/packageMapper';
const config = window.__pt.settings.site;
const hotelDetail = window.__pt.hotel;
const paramsConfig = window.__pt.box;
const places_info = window.__pt.places_info || {};

export const submitCheckOut = async (quote,  extraParams = {}) => {
    let paramsUrl = searchParams();
    
    let items = PackageMapper.map(hotelDetail.content, quote);
    let hotel = hotelDetail;
    hotel.content.rooms = items.hotelRooms;

    let room = getRoomAndRateSelected(paramsUrl.roomId, paramsUrl.rateId, hotel)


    let paxes = mapPaxToUrl(paramsConfig.pax, true, true);

    let params = {
        "CheckIn": quote.booker.checkIn,
        "CheckOut": quote.booker.checkout,
        "hotelCheckIn": quote.booker.hotelCheckIn,
        "startingFromAirport": quote.booker.startingAirportFrom,
        "returningFromAirport": quote.booker.returningAirportFrom,
        "startingFromDateTime": quote.flightItinerary.starting.arrival.date,
        "returningFromDateTime": quote.flightItinerary.returning.arrival.date,
        "idHotel": hotel.content.hotelId,
        "idRoom": room.rateSelected.roomId,
        "idRate": room.rateSelected.rateId,
        "TotalRate": room.rateSelected.totalAmount,
        "PrePromotionAmount": room.rateSelected.prePromotionAmount,
        "FareKey": room.rateSelected.flightFareKey,
        "CheckoutData": room.rateSelected.checkoutData,
        "isDomesticRoute": isDomestic,

        "placeId": places_info.returning.placeId,
        "selectedOutboundFlight": quote.flightItinerary.starting.flightNumber,
        "selectedReturnFlight": quote.flightItinerary.returning.flightNumber,
        "productUrl": window.location.href,
        "engine": quote.flightItinerary.starting.engine,
        "site": config.domain,

        "checkInWasModified": false,
        "IsUpsell": false,
        "placeType": 0,
        "Source": "Rooms",
        "isPackage": true,
        "hasSpecialDiscountAplied": true,
        "chkSource": 1,
        "rdmCksrreal": 1,
        "checkInWasModified": false,
        "IsUpsell": false,
        "checkInWasModifiedFromUser": false,
        "prevSelectedOutboundFlight": "",
        "prevSelectedReturnFlight": "",
    };

    params = { ...params, ...paxes };
    let form = document.createElement("form");
    form.setAttribute("method", "POST");
    form.setAttribute("action", `${config.checkoutUrl}`);

    for (let key in params) {
        if (params[key] != null) {
            let hiddenField = document.createElement("input");
            hiddenField.setAttribute("type", "hidden");
            hiddenField.setAttribute("name", key);
            hiddenField.setAttribute("value", params[key]);
            form.appendChild(hiddenField);
        }
    }

    document.body.appendChild(form);
    form.submit();
};

const getRoomAndRateSelected = (roomId, rateId, hotel) => {
    let room = null;
    room = hotel.content.rooms.find(r => r.roomId == roomId);
    if (room) {
        let rateSelected = room.rate.find(r => r.rateId == rateId && r.roomId == roomId);

        if (rateSelected) {
            rateSelected.selected = rateSelected.rateId;
            room.rateSelected = rateSelected;
        } else {
            room = null
        }

    }

    return room;
}