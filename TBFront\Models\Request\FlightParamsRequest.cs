﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using TBFront.Models.FlightParams;
using TBFront.Providers.ModelBinder;

namespace TBFront.Models.Request
{
    public class FlightParamsRequest
    {

        //[FromQuery(Name = "transporte")]
        //[FromQuery(Name = "tripMode")]
        [FromMultipleQuery("transporte", "tripMode")]
        public string Mode { get; set; }

        //[FromQuery(Name = "origenName")]
        //[FromQuery(Name = "originName")]
        [FromMultipleQuery("origenName", "origenName")]
        public string OriginName { get; set; }

        //[FromQuery(Name = "origen")]
        //[FromQuery(Name = "startingFromAirport")]
        [FromMultipleQuery("origen", "startingFromAirport")]
        public string OriginCode { get; set; }

        //[FromQuery(Name = "destinoName")]
        //[FromQuery(Name = "destinationName")]
        [FromMultipleQuery("destinoName", "destinationName")]
        public string DestinationName { get; set; }

        //[FromQuery(Name = "destino")]
        //[FromQuery(Name = "returningFromAirport")]
        [FromMultipleQuery("destino", "returningFromAirport")]
        public string DestinationCode { get; set; }

        //[FromQuery(Name = "from")]
        //[FromQuery(Name = "startingFromDateTime")]
        [FromMultipleQuery("from", "startingFromDateTime")]
        public string DateFrom { get; set; }

        //[FromQuery(Name = "to")]
        //[FromQuery(Name = "returningFromDateTime")]
        [FromMultipleQuery("to", "returningFromDateTime")]
        public string DateTo { get; set; }

        //[FromQuery(Name = "adultos")]
        //[FromQuery(Name = "adults")]
        [FromMultipleQuery("adultos", "adults")]
        public int Adults { get; set; } = 1;

        //[FromQuery(Name = "ninos")]
        //[FromQuery(Name = "kids")]
        [FromMultipleQuery("ninos", "kids")]
        public int Children { get; set; }

        //nuevo
        [FromQuery(Name = "ageKids")]
        public string AgeKids { get; set; }



        [FromQuery(Name = "addfirst")]
        public string AddFirst { get; set; }

        [FromQuery(Name = "option")]
        public string Option { get; set; }
        [FromQuery(Name = "view")]
        public string View { get; set; }

        [FromQuery(Name = "tripDays")]
        public string TripDays { get; set; }

        [FromQuery(Name = "daysInAdvance")]
        public string DaysInAdvance { get; set; }

        [FromQuery(Name = "airlineCode")]
        public string AirlineCode { get; set; }




        [FromQuery(Name = "localizador")]
        public string MasterLocatorId { get; set; }

        [FromQuery(Name = "email")]
        public string Email { get; set; }
        
        [FromQuery(Name = "callus")]
        public bool? Callus { get; set; }

        [FromQuery(Name = "cache")]
        public bool Cache { get; set; }

        [ModelBinder(typeof(PaxBinder))]
        public List<FlightParams.Pax> Paxes { get; set; }



        [FromQuery(Name = "culture")]
        public string Culture { get; set; }

        public DateTime StartingDate => GetDateFrom();
        public DateTime ReturningDate => GetDateTo();


        private DateTime GetDateFrom()
        {
            var date = DateTime.Now;

            if (DateTime.TryParse(DateFrom, out date))
            {
                return date;
            }

            return DateTime.Now;
        }

        private DateTime GetDateTo()
        {
            var date = DateTime.Now;

            if (DateTime.TryParse(DateTo, out date))
            {
                return date;
            }

            return DateTime.Now;
        }

    }

    public class DateRange
    {
        public string? Starting { get; set; }
        public string? Returning { get; set; }
        public DateTime StartingDate { get; set; }
        public DateTime ReturningDate { get; set; }
        public bool IsDefault { get; set; }
    }

    public class PaxBinder : IModelBinder
    {
        private const string valueDefault = "";

        public Task BindModelAsync(ModelBindingContext bindingContext)
        {
            var name = bindingContext.ModelName;
            var pax = new List<FlightParams.Pax>();

            try
            {
                var rooms = bindingContext.ValueProvider.GetValue("adultos").FirstValue ?? bindingContext.ValueProvider.GetValue("adults").FirstValue;

                if (string.IsNullOrEmpty(rooms))
                {
                    var _children = new List<int>();
                    pax.Add(new FlightParams.Pax() { Adults = 1, Children = _children });

                }
                else
                {
                    var roomsNum = 2;

                    for (int i = 1; i < roomsNum; i++)
                    {

                        var _children = new List<int>();
                        var adult = bindingContext.ValueProvider.GetValue($"adultos").FirstValue ?? bindingContext.ValueProvider.GetValue($"adults").FirstValue;
                        string kidsOld = bindingContext.ValueProvider.GetValue($"ninos").FirstValue;
                        string kids = bindingContext.ValueProvider.GetValue($"kids").FirstValue;

                        if (kidsOld != null)
                        {
                            var subs = Convert.ToInt32(kidsOld);

                            for (int j = 0; j < subs; j++)
                            {
                                var age = bindingContext.ValueProvider.GetValue($"edad{j + 1}").FirstValue ?? bindingContext.ValueProvider.GetValue($"ageKids{j + 1}").FirstValue;
                                if (!string.IsNullOrEmpty(age))
                                {
                                    _children.Add(Convert.ToInt32(age));
                                }
                            }
                        }

                        if (kids != null && kids != "0")
                        {
                            var ageKids = (bindingContext?.ValueProvider?.GetValue($"ageKids").FirstValue)?.Split(",");

                            for (int j = 0; j < ageKids.Count(); j++)
                            {
                                var age = ageKids[j];
                                if (!string.IsNullOrEmpty(age))
                                {
                                    _children.Add(Convert.ToInt32(age));
                                }
                            }
                        }

                        if (string.IsNullOrEmpty(adult))
                        {
                            adult = "1";
                        }

                        pax.Add(new FlightParams.Pax() { Adults = Convert.ToInt32(adult), Children = _children });

                    }
                }
            }
            catch (Exception ex)
            {
                var _children = new List<int>();
                pax = new List<FlightParams.Pax>
                {
                    new FlightParams.Pax() { Adults = 1, Children = _children }
                };
            }


            bindingContext.ModelState.SetModelValue(name, pax, valueDefault);
            bindingContext.Result = ModelBindingResult.Success(pax);
            return Task.CompletedTask;
        }
    }
}
