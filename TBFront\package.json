{"version": "1.0.0", "name": "tiquetesbaratos-flights-front", "private": true, "scripts": {"dev": "npm run development", "watch": "cross-env --MIX_SITE=tiquetesbaratos mix watch", "watch-tb": "npm run watch-tique<PERSON><PERSON><PERSON>", "watch-tiquetesbaratos": "cross-env --MIX_SITE=tiquetesbaratos mix watch", "test": "cross-env --MIX_ENV=test mix --production --mix-config=webpack.vue.mix.js", "stage": "cross-env --MIX_ENV=stage mix --production --mix-config=webpack.vue.mix.js", "production": "cross-env --MIX_ENV=production mix --production --mix-config=webpack.vue.mix.js", "styles": "mix --production --mix-config=webpack.styles.mix.js", "all": "npm run styles && npm run test && npm run stage && npm run production"}, "dependencies": {"@easepick/bundle": "^1.2.1", "@fingerprintjs/fingerprintjs-pro": "^3.11.5", "@popperjs/core": "^2.11.6", "@tailwindcss/forms": "^0.5.3", "@vee-validate/i18n": "^4.12.6", "@vee-validate/rules": "^4.12.6", "@vueuse/core": "^10.7.2", "axios": "^1.2.1", "bootstrap": "^5.3.2", "bootstrap-icons": "^1.10.2", "bootstrap-slider": "^11.0.2", "crypto-js": "^4.0.0", "dayjs": "^1.11.7", "intl-tel-input": "^22.0.2", "jquery": "^3.7.1", "js-cookie": "^3.0.5", "laravel-mix": "^6.0.49", "lodash": "^4.17.21", "moment": "^2.29.3", "pinia": "^2.1.6", "postcss": "^8.4.20", "resolve-url-loader": "^5.0.0", "sass": "^1.57.1", "sass-loader": "^13.2.0", "slick-carousel": "^1.8.1", "vee-validate": "^4.7.3", "vue": "^3.2.45", "vue-lazy-loading": "^1.0.3", "vue-loader": "^17.0.1", "vue-template-compiler": "^0.1.0", "vue3-carousel": "^0.3.3", "vue3-gmap-custom-marker": "^1.0.0", "vue3-google-map": "^0.18.0", "webpack": "^5.75.0", "webpack-dev-server": "^4.11.1", "yup": "^1.4.0"}, "devDependencies": {"cross-env": "^7.0.3", "eslint": "^8.48.0", "sass-resources-loader": "^2.2.5", "webpack-cli": "^5.0.1"}}