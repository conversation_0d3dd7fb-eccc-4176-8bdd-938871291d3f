using FlightFront.Application.Implementations;
using Microsoft.AspNetCore.Mvc.Razor;
using Microsoft.AspNetCore.ResponseCompression;
using System.IO.Compression;
using System.Reflection;
using TBFront.Agent;
using TBFront.Application.Implementations;
using TBFront.Helpers;
using TBFront.Infrastructure.DatabaseService.DynamoDB;
using TBFront.Infrastructure.DatabaseService.Redis;
using TBFront.Infrastructure.Datadog;
using TBFront.Infrastructure.HttpService.APIB2CService;
using TBFront.Infrastructure.HttpService.BlacklistBookingService;
using TBFront.Infrastructure.HttpService.BookingService;
using TBFront.Infrastructure.HttpService.ContentDeliveryNetwork;
using TBFront.Infrastructure.HttpService.FlightFacade;
using TBFront.Infrastructure.HttpService.FlightQuote;
using TBFront.Infrastructure.HttpService.GrowthBookAPI;
using TBFront.Infrastructure.HttpService.Kerberus;
using TBFront.Infrastructure.HttpService.PaymentGateway;
using TBFront.Infrastructure.HttpService.Place;
using TBFront.Infrastructure.MailService.SendGrid;
using TBFront.Interfaces;
using TBFront.Mappers;
using TBFront.Middleware;
using TBFront.Options;
using TBFront.Providers.ModelBinder;
using TBFront.Services;
using TBFront.ServicesRegister;
using WebMarkupMin.AspNetCore6;


namespace TBFront
{
    public class Startup
    {
        public IConfiguration Configuration { get; }
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public void ConfigureServices(IServiceCollection services)
        {

            services.AddLocalization(options => options.ResourcesPath = "Resources/lang");
            services.AddMvc().AddViewLocalization(LanguageViewLocationExpanderFormat.Suffix).AddDataAnnotationsLocalization();

            services.Configure<RedirectOptions>(Configuration.GetSection("Redirect"));
            services.Configure<SettingsOptions>(Configuration.GetSection("Settings"));
            services.Configure<SiteOptions>(Configuration.GetSection("Site"));
            services.Configure<ExperimentsOptions>(Configuration.GetSection("AbTesting"));
            services.Configure<CultureOptions>(Configuration.GetSection("Culture"));
            services.Configure<CurrencyOptions>(Configuration.GetSection("Currency"));

            services.AddSingleton<IFlightQuoteHandler, FlightQuoteHandler>();

            services.AddSingleton<IMKTCollectionHandler, MKTCollectionHandler>();
            services.AddSingleton<IItineraryHandler, ItineraryHandler>();
            services.AddSingleton<IGrowthBookHandler, GrowthBookHandler>();
            services.AddSingleton<IPlaceHandler, PlaceHandler>();
            services.AddSingleton<IDestinationHandler, DestinationHandler>();
            services.AddSingleton<IBlacklistHandler, BlacklistHandler>();
            services.AddSingleton<IDataDogHandler, DataDogHandler>();
            services.AddSingleton<IOnlinePaymentHandler, OnlinePaymentHandler>();
            services.AddSingleton<ICommonHandler, CommonHandler>();
            services.AddSingleton<IAlternateHandler, AlternateHandler>();
            services.AddSingleton<IContentDeliveryNetworkHandler, ContentDeliveryNetworkHandler>();
            services.AddSingleton<PlaceHandler>();
            services.AddSingleton<IUserHandler, UserHandler>();

            services.AddSingleton<AgentBrowser>();
            services.AddSingleton<DynamoServices>();
            services.AddSingleton<HashService>();
            services.AddScoped<ConfigService>();

            services.AddScoped<StaticHelper>();
            services.AddSingleton<LocalizerHelper>();
            services.AddScoped<ViewHelper>();
            services.AddScoped<FlightContentMapper>();
            services.AddScoped<BookingHandler>();

            services.AddScoped<MailHandler>();
            services.AddScoped<MailService>();
            services.AddScoped<BookingService>();
            services.AddScoped<BookingMapper>();
            services.AddScoped<ViewToStringRenderer>();

            services.AddHttpContextAccessor();
            services.AddHttpClient();

            services.AddResponseCompression(options =>
            {
                options.EnableForHttps = true;
                options.Providers.Add<BrotliCompressionProvider>();
            });
            services.Configure<BrotliCompressionProviderOptions>(options =>
            {
                options.Level = CompressionLevel.Fastest;
            });


            LoginRegister.AddRegisters(services, Configuration);
            WebMarkupRegister.AddRegisters(services, Configuration);
            WebMarkupRegister.AddLanguagesRegisters(services, Configuration);
            RedisCacheServicesRegister.AddRedisCacheDependencies(services, Configuration);
            FlightQuoteServiceRegister.AddFlightQuoteDependencies(services, Configuration);
            BlinkServiceRegister.AddBlacklistServiceRegister(services, Configuration);
            APIB2CServiceRegister.AddAPIB2CDependencies(services, Configuration);
            PaymentGatewayServiceRegister.AddPaymentGatewayServiceRegister(services, Configuration);
            ItineraryServiceRegister.AddItineraryServiceRegister(services, Configuration);
            GrowthBookServicesRegister.AddGrowthBookServicesRegister(services, Configuration);
            PlaceServiceRegister.AddPlaceServiceRegister(services, Configuration);
            ContentDeliveryNetworkServiceRegister.AddContentDeliveryNetworkServiceRegister(services, Configuration);
            KerberusServiceRegister.AddKerberusServiceRegister(services, Configuration);
            DataDogServicesRegister.AddDataDogServiceRegister(services, Configuration);
            PlacesAirportServiceRegister.AddPlacesAirportDependencies(services, Configuration);
            DataProtectionRegister.AddDataProtectionRegister(services, Configuration);
            ContentDeliveryNetworkServicesRegister.AddContentDeliveryNetworkRegister(services, Configuration);
            FlightFacadeServiceRegister.AddFlightFacadeServiceRegister(services, Configuration);

            services.AddControllersWithViews().AddRazorRuntimeCompilation();
            services.AddControllers(options =>
            {
                options.ModelBinderProviders.Insert(0, new MultiNameModelBinderProvider());
            });

        }

        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            var version = Environment.GetEnvironmentVariable("ASPNETCORE_APPVERSION") ?? Assembly.GetExecutingAssembly().GetName().Version?.ToString();

            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }
            else
            {
                app.UseMiddleware<ErrorHandlerMiddleware>();
            }

            app.UseExceptionHandler("/error-page");
            app.UseStatusCodePagesWithReExecute("/error-page/{0}");
            app.UseResponseCompression();
            app.UseStaticFiles(StartupMiddleware.StaticFileOptionsMiddleware());
            app.UseMiddleware<RedirectMiddleware>();
            app.UseMiddleware<CultureValidationMiddleware>();
            app.UseHttpsRedirection();
            app.UseWebMarkupMin();
            app.UseRequestLocalization(StartupMiddleware.RequestLocalizationMiddleware(Configuration));
            app.UseRouting();
            app.UseAuthorization();

            app.Use(async (context, next) =>
            {
                context.Response.Headers.TryAdd("X-Frame-Options", "DENY");
                context.Response.Headers.TryAdd("x-content-type-options", "nosniff");
                context.Response.Headers.TryAdd("content-security-policy", "frame-ancestors 'self'");
                context.Response.Headers.TryAdd("x-machine", Environment.MachineName);
                context.Response.Headers.TryAdd("x-os-version", Environment.OSVersion.VersionString);
                context.Response.Headers.TryAdd("x-version", version);
                if (!env.IsDevelopment())
                {
                    context.Response.Headers.TryAdd("Strict-Transport-Security", "max-age=7776000; includeSubDomains;");
                }
                await next();
            });

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllerRoute(
                    name: "default",
                    pattern: "{controller=Home}/{action=Index}/{id?}");

                endpoints.MapControllerRoute(
                    name: "localized",
                    pattern: "{culture}/{controller=Home}/{action=Index}/{id?}",
                    defaults: new { culture = "en-us" },
                    constraints: new { culture = @"[a-z]{2}-[a-z]{2}" });


                endpoints.MapGet("/version", () => "TBFront Version: " + version);
                endpoints.MapGet("/vuelos/version", () => "TBFront Version: " + version);

            });
        }

    }
}