<template>
    <div v-if="typeSkeleton == 'typeList'" class="container-sketeton" id="skeletonList">
        <section class="container px-md-0 my-4">
            <div class="shadow rounded">
                <div class="row c-cards m-0 border-bottom">
                    <div class="col-4 col-md-2">
                        <div class="c-card is-loading p-0 m-0 w-100">
                            <div class="px-0 py-3 p-md-3">
                                <p class="m-0 py-3"></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-4 offset-4 offset-md-8 col-md-2">
                        <div class="c-card is-loading p-0 m-0 w-100">
                            <div class="px-0 py-3 p-md-3">
                                <p class="m-0 py-3"></p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row c-cards m-0">
                    <div class="col-4 col-md-2">
                        <div class="c-card is-loading p-0 m-0 w-100">
                            <div class="px-0 py-3 p-md-3">
                                <p class="m-0 py-2"></p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row c-cards m-0">
                    <div class="col-6 col-md-4">
                        <div class="c-card is-loading p-0 m-0 w-100">
                            <div class="px-0 pb-3 pb-md-3 px-md-3 pt-md-0">
                                <p class="m-0 py-2"></p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row c-cards m-0">
                    <div class="col-8 col-md-6">
                        <div class="c-card is-loading p-0 m-0 w-100">
                            <div class="px-0 pb-3 pb-md-3 px-md-3 pt-md-0">
                                <p class="m-0 py-2"></p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row c-cards m-0">
                    <div class="col-6 col-md-5">
                        <div class="c-card is-loading p-0 m-0 w-100">
                            <div class="px-0 pb-3 pb-md-3 px-md-3 pt-md-0">
                                <p class="m-0 py-2"></p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row c-cards m-0">
                    <div class="col-2 offset-10 offset-md-11 col-md-1">
                        <div class="c-card is-loading p-0 m-0 w-100">
                            <div class="px-0 pb-3 pb-md-3 px-md-3 pt-md-0">
                                <p class="m-0 py-2"></p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row c-cards m-0">
                    <div class="col-4 offset-8 offset-md-10 col-md-2">
                        <div class="c-card is-loading p-0 m-0 w-100">
                            <div class="px-0 pb-3 pb-md-3 px-md-3 pt-md-0">
                                <p class="m-0 py-2"></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
    <div v-else-if="typeSkeleton == 'typeFamily'"
        class="ch-column col-rate mr-3 px-3 pt-2 border ml-3 position-relative"
        :class="isSelected ? 'c-rate-active' : ('c-rate-continue family-rate-1')" id="skeletonFamily">
        <div class="c-rate-style"></div>
        <div id="cSkeletonInt02" class="row d-block c-skeleton-int">
            <div class="col-12">
                <div class="c-card is-loading p-0 px-md-3 m-0 w-100">
                    <div class="px-0 py-3">
                        <p class="m-0 py-2"></p>
                    </div>
                </div>
            </div>
            <div class="col-12">
                <div class="c-card is-loading p-0 px-md-3 m-0 w-100">
                    <div class="px-0">
                        <p class="m-0 py-4"></p>
                    </div>
                </div>
            </div>
            <div class="col-12 mt-4">
                <div class="c-card is-loading p-0 px-md-3 m-0 w-100">
                    <div class="px-0 py-3">
                        <p class="m-0 py-2"></p>
                    </div>
                </div>
            </div>
            <div class="col-12">
                <div class="c-card is-loading p-0 px-md-3 m-0 w-100">
                    <div class="px-0 py-3">
                        <p class="m-0 py-2"></p>
                    </div>
                </div>
            </div>
            <div class="col-12">
                <div class="c-card is-loading p-0 px-md-3 m-0 w-100">
                    <div class="px-0 py-3">
                        <p class="m-0 py-2"></p>
                    </div>
                </div>
            </div>
            <div class="col-12">
                <div class="c-card is-loading p-0 px-md-3 m-0 w-100">
                    <div class="px-0 py-3">
                        <p class="m-0 py-2"></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div v-else-if="typeSkeleton == 'typeFamilyNational'" class="shadow rounded mt-3">
        <div class="row c-cards m-0 pt-3">

            <div class="col-3 col-md-2 offset-2">
                <div class="c-card is-loading p-0 m-0 w-100">
                    <div class="px-0 pb-3 pb-md-3 px-md-3 pt-md-0">
                        <p class="m-0 py-2"></p>
                    </div>
                </div>
            </div>
            <div class="col-3 col-md-2">
                <div class="c-card is-loading p-0 m-0 w-100">
                    <div class="px-0 pb-3 pb-md-3 px-md-3 pt-md-0">
                        <p class="m-0 py-2"></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row c-cards m-0">
            <div class="col-3 col-md-2">
                <div class="c-card is-loading p-0 m-0 w-100">
                    <div class="px-0 pb-3 pb-md-3 px-md-3 pt-md-0">
                        <p class="m-0 py-2"></p>
                    </div>
                </div>
            </div>
            <div class="col-3 col-md-2">
                <div class="c-card is-loading p-0 m-0 w-100">
                    <div class="px-0 pb-3 pb-md-3 px-md-3 pt-md-0">
                        <p class="m-0 py-2"></p>
                    </div>
                </div>
            </div>
            <div class="col-3 col-md-2">
                <div class="c-card is-loading p-0 m-0 w-100">
                    <div class="px-0 pb-3 pb-md-3 px-md-3 pt-md-0">
                        <p class="m-0 py-2"></p>
                    </div>
                </div>
            </div>
            <div class="col-3 col-md-2">
                <div class="c-card is-loading p-0 m-0 w-100">
                    <div class="px-0 pb-3 pb-md-3 px-md-3 pt-md-0">
                        <p class="m-0 py-2"></p>
                    </div>
                </div>
            </div>
            <div class="col-3 col-md-2">
                <div class="c-card is-loading p-0 m-0 w-100">
                    <div class="px-0 pb-3 pb-md-3 px-md-3 pt-md-0">
                        <p class="m-0 py-2"></p>
                    </div>
                </div>
            </div>
            <div class="col-3 col-md-2">
                <div class="c-card is-loading p-0 m-0 w-100">
                    <div class="px-0 pb-3 pb-md-3 px-md-3 pt-md-0">
                        <p class="m-0 py-2"></p>
                    </div>
                </div>
            </div>
        </div>


        <div class="row c-cards m-0">
            <div class="col-3 col-md-2">
                <div class="c-card is-loading p-0 m-0 w-100">
                    <div class="px-0 pb-3 pb-md-3 px-md-3 pt-md-0">
                        <p class="m-0 py-2"></p>
                    </div>
                </div>
            </div>
            <div class="col-3 col-md-2">
                <div class="c-card is-loading p-0 m-0 w-100">
                    <div class="px-0 pb-3 pb-md-3 px-md-3 pt-md-0">
                        <p class="m-0 py-2"></p>
                    </div>
                </div>
            </div>
            <div class="col-3 col-md-2">
                <div class="c-card is-loading p-0 m-0 w-100">
                    <div class="px-0 pb-3 pb-md-3 px-md-3 pt-md-0">
                        <p class="m-0 py-2"></p>
                    </div>
                </div>
            </div>
            <div class="col-3 col-md-2">
                <div class="c-card is-loading p-0 m-0 w-100">
                    <div class="px-0 pb-3 pb-md-3 px-md-3 pt-md-0">
                        <p class="m-0 py-2"></p>
                    </div>
                </div>
            </div>
            <div class="col-3 col-md-2">
                <div class="c-card is-loading p-0 m-0 w-100">
                    <div class="px-0 pb-3 pb-md-3 px-md-3 pt-md-0">
                        <p class="m-0 py-2"></p>
                    </div>
                </div>
            </div>
            <div class="col-3 col-md-2">
                <div class="c-card is-loading p-0 m-0 w-100">
                    <div class="px-0 pb-3 pb-md-3 px-md-3 pt-md-0">
                        <p class="m-0 py-2"></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row c-cards m-0">
            <div class="col-3 col-md-2">
                <div class="c-card is-loading p-0 m-0 w-100">
                    <div class="px-0 pb-3 pb-md-3 px-md-3 pt-md-0">
                        <p class="m-0 py-2"></p>
                    </div>
                </div>
            </div>
            <div class="col-3 col-md-2">
                <div class="c-card is-loading p-0 m-0 w-100">
                    <div class="px-0 pb-3 pb-md-3 px-md-3 pt-md-0">
                        <p class="m-0 py-2"></p>
                    </div>
                </div>
            </div>
            <div class="col-3 col-md-2">
                <div class="c-card is-loading p-0 m-0 w-100">
                    <div class="px-0 pb-3 pb-md-3 px-md-3 pt-md-0">
                        <p class="m-0 py-2"></p>
                    </div>
                </div>
            </div>
            <div class="col-3 col-md-2">
                <div class="c-card is-loading p-0 m-0 w-100">
                    <div class="px-0 pb-3 pb-md-3 px-md-3 pt-md-0">
                        <p class="m-0 py-2"></p>
                    </div>
                </div>
            </div>
            <div class="col-3 col-md-2">
                <div class="c-card is-loading p-0 m-0 w-100">
                    <div class="px-0 pb-3 pb-md-3 px-md-3 pt-md-0">
                        <p class="m-0 py-2"></p>
                    </div>
                </div>
            </div>
            <div class="col-3 col-md-2">
                <div class="c-card is-loading p-0 m-0 w-100">
                    <div class="px-0 pb-3 pb-md-3 px-md-3 pt-md-0">
                        <p class="m-0 py-2"></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div v-else-if="typeSkeleton == 'matrix'" class="container-sketeton hide-xs" id="skeletonMatrix">
        <!-- start skeleton  top -->
        <section class="container px-md-0 my-4">
            <div class="shadow rounded">
                <div class="row c-cards m-0">
                    <div class="col-2 pr-0">
                        <div class="c-card is-loading p-0 m-0 w-100">
                            <div class="px-0 py-3 p-md-3">
                                <p class="m-0 py-2"></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-1 px-0 offset-1">
                        <div class="c-card is-loading p-0 m-0 w-100">
                            <div class="px-0 py-3 p-md-3">
                                <p class="m-0 py-2"></p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row c-cards m-0">
                    <div class="col-2 pr-0">
                        <div class="c-card is-loading p-0 m-0 w-100">
                            <div class="px-0 pb-3 pb-md-3 px-md-3 pt-md-0">
                                <p class="m-0 py-2"></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-1 px-0">
                        <div class="c-card is-loading p-0 m-0 w-100">
                            <div class="px-0 pb-3 pb-md-3 px-md-3 pt-md-0">
                                <p class="m-0 py-2"></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-2">
                        <div class="c-card is-loading p-0 m-0 w-100">
                            <div class="px-0 pb-3 pb-md-3 pt-md-0">
                                <p class="m-0 py-2"></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-1 pr-0">
                        <div class="c-card is-loading p-0 m-0 w-100">
                            <div class="px-0 pb-3 pb-md-3 pt-md-0">
                                <p class="m-0 py-2"></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
    <div v-else-if="typeSkeleton == 'typeListOneWay'">
        <section class="container px-0 px-md-0 my-4">
            <div class="shadow rounded">
                <div class="row c-cards m-0 border-bottom">
                    <div class="col-4 col-md-2">
                        <div class="c-card is-loading p-0 m-0 w-100">
                            <div class="px-0 py-3 p-md-3">
                                <p class="m-0 py-3"></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-4 offset-4 offset-md-8 col-md-2">
                        <div class="c-card is-loading p-0 m-0 w-100">
                            <div class="px-0 py-3 p-md-3">
                                <p class="m-0 py-3"></p>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- body -->
                <div class="row c-cards m-0 px-3">
                    <div class="col-7 col-md-6 px-0 px-md-3">
                        <div class="row">
                            <div class="col-3 col-md-2 pe-1 pe-md-3">
                                <div class="c-card is-loading p-0 m-0 w-100">
                                    <div class="px-0 py-3">
                                        <p class="m-0 py-2"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6 col-md-8 px-0">
                                <div class="c-card is-loading p-0 m-0 w-100">
                                    <div class="px-0 py-3">
                                        <p class="m-0 py-2"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-3 col-md-2 ps-1 ps-md-3">
                                <div class="c-card is-loading p-0 m-0 w-100">
                                    <div class="px-0 py-3">
                                        <p class="m-0 py-2"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-5 col-md-2 px-0 px-md-3">
                        <div class="col-12 ps-3 ps-md-0 pe-0 pe-md-3">
                            <div class="c-card is-loading p-0 m-0 w-100">
                                <div class="px-0 py-3">
                                    <p class="m-0 py-2"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-2 col-md-1 px-0 px-md-0">
                        <div class="col-12 pe-3 pe-md-0 ps-0 ps-md-0">
                            <div class="c-card is-loading p-0 m-0 w-100">
                                <div class="px-0 pt-0 pt-md-3 py-3">
                                    <p class="m-0 py-2"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-3 col-md-3 px-0 ps-md-3">
                        <div class="col-12 px-0 px-md-3">
                            <div class="c-card is-loading p-0 m-0 w-100">
                                <div class="px-0 pt-0 pt-md-3 py-3">
                                    <p class="m-0 py-2"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- divisor -->
                <div class="row c-cards m-0 border-bottom"></div>
                <!-- body -->
                <div class="row c-cards m-0 px-3">
                    <div class="col-7 col-md-6 px-0 px-md-3">
                        <div class="row">
                            <div class="col-3 col-md-2 pe-1 pe-md-3">
                                <div class="c-card is-loading p-0 m-0 w-100">
                                    <div class="px-0 py-3">
                                        <p class="m-0 py-2"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6 col-md-8 px-0">
                                <div class="c-card is-loading p-0 m-0 w-100">
                                    <div class="px-0 py-3">
                                        <p class="m-0 py-2"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-3 col-md-2 ps-1 ps-md-3">
                                <div class="c-card is-loading p-0 m-0 w-100">
                                    <div class="px-0 py-3">
                                        <p class="m-0 py-2"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-5 col-md-2 px-0 px-md-3">
                        <div class="col-12 ps-3 ps-md-0 pe-0 pe-md-3">
                            <div class="c-card is-loading p-0 m-0 w-100">
                                <div class="px-0 py-3">
                                    <p class="m-0 py-2"></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-2 col-md-1 px-0 px-md-0">
                        <div class="col-12 pe-3 pe-md-0 ps-0 ps-md-0">
                            <div class="c-card is-loading p-0 m-0 w-100">
                                <div class="px-0 pt-0 pt-md-3 py-3">
                                    <p class="m-0 py-2"></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-3 col-md-3 px-0 ps-md-3">
                        <div class="col-12 px-0 px-md-3">
                            <div class="c-card is-loading p-0 m-0 w-100">
                                <div class="px-0 pt-0 pt-md-3 py-3">
                                    <p class="m-0 py-2"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- divisor -->
                <div class="row c-cards m-0 border-bottom"></div>
                <!-- body -->
                <div class="row c-cards m-0 px-3">
                    <div class="col-7 col-md-6 px-0 px-md-3">
                        <div class="row">
                            <div class="col-3 col-md-2 pe-1 pe-md-3">
                                <div class="c-card is-loading p-0 m-0 w-100">
                                    <div class="px-0 py-3">
                                        <p class="m-0 py-2"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6 col-md-8 px-0">
                                <div class="c-card is-loading p-0 m-0 w-100">
                                    <div class="px-0 py-3">
                                        <p class="m-0 py-2"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-3 col-md-2 ps-1 ps-md-3">
                                <div class="c-card is-loading p-0 m-0 w-100">
                                    <div class="px-0 py-3">
                                        <p class="m-0 py-2"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-5 col-md-2 px-0 px-md-3">
                        <div class="col-12 ps-3 ps-md-0 pe-0 pe-md-3">
                            <div class="c-card is-loading p-0 m-0 w-100">
                                <div class="px-0 py-3">
                                    <p class="m-0 py-2"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-2 col-md-1 px-0 px-md-0">
                        <div class="col-12 pe-3 pe-md-0 ps-0 ps-md-0">
                            <div class="c-card is-loading p-0 m-0 w-100">
                                <div class="px-0 pt-0 pt-md-3 py-3">
                                    <p class="m-0 py-2"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-3 col-md-3 px-0 ps-md-3">
                        <div class="col-12 px-0 px-md-3">
                            <div class="c-card is-loading p-0 m-0 w-100">
                                <div class="px-0 pt-0 pt-md-3 py-3">
                                    <p class="m-0 py-2"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- divisor -->
                <div class="row c-cards m-0 border-bottom"></div>
                <!-- body -->
                <div class="row c-cards m-0 px-3">
                    <div class="col-7 col-md-6 px-0 px-md-3">
                        <div class="row">
                            <div class="col-3 col-md-2 pe-1 pe-md-3">
                                <div class="c-card is-loading p-0 m-0 w-100">
                                    <div class="px-0 py-3">
                                        <p class="m-0 py-2"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6 col-md-8 px-0">
                                <div class="c-card is-loading p-0 m-0 w-100">
                                    <div class="px-0 py-3">
                                        <p class="m-0 py-2"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-3 col-md-2 ps-1 ps-md-3">
                                <div class="c-card is-loading p-0 m-0 w-100">
                                    <div class="px-0 py-3">
                                        <p class="m-0 py-2"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-5 col-md-2 px-0 px-md-3">
                        <div class="col-12 ps-3 ps-md-0 pe-0 pe-md-3">
                            <div class="c-card is-loading p-0 m-0 w-100">
                                <div class="px-0 py-3">
                                    <p class="m-0 py-2"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-2 col-md-1 px-0 px-md-0">
                        <div class="col-12 pe-3 pe-md-0 ps-0 ps-md-0">
                            <div class="c-card is-loading p-0 m-0 w-100">
                                <div class="px-0 pt-0 pt-md-3 py-3">
                                    <p class="m-0 py-2"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-3 col-md-3 px-0 ps-md-3">
                        <div class="col-12 px-0 px-md-3">
                            <div class="c-card is-loading p-0 m-0 w-100">
                                <div class="px-0 pt-0 pt-md-3 py-3">
                                    <p class="m-0 py-2"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- divisor -->
                <div class="row c-cards m-0 border-bottom"></div>
                <!-- footer -->
                <div class="c-cards">
                    <div class="col-6 offset-3 col-md-4 offset-md-4 px-0">
                        <div class="c-card is-loading p-0 m-0 w-100">
                            <div class="px-0 py-3">
                                <p class="m-0 py-2"></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
    <div v-else-if="typeSkeleton == 'typePriceCheckout'">
        <section class="container">
            <div class="row justify-content-between">
                <div class="col-5 px-0">
                    <div class="c-card is-loading p-0 m-0 w-100">
                        <div class="px-0 py-1">
                            <p class="m-0 py-2"></p>
                        </div>
                    </div>
                </div>
                <div class="col-4 px-0">
                    <div class="c-card is-loading p-0 m-0 w-100">
                        <div class="px-0 py-1">
                            <p class="m-0 py-2"></p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row justify-content-between">
                <div class="col-5 px-0">
                    <div class="c-card is-loading p-0 m-0 w-100">
                        <div class="px-0 py-1">
                            <p class="m-0 py-2"></p>
                        </div>
                    </div>
                </div>
                <div class="col-4 px-0">
                    <div class="c-card is-loading p-0 m-0 w-100">
                        <div class="px-0 py-1">
                            <p class="m-0 py-2"></p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row justify-content-between">
                <div class="col-3 px-0">
                    <div class="c-card is-loading p-0 m-0 w-100">
                        <div class="px-0 py-2">
                            <p class="m-0 py-2"></p>
                        </div>
                    </div>
                </div>
                <div class="col-5 px-0">
                    <div class="c-card is-loading p-0 m-0 w-100">
                        <div class="px-0 py-2">
                            <p class="m-0 py-2"></p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

</template>

<script>
    export default {
        props: {
            typeSkeleton: String,
            isSelected: Boolean
        }
    }
</script>

<style>
    .c-cards {
        background-color: #fff;
        display: -ms-flexbox;
        display: flex
    }
    .c-card {
        margin: 10px
    }
    .c-card .image img {
        max-width: 100%;
        border-top-left-radius: 5px;
        border-top-right-radius: 5px
    }
    .c-card.is-loading .image, .c-card.is-loading .s-line-20, .c-card.is-loading h2, .c-card.is-loading p {
        background: #eee;
        background: linear-gradient(110deg,#ececec 8%,#f5f5f5 18%,#ececec 33%);
        border-radius: 5px;
        background-size: 200% 100%;
        animation: 1.5s d linear infinite
    }
    .c-card.is-loading .image {
        height: 200px;
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0
    }
    .ch-column {
        -ms-flex: initial;
        flex: initial
    }
    .ch-column div {
        -ms-flex-wrap: wrap;
        flex-wrap: wrap
    }
    .ch-column .c-pass-flight {
        font-size: 14px;
        position: absolute;
        left: 10px;
        top: -25px
    }
    .c-one-family .c-row-steps, .c-one-family .ch-column, .c-one-family .cns-body {
        width: 100%
    }
    .col-rate {
        border-radius: 8px;
        width: 22rem
    }
    @media (max-width: 767px) {
        .col-rate {
            width: 300px
        }
    }
    .col-rate * {
        line-height: 1.2;
        white-space: normal
    }
    .col-rate .btn * {
        color: #fff !important;
        transition: all .2s linear
    }
    .border {
        border: 1px solid #dee2e6 !important
    }
    .position-relative {
        position: relative !important
    }
    .c-rate-continue {
    border-color: #eee!important
    }
    .c-rate-continue .chc-footer {
        background-color: #f3f3f3
    }
    .c-rate-continue .btn {
        background: #00a0e4;
        border: 1px solid #00a0e4
    }
    .c-rate-continue .btn:hover {
        background: #0090cd !important;
    }
    .family-rate-1 .c-rate-style {
        background-color: #5c469c
    }
    .family-rate-1 .chc-header h3 {
        color: #5c469c
    }
    .family-rate-1 .chc-header h3 span {
        color: #5c469c
    }
    .c-rate-active {
        border-color: #428e1e !important
    }
    .c-rate-active .chc-footer {
        background-color: #e4f3d1
    }
    .c-rate-active .btn {
        background: #428e1e;
        border: 1px solid #428e1e
    }
    .c-rate-active .btn:hover {
        background: #3b7f1b !important;
    }
    .c-rate-active .c-rate-style {
        background-color: #428e1e
    }
    .c-rate-active .chc-header h3 {
        color: #428e1e;
        font-size: 23px;
    }
    @media (max-width: 767px) {
        .c-rate-active .chc-header h3 {
            font-size: 20px;
        }
    }
    .c-rate-active .rate-selected * {
        color: #428e1e;
        font-size: 14px
    }
    .c-rate-active .c-btn-rate {
        background-color: #e4f3d1
    }
    .c-rate-style {
        background-color: #ccc;
        border-radius: 4px 0 10px 0;
        left: 0;
        height: 30px;
        position: absolute;
        top: 0;
        width: 10px
    }
    .c-rate-active .c-rate-style {
        background-color: #428e1e
    }
    .family-rate-1 .c-rate-style {
        background-color: #5c469c
    }
</style>