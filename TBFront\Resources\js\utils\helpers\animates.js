
export const goSectionAnimate = (id, gap = 45) => {
    let element = document.querySelector(`#${id}`);
    if (element && element.scrollIntoView) {
        element.scrollIntoView({ behavior: "smooth" });
    } else {
        $('html, body').scrollTop($(`#${section}`).offset().top - gap);
    }
};



export const windowScrollTop = () => {
    window.scrollTo(0, 0);
};


export const hidenCloak = () => {
    setTimeout(function () {
        var cloaks = document.querySelectorAll('.cloak');

        cloaks.forEach(function (cloak) {
            cloak.classList.remove('cloak');
        });

    }, 200);
};
