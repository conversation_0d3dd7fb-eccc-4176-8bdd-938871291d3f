﻿@using TBFront.Helpers
@using Microsoft.Extensions.Options
@using TBFront.Options;
@using TBFront.Types;
@using TBFront.Models.Meta.Metatags
@inject IOptions<SettingsOptions> settingOptions

@using TBFront.Models.Request

@inject ViewHelper _
@inject StaticHelper staticHelper
@{
    var resolution = _.GetImageResolution();
    var isMobile = resolution.Device == DeviceType.Mobile;
    var isRobot = _.IsRobot();

    ViewData["Page"] = "home";

    ViewData["IsRobot"] = isRobot;
    ViewData["isMobile"] = isMobile;
    ViewData["Resolution"] = resolution;
    var data = ViewData["Request"] as VoucherRequest;
    var culture = ViewData["CultureData"] as Culture;
    var isValid = (bool)ViewData["ValidRequest"];

}

@await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.Code}/_Header.cshtml", new ViewDataDictionary(ViewData) { { "navs", true }, { "login", isRobot } })

<div class="container-default-loading">
    <checkout-three-page></checkout-three-page>
</div>

<div >
    @await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.Code}/_Footer.cshtml", new ViewDataDictionary(ViewData) { { "newsletter", false } })
</div>


@section Meta {
    @await Html.PartialAsync("_MetaMain", new ViewDataDictionary(ViewData) { { "Meta", ViewData["MetaTag"] }})
    <meta name="robots" content="noindex,nofollow" />
    <meta name="googlebot" content="noindex,nofollow" />
}


@section Preload {
    <link rel="preconnect" href="@settingOptions.Value.CloudCdn">
    <link rel="preload" href="@staticHelper.GetVersion($"/{settingOptions.Value.SiteName}/css/confirmation.css", settingOptions.Value.Assets)" as="style" />
}

@section Css {
    <link type="text/css" rel="stylesheet" href="@staticHelper.GetVersion($"/{settingOptions.Value.SiteName}/css/confirmation.css", settingOptions.Value.Assets)">
   
}



@section ScriptsPriority {
}

@section Scripts {
    @if (isValid)
    {
        <script>
            window.__pt = window.__pt || {};
            window.__pt.data = @Json.Serialize(data);
            window.__pt.cultureData = @Json.Serialize((ViewData["CultureData"]));
            window.__pt.exchange = @Json.Serialize((ViewData["Exchange"]));
        </script>
    }
    <script src="@staticHelper.GetVersion("/js/vue/app.js", settingOptions.Value.Assets)"></script>
}