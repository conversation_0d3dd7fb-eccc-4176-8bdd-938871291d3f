.table-list-container {
  border: var(--border-width-sm) solid var(--border-subtle);
  border-radius: var(--border-radius-sm);
}

.iata {
  font: var(--body-sm);
  color: var(--text-subtle);
  text-transform: uppercase;
  font-size: 12px;
}

.table-list-header {
  border-bottom: var(--border-width-sm) solid var(--border-subtle);
  align-items: center;
  font: var(--title-xxs);
  color: var(--text-strong);

  .mgs-price {
    display: grid;
    text-align: end;
    padding-top: var(--space-8);
    padding-bottom: var(--space-8);

    .mp-text {
      font: var(--body-sm-bold);
      color: var(--text-strong);
    }
    .mp-price {
      font: var(--title-xs);
      color: var(--text-strong);
    }
    .mp-tax {
      font: var(--body-sm);
      color: var(--text-subtle);
    }
  }
  .mgs-airline{
    img {
      width: 40px;
    }
    .ma-iata{
      margin-left: .3rem;
    }
  }
  @media (max-width: 767px) {
    .mgs-airline{
      display: grid;
      text-align: center;
      justify-items: center;
      img {
        width: 40px;
      }
      .ma-airline{
        font-size: .8rem;
      }
      .ma-iata{
        font-size: .8rem;
      }
    }
  }

}
.table-list {
  .row-item {
    padding-top: var(--space-12);
    padding-bottom: var(--space-12);
    border-bottom: var(--border-width-sm) solid var(--border-subtle);

    .list-price-flight {
      display: flex;
      align-items: center;
      justify-content: end;
      font: var(--title-xxs);
      color: var(--text-strong);
    }

    .list-price-flight > .icon {
      color: var(--icon-primary);
      font-size: 28px;
    }
  }

 /*  div:last-child {
    border-bottom: none;
    border-radius: 0 0 var(--border-radius-sm) var(--border-radius-sm);
  } */

  .dashed-line-container {
    display: flex;
    align-items: center;
    justify-content: space-around;
    line-height: 1;

    .dlc-time {
      font-family: 'Roboto-Bold';
      font-size: 1.125rem;
      color: var(--text-subtle);
    }

    .dlc-duration {
      color: var(--text-subtle);
      font: var(--tight-sm);
    }

    @media (max-width: 767px) {

      .dlc-time {
        font-family: 'Roboto-Bold';
        font-size: 1rem;
        color: var(--text-subtle);
      }

      .dlc-duration {
        color: var(--text-subtle);
        font: var(--tight-sm);
        font-size: 11px;
      }
    }

  }

  .dashed-line-space{
    flex-grow: 1;
    position: relative;
  }
      .dashed-line-iata-depurate{
        position: absolute;
        top: -16px;
        left: 10px;
        font-size: 10px;
        color: var(--text-subtle);
      }
      .dashed-line-iata-arrival{
        position: absolute;
        top: -16px;
        right: 10px;
        font-size: 10px;
        color: var(--text-subtle);
      }

  .dashed-line {
    /* flex-grow: 1; */
    border: var(--border-width-sm) dashed var(--border-strong);
    margin: 0 var(--space-8);

  }

    @media (max-width: 374.9px) {
      .dashed-line {
        display: none;
      }
  
      .dlc-duration {
        display: none;
      }
      .dlc-icon{
        display: unset !important;
      }
    }

}

.table-list-footer {
  text-align: center;
  font: var(--tight-bold);
  color: var(--text-link);
  padding-top: var(--space-12);
  padding-bottom: var(--space-12);
  background-color: var(--bg-level1);
  border-radius: 0 0 var(--border-radius-sm) var(--border-radius-sm);
  box-shadow: 0 0 12px #d8d8d8;
  margin-top: -20px;
  position: relative;

  .icon-expand {
    font-size: 28px;
  }
}

.info-line-container {
  display: flex;
  align-items: center;
  justify-content: space-around;
  
  @media (min-width: 767px) and (max-width: 1023.9px) {
    justify-content: space-between;
  }

  .ilc-days {
    font: var(--tight-sm);
    color: var(--text-subtle);
    width: 50px;
  }

  .ilc-scales {
    font: var(--body-bold);
    color: var(--text-primary);
  }

  .ilc-bags {
    font-size: 1rem;
    min-width: 34px;
    color: var(--icon-subtle);
  }

  @media (max-width: 767px) {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .ilc-days {
      order: 5 !important;
      font: var(--tight-sm);
      visibility: hidden;
    }

    .ilc-scales {
      font: var(--body-sm-bold);
      font-size: 14px;
      color: var(--text-primary);
    }

    .ilc-bags {
      font-size: 1rem;
      order: -1 !important;
      margin-right: 0.75rem;
    }
  }
}

.text-ellipsis {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

.cheap-active {
  background-color: #ffff54;
  border: 1px solid var(--border-subtle);
  border-radius: var(--border-radius-full);
  padding: 2px 6px;
}

.exponent {
  font: var(--body-sm);
  font-size: 12px;
  color: var(--text-subtle);
  position: absolute;
  right: -18px;
  top: -2px;
}

.space-price-multipleairport {
  margin-top:  0.825rem;
}

.space-price-multipleairport-cheap{
  margin-top: 0.65rem;
}
.space-price{
  margin-top: -.25rem;
}
.space-price-cheap{
  margin-top: -.4rem;
}

@media (max-width: 576px) {
  .px-sm-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }
}

.flight-selected-collapse { // seccion de vuelo seleccionado
  .airline-title {
    font: var(--title-xxs);
    color: var(--text-strong);
  }

  .mgs-price {
    display: grid;
    text-align: end;
    span:nth-child(1) {
      font: var(--body-sm-bold);
    }

    span:nth-child(2) {
      font: var(--title-xs);
    }

    span:nth-child(3) {
      font: var(--body-xs);
      color: var(--text-subtle);
    }
  }

  .accordion-button {
    border-top: 0px solid $gray-300;
    background-color: rgba(248, 249, 250, 1);
    --bs-accordion-active-bg: #f5f5f5;
    --bs-accordion-active-color: #212529;
    --bs-accordion-btn-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23186CDF'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z' stroke='%23186CDF' stroke-width='1' /%3e%3c/svg%3e");
    --bs-accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23186CDF'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z' stroke='%23186CDF' stroke-width='1' /%3e%3c/svg%3e");
  }

  .accordion-item {
    border: unset;
    background-color: rgba(248, 249, 250, 1);
  }
  .accordion-button::after {
    margin-left: 5px;
  }

  .accordion-button:focus {
    border-color: unset;
    box-shadow: unset;
  }

  .accordion-button:not(.collapsed) {
    box-shadow: unset;
  }

  .text-toggle {
    color: $navy-500;
  }
  .text-toggle-accordion {
    margin-right: 1rem !important;
  }
  .accordion-header {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }
  .row-item {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }

  .dashed-line-container {
    display: flex;
    align-items: center;
    justify-content: space-around;
    .dlc-time {
      font-size: 1rem;
      font-family: 'Roboto-Bold';
      color: var(--text-subtle);
    }

    .dlc-duration {
      font-size: 11px;
      color: var(--text-subtle);
      font: var(--tight-sm);
    }
  }

  .dashed-line-wrapper {
    position: relative;
    flex-grow: 1;
    margin: 0 10px;
  }

  .dashed-line {
    border-bottom: var(--border-width-sm) dashed var(--border-strong);
    width: 100%;
    position: relative;
  }

  @media (max-width: 400px) { // mas grande por el contenedor colapsable
    .dashed-line {
      display: none;
    }

    .dlc-duration {
      display: none;
    }
    .dlc-icon{
      display: unset !important;
    }
  }

  .dashed-line-text-departure {
    position: absolute;
    top: -20px;
    left: 0%;
    font-size: 12px;
    white-space: nowrap;
    color: #a6acb4;
  }

  .dashed-line-text-arrival {
    position: absolute;
    top: -20px;
    right: 0%;
    font-size: 12px;
    white-space: nowrap;
    color: #a6acb4;
  }

  @media (max-width: 767px) {
    .text-toggle-accordion {
      margin-right: auto !important;
      padding: 0.25rem;
    }
    .accordion-header {
      padding-top: 0rem;
      padding-bottom: 0rem;
    }
  }
}

.sticky-settings {
  position: sticky;
  top: 0;
  background-color: var(--bg-base);
  z-index: 1;

  .info-header-sticky {
    display: none;
  }
  @media (min-width: 767px) {
    .info-header-sticky {
      display: unset;
    }
  }
}

.main-header-list {
  span:nth-child(1) {
    display: flex;
    align-items: center;
    gap: var(--space);
    font: var(--title-xl);
    color: var(--text-strong);
    margin-bottom: 0.25rem;
  }

  span:nth-child(2) {
    font: var(--body);
    color: var(--text-strong);
  }
}

.btn-change{
  font: var(--body-bold);
    color: var(--text-link);
}

.bg-selected {
  background-color: #f2f2f2 !important;
  border-radius: 10px !important;
}

.icon-chevron-right-price {
  background-image: url("data:image/svg+xml,%3Csvg height='64px' width='64px' version='1.1' id='Capa_1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' viewBox='-18.53 -18.53 222.40 222.40' xml:space='preserve' fill='%23186cdf' stroke='%23186cdf' stroke-width='13.900725'%3E%3Cg id='SVGRepo_bgCarrier' stroke-width='0'%3E%3C/g%3E%3Cg id='SVGRepo_tracerCarrier' stroke-linecap='round' stroke-linejoin='round' stroke='%23CCCCCC' stroke-width='1.4827439999999998'%3E%3C/g%3E%3Cg id='SVGRepo_iconCarrier'%3E%3Cg%3E%3Cg%3E%3Cpath style='fill:%23186cdf;' d='M51.707,185.343c-2.741,0-5.493-1.044-7.593-3.149c-4.194-4.194-4.194-10.981,0-15.175 l74.352-74.347L44.114,18.32c-4.194-4.194-4.194-10.987,0-15.175c4.194-4.194,10.987-4.194,15.18,0l81.934,81.934 c4.194,4.194,4.194,10.987,0,15.175l-81.934,81.939C57.201,184.293,54.454,185.343,51.707,185.343z'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  width: 16px;
  height: 16px;
  align-self: center;
}