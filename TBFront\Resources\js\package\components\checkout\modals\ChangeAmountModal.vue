<template>
    <div class="modal fade modal-change" id="modal-priceChange" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered modal-md">
            <div class="modal-content">
                <div class="modal-header">
                    <p class="modal-title font-16 font-regular">{{__('messages.change_your_reservation')}}</p>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body px-2">
                    <template v-if="strPrice == 'priceUp'">
                        <span class="icon icon-bell d-block text-center color-green mb-3 font-26" :class="{'color-subtle': strPrice == 'priceUp' }"></span>
                        <h5 class="text-center mb-3">{{ __('messages.chage_price') }}</h5>
                    </template>
                    <template v-if="strPrice == 'priceDown'">
                        <span class="icon icon-bell d-block text-center color-green mb-3 font-26"></span>
                        <h5 class="text-center mb-3">
                            ¡{{ __('messages.price_your_flight_has_down') }} <br> <span class="color-green">
                                <CurrencyDisplay :amount="diffPrice" :showCurrencyCode="true" />
                            </span> !
                        </h5>
                    </template>

                    <template v-if="strPrice == 'noAvailability'">
                        <div class="row">
                            <div class="col-12 d-flex justify-content-center mb-3">
                                <svg width="45" height="45" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M10.656 8.864q0-2.208 1.568-3.776t3.776-1.568 3.776 1.568 1.6 3.776q0 0.256-0.064 0.448l-1.76 6.944q-0.096 1.408-1.12 2.368t-2.432 0.96q-1.376 0-2.4-0.928t-1.152-2.304q-0.32-0.96-0.672-2.112t-0.736-2.784-0.384-2.592zM12.416 24.928q0-1.472 1.056-2.496t2.528-1.056 2.528 1.056 1.056 2.496q0 1.504-1.056 2.528t-2.528 1.056-2.528-1.056-1.056-2.528z"
                                          fill="#bd030a"></path>
                                </svg>
                            </div>
                            <div class="col-12 d-flex justify-content-center mb-3 text-center">
                                <h4 style="color: #bd030a;">{{ __('messages.error_reserv') }}</h4>
                            </div>

                            <div class="col-6 d-flex justify-content-center mb-3">

                            </div>
                        </div>
                    </template>
                    <!--- Cambio de familias en la reservacion -->
                    <template v-if="oldDepartureFamily !== newDepartureFamily">
                        <div class="alert alert-light px-2 font-14 mx-2 py-1">
                            {{__("messages.the_rate")}} <strong>{{quote.familyFareName ? quote.familyFareName : oldDepartureFamily}}</strong> <span v-html="__('messages.is_not_available_html')"></span> <strong>{{quote.newFamilyFareName ? quote.newFamilyFareName : newDepartureFamily}}</strong>.
                        </div>
                    </template>
                    <!--- informacion de cambio de precio -->
                    <template v-if="strPrice == 'priceUp' || strPrice == 'priceDown'">
                        <div class="row mx-0">
                            <div class="col pe-0 ps-0">
                                <div class="alert alert-light px-2 font-14 mx-2 py-1 bg-white">
                                    <span class="d-block text-center font-14 font-semibold color-gray-100">{{ __('messages.before') }}</span>
                                    <span class="d-block text-center font-18 font-semibold color-gray-100">
                                        <CurrencyDisplay :amount="totalAmountInit" :showCurrencyCode="true" />
                                    </span>
                                </div>
                            </div>
                            <div class="col-1 px-0">
                                <span class="icon icon-arrow-forward color-green font-26 d-block mt-2 pt-2 ms-1 color-green" :class="{'color-subtle': strPrice == 'priceUp' }"></span>
                            </div>
                            <div class="col ps-0 pe-0">
                                <div class="alert alert-light px-2 font-14 mx-2 py-1 bg-white border-hard">
                                    <span class="d-block text-center color-green font-14" :class="{'color-subtle': strPrice == 'priceUp' }">{{__('messages.today')}}:</span>
                                    <span class="d-block text-center font-18 font-bold" :class="{'color-green': strPrice == 'priceDown'}">
                                        <CurrencyDisplay :amount="summary.totalAmount" :showCurrencyCode="true" />
                                    </span>
                                </div>
                            </div>
                            <div class="col-12">
                                <p class="text-center">{{__('messages.finalize_your_reservation')}}</p>
                            </div>

                        </div>
                    </template>
                </div>
                <div class="modal-footer border-0 pt-0">
                    <button v-if="strPrice != 'noAvailability'" type="button" id="CloseButton" class="btn w-100-xs py-3 btn-orange px-4" data-bs-dismiss="modal" aria-label="Close">{{__('messages.ok')}}</button>
                    <button v-else type="button" id="CloseButton" class="btn w-100-xs py-3 btn-orange px-4" v-on:click="goBack()">{{ __('messages.return_label') }} </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>

    const site = window.__pt.settings.site;
    import { FlightsAnalytic } from '../../../../utils/analytics/FlightsAnalytics';
    import CurrencyDisplay from '../../common/CurrencyDisplay.vue';
    export default {
        props: {
            quote: {},
            summary: {},
            productUrl: '',
            totalAmountInit: 0
        },
        components: {
            CurrencyDisplay
        },
        data() {
            return {
                diffPrice: 0,
                strPrice: "",
                siteConfig: site,
                oldDepartureFamily: '',
                newDepartureFamily: '',
                oldReturningFamily: '',
                newReturningFamily: '',
            };
        }, async mounted() {

            this.setDiff();
            this.setDiffFamily()
        },
        methods: {
            setDiff() {
                this.diffPrice = this.summary.totalAmount - this.summary.totalAmountOld;
                if (this.summary.totalAmount > this.summary.totalAmountOld) {
                    this.strPrice = "priceUp";
                    FlightsAnalytic.setChangeFare({
                        action: "mayor",
                        oldRate: this.summary.totalAmountOld,
                        newRate: this.summary.totalAmount
                    }, this.quote);
                } else if (this.summary.totalAmount < this.summary.totalAmountOld) {
                    this.strPrice = "priceDown";
                    FlightsAnalytic.setChangeFare({
                        action: "menor",
                        oldRate: this.summary.totalAmountOld,
                        newRate: this.summary.totalAmount
                    }, this.quote);
                } else if (this.summary.totalAmount == 0) {
                    this.strPrice = "noAvailability";
                    FlightsAnalytic.setChangeFare({
                        action: "no-disponible",
                        oldRate: this.summary.totalAmountOld,
                        newRate: this.summary.totalAmount
                    }, this.quote);
                }
            },
            goBack() {
                window.location.href = `${this.siteConfig.siteUrl}${this.productUrl}`;
            },
            close() {
                var modalPriceChange = bootstrap.Modal.getInstance(document.getElementById('modal-priceChange'));
                modalPriceChange.hide();
            },
            setDiffFamily() {
                if (this.quote.flightItinerary.starting?.fareGroup) this.oldDepartureFamily = this.quote.flightItinerary.starting?.fareGroup;
                /*if(this.quote.flightItinerary.returning?.fareGroup) this.oldReturningFamily = this.quote.flightItinerary.returning?.fareGroup*/
                if (this.quote.revalidate.faresByLeg[1][1]?.familyFare.code) this.newDepartureFamily = this.quote.revalidate.faresByLeg[1][1]?.familyFare.code;
                /*if(this.quote.revalidate.faresByLeg[2][1]?.familyFare.code) this.newReturningFamily = this.quote.revalidate.faresByLeg[2][1]?.familyFare.code*/
            }
        }
    }
</script>
