﻿@using System.Text.RegularExpressions
@using TBFront.Helpers
@using Microsoft.Extensions.Options
@using TBFront.Models.Login
@using TBFront.Models.Meta.Alternate
@using TBFront.Options
@using TBFront.Models.Configuration
@using TBFront.Types
@using Newtonsoft.Json
@using PT.Platform.B2C.User.Entities;

@inject ViewHelper viewHelper
@inject IOptions<SettingsOptions> settingOptions
@inject IOptions<SiteOptions> siteOptions
@inject IOptions<CurrencyOptions> currencyOptions
@inject StaticHelper staticHelper;

@{
    var navs = (bool)ViewData["navs"];
    var login = (bool)ViewData["login"];
    // var tabs = siteOptions.Value.Tabs;
    var phones = siteOptions.Value.Phones;
    var resolution = ViewData["Resolution"] as SectionResolution;
    var mobile = resolution.Device == DeviceType.Mobile;
    var citysUtf8 = viewHelper.GetSpecialCitiesWords();
    var routeHome = ViewData["IsRoutMain"] ?? "";
    var isRobot = viewHelper.IsRobot();
    var user = ViewData["User"] as User;
    var page = ViewData["Page"];
    var culture = ViewData["CultureData"] as Culture;
    var alternates = ViewData["Alternates"] as AlternateMain;
    var currencyConfiguration = ViewData["currencyData"] as Currency;
    var queryString = Context.Request.QueryString.Value;
    var checkout = (bool)(ViewData["Checkout"] ?? false);
    var query = viewHelper.GetCurrentQueryStringCom(Context);
    var userLocation = ViewData["UserLocation"] as UserLocation;

    if (culture is null)
    {
        culture = new Culture();
    }

    if (userLocation is null)
    {
        userLocation = new UserLocation();
    }
}

<language-currency-modal></language-currency-modal>
<!--Header-->
<header class="cntb-header container-fluid shadow-sm position-relative">
    @if (page == "home")
    {
        <div class="cm-ctrl">
            <div class="cm-ctrl-left"></div>
            <div class="cm-ctrl-right ccr-blur" id="scrollRightHeader"></div>
        </div>
    }
    <div class="container px-0">


        <div class="d-flex justify-content-between header-top-desk">
            <div class="cn-logo my-2 mb-md-0 my-lg-2">
                <a class="navbar-brand" href="@settingOptions.Value.SiteUrl" title="@settingOptions.Value.AppName">
                    <img class="logo-tb" width="@(mobile ? "80": "100%" )" height="@(mobile ? "auto": "auto" )" alt="@settingOptions.Value.AppName"
                        src="@(settingOptions.Value.CloudCdn)/assets-tb/img/logo-tiquetesbaratos.png"
                        srcset="@(settingOptions.Value.CloudCdn)/assets-tb/img/logo-tiquetesbaratos.png 1x" />
                </a>
            </div>
            <div class="ch-menu-up d-flex align-items-center">
                <!--------------- LANGUAGE & CURRENCY--------------->
                @if (!checkout)
                {
                    <div class="hide-xs">
                        <language-currency-button :culture='@Json.Serialize(culture)' :currency='@Json.Serialize(currencyConfiguration)' :mobile="@Json.Serialize(false)"></language-currency-button>
                    </div>
                    
                }
                <div id="idIconMenu" class="ch-btn-mobile hide-md hide-lg hide-medium" onclick="window.__pt.settings.site.openMenuHeader()">
                    <span class="icon icon-menu font-30"></span>
                </div>
                <div id="idMenuMobile" class="hide-xs">
                    <ul class="c-menu-mobile list-unstyled mb-0 ch-mobile-menu py-md-0 pe-lg-0">
                        <div class="cm-head cmh-login px-2 @(page == "Checkout" && user != null ? "pt-2" : "")" style="@(page == "Checkout" ? "padding-top: 0px; padding-bottom: 0px;height: 51px;" : "") ">
                            <span id="idCloseMenu" class="@(user == null && page == "Checkout" ? "window_closeBtnCheck" : "window_closeBtn") icon icon-close hide-md hide-lg hide-medium" onclick="window.__pt.settings.site.closeMenuHeader()"></span>
                            <!--------------- LOGIN --------------->
                            @if (navs && user == null && settingOptions.Value.Login)
                            {
                                <div class="col-12 ps-3 mb-3">
                                    <span class="icon icon-person-black font-18"></span>
                                    <span class="font-20 font-bold">¡@viewHelper.Localizer("hi")!</span>
                                </div>
                                <div class="px-5 info-login">
                                    
                                    <p class="text-save">@viewHelper.Localizer("ahorrar_hasta")</p>
                                    <a href="/@culture?.CultureCode/login" class="d-block">@viewHelper.Localizer("sign_in") <span class="icon icon-keyboard-right font-22"></span></a>
                                </div>
                            }
                            <!--------------- LOGGED --------------->
                            @if (navs && user != null && settingOptions.Value.Login)
                            {
                                <div class="col-12 ps-3 @(page == "Checkout" ? "mt-2 mb-2" : "mb-3") ">
                                    <span class="icon icon-person-black font-18"></span>
                                    <span class="font-20 font-bold session-title">¡@viewHelper.Localizer("hi"), @(user.UserProfile.Name)!</span>
                                </div>
                                <div class="px-5">
                                    @if (navs)
                                    {
                                        <a href="#" onclick="event.preventDefault(); document.getElementById('logout-form').submit();" class="d-block">@viewHelper.Localizer("log_out") <span class="icon icon-keyboard-right font-22"></span></a>
                                    }                                   
                                </div>
                            }

                            
                            
                        </div>
                        <li id="idClickAyuda" class="ps-2 pt-4 pb-md-2 pt-md-2 order-md-2 order-2 position-relative mx-lg-2 mx-md-2 tabs-header">
                            <div class="cd-title mt-2 mt-md-0">
                                <span class="icon icon-help hide-md hide-md-pro hide-lg font-18 position-relative me-1"></span>
                                <span class="hide-xs">@viewHelper.Localizer("ayuda")</span>
                                <span class="hide-md hide-medium hide-md-pro hide-lg">@viewHelper.Localizer("sericio_cliente")</span>
                                <span class="icon icon-expand i-arrow hide-xs"></span>
                            </div>
                            <div id="idContainerAyuda" class="ch-dropdown chd-default px-0  pt-3 pt-md-0 pb-0 d-none d-xs">
                                <ul class="px-4 p-md-0 pb-3 pb-md-0">
                                    <li class="mb-3 mb-md-0 text-truncate px-md-3 py-md-2">
                                        <a href="@(settingOptions.Value.SiteUrl)/@culture?.CultureCode/vuelos/preguntas-frecuentes" class="text-dark">@viewHelper.Localizer("preguntas-frecuentes")</a>
                                    </li>
                                    <li class="mb-3 mb-md-0 text-truncate px-md-3 py-md-2" onclick="window.__pt.settings.site.openModal()">
                                        <a class="text-dark">@viewHelper.Localizer("contactUsTitle")</a>
                                    </li>
                                    <li class="mb-3 mb-md-0 text-truncate px-md-3 py-md-2">
                                        <a class="text-dark" href="@(settingOptions.Value.SiteUrl)/@culture?.CultureCode/vuelos/escribenos">@viewHelper.Localizer("writeUsTitle")</a>
                                    </li>
                                    <li class="mb-3 mb-md-0 text-truncate px-md-3 py-md-2">
                                        <a class="text-dark" href="@(settingOptions.Value.SiteUrl)/@culture?.CultureCode/vuelos/grupos">@viewHelper.Localizer("viajes-en-grupo")</a>
                                    </li>
                                    <li class="mb-3 mb-md-0 text-truncate px-md-3 py-md-2 d-flex align-items-center justify-content-center-xs">
                                        <span class="icon icon-whatsapp pr-2 pr-lg-0"></span>
                                        <a href="http://wa.me/573104915803" title="Whatsapp" target="_blank" class="text-dark ms-2">@viewHelper.Localizer("whatsapp")</a>
                                    </li>
                                    <li class="mb-3 mb-md-0 text-truncate px-md-3 py-md-2 d-flex align-items-center justify-content-center-xs">
                                        <span class="icon icon-messenger pr-2 pr-lg-0"></span>
                                        <a href="https://m.me/128565927176678/" title="Messenger" target="_blank" class="text-dark ms-2">@viewHelper.Localizer("messenger")</a>
                                    </li>
                                </ul>
                            </div>
                        </li>
                        <li id="idClickPhone" class="ps-2 py-2 position-relative order-3 order-md-1 mx-lg-2 mx-md-2 tabs-header">
                            <div class="cd-title mt-2 mt-md-0 ">
                                <span class="icon icon-phone me-1"></span>
                                <span class="hide-xs hide-md">@viewHelper.Localizer("para-reservar")</span> <strong class="hide-xs hide-md">************</strong>
                                <span class="hide-md hide-lg hide-medium">@viewHelper.Localizer("reserva-por-teléfono") </span>
                                <span class="icon icon-expand i-arrow hide-xs"></span>
                                @if (navs)
                                {
                                    <ul class="pt-3 p-md-0 hide-md hide-lg hide-medium">
                                        <li class="mb-md-0 text-truncate px-md-3 py-md-2">
                                            <p class="a-link-xs">@viewHelper.Localizer("llamar-a-un-asesor")</p>
                                        </li>
                                    </ul>
                                }
                            </div>
                            
                            <div id="idContainerPhone" class="ch-dropdown bg-white px-0 pt-4 pt-md-3 pb-0 d-none chd-mobile">
                                <div class="row px-3 px-md-0">
                                    <div class="col-12 d-flex justify-content-between mb-2 hide-md hide-lg hide-medium">
                                        <p class="font-24 strong ps-3">@viewHelper.Localizer("llamar-a-un-asesor")</p>
                                        <span class="icon icon-chevron-left font-40"></span>
                                    </div>
                                    @{
                                        string phonesString = JsonConvert.SerializeObject(phones);
                                        string specialCitiesWordsString = JsonConvert.SerializeObject(citysUtf8);
                                    }
                                    <header-phones 
                                        :special="'@(specialCitiesWordsString)'"
                                        :phone="'@(phonesString)'">
                                    </header-phones>
                                    <div class="col-12 hide-xs" onclick="window.__pt.settings.site.openModal()">
                                        <div class="chd-footer-sm px-3 py-3 d-flex justify-content-center hide-xs">
                                            <span class="icon icon-support-agent color-link font-18 position-relative mr-1 me-1"></span>
                                            <span>
                                                <a class="color-link a-link text-center">@viewHelper.Localizer("contactUsTitle")</a>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>

                        @if (navs)
                        {
                            
                            <li id="idMyfavorites" class="px-2 pb-md-2 pt-md-2 order-1 order-md-3 position-relative mx-lg-2 mx-md-2 hide-xs">
                                <div class="cd-title mt-2 mt-md-0">
                                    <span class="icon icon-fav me-1 position-relative"></span>
                                    <a class="text-dark hide-md hide-xs" href="/@culture.CultureCode/favoritos">@viewHelper.Localizer("favoritos")</a>
                                </div>
                            </li>
                            <li id="idCurrency">
                                <ul class="tabs px-4 pt-4 p-md-0 hide-md hide-lg hide-medium pb-1 separator-section-language">
                                    <li class="mb-3 mb-md-0 text-truncate px-md-3 py-md-2">
                                        <language-currency-button :culture='@Json.Serialize(culture)' :currency='@Json.Serialize(currencyConfiguration)' :mobile="@Json.Serialize(true)"></language-currency-button>
                                    </li>
                                </ul>
                            </li>
                            // si se va a ocultar en home colocar esta linea en la clase @(page == "home" ? "hide-xs" : "")
                            <li id="idtabs" class="  pb-md-2 pt-md-2 order-1 order-md-3 position-relative ms-lg-2 ms-md-2 hide-lg hide-md hide-medium hide-lg-pro">
                                <ul class=" tabs pb-2">
                                    @foreach (var tab in culture.Tabs)
                                    {
                                        <li class="mb-3 mb-md-0 text-truncate px-md-3 py-md-2">
                                            <span class="@tab.Class me-1"></span>
                                            <a class="text-dark" href="@tab.Url" @if (tab.IsDisney()) {  @Html.Raw("target='_blank'") }>
                                                @viewHelper.Localizer(tab.Title)      
                                            </a>
                                        </li>
                                    }
                                </ul>                               
                            </li>
                            <li id="idClickMisViajes" class="ps-2 py-4 pb-md-2 pt-md-2 order-1 position-relative mx-lg-2 mx-md-2 tabs-header">
                                <div class="cd-title mt-2 mt-md-0">
                                    <span class="icon icon-suitecase position-relative me-1"></span>
                                    <span>@viewHelper.Localizer("mis-viajes")</span>
                                    <span class="icon icon-expand i-arrow hide-xs"></span>
                                </div>
                                <div id="idContainerMisViajes" class="ch-dropdown chd-default px-0 pt-3 pt-md-0 pb-0 d-none d-xs">
                                    <ul class="px-4 pb-3 pb-md-0 p-md-0">
                                        <li class="mb-3 mb-md-0 text-truncate px-md-3 py-md-2">
                                            <a href="@(settingOptions.Value.SiteUrl)/@culture?.CultureCode/vuelos/consultar-reservas" class="text-dark">@viewHelper.Localizer("consultReservation")</a>
                                        </li>
                                        <li class="mb-3 mb-md-0 text-truncate px-md-3 py-md-2">
                                            <a href="@(settingOptions.Value.SiteUrl)/@culture?.CultureCode/vuelos/check-in" class="text-dark">@viewHelper.Localizer("webCheckIn")</a>
                                        </li>
                                        <li class="mb-3 mb-md-0 text-truncate px-md-3 py-md-2">
                                            <a href="@(settingOptions.Value.SiteUrl)/@culture?.CultureCode/vuelos/pago-en-linea" class="text-dark">@viewHelper.Localizer("paymentOnline")</a>
                                        </li>
                                        <li class="mb-3 mb-md-0 text-truncate px-md-3 py-md-2">
                                            <a href="@(settingOptions.Value.SiteUrl)/@culture?.CultureCode/vuelos/consultar-reservas" class="text-dark">@viewHelper.Localizer("facturacion")</a>
                                        </li>
                                    </ul>
                                </div>
                            </li>


                            <!--------------- LOGIN --------------->
                            @if (navs && user == null && settingOptions.Value.Login)
                            {     
                                <li class="ms-lg-4" >
                                    <div class=" order-2 position-relative ">
                                        <button class="header_navbar_btn btn login d-none d-md-flex" role="button" data-bs-toggle="collapse" data-bs-target="#no_login_content" aria-controls="no_login_content" aria-expanded="false" aria-label="Toggle navigation">
                                            <span class="d-none d-sm-block menu-text fw-semibold">@viewHelper.Localizer("sign_in")</span>
                                        </button>
                                        <div class="header_navbar_dropdown_pt collapse user_navbar collapse navbar-collapse" id="no_login_content">
                                            <div class="header_navbar_dropdown_window">
                                                <p class="mb-0">@viewHelper.Localizer("save_up_to_10_percent")</p>
                                                <a class="btn-primary back-remo my-3" href="/login">@viewHelper.Localizer("sign_in")</a>
                                                <a class="btn-link" href="/login">@viewHelper.Localizer("create_account")</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            }
                        }

                        <!--------------- LOGGED --------------->
                        @if (navs && user != null && settingOptions.Value.Login)
                        { 
                            <li class="ms-4" >
                                <div class="order-2 position-relative ">
                                    <button class="header_navbar_btn-user-logged  d-none d-md-flex" data-bs-toggle="dropdown" aria-expanded="false">
                                        <div class="header_navbar_btn-user-logged_pic" style="background-image: url(@(user.UserProfile.Image))">
                                            <span> @(string.IsNullOrEmpty(user.UserProfile.Image) ? (!string.IsNullOrEmpty(user.UserProfile.Name) ? user.UserProfile.Name[0] : user.UserProfile.ContactEmail[0]) : "") </span>
                                        </div>
                                        <div class="d-md-none d-lg-block text-left">
                                            <span>@viewHelper.Localizer("hi")</span> <br>
                                            <span class="header_navbar_btn-user-logged_name fw-bold">@(user.UserProfile.Name)</span>
                                        </div>
                                        @if (navs)
                                        {
                                            <div class="divider"></div>
                                            <span class="icon icon-expand i-arrow hide-xs"></span>
                                        }
                                    </button>
                                    @if (navs)
                                    {
                                        <div class="header_navbar_dropdown user-logged dropdown-menu dropdown-custom-navbar">
                                            <ul>
                                                <li class="log-out w-100">
                                                    <a href="#" class="text-dark" onclick="event.preventDefault(); document.getElementById('logout-form').submit();" title="Log out" role="button">@viewHelper.Localizer("log_out")</a>
                                                </li>
                                            </ul>
                                        </div>
                                    }

                                </div>
                            </li>
                        }

                        <div class="chd-footer px-3 py-3 d-flex justify-content-center hide-md hide-lg hide-lg-pro">
                            <span class="icon icon-support-agent color-link font-18 position-relative mr-1 me-1"></span>
                            <span onclick="window.__pt.settings.site.openModal()">
                                <a class="color-link a-link text-center">@viewHelper.Localizer("contactUsTitle")</a>
                            </span>
                        </div>
                    </ul>
                </div>
            </div>
        </div>
        @if (navs)
        {
            <ul class="@(page != "home" ? "hide-xs" : "") list-unstyled c-submenu position-relative mb-0 mt-2 d-flex gap-3" id="subMenu">
                @foreach (var tab in culture.Tabs)
                {
                    <li class="py-2 @(!tab.IsDisney() && tab.Active ? "" : "")">
                        <a class="text-dark p-2 navi-tabs @(!tab.IsDisney() && tab.Active ? "m-active" : "") " href="@tab.Url"
                        @if (tab.IsDisney())
                        {
                            @Html.Raw("target='_blank'")
                        }>
                            <span class="@tab.Class"></span>
                            <span>@viewHelper.Localizer(tab.Title)</span>
                        </a>
                    </li>
                }
            </ul>
        }
    </div>
</header>

@section Scripts {
    <script>
        window.__pt.cultureData = @Json.Serialize((ViewData["CultureData"]));
        window.__pt.exchange = @Json.Serialize((ViewData["Exchange"]));
    </script>

    <script src="@staticHelper.GetVersion("/js/vue/app.js", settingOptions.Value.Assets)"></script>
}

@* modal calls *@
<div class="modal fade" id="staticBackdrop" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <need-a-call></need-a-call>
        </div>
    </div>
</div>