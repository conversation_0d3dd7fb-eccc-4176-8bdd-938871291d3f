import { getDate } from "../helpers/dates";
import { setDatalayer, UTILS, rangeNight, countNight, isoWeekNumber, getPaxes  } from "./main"

export default class HotelListAnalytics {


    constructor() {
        this.settings = window.__pt.settings.site;
        this.places = window.__pt.places_info;
        this.hotels = window.__pt.hotel_response;
        this.request = window.__pt.data || {};
    }

    listFilters() {
        let starting = this.places.starting;
        let returning = this.places.returning;
        let request = this.request;
        let paxes = getPaxes(this.request.paxes)


        let event = {
            event: UTILS.events.gtmEvent,
            eventName: UTILS.events.gtmEvent,
            eventCategory: UTILS.categories.list_filters,
            eventAction: `o:${starting.code} | d:${returning.code}| o:${starting.description} | d:${returning.description} | o:${starting.placeId} | d:${returning.placeId} | ci:${request.checkIn} | co:${request.checkOut} | r:${request.paxes.length} | ${paxes.join}`,
            eventLabel: "Nombre{All}|Categoria{All}|Alimentos{All}|Interes{All}|Vista{All}|Amenidades{All}|Zonas{All}"
        }

        setDatalayer(event);
    }

    listFiltersLayer(category, filter) {

        let event = {
            content_type: "filtro",
            element: `${category} :: ${filter}`,
            event: UTILS.events.trackEvent,
            eventName: UTILS.events.select_content,
            layer: UTILS.misc.layer
        }

        setDatalayer(event);
    }

    listGallery(hotel) {
        let returning = this.places.returning;

        let event = {
            event: UTILS.events.gtmEvent,
            eventName: UTILS.events.gtmEvent,
            eventCategory: UTILS.categories.list_gallery,
            eventAction: UTILS.actions.click_more_images,
            eventLabel: `placeid:${returning.placeId}|hotelid:${hotel.hotelId}|images:${1}`
        }

        setDatalayer(event);
    }

    search(quote) {

        if (quote.token) {
            let starting = this.places.starting;
            let returning = this.places.returning;
            let flightItinerary = quote.flightItinerary;
            let weekNumber = isoWeekNumber(getDate(flightItinerary.stating.arrival.date));
            let type = starting.country == returning.country ? UTILS.misc.domestic : UTILS.misc.international;
            let rangeNightValue = rangeNight(flightItinerary.stating.departure.date, flightItinerary.returning.departure.date);

            let event = {
                event: UTILS.events.gtmEvent,
                eventName: UTILS.events.gtmEvent,
                eventCategory: UTILS.categories.search,
                eventAction: `${type} | ${rangeNightValue}`,
                eventLabel: `${flightItinerary.stating.departure.airport} | ${flightItinerary.returning.departure.airport}  | ${weekNumber}`
            }

            setDatalayer(event);
        }
        
    }

    ads(index) {
        let returning = this.places.returning;
        let request = this.request;
        let paxes = getPaxes(this.request.paxes)
        let checkIn = getDate(this.request.checkIn);
        let checkOut = getDate(this.request.checkOut);


        let event = {
            event: UTILS.events.ads,
            adDivId: `ad-skyscraper-${index + 1}`,
            step: UTILS.pages.hotel_list,
            placeid: returning.placeId,
            destination: returning.description,
            checkinYear: `${checkIn.getFullYear()}`,
            checkinMonth: `${checkIn.getMonth() + 1}`,
            checkinDay: `${checkIn.getDate()}`,
            checkoutYear: `${checkOut.getFullYear()}`,
            checkoutMonth: `${checkOut.getMonth() + 1}`,
            checkoutDay: `${checkOut.getDate()}`,
            nights: countNight(checkIn, checkOut),
            rooms: request.paxes.length,
            adults: paxes.adults,
            kids: paxes.children,
            productCountry: returning.country,
            profileId: request.profileId || "",
            campaignToken: request.campaignToken || "",
            product: "pk",
            hotelId: "",
            hotelStars: "0",
            paymentType: ""
        };

        setDatalayer(event);
    }

    impressionView(hotel, quote, index, page) {
        const hotelBase = this.hotelBase(hotel, quote);
        let event = {
            EECImpressionViewImpressions: [
                {
                    EECImpressionViewAdults: hotelBase.adults,
                    EECImpressionViewAvailable: hotelBase.available,
                    EECImpressionViewCheckin: hotelBase.checkIn,
                    EECImpressionViewCheckout: hotelBase.checkOut,
                    EECImpressionViewCity: hotelBase.city,
                    EECImpressionViewCountry: hotelBase.country,
                    EECImpressionViewDestinationAirline: hotelBase.destinationAirCode,
                    EECImpressionViewDestinationAirlineText: hotelBase.destinationAirline,
                    EECImpressionViewDestinationAirport: hotelBase.destinationAirport,
                    EECImpressionViewFlightType: hotelBase.type,
                    EECImpressionViewId: hotelBase.id,
                    EECImpressionViewKids: hotelBase.kids,
                    EECImpressionViewList: UTILS.actions.package_list,
                    EECImpressionViewMealplan: hotelBase.mealplan,
                    EECImpressionViewName: hotelBase.hotelName,
                    EECImpressionViewNights: hotelBase.nights, 
                    EECImpressionViewOriginAirline: hotelBase.originAirCode,
                    EECImpressionViewOriginAirport: hotelBase.originAirport,
                    EECImpressionViewPage: page,
                    EECImpressionViewPlaceId: hotelBase.placeId,
                    EECImpressionViewPosition: index + 1,
                    EECImpressionViews: 1,
                    EECImpressionViewRating: hotelBase.rating,
                    EECImpressionViewStars: hotelBase.stars,
                    EECImpressionViewState: hotelBase.state,
                    EECImpressionViewWeek: hotelBase.week,
                    EECImpressionViewWeekDay: hotelBase.weekDay,
                    EECImpressionViewOriginPlaceText: hotelBase.originText,
                    EECImpressionViewDestinationPlaceText: hotelBase.destinationText,
                    EECImpressionViewOriginAirlineText: hotelBase.originAirline,
                    EECImpressionViewProductUrl: hotelBase.url,
                    EECImpressionViewNightlyRate: hotelBase.priceByNight,
                    EECImpressionViewTotalRate: hotelBase.totalPrice,
                    EECImpressionViewImageUrl: hotelBase.image
                }
            ],
            event: UTILS.events.event_impresion_view
        }

        setDatalayer(event);
    }

    impressionClick(hotel, quote, index, page) {
        const hotelBase = this.hotelBase(hotel, quote);
        let event = {
            EECImpressionClickAdults: hotelBase.adults,
            EECImpressionClickAvailable: hotelBase.available,
            EECImpressionClickCheckin: hotelBase.checkIn,
            EECImpressionClickCheckout: hotelBase.checkOut,
            EECImpressionClickCity: hotelBase.city,
            EECImpressionClickCountry: hotelBase.country,
            EECImpressionClickDestinationAirline: hotelBase.destinationAirCode,
            EECImpressionClickDestinationAirlineText: hotelBase.destinationAirline,
            EECImpressionClickDestinationAirport: hotelBase.destinationAirport,
            EECImpressionClickFlightType: hotelBase.type,
            EECImpressionClickId: hotelBase.id,
            EECImpressionClickKids: hotelBase.kids,
            EECImpressionClickList: UTILS.actions.hotel_detail,
            EECImpressionClickMealplan: hotelBase.mealplan,
            EECImpressionClickName: hotelBase.hotelName,
            EECImpressionClickNights: hotelBase.nights,
            EECImpressionClickOriginAirline: hotelBase.originAirCode,
            EECImpressionClickOriginAirport: hotelBase.originAirport,
            EECImpressionClickPage: page,
            EECImpressionClickPlaceId: hotelBase.placeId,
            EECImpressionClickPosition: index + 1,
            EECImpressionClickQuantity: 1,
            EECImpressionClickRating: hotelBase.rating,
            EECImpressionClickStars: hotelBase.stars,
            EECImpressionClickState: hotelBase.state,
            EECImpressionClickWeek: hotelBase.week,
            EECImpressionClickWeekDay: hotelBase.weekDay,
            EECImpressionClickOriginPlaceText: hotelBase.originText,
            EECImpressionClickDestinationPlaceText: hotelBase.destinationText,
            EECImpressionClickOriginAirlineText: hotelBase.originAirline,
            EECImpressionClickProductUrl: hotelBase.url,
            EECImpressionClickNightlyRate: hotelBase.priceByNight,
            EECImpressionClickTotalRate: hotelBase.totalPrice,
            EECImpressionClickImageUrl: hotelBase.image,
            event: UTILS.events.event_impression_click
        }

        setDatalayer(event);
    }

    hotelBase(hotel, quote) {
        let returning = this.places.returning;
        let starting = this.places.starting;

        let paxes = getPaxes(this.request.paxes);
        let checkIn = getDate(this.request.checkIn);
        let checkOut = getDate(this.request.checkOut);
        let flightItinerary = quote.flightItinerary || {};
        let countNightValue = countNight(checkIn, checkOut);

        const base = {
            adults: paxes.adults,
            kids: paxes.children,
            available: hotel.price > 0,
            checkIn: this.request.checkIn,
            checkOut: this.request.checkOut,
            city: hotel.locationInfo.city,
            country: hotel.locationInfo.country,
            state: hotel.locationInfo.state,
            destinationAirline: flightItinerary.returning ? flightItinerary.returning.airline: "",
            destinationAirCode: flightItinerary.returning ? flightItinerary.returning.airlineCode : "",
            destinationAirport: flightItinerary.returning ? flightItinerary.returning.departure.airport : "",
            originAirline: flightItinerary.stating ? flightItinerary.stating.airline : "",
            originAirCode: flightItinerary.stating ? flightItinerary.stating.airlineCode : "",
            originAirport: flightItinerary.stating ? flightItinerary.stating.departure.airport : "",
            type: "Roundtrip",
            id: hotel.hotelId,
            mealplan: hotel.taxes ? hotel.taxes.mealPlanCode: "",
            hotelName: hotel.name,
            placeId: returning.placeId,
            rating: hotel.surveyAverage?.averageValue ?? 0,
            stars: hotel.stars,
            week: isoWeekNumber(),
            nights: countNightValue,
            weekDay: checkIn.getDay(),
            priceByNight: hotel.price || 0,
            totalPrice: hotel.taxes ? hotel.taxes.totalRoomRate: 0,
            originText: starting.description,
            destinationText: returning.description,
            url: `${this.settings.siteUrl}${this.settings.pathHotelDetail}${hotel.uri}${this.settings.uriHotelDetailPath}`,
            image: hotel.mainPhoto.cloudUri
        };

        return base;
    }

    requote(display = false) {

        let event = {
            event: UTILS.events.gtmEvent,
            eventName: UTILS.events.gtmEvent,
            eventAction: !display ? UTILS.actions.search_again_display : UTILS.actions.search_again_click,
            eventCategory: UTILS.categories.modal,
            eventLabel: UTILS.actions.hotel_list
        }

        setDatalayer(event);
    }

    error(errortype) {
        let starting = this.places.starting;
        let returning = this.places.returning;
        let request = this.request;
        let paxes = getPaxes(this.request.paxes);


        let event = {
            event: UTILS.events.gtmEvent,
            eventName: UTILS.events.gtmEvent,
            eventCategory: UTILS.actions.hotel_error,
            eventAction: `${UTILS.misc.results} ${errortype}`,
            eventLabel: `o:${starting.code} | d:${returning.code}| o:${starting.description} | d:${returning.description} | o:${starting.placeId} | d:${returning.placeId} | ci:${request.checkIn} | co:${request.checkOut} | r:${request.paxes.length} | ${paxes.join}`,
        }

        setDatalayer(event);
    }

} 

export const HotelListAnalytic = new HotelListAnalytics();