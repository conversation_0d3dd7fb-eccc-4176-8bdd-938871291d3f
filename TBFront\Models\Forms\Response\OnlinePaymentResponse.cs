﻿using TBFront.Models.BookingItinerary;
using TBFront.Models.Forms.Request;

namespace TBFront.Models.Forms.Response
{
    public class OnlinePaymentResponse
    {
        public string Status { get; set; }
        public string Message { get; set; }
        public string KeyValidation { get; set; } = string.Empty;
        public string UrlRedirect { get; set; } = string.Empty;
        public OnlinePaymentRequest Request { get; set; }
        public TravelItinerary TravelItinerary { get; set; }
        public OnlinePaymentClient Client { get; set; }

    }

    public class OnlinePaymentClient
    {
        public string Name { get; set; } = string.Empty;
        public string Lastname { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public double TotalAmount { get; set; }
        public double ServiceAmountPaid { get; set; }
        public int Id { get; set; }
        public string Currency { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;



    }
}
