<template>
	<ProgressBar v-if="!params.isNational" :progressBar="getProgressBar" />
	<!--<InfoPriceFlight v-if="!params.isNational" />-->
	<div class="alert-box container px-md-0 mt-4 cvi-bar container-notification-mobile" v-show="notification">
		<span class="icon icon-info me-1 font-16 alert-icon"></span>
		<span class="alert-text">
			<b>
				{{ __("multiticket.notification_flight") }} {{ Airline }} de {{ startingPlace.cityName }} a {{ returningPlace.cityName }}. {{ __("multiticket.notification_flight_options") }}
			</b>
		</span>
	</div>
	<MultiTicketListSelected v-if="!params.isNational && getIsStepTwo"></MultiTicketListSelected>

	<FiltersPill v-if="showMatrix && (isMobileAndTablet || params.isNational)" :isMobile="isMobileAndTablet" />

	<NearByDates v-if="Object.keys(getGroups).length > 0" :isRountrip="params.isRoundtrip"
				 :isInternational="!params.isNational" :mobile="mobile" />
	<MatrixFlight v-if="showMatrix && !isMobileAndTablet && !params.isNational" />
	<Skeleton v-if="getProgressBar < 100 && !params.isNational && !isMobileAndTablet" :typeSkeleton="'matrix'" />

	<NationalFlight v-if="params.isNational" :is-round-trip="params.isRoundtrip" />
	<MultiTicketList v-if="!params.isNational && !getIsStepTwo" :isStarting="true"></MultiTicketList>
	<MultiTicketList v-if="!params.isNational && params.isRoundtrip && getIsStepTwo" :isStarting="false">
	</MultiTicketList>

	<!--<Filters :isOneWay="!params.isRoundtrip" v-if="showMatrix && siteConfig.isMobile() && !params.isNational" />-->
	<FlightNotAvailable v-if="getProgressBar >= 100 && Object.keys(getGroups).length == 0 && !getIsLoadFlightResponses" />
	<RequoteModal />
	<ModalMessages :msg='__("messages.date_validation_roundtrip")' :btn='__("booker.ok")' :title='__("messages.choose_another_flight")' />
	<div class="modal fade" id="LoaderFullPage" tabindex="-1" aria-labelledby="LoaderFullPageLabel">
		<div class="modal-dialog modal-fullscreen">
			<div class="modal-content">
				<div class="modal-body d-flex justify-content-center align-items-center" style="height: 100vh;">
					<div class="container text-center">
						<div class="row justify-content-center">
							<div class="col">
								<div class="h4">{{ __("multiticket.search_flights") }}</div>
								<img width="130" height="130" src="/assets-tb/img/Loading-step.gif" alt="Cargando" />
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>


</template>

<script setup>
import { storeToRefs } from 'pinia';
import { onMounted, ref, watch, onBeforeMount, computed } from 'vue';
import { useFlightStore } from '../../stores/flight';
import { getList, getMatrix, getParamsFlight, getParamsDetailFlight, getFamilyFare, getLuggage } from '../../services/ApiFlightFrontServices';
import { getPromotionsCalendar, getParamsPromotions } from '../../services/ApiPromotionsFrontServices';
import { useFlightUpsellStore } from '../../stores/flightUpsell';
import { useFlightInfoStore } from '../../stores/flightInfo';
import { useFlightMatrixStore } from '../../stores/flightMatrix';
import { usePromotionStore } from '../../stores/promotion';
import { useUserSelectionStore } from '../../stores/user-selection';
import { List } from '../../../utils/analytics/flightList.js';
import { Logger } from '../../../utils/helpers/logger';
import { useFlightFamilyFareStore } from '../../stores/flightFamilyFare';
import { useMultiTicketStore } from '../../stores/multiTicket';

const storeFlight = useFlightStore();
const storeFlightUpsell = useFlightUpsellStore();
const storeFlightInfo = useFlightInfoStore();
const storeFlightMatrix = useFlightMatrixStore();
const storePromotions = usePromotionStore();
const storeUserSelectionStore = useUserSelectionStore();
const storeFlightFamilyFareStore = useFlightFamilyFareStore();

const { getParams, getProgressBar, getAllQuoteTokens, getStartQuoteTokens, getReturnQuoteTokens, getGroups , getIsLoadFlightResponses} = storeToRefs(storeFlight);
const { getShowUpsell } = storeToRefs(storeFlightUpsell);
const { getShowInfo } = storeToRefs(storeFlightInfo);
const { getStartDate, getReturningDate } = storeToRefs(storePromotions)
const { getFiltersAppliedArray } = storeToRefs(storeUserSelectionStore);


const { setFlightResponse, resetFlightResponse, setProgressBar } = storeFlight;
const { setLoading, setFlightMatrix } = storeFlightMatrix;
const { setSelected, setDestinationObjectSelected, setTripMode, setStartingDate, setReturningDate, setFromLanding, setOrigin, setDestination, setAirline, setStartingDepartureDate } = storePromotions;
const { initRequoteTimeout } = storeUserSelectionStore;
const { setLuggage , setIsLoadingLuggages} = storeFlightFamilyFareStore;

const siteConfig = window.__pt.settings.site || {};
const quoteConfig = siteConfig.quoteConfiguration || {};
const params = getParams;
const showMatrix = ref(false);
const isAllQuotesFinished = ref(false);
const isMobileAndTablet = ref(siteConfig.isMobileAndTablet());
const notification = ref(false);

const useMultiTicket = useMultiTicketStore();
	const { getIsStepTwo } = storeToRefs(useMultiTicket);

	const { getAirlines } = storeToRefs(storeFlightMatrix);
	let Airline = "";

defineProps({
	mobile: Boolean,
	landing: String,
})
sessionStorage.setItem('isNational', params.value.isNational)
	const initialize = async () => {
	resetFlightResponse();
	setValuesPromotions();
	let progressBar = 0;
	const currentConfig = params.value.isNational ? quoteConfig.national : quoteConfig.international;
	const promises = [];
	let promisesCompleted = 0;
	const filteredQuoteConfigs = currentConfig.quoteConfigurations.filter(x => x.isActive && (!x.blockedCarrierCode.length || !x.blockedCarrierCode.includes(params.value.airlineCode)));
	const url = new URL(window.location.href);
	const values = new URLSearchParams(url.search);
	filteredQuoteConfigs.forEach(cc => {
		promises.push(new Promise(async (resolve, reject) => {
			const extraParams = {};
			// Changing base request using config from appsettings
			if (cc.engine && cc.engine.length) {
				extraParams.engine = cc.engine.join(',');
			}

			if (cc.carrierCode && cc.carrierCode.length) {
				extraParams.carrierCode = cc.carrierCode.join(',');
			}

			if (cc.blockedCarrierCode && cc.blockedCarrierCode.length) {
				extraParams.blockedCarrierCode = cc.blockedCarrierCode.join(',');
			}

			if (values.get('cache')) {
				extraParams.cache = values.get('cache');
			}
			extraParams.fareMode = params.value.isNational ? 1 : 0; // MultiplesFares(1) or LowestFares (0)
			extraParams.simpleFlightQuotes = true;//params.value.isNational // if national will search roundtrip as double oneway
			extraParams.step = true;
			extraParams.allQuotes = !params.value.isNational;
			const rq = getParamsFlight(params.value, extraParams);
			const response = await getList(rq);

			if (response && !response.error) {
				response.configName = cc.configName;
				resolve(response);
			}

			reject("NO QUOTES " + cc.configName);
		}).then(res => {
			// Set response in store
			setFlightResponse(res);
		}).catch(err => {
			// Error handler when any requesst fails
			Logger.warn(err);
		}).finally(async () => {
			promisesCompleted++;
			progressBar += 101 / filteredQuoteConfigs.length; // se agrega 1 toleracia a decimales 100 / 3
            if (progressBar >= 100) {
                let dataGroups = {
                    lengthGroups: groups.value.length,
                    departure: {},
                    returning: {}
                }
                let consolidatedItems = [];
                let consolidatedEvents = [];

                for (let index in groups.value) {
                    let dataGroup = {
                        lengthGroups: groups.value.length,
                        index: index,
                        code: groups.value[index].departure?.code,
                        departure: {
                            price: groups.value[index].departure?.cheapest,
                            view: "departing",
                            total_flights: groups.value[index].departure?.flights?.length
                        },
                        returning: {}
                    };
                    if (params.value.isRoundtrip) {
                        dataGroup.returning = {
                            price: groups.value[index].returning?.cheapest,
                            view: "returning",
                            total_flights: groups.value[index].returning?.flights?.length
                        };
                    }
                    consolidatedEvents.push(dataGroup);
                }
                let event = List.flights(consolidatedEvents);
            }
			setProgressBar(progressBar);
			if (promisesCompleted === promises.length && currentConfig.isMatrixActive) {
                setMatrix();
				if (!params.value.isNational) {
                    isAllQuotesFinished.value = true;
                    searchLuggage();
				}
			}
		}));
	});
}

const setValuesPromotions = () => {
	const tripMode = params.value.tripMode;

	const originParams = {
		code: params.value.startingAirportPlace.airportCode,
		name: params.value.startingAirportPlace.cityFullName,
	}
	const destinationParams = {
		code: params.value.returningAirportPlace.airportCode,
		name: params.value.returningAirportPlace.cityFullName,
	}
	setSelected(originParams);
	setDestinationObjectSelected(destinationParams);

	if (tripMode === 0) {
		setStartingDepartureDate(params.value.startingFromDateTime);
		setStartingDate(params.value.startingFromDateTime);
		setReturningDate(params.value.startingFromDateTime);
	} else {
		setStartingDate(params.value.startingFromDateTime);
		setReturningDate(params.value.returningFromDateTime);
	}

	setOrigin(params.value.startingAirportPlace.airportCode);
	setDestination(params.value.returningAirportPlace.airportCode);
	setFromLanding("home");
	setTripMode(tripMode);
	setAirline(params.value.addFirst)
}

const setMatrix = async () => {
	if (getStartQuoteTokens.value && getStartQuoteTokens.value.length) {
		setLoading(true);
		const response = await getMatrix({ token: (!getIsStepTwo.value ? getStartQuoteTokens.value.join(',') : getReturnQuoteTokens.value.join(',')), step: params.value.isRoundtrip == 1 });
        if (response.airlines.length > 1 || Object.keys(response.avilableStops).length > 1) {
            showMatrix.value = true;
            setFlightMatrix(response);
        }
		setLoading(false);
	}
}

const searchLuggage = async () => {
	if (getAllQuoteTokens.value && getAllQuoteTokens.value.length) {
		const promises = new Promise(async (resolve, reject) => {
			const response = getLuggage({ token: getAllQuoteTokens.value.join(','), site: siteConfig.apiFlights.siteConfig });
			if (response && !response.error) {
				resolve(response);
			}
		}).then(res => {
			setLuggage(res.luggages);
			setIsLoadingLuggages(false);
		}).catch(err => {
			// Error handler when any requesst fails
			Logger.warn(err);
		});
	}
}

const checkScreenSize = () => {
	isMobileAndTablet.value = siteConfig.isMobileAndTablet();
}

const groups = computed(() => {
    return getGroups.value;
});


onMounted(() => {
    window.addEventListener("resize", checkScreenSize);
});

const returningPlace = computed(() => {
	return getParams.value.returningAirportPlace

})

const startingPlace = computed(() => {
	return getParams.value.startingAirportPlace
})

watch(getAirlines, (newVal) => {
	const airlineConfiguration = siteConfig?.airlineConfiguration;
	if (newVal.length > 0 && getParams.value.addFirst.trim() !== "") {
		const addFirstName = getParams.value.addFirst.toUpperCase();
        const foundAirline = getAirlines.value.find(airline =>
			airline.name.toUpperCase().includes(addFirstName)
			|| airline.code.toUpperCase().includes(addFirstName)
		);
        if (foundAirline?.code.trim() === undefined) {
            const airlineCategory = getParams.value.isNational ? airlineConfiguration.national : airlineConfiguration.international;
            const foundNameAirline = airlineCategory.find(airline =>
                airline.airlineCode.includes(addFirstName)
            );
            Airline = foundNameAirline?.airlineName?.toUpperCase() ?? addFirstName;
            notification.value = true;
        } else {
            notification.value = false;
        }
    }
}, { immediate: true }); 

onBeforeMount(() => {
	initRequoteTimeout();
});

window.addEventListener('pageshow', (event) => {
	initialize()
});
</script>
