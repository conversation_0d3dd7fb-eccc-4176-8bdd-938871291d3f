﻿using TBFront.Models.Flight.Quote;

namespace TBFront.Models.Common
{
    public class FlightResponse
    {
        public Dictionary<string, Dictionary<string, Flight.Quote.Flight>> Flights { get; set; }
        public Dictionary<string, Recommendation> Recommendations { get; set; }
        public Dictionary<string, Dictionary<string, FareLeg>> FaresByLeg { get; set; }
        public int TotalRecommendations { get; set; }
        public string Currency { get; set; }
        public string QuoteTaskID { get; set; }
        public IEnumerable<int> EnginesInResponse { get; set; }
        public Dictionary<string, Dictionary<string, BookingInfo>> BookingInfos { get; set; }
    }

}
