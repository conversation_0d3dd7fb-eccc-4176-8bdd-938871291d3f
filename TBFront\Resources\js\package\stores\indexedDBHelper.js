// indexedDBHelper.js

// Detección de soporte para IndexedDB
const isIndexedDBSupported = () => {
  return 'indexedDB' in window;
};

// Fallback para LocalStorage
const fallbackStorage = {
  setItem: (key, value) => {
      const data = { value, timestamp: Date.now() };
      localStorage.setItem(key, JSON.stringify(data));
  },
  getItem: (key) => {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : null;
  },
};

// Función para abrir la base de datos IndexedDB o usar LocalStorage
export const openDatabase = (dbName, storeName) => {
  if (!isIndexedDBSupported()) {
      console.warn("IndexedDB no está disponible. Usando LocalStorage como respaldo.");
      return Promise.resolve(null); // Devuelve null para indicar que se usará LocalStorage
  }

  return new Promise((resolve, reject) => {
      const request = indexedDB.open(dbName, 1);
      request.onupgradeneeded = (event) => {
          const db = event.target.result;
          if (!db.objectStoreNames.contains(storeName)) {
              db.createObjectStore(storeName, { keyPath: 'key' });
          }
      };

      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
  });
};

// Función para guardar un elemento (IndexedDB o LocalStorage)
export const setItem = async (dbName, storeName, key, value) => {
  const db = await openDatabase(dbName, storeName);
  if (!db) {
      fallbackStorage.setItem(key, value); // Usa LocalStorage
      return;
  }

  const transaction = db.transaction(storeName, 'readwrite');
  const store = transaction.objectStore(storeName);
  store.put({ key, value, timestamp: Date.now() });

  return new Promise((resolve, reject) => {
      transaction.oncomplete = () => resolve();
      transaction.onerror = () => reject(transaction.error);
  });
};

// Función para obtener un elemento (IndexedDB o LocalStorage)
export const getItem = async (dbName, storeName, key) => {
  const db = await openDatabase(dbName, storeName);
  if (!db) {
      return fallbackStorage.getItem(key); // Usa LocalStorage
  }

  return new Promise((resolve, reject) => {
      const transaction = db.transaction(storeName, 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.get(key);

      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
  });
};
