﻿using TBFront.Interfaces;
using TBFront.Infrastructure.DatabaseService.DynamoDB.Dtos;
using Places.Standard.Services;

namespace TBFront.Infrastructure.DatabaseService.DynamoDB
{
    public static class PlacesAirportServiceRegister
    {
        public static void AddPlacesAirportDependencies(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddSingleton(s => configuration.GetSection("PlaceAirportConfigurations").Get<PlaceAirportConfiguration>());
            services.AddSingleton<IPlacesAirportService, PlacesAirportService>();
        }
    }
}
