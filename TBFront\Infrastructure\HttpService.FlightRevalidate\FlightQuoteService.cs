﻿using System.Net.Http.Headers;
using System.Net.Mime;
using System.Text.Json;
using TBFront.Infrastructure.HttpService.FlightQuote.Dtos;
using TBFront.Interfaces;
using TBFront.Models.Flight.Quote;

namespace TBFront.Infrastructure.HttpService.FlightRevalidate
{
    public class FlightRevalidateService : IQueryHandlerAsync<FlightQuoteRequest, FlightQuoteResponse>
    {

        private readonly HttpClient _httpClient;
        private readonly FlightQuoteConfiguration _configuration;
        private readonly static JsonSerializerOptions _jsonSerializerOptions = new() { PropertyNameCaseInsensitive = true };
        private readonly ICacheService _cacheService;
        public FlightRevalidateService(HttpClient httpClient, FlightQuoteConfiguration configuration, ICacheService cacheService)
        {
            _configuration = configuration;
            _httpClient = httpClient;
            _httpClient.DefaultRequestHeaders.Add("Accept", MediaTypeNames.Application.Json);
            _httpClient.BaseAddress = new Uri(_configuration.UriBase);
            _cacheService = cacheService;
        }

        public async Task<FlightQuoteResponse> QueryAsync(FlightQuoteRequest request, CancellationToken ct)
        {

            var key = request.KeyRedis;
            var response = await _cacheService.RedisGetCache<FlightQuoteResponse>(key, ct); ;

            if (response != null)
            {
                return response;
            }

            var uriService = $"{_configuration.UriTravelItinerary}";
            var payload = JsonSerializer.Serialize(request);
            var body = new StringContent(payload);
            body.Headers.ContentType = new MediaTypeHeaderValue("application/json");
            var httpResponseMessage = await _httpClient.PostAsync(uriService, body);

            using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync();
            response = await JsonSerializer.DeserializeAsync<FlightQuoteResponse>(contentStream, _jsonSerializerOptions);

            if (response != null)
            {
                //response.FlightQuoteId = request.Name;
                _cacheService.RedisSetCache(key, response);
            }

            return response;

        }
    }
}
