using System.Net.Mime;
using System.Text.Json;
using TBFront.Infrastructure.HttpService.ContentDeliveryNetwork.Dtos;
using TBFront.Interfaces;
using TBFront.Models.Blacklist.Request;
using TBFront.Models.Blacklist.Response;

namespace TBFront.Infrastructure.HttpService.ContentDeliveryNetwork
{
    public class BlacklistService : IQueryHandlerAsync<BlacklistRequest, BlacklistResponse>
    {
        private readonly HttpClient _httpClient;
        private readonly BlacklistConfiguration _configuration;
        private readonly ICacheService _cacheService;
        private readonly static JsonSerializerOptions _jsonSerializerOptions = new() { PropertyNameCaseInsensitive = true };

        public BlacklistService(HttpClient httpClient, BlacklistConfiguration configuration, ICacheService cacheService)
        {
            _httpClient = httpClient;
            _configuration = configuration;
            _cacheService = cacheService;

            _httpClient.DefaultRequestHeaders.Add("Accept", MediaTypeNames.Application.Json);
            _httpClient.BaseAddress = new Uri(_configuration.Uri);
        }

        public async Task<BlacklistResponse> QueryAsync(BlacklistRequest request, CancellationToken ct)
        {
            var key = $"blacklist";
            var blacklistResponse = await _cacheService.GetCache<BlacklistResponse>(key, ct);

            if (blacklistResponse is not null)
            {
                return blacklistResponse;
            }

            var httpResponseMessage = await _httpClient.GetAsync($"{_configuration.SettingsPath}", ct);

            if (httpResponseMessage.IsSuccessStatusCode)
            {
                blacklistResponse = await httpResponseMessage.Content.ReadFromJsonAsync<BlacklistResponse>(_jsonSerializerOptions, ct);

                if (blacklistResponse is not null)
                {
                    _cacheService.SetCache(key, blacklistResponse);
                }
            }


            return blacklistResponse;
        }

       
    }
}
