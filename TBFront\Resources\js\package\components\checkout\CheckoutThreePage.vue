<template>
    <div v-if="is_valid">
        <VoucherDefault v-if="!data.bookingId" v-bind:data="information" :retry="retry_loading"></VoucherDefault>
        <VoucherSuccess v-if="data.bookingId" v-bind:data="data" :item="item"></VoucherSuccess>
    </div>
    <error-checkout v-if="!loading && !is_valid" :quote="response"></error-checkout>
    <div class="space-white" v-if="loading"></div>
    <div v-if="loading" id="loader-page" class="loading-page d-center">
        <div class="loader__logo"></div>
    </div>
</template>
<script>
    import { windowScrollTop } from '../../../utils/helpers';
    import VoucherSuccess from './components/VoucherSuccess.vue';
    import VoucherDefault from './components/VoucherDefault.vue';
    import LoaderPage from '../common/LoaderPage.vue';
    import { algoliaEvent } from "../../../utils/analytics/algolia";
    import { VoucherAnalytic } from "../../../utils/analytics/VoucherAnalytics";
    import ErrorCheckout from './ErrorCheckout.vue';
    import {responsiveObserver} from "../../../utils/helpers/responsiveObserver";

    const site = window.__pt.settings.site;


    export default {
        data() {
            return {
                step: 3,
                rapd: true,
                loading: true,
                siteConfig: site,
                data: {},
                is_valid: false,
                response: {},
                information: {
                    id: "",
                    email: ""
                },
                title: '',
                retry_attempts: 1,
                retry_loading: true,
                isNational: sessionStorage.getItem('isNational') === 'true'

            };
        },
        async mounted() {
            this.initialize()
        },
        setup() {
            const isResponsiveRef = responsiveObserver.getResponsiveStatus();
            return {
                isResponsiveRef,
            };
        },
        computed: {
            isResponsive() {
                return this.isResponsiveRef;
            }
        },
        methods: {
            async initialize() {
                this.loading = true;
                await this.get();
                windowScrollTop()
                this.loading = false;

            },
            async get() {
                let params = this.getParams();
                let response = await axios.get(site.voucherCheckout, { params }).catch(this.onError);
                if (response && response.status == 200) {
                    this.response = response.data;
                    this.data = this.response.reservation;
                    this.item = this.response.quote;
                    this.information = this.response.info;
                    this.is_valid = this.information.valid;
                    this.rapd = this.data.reserveNowPayLater;
                    VoucherAnalytic.setVoucherAnalytics(this.response);
                    this.algoliaEventAddToCart();
                }
            },
            onError(data) {
                this.loading = false;
                let response = data && data.response && data.response.data ? data.response.data : null;
                if (response) {
                    this.response = data.response.data;
                    this.information = this.response.info;
                    this.is_valid = this.information.valid;
                }

                this.retryAttempts(response);

            },
            getParams() {
                let paramsCheckout = window.__pt.data || {};
                paramsCheckout.site = this.siteConfig.site;
                return paramsCheckout;
            },
            retryAttempts(response) {

                if (this.retry_attempts <= site.retry) {
                    setTimeout(async () => {
                        await this.get();
                    }, site.retryTimeOut * 1000);

                } else {
                    this.retry_loading = false;
                    let params = this.getParams();
                    VoucherAnalytic.onError(response, params, 'voucher');
                }

                ++this.retry_attempts;
            },
            algoliaEventAddToCart() {
                algoliaEvent("Order");
            }
        },
        components: {
            VoucherSuccess,
            LoaderPage,
            VoucherDefault,
            ErrorCheckout
        }

    }
</script>
<style lang="scss" scoped>
    .space-white {
    min-height: 600px;
    }
</style>