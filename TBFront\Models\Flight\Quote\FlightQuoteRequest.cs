﻿using TBFront.Models.Common;

namespace TBFront.Models.Flight.Quote
{

    public class PointItem
    {
        public string Code { get; set; }
    }


    public class Passenger
    {
        public int Type { get; set; }
        public int Quantity { get; set; }
        public int Age { get; set; }
    }
    public class Point
    {
        public PointItem Origin { get; set; }
        public PointItem Destination { get; set; }
        public DateTime DepartureDate { get; set; }
    }
    public class FlightQuoteRequest
    {
        public Context Context { get; set; }
        public string Currency { get; set; }
        public int TripMode { get; set; }
        public List<Point> Points { get; set; }
        public List<Passenger> Passengers { get; set; }
        public string View { get; set; } = string.Empty;
        public bool IsPackage { get; set; }
        public List<int> Engines { get; set; }
        public List<string> CarrierCode { get; set; }
        public int MaxRecommendations { get; set; }
        public int FareMode { get; set; }
        public bool ShowDetailAmounts { get; set; }
        public bool ShowRevenueByLeg { get; set; }
        public Dictionary<string, List<string>> FlightNumbers { get; set; }
        public string? KeyRedis { get; set; }
    }
}
