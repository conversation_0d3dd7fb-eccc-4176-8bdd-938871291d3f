import { setDatalayer, UTILS, getChildren } from "./main"

export default class Bookers {
    constructor() {
        this.settings = window.__pt.settings.site;
    }

    setDataLayerHistory(bookerStore, type) {
        let history = true;
        for (let view in type) {
            if (type[view] != "suggestion") {
                let event = {
                    eventAction: `${bookerStore.startingAirport}-${bookerStore.returningAirport} :: ${bookerStore.flightType}`,
                    event: UTILS.events.gtmEvent,
                    eventCategory: '',
                    eventLabel: '',
                    eventName: UTILS.events.gtmEvent,
                    eventExtra: ''
                }
                if (type[view] == 'history' && history) {
                    const children = getChildren(bookerStore.paxes.ageKids);
                    event['eventCategory'] = UTILS.categories.recientes_autocomplete;
                    event['eventLabel'] = `adults:${bookerStore.paxes.adults} :: children:${children.kids} :: infants:${children.infants}`;
                    history = false;
                    setDatalayer(event);
                }
                if (type[view] == 'most_serched') {
                    event['eventCategory'] = UTILS.categories.autocomplete_mas_buscados;
                    event['eventLabel'] = `${view}_popular`;
                    setDatalayer(event);
                }
            }
        }
    }

    setDataLayerCards(data) {
        const ageKids = data.agekids.split(',');
        const children = getChildren(ageKids);
        const tripMode = data.tripMode == 0 ? 'oneway' : 'roundtrip'; 
        let event = {
            eventAction: `${data.startingFromAirport}-${data.returningFromAirport} :: ${tripMode}`,
            event: UTILS.events.gtmEvent,
            eventName: UTILS.events.gtmEvent,
            eventCategory: UTILS.categories.recientes_tarjetas_home,
            eventLabel: `adults:${data.adults} :: children:${children.kids} :: infants:${children.infants}`,
            eventExtra: '',
        }
        setDatalayer(event);
    }
}

export const Booker = new Bookers();