﻿const mix = require('laravel-mix');
mix.setPublicPath('wwwroot');
const path = 'assets-tb';
const site = process.env?.MIX_SITE ? process.env.MIX_SITE : "tiquetesbaratos";

mix.js('Resources/js/package/app.js', `wwwroot/${path}/js/vue`).vue({
	extractStyles: false,
});


mix.sass(`Resources/sass/${site}/common.scss`, `wwwroot/${path}/${site}/css`);
mix.sass(`Resources/sass/${site}/home.scss`, `wwwroot/${path}/${site}/css`);
mix.sass(`Resources/sass/${site}/list.scss`, `wwwroot/${path}/${site}/css`);
mix.sass(`Resources/sass/${site}/promotion.scss`, `wwwroot/${path}/${site}/css`);
mix.sass(`Resources/sass/${site}/checkout.scss`, `wwwroot/${path}/${site}/css`);
mix.sass(`Resources/sass/${site}/error-page.scss`, `wwwroot/${path}/${site}/css`);
mix.sass(`Resources/sass/${site}/check-reservation.scss`, `wwwroot/${path}/${site}/css`);
mix.sass(`Resources/sass/${site}/checkin.scss`, `wwwroot/${path}/${site}/css`);
mix.sass(`Resources/sass/${site}/payonline.scss`, `wwwroot/${path}/${site}/css`);
mix.sass(`Resources/sass/${site}/groups.scss`, `wwwroot/${path}/${site}/css`);
mix.sass(`Resources/sass/${site}/confirmation.scss`, `wwwroot/${path}/${site}/css`);
mix.sass(`Resources/sass/${site}/write-us.scss`, `wwwroot/${path}/${site}/css`);
mix.sass(`Resources/sass/${site}/booker.scss`, `wwwroot/${path}/${site}/css`);


mix.copy('Resources/js/lang/es.js', `wwwroot/${path}/js/lang`);
mix.copy('Resources/js/lang/en.js', `wwwroot/${path}/js/lang`);
/*
mix.copy('Resources/js/jquery.mobile-slider.min.js', `wwwroot/${path}/js`);

mix.combine(['./node_modules/jquery/dist/jquery.slim.min.js',
	'./node_modules/slick-carousel/slick/slick.min.js'], `wwwroot/${path}/js/libs/slick.bundle.min.js`);
*/
mix.combine([
	'node_modules/bootstrap/dist/js/bootstrap.bundle.min.js',
	//'node_modules/bootstrap-slider/dist/bootstrap-slider.min.js',
	//'Resources/js/settings.js',
	//'Resources/js/main.js',
	'./node_modules/@easepick/core/dist/index.umd.js',
	'./node_modules/@easepick/datetime/dist/index.umd.js',
	'./node_modules/@easepick/base-plugin/dist/index.umd.js',
	'./node_modules/@easepick/lock-plugin/dist/index.umd.js',
	'./node_modules/@easepick/range-plugin/dist/index.umd.js',
], `wwwroot/${path}/js/default.js`);

mix.options({
	processCssUrls: true
});

if (mix.inProduction()) {
	mix.sourceMaps();
}

mix.disableSuccessNotifications();

