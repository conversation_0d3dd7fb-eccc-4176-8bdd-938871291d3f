<template>
  <div class="container py-4">
    <div class="row">
      <!-- Mobile: Vista de Categorías -->
      <div
        class="d-flex flex-column gap-5 d-md-none"
        v-if="!mobileView.questionOpen"
      >
        <div class="list-group">
          <button
            v-for="(category, index) in categories"
            :key="index"
            @click="setActiveCategory(category)"
            type="button"
            class="list-faq list-group-item list-group-item-action d-flex justify-content-between align-items-center"
          >
            <div class="d-flex align-items-center gap-2">
              <span :class="category.icon"></span>

              <span>{{ category.title }}</span>
            </div>
          </button>
        </div>
      </div>

      <!-- Mobile: Vista de Preguntas -->
      <div
        class="col-12 d-md-none"
        v-if="mobileView.questionOpen"
      >
         <button class="btn btn-link px-0 mb-3 d-flex" @click="goBack">
          <span name="chevronleft" class="icon icon-chevron-left font-24" @click="goBack"></span> Regresar
        </button> 
        
        <h2 class="title-lg mb-3">{{ activeCategoryTitle }}</h2>
        <div class="my-4">
          <h2 class="title-xs">¿Qué estás buscando?</h2>
          <input
            v-model="search"
            type="text"
            class="form-control mb-3"
            placeholder="Escribe una palabra o pregunta"
          />
        </div>
        <div class="accordion accordion_faq" id="faqAccordionMobile">
          <div
            v-for="(item, index) in filteredQuestions"
            :key="index"
            class="accordion-item_faq"
          >
            <h2 class="accordion-header">
              <button
                class="accordion-button accordion-button_faq"
                :class="{ collapsed: !item.open }"
                type="button"
                @click="toggle(item)"
              >
                {{ item.question }}
              </button>
            </h2>
            <div class="accordion-collapse collapse" :class="{ show: item.open }">
              <div class="accordion-body body">
                {{ item.answer }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Desktop/Tablet: Categorías -->
      <div class="col-md-3 mb-4 d-none d-md-block">
        <div class="list-group">
          <button
            v-for="(category, index) in categories"
            :key="index"
            @click="setActiveCategory(category)"
            type="button"
            class="list-faq list-group-item list-group-item-action d-flex justify-content-between align-items-center"
            :class="{ active: category.key === activeCategory }"
          >
            <div class="d-flex align-items-center gap-2">
              <!-- <span class="icon-credit-card">{{ category.icon }}</span> -->
              <!-- <span class="icon-warning">{{ category.icon }}</span> -->
              <!-- <span class="icon-flight-takeoff">{{ category.icon }}</span> -->
              <!-- <span class="icon-check-circle">{{ category.icon }}</span> -->
              <!-- <span class="icon-lock">{{ category.icon }}</span> -->
              <!-- <span class="icon-info">{{ category.icon }}</span> -->
              <!-- <span class="icon-check-circle">{{ category.icon }}</span> -->
              <span :class="category.icon"></span>

              <span>{{ category.title }}</span>
            </div>
          </button>
        </div>
      </div>

      <!-- Desktop/Tablet: Preguntas -->
      <div class="col-md-9 d-none d-md-flex flex-column gap-5">
        <div v-if="activeCategory">
          <h2 class="title-lg">{{ activeCategoryTitle }}</h2>
          <div class="my-4">
          <h2 class="title-xs">¿Qué estás buscando?</h2>
          <input
            v-model="search"
            type="text"
            class="form-control"
            placeholder="Escribe una palabra o pregunta"
          />
        </div>
          <div class="accordion accordion_faq" id="faqAccordionDesktop">
            <div
              v-for="(item, index) in filteredQuestions"
              :key="index"
              class="accordion-item_faq"
            >
              <h2 class="accordion-header">
                <button
                  class="accordion-button accordion-button_faq"
                  :class="{ collapsed: !item.open }"
                  type="button"
                  @click="toggle(item)"
                >
                  {{ item.question }}
                </button>
              </h2>
              <div class="accordion-collapse collapse" :class="{ show: item.open }">
                <div class="accordion-body body">
                  {{ item.answer }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent } from 'vue'
import { __ } from '../../../utils/helpers/translate' // ✅ Traducciones

export default defineComponent({
  name: 'FaqComponent',
  data() {
    return {
      search: '',
      activeCategory: '',
      activeCategoryTitle: '',
      mobileView: {
        questionOpen: false
      },
      categories: [
        { title: __('categories.paymentsAndReservations'), icon: 'icon-credit-card', key: 'paymentsAndReservations' },
        { title: __('categories.changesAndCancellations'), icon: 'icon-warning', key: 'changesAndCancellations' },
        { title: __('categories.baggageAndProcesses'), icon: 'icon-flight-takeoff', key: 'baggageAndProcesses' },
        { title: __('categories.billing'), icon: 'icon-check-circle', key: 'billing' },
        { title: __('categories.onlineSecurity'), icon: 'icon-lock', key: 'onlineSecurity' },
        { title: __('categories.additionalServices'), icon: 'icon-info', key: 'additionalServices' }
    ],

      allQuestions: __('faq') || {} // Objeto con todas las preguntas categorizadas
    }
  },
  computed: {
    filteredQuestions() {
      if (this.activeCategory && this.allQuestions.hasOwnProperty(this.activeCategory)) {
        const questions = this.allQuestions[this.activeCategory] || []
        return questions.filter(q =>
          q.question.toLowerCase().includes(this.search.toLowerCase())
        )
      }
      return []
    }
  },
  methods: {
    setActiveCategory(category) {
      this.activeCategory = category.key
      this.activeCategoryTitle = category.title
      this.mobileView.questionOpen = window.innerWidth < 768
      const questions = this.allQuestions[category.key]
      if (questions) {
        questions.forEach(q => (q.open = false))
      }
    },
    toggle(item) {
      item.open = !item.open
    },
    goBack() {
      this.mobileView.questionOpen = false
    }
  },
  mounted() {
    if (!this.activeCategory && this.categories.length) {
      this.activeCategory = this.categories[0].key
      this.activeCategoryTitle = this.categories[0].title
    }
  }
})
</script>

<style lang="scss">

.accordion-button {
  outline: none !important;
  box-shadow: none !important;
  border: none !important;
}





</style>

