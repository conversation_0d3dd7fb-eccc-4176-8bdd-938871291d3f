﻿using ProtoBuf;
using System.Text.Json.Serialization;

namespace TBFront.Models.Collection
{
    [ProtoContract]
    public class Section
    {
        [ProtoMember(1)]
        [JsonPropertyName("title")]
        public string? Title { get; set; }
        [ProtoMember(2)]
        [JsonPropertyName("description")]
        public string? Description { get; set; }
        [ProtoMember(3)]
        [JsonPropertyName("order")]
        public int Order { get; set; }
        [ProtoMember(4)]
        [Json<PERSON>ropertyName("active")]
        public bool Active { get; set; }
        [ProtoMember(5)]
        [JsonPropertyName("cardtype")]
        public string? CardType { get; set; }
        [ProtoMember(6)]
        [JsonPropertyName("cards")]
        public List<Card>? Cards { get; set; }
        public Section()
        {
            this.Cards = new List<Card>();
        }
    }
}