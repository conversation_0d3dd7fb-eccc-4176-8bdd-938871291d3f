﻿// Estos estilos son para sobreescribir los estilos heredados de bootstrap 


.list-faq {
    background-color: var(--bg-base) !important;
}

.list-faq.active {
    background-color: var(--bg-base) !important;
}

.list-faq:hover {
    background-color: var(--bg-level1) !important;
}


.form-control:focus {
    color: var(--text-strong);
    background-color: var(--bg-base);
    border: var(--border-width-md) solid var(--border-info) !important;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(57, 96, 213, 0.15) !important;
}

.list-group-item {
    font: var(--body) !important;
    color: var(--text-strong) !important;
    // background-color: var(--bg-base) !important;
    border-color: var(--border-subtle) !important;
}

.list-group-item.active {
    color: var(--text-main) !important;
    font: var(--title-xxs) !important; 
    transition: all 0.2s ease;
    border-color: var(--border-subtle) !important;
//    border-left: var(--border-width-xxl) solid var(--border-primary) !important;
}

.list-group-item:hover {
    // background-color: var(--bg-level1) !important;
    border-color: var(--border-strong) !important;
    // border-left: var(--border-width-xxl) solid var(--border-primary) !important;
}

.list-group-item:hover.active {
    // background-color: var(--bg-level1) !important;
    border-color: var(--border-strong) !important;
    // border-left: var(--border-width-xxl) solid var(--border-primary) !important;
}

.accordion_faq {
    display: flex;
    flex-direction: column;
    gap: var(--space-16);
}

.accordion-item_faq {
    color: var(--text-main) !important;
    border: var(--border-width-xs) solid var(--border-subtle) !important;
    border-radius: var(--border-radius-xs) !important;
    overflow: visible;
}



.accordion-item_faq:first-of-type .accordion-button {
    border-top-left-radius: var(--border-radius-xs) !important;
    border-top-right-radius: var(--border-radius-xs) !important;
}

.accordion-item_faq:last-of-type .accordion-button.collapsed {
    border-bottom-right-radius: var(--border-radius-xs) !important;
    border-bottom-left-radius: var(--border-radius-xs) !important;
}

.accordion-button_faq {
    cursor: pointer;
    // position: relative;
    // display: flex;
    // align-items: center;
    // width: 100%;
    // padding: var(--bs-accordion-btn-padding-y) var(--bs-accordion-btn-padding-x);
    font: var(--title-xs) !important;
    color: var(--text-strong);
    // text-align: left;
    background-color: var(--bg-level1) !important;
    border: var(--border-width-xs) solid var(--border-subtle) !important;
    border-radius: var(--border-radius-xs) !important;
    // overflow-anchor: none;
    // transition: var(--bs-accordion-transition);
}

.accordion-button:not(.collapsed) {
    color: var(--text-strong);
    background-color: var(--bg-level1) !important;
    box-shadow: inset 0 calc(-1 * var(--border-width-xs)) 0 var(--border-subtle) !important;
}

.accordion-button:focus {
    z-index: 3;
    background-color: var(--bg-base);
    border: var(--border-width-md) solid var(--border-info) !important;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(57, 96, 213, 0.15) !important;
}
