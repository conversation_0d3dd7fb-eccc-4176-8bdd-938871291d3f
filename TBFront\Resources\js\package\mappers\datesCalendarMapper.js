class DatesCalendarMapper {

    map(responses, typeFlight, params) {
        let dates_quote = [];
        let dates_departure = [];
        let flag = true;
        let allowedDates = {
            DepartureDate: this.allowedDates(params.startDate),
            ReturnDate: ""
        };
        if (typeFlight === 0) {
            responses = responses.filter((obj) => {
                const parseDepartureDate = this.createLocalDateFromISOString(obj.DepartureDate).getTime();
                return allowedDates.DepartureDate.includes(parseDepartureDate);
            });
            responses.sort((a, b) => new Date(b.ReturnDate) - new Date(a.ReturnDate));
        } else {
            allowedDates['ReturnDate'] = this.allowedDates(params.returnDate);
            responses = responses.filter((obj) => {
                const parseDepartureDate = this.createLocalDateFromISOString(obj.DepartureDate).getTime();
                const parseReturnDate = this.createLocalDateFromISOString(obj.ReturnDate).getTime();
                return new Date(obj.ReturnDate) >= new Date(obj.DepartureDate) &&
                    allowedDates.DepartureDate.includes(parseDepartureDate) && allowedDates.ReturnDate.includes(parseReturnDate);
            });
        }
        responses.forEach(response => {
            let allReturnDates = [];
            let lowestPrices = {};
            let dateParsed = this.createLocalDateFromISOString(response.DepartureDate);

            if (dates_quote.findIndex((item) => item.day === dateParsed.getUTCDate()) < 0) {
                let date_destination = {
                    date: dateParsed,
                    day: dateParsed.getUTCDate(),
                    month: dateParsed.getMonth(),
                }
                dates_quote.push(date_destination);
            }

            let returnDate = this.createLocalDateFromISOString(response.ReturnDate);

            if (typeFlight === 0) {
                if (flag) {
                    allReturnDates = responses.map((obj) => {
                        let dayDate = this.createLocalDateFromISOString(obj.DepartureDate);
                        let quouteResult = {
                            date: dayDate,
                            day: dayDate.getUTCDate(),
                            month: dayDate.getMonth(),
                            price: obj.Amount,
                            airline: obj.Airline,
                        }
                        return quouteResult;
                    });


                    allReturnDates.forEach(item => {
                        if (!lowestPrices[item.day] || item.price < lowestPrices[item.day].price) {
                            lowestPrices[item.day] = item;
                        }
                    });

                    let resultFiltered = Object.values(lowestPrices).sort((a, b) => a.month - b.month || a.day - b.day);

                    if (resultFiltered.length > 7)
                        resultFiltered = resultFiltered.slice(0, 7);
                    let parsedResult = {
                        date: returnDate,
                        day: returnDate.getUTCDate(),
                        month: returnDate.getMonth(),
                        rates: resultFiltered
                    }
                    dates_departure.push(parsedResult);
                    flag = false;
                }
            } else {
                if (dates_departure.findIndex((item) => item.day === returnDate.getUTCDate()) < 0) {
                    allReturnDates = responses.filter((quote) => {
                        let parsedDate = this.createLocalDateFromISOString(quote.ReturnDate);
                        return returnDate.getUTCDate() === parsedDate.getUTCDate();
                    }).map( (obj) => {
                        let dayDate = this.createLocalDateFromISOString(obj.DepartureDate);
                        let quouteResult = {
                            date: dayDate,
                            day: dayDate.getUTCDate(),
                            month: dayDate.getMonth(),
                            price: obj.Amount,
                            airline: obj.Airline,
                        }
                        return quouteResult;
                    });

                    allReturnDates.forEach(item => {
                        if (!lowestPrices[item.day] || item.price < lowestPrices[item.day].price) {
                            lowestPrices[item.day] = item;
                        }
                    });

                    let resultFilteredRoundTrip = Object.values(lowestPrices).sort((a, b) => a.month - b.month || a.day - b.day);

                    if (resultFilteredRoundTrip.length > 7)
                        resultFilteredRoundTrip = resultFilteredRoundTrip.slice(0, 7);

                    let parsedResultRounTrip = {
                        date: returnDate,
                        day: returnDate.getUTCDate(),
                        month: returnDate.getMonth(),
                        rates: resultFilteredRoundTrip
                    }
                    dates_departure.push(parsedResultRounTrip);
                }
            }
        });

        dates_quote.sort((a, b) => a.month - b.month || a.day - b.day);
        dates_departure.sort((a, b) => a.month - b.month || a.day - b.day);

        if (dates_quote.length > 7)
            dates_quote = dates_quote.slice(0, 7);

        if (dates_departure.length > 7)
            dates_departure = dates_departure.slice(0, 7);
        const fligth = {
            dates_quote: dates_quote,
            dates_departure: dates_departure,
            typeFlight: typeFlight
        }
        return fligth;
    }

    createLocalDateFromISOString(dateString) {
        const localDateString = dateString.replace('Z', '');
        const [year, month, day] = localDateString.split('T')[0].split('-').map(Number);
        return new Date(year, month - 1, day);
    }

    allowedDates(date) {
        let parseDate = this.createLocalDateFromISOString(date);
        let allowedDates = [];
        allowedDates.push(parseDate.getTime());
        for (let i = 1; i <= 3; i++) {
            const localDateString = date.replace('Z', '');
            const [year, month, day] = localDateString.split('T')[0].split('-').map(Number);
            allowedDates.push(new Date(year, month - 1, day - i).getTime());
            allowedDates.push(new Date(year, month - 1, day + i).getTime());
        }
        return allowedDates;
    }
}

export const datesCalendarMapper = new DatesCalendarMapper();