﻿using Places.Standard.Services;
using Places.Standard.Services.Implementations;
using TBFront.Infrastructure.HttpService.FlightFacade.Dtos;
using TBFront.Interfaces;
using TBFront.Models.Places.Request;

namespace TBFront.Infrastructure.HttpService.FlightFacade
{
    public class PlaceStandardService : IPlaceStandardService
    {
        private readonly IPlaceService _placeService;
        private readonly FlightFacadeConfiguration _configuration;
        private readonly ILogger<PlaceStandardService> _logger;
        private readonly ICacheService _cache;
        public PlaceStandardService(FlightFacadeConfiguration options, ILogger<PlaceStandardService> logger, ICacheService cache)
        {
            _cache = cache;
            _logger = logger;
            _placeService = new PlaceService(options.Production);
        }
        public async Task<FrontBasePlace> QueryAsync(PlaceRequest request, CancellationToken ct)
        {
            var response = new Places.Standard.Dtos.BasePlace();
            try
            {
                if (string.IsNullOrEmpty(request.Uri))
                    response = await _placeService.QueryAsync((int)request.Id!.Value, request.InternalCulture, ct);

                else
                    response = await _placeService.QueryAsync(request.Uri, request.InternalCulture, ct);

            }
            catch (Exception e)
            {
                _logger.LogInformation($"placeStandard_Uri:{request.Uri}_{request.Id}_message:{e.Message}");
            }
            return new FrontBasePlace
            {
                Culture = request.Culture,
                Place = response
            };
        }

    }
}