﻿using Places.Standard.Dtos;
using Places.Standard.Services;
using Places.Standard.Services.Implementations;
using TBFront.Infrastructure.DatabaseService.DynamoDB.Dtos;
using TBFront.Interfaces;

namespace TBFront.Infrastructure.DatabaseService.DynamoDB
{
    public class PlacesAirportService : IPlacesAirportService
    {
        private readonly IAirportsPlaceService _placeService;
        private readonly PlaceAirportConfiguration _placeAirportConfiguration;
        public PlacesAirportService(PlaceAirportConfiguration placeAirportConfiguration)
        {
            _placeAirportConfiguration = placeAirportConfiguration;
            _placeService = new AirportsPlaceService(_placeAirportConfiguration.Production);
        }

        public async Task<List<Place>> QueryAsync(Places.Standard.Dtos.AirportsPlaceRequest request, CancellationToken ct)
        {
            var result = await _placeService.QueryAsync(request, ct);
            var enumerable = result as Place[] ?? result.ToArray();
            return [.. enumerable];
        }
    }
}
