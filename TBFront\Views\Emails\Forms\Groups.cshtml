﻿@using Microsoft.Extensions.Options
@using TBFront.Helpers
@using TBFront.Options;
@inject IOptions<SettingsOptions> settingOptions
@inject ViewHelper viewHelper
@{
	Layout = null;
	//ddMMyyyyHHmmss
	var fecha = DateTime.Now.ToString("ddMMyyyyHHmmss");
}

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" >
        <meta http-equiv="X-UA-Compatible" content="IE=edge" >
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
    </head>
    <body>
        <table cellpadding="0" cellspacing="0" align="center" style="font-family:Myriad Pro !important;">
				<!--cabecara-->
				<tr><td><br></td></tr>
				<tr>
					<td height="177" background="http://www.tiquetesbaratos.com/components/com_tiquetes-aereos-para-grupos/images/imgmail/arriba.png" style="background-repeat:no-repeat; width:720px; height:177px;"></td>
				</tr>
				<!--fin cabecera-->
				<!--contenido-->
				<tr>
					<td align="right" valign="top" background="http://www.tiquetesbaratos.com/components/com_tiquetes-aereos-para-grupos/images/imgmail/medio.png" style="background-repeat:repeat-y; width:720px;">
						<div>
							<table cellpadding="0" cellspacing="0" align="center" width="600" border="0" style="font-family:Arial, Helvetica, sans-serif">
								<tr><td colspan="2" height="30"></td></tr>
								<tr>
									<td align="left" style="font-size:17px; font-weight:bold;color:#006;">
										@viewHelper.Localizer("email_groups_title")
									</td>
									<td>
										<div align="center" style="background-image:url(http://www.tiquetesbaratos.com/components/com_sabre/images/imgmail/campos_1.png); width:305px; height:30px; background-repeat:no-repeat; color:#333; padding-top:8px; font-weight:bold;">
											@viewHelper.Localizer("email_groups_reservation_code"): @fecha
										</div>
									</td>
								</tr>
								<tr height="40"></tr>
								<tr>
									<td colspan="2" align="left" style="text-align:left;">
										@viewHelper.Localizer("email_groups_description_1") <span style="font-weight:bold;">@fecha</span> @viewHelper.Localizer("email_groups_description_2") <br /> <br /> @viewHelper.Localizer("email_groups_description_3") <span style="font-weight:bold;color:#006;">@viewHelper.Localizer("email_groups_email")</span><br><br>
										<span style="font-size:20px; font-weight:bold;color:#006;">@viewHelper.Localizer("email_groups_titular_name"): @Model.Name</span>
									</td>
								</tr> 
							</table>  
						</div>
						<br>
						<table cellpadding="0" cellspacing="0" align="center" style="font-family:Arial, Helvetica, sans-serif; margin-left:50px;">
							<tr>
								<td background="http://www.tiquetesbaratos.com/components/com_tiquetes-aereos-para-grupos/images/imgmail/amarillo.png" style="background-repeat:no-repeat; width:618px; height:24px;color:#006; font-weight:bold;font-size:16px;padding-left:15px;" align="left">
									@viewHelper.Localizer("email_groups_group")
								</td>
							</tr>
							<tr>
								<td background="http://www.tiquetesbaratos.com/components/com_tiquetes-aereos-para-grupos/images/imgmail/gris.png" style="background-repeat:repeat-y; width:618px; height:6px;">
									<table cellpadding="0" cellspacing="0" align="center" width="590">
										<tr>
											<td colspan="2" height="10"></td>
										</tr>
										<tr>
											<td align="left" style="font-weight:bold;">
												@viewHelper.Localizer("email_groups_origin")
											</td>
											<td align="left" style="font-weight:bold;">
												@viewHelper.Localizer("email_groups_destination")
											</td>
										</tr>
										<tr>
											<td align="left">@Model.Origin</td>
											<td align="left">@Model.Destination</td>
										</tr>
										<tr>
											<td align="left" style="font-weight:bold;">
												@viewHelper.Localizer("email_groups_outbound_date")
											</td>
											<td align="left" style="font-weight:bold;">
												@viewHelper.Localizer("email_groups_returning_date")
											</td>
										</tr>
										<tr>
											<td align="left">@Model.OutboundDate.ToString("dd/MM/yyyy")</td>
											<td align="left">@Model.ReturningDate.ToString("dd/MM/yyyy")</td>
										</tr>
										<tr>
											<td colspan="2" height="10"></td>
										</tr>
									</table>
								</td>
							</tr>
							<tr>
								<td background="http://www.tiquetesbaratos.com/components/com_tiquetes-aereos-para-grupos/images/imgmail/gris-blanco.png" style="background-repeat:repeat-y; width:618px; height:24px;" valign="top">
									<table cellpadding="0" cellspacing="0" align="center" style="color:#333; margin-top:30px; margin-left:25px;" width="685" border="0">
										<tr><td colspan="2" height="5"></td></tr>
										<tr>
											<td align="left" width="685">
												<table cellpadding="0" cellspacing="0" align="left" width="580">
													<tr>
														<td width="140" align="left" style="font-weight:bold;">
															@viewHelper.Localizer("adults"):
														</td>
														<td width="160" align="left">@Model.Adults</td>
														<td width="280" rowspan="25" align="left" valign="middle">                
															<img src="http://www.tiquetesbaratos.com/components/com_tiquetes-aereos-para-grupos/images/imgmail/grupo.jpg" width="250" height="220" alt="grupos-tiquetes-reservas" />
														</td>
													</tr>
													<tr>
														<td align="left" style="font-weight:bold;">
															@viewHelper.Localizer("email_groups_kids")
														</td>
														<td align="left">@Model.Kids</td>
													</tr>
													<tr>
														<td align="left" style="font-weight:bold;">
															@viewHelper.Localizer("email_groups_group_type")
														</td>
														<td align="left">@Model.GroupType</td>
													</tr>
													@if (Model.GroupType =="Otros") {
														<tr><td colspan="2" height="15"></td></tr>
														<tr>
															<td colspan="2" align="left">
																@Model.OtherGroup
															</td>
														</tr>
													}
													@if (Model.NeedShuttle) {
														<tr><td colspan="2" height="15"></td></tr>
														<tr>
															<td colspan="2" align="left" style="font-weight:bold;">
																@viewHelper.Localizer("email_groups_shuttle"): Sí
															</td>
														</tr>
													}
													@if (Model.NeedCar) {
														<tr><td colspan="2" height="15"></td></tr>
														<tr>
															<td colspan="2" align="left" style="font-weight:bold;">
																@viewHelper.Localizer("email_groups_car"): Sí
															</td>
														</tr>
													}
													@if(Model.NeedHotel) {
														<tr><td colspan="2" height="15"></td></tr>
														<tr>
															<td colspan="2" align="left" style="font-weight:bold;">
																@viewHelper.Localizer("email_groups_hotel"): Sí
															</td>
														</tr>
													}
													<tr><td colspan="2" height="10"></td></tr>
													<tr><td colspan="2" height="10"></td></tr>
													<tr>
														<td colspan="2" align="left" style="font-weight:bold;">
															@viewHelper.Localizer("email_groups_contact"):
														</td>
													</tr>
													<tr>
														<td align="left" style="font-weight:bold;">
															@viewHelper.Localizer("email_groups_name"):
														</td>
														<td align="left">
															@Model.Name
														</td>
													</tr>
													<tr>
														<td align="left" style="font-weight:bold;">
															@viewHelper.Localizer("email_groups_contact_email"):
														</td>
														<td align="left">
															@Model.Email
														</td>
													</tr>
													<tr>
														<td align="left" style="font-weight:bold;">
															@viewHelper.Localizer("email_groups_contact_phone"):
														</td>
														<td align="left">
															@Model.Phone
														</td>
													</tr>
													<tr><td colspan="2" height="10"></td></tr>
													<tr><td colspan="2" height="10"></td></tr>
												</table>
											</td>
										</tr>
										<tr><td colspan="2" height="20"></td></tr>
										@if (!string.IsNullOrEmpty(Model.Observations)) {
											<tr>
												<td colspan="2" align="left" style="font-weight:bold;">
													<div style="text-align:left; width:580px;">@Model.Observations</div>
												</td>
											</tr>
										}
										<tr><td colspan="2" height="20"></td></tr>
									</table>
								</td>
							</tr>
							<tr>
								<td background="http://www.tiquetesbaratos.com/components/com_tiquetes-aereos-para-grupos/images/imgmail/gris.png" style="background-repeat:repeat-y; width:618px; height:15px;" height="15">
								</td>
							</tr>
						</table>
					</td>
				</tr> 
				<!--espacios-->
				<tr>
					<td align="center" valign="top" background="http://www.tiquetesbaratos.com/components/com_tiquetes-aereos-para-grupos/images/imgmail/medio.png" style="background-repeat:repeat-y; width:720px;">
						<br>
					</td>
				</tr> 
				<!--espacios-->
				<!--modulo llama-->
				@* <form method="post" name="formllama" id="formllama" action="http://www.tiquetesbaratos.com/index.php?option=com_llama">
					<tr>
						<td align="left" valign="top" height="142" background="http://www.tiquetesbaratos.com/components/com_tiquetes-aereos-para-grupos/images/imgmail/abajo.png" style="background-repeat:no-repeat; width:720px; height:142px;">
							<table cellpadding="0" cellspacing="0" align="left" width="315" style="margin-left:160px; font-size:12px;">
								<tr><td height="73"></td></tr>
								<tr>
									<td align="left" valign="top">
										Indicativo
									</td>
									<td align="left" valign="top">
										N&uacute;mero de contacto
									</td>
								</tr>
								<tr>
									<td align="left" valign="top">
										<select style="width:80px;" name="indicativo" id="indicativo">
											<option value="0">Celular</option>
											<option value="1">1</option>
											<option value="2">2</option>
											<option value="4">4</option>
											<option value="5">5</option>
											<option value="6">6</option>
											<option value="7">7</option>
											<option value="8">8</option>               
										</select>
									</td>
									<td align="left" valign="top">
										<input type="text" name="telefono" id="telefono" size="14" />
									</td>     
									<td align="left" valign="top">
										<input type="submit" name="enviar" style="background:none; background-color:#F00;  background-image:url(http://www.tiquetesbaratos.com/components/com_tiquetes-aereos-para-grupos/images/imgmail/btn.png); width:69px; height:19px; border:0px; cursor:pointer;" value="" />
									</td>   
								</tr>    
							</table> 
						</td>
					</tr>
				</form> *@
				<!--Fin modulo llama-->
			</table>
    </body>
</html>
