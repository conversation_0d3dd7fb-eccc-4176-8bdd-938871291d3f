﻿@using TBFront.Helpers
@using Microsoft.Extensions.Options
@using TBFront.Models.Destination.Response;
@using TBFront.Models.Request;
@using TBFront.Options;
@using TBFront.Types;
@using TBFront.Models.Meta.Metatags
@inject IOptions<SettingsOptions> settingOptions
@inject IOptions<CurrencyOptions> currencyOptions

@inject ViewHelper _
@inject StaticHelper staticHelper
@{
    var resolution = _.GetImageResolution();
    var isMobile = resolution.Device == DeviceType.Mobile;
    var isRobot = _.IsRobot();
    var request = ViewData["request"] as FlightRequest;
    var destinations = ViewData["destinations"] as List<DestinationResponse>;
    var metaTag = ViewData["MetaTag"] as MetaTag;
    var typeFlight = ViewData["type"] as String;
    var destination = ViewData["destination"] as String;
    var originCode = ViewData["originCode"] as String;

    ViewData["Page"] = "home";

    ViewData["IsRobot"] = isRobot;
    ViewData["isMobile"] = isMobile;
    ViewData["Resolution"] = resolution;

}

@await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.Code}/_Header.cshtml", new ViewDataDictionary(ViewData) { { "navs", true }, { "login", isRobot } })


<origins-select-form :destinations='@Json.Serialize(destinations)' :selected-origin='@Json.Serialize(originCode)' typee="@typeFlight"></origins-select-form>

<promotions-list-cards typee="@typeFlight" :mobile='@Json.Serialize(isMobile)'></promotions-list-cards>


@* <section class="container-fluid cp-banner-full py-4 py-md-5 lazy" data-imgbg="/assets-tb/img/promociones/bg-footer-promo.png">
    <div class="container pb-5 pt-2">
        <div class="row pb-5 pb-md-0">
            <div class="col-12 col-md-8 col-lg-6 pb-5 pb-md-0">
                <h2 class="text-white font-40 m-0 py-2 py-md-3 font-poppins-semibold">Encuentra las mejores ofertas a los mejores destinos</h2>
            </div>
        </div>
    </div>
</section> *@

<div>
    @await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.Code}/_Footer.cshtml", new ViewDataDictionary(ViewData) { { "newsletter", false } })
</div>

@section Meta {
    @await Html.PartialAsync("_MetaMain", new ViewDataDictionary(ViewData) { { "Meta", ViewData["MetaTag"] }})
}


@section Preload {
    @if (!string.IsNullOrEmpty(settingOptions.Value.CloudCdn))
    {
        <link rel="preconnect" href="@settingOptions.Value.CloudCdn">
    }

    <link rel="preload" href="@staticHelper.GetVersion($"/fonts/icomoon.woff2", "/assets-tb", false)" as="font" type="font/woff2" crossorigin="" />
    <link rel="preload" href="@staticHelper.GetVersion($"/fonts/Roboto-Medium.woff2", "/assets-tb", false)" as="font" type="font/woff2" crossorigin="" />
    <link rel="preload" href="@staticHelper.GetVersion($"/fonts/Roboto-Regular.woff2","/assets-tb", false)" as="font" type="font/woff2" crossorigin="" />
    <link rel="preload" href="@staticHelper.GetVersion($"/fonts/Poppins-Regular.woff2", "/assets-tb", false)" as="font" type="font/woff2" crossorigin="" />



    <link rel="preload" href="@staticHelper.GetVersion($"/{settingOptions.Value.SiteName}/css/promotion.css", settingOptions.Value.Assets)" as="style" />
}

@section Css {
    <link type="text/css" rel="stylesheet"
          href="@staticHelper.GetVersion($"/{settingOptions.Value.SiteName}/css/promotion.css", settingOptions.Value.Assets)">
    <style>
        body.cloak {
            display: none !important;
        }
    </style>
}



@section ScriptsPriority {
}


@section Scripts {
    <script>
        window.__pt = window.__pt || {};
        window.__pt.data = @Json.Serialize(request);
        window.__pt.destinations = @Json.Serialize(destinations);
        window.__pt.places_info = window.__pt.places_info || {};
        window.__pt.resolution = window.__pt.resolution || @Json.Serialize(resolution);
        window.__pt.cultureData = @Json.Serialize((ViewData["CultureData"]));
        window.__pt.exchange = @Json.Serialize((ViewData["Exchange"]));
        window.__pt.alternates = @Json.Serialize((ViewData["Alternates"]));
        window.__pt.currency = @Json.Serialize((ViewData["CurrencyData"]));
        window.__pt.currencies = @Json.Serialize((currencyOptions.Value.Currencies));
    </script>

    <script src="@staticHelper.GetVersion("/js/vue/app.js", settingOptions.Value.Assets)"></script>
}