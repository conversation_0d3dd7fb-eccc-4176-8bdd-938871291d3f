﻿using System.ComponentModel.DataAnnotations;

namespace TBFront.Models.Forms.Request
{
    public class OnlinePaymentRequest
    {
        [Required]
        public string Code { get; set; }

        [Required]
        public string Email { get; set; }
        public string? SessionId { get; set; }

        [Required]
        public string RecaptchaToken { get; set; }

        public bool NoValid()
        {
            return string.IsNullOrEmpty(Code) && string.IsNullOrEmpty(Email) && string.IsNullOrEmpty(RecaptchaToken);
        }
    }
}
