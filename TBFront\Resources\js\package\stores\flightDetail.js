import { defineStore } from "pinia";
import { __ } from "../../utils/helpers/translate";

export const useFlightDetailStore = defineStore({
    id: "flightDetail",
    state: () => ({
        showDetail: false,
        flightDetailResponse: {
            flightDuration: '',
            flightSegments: [],
            stops: '',
        },
        extraData: {}
    }),
    getters: {
        getShowDetail: (state) => {
            return state.showDetail;
        },
        getFlightDetail: (state) => {
            return state.flightDetailResponse;
        },
        getExtraData: (state) => {
            return state.extraData;
        }
    },
    actions: {
        setFlightDetailResponse(response) {
            this.flightDetailResponse = response;

        },
        activeModalDetail() {
            this.showDetail = !this.showDetail;
        },
        durationFormat(time) {
            // Dividir el string en horas y minutos
            let [hours, minutes] = time.split(' ');
            // Extraer el n�mero de horas y minutos
            hours = parseInt(hours.replace('h', ''));
            minutes = parseInt(minutes.replace('m', ''));
            // Construir la parte de horas del string
            let hoursText = hours === 1 ? `${hours} ${__("messages.hour")}` : `${hours} ${__("messages.hours")}`;
            // Construir la parte de minutos del string
            let minutesText = minutes === 1 ? `${minutes} ${__("messages.minute")}` : `${minutes} ${__("messages.minutes")}`;
            // Combinar ambos textos
            let flightDuration = `${hoursText} ${__("booker.and")} ${minutesText}`;
            if (hours < 1) {
                flightDuration = minutesText;
            } else if (minutes < 1) {
                flightDuration = hoursText;
            }
            return flightDuration;
        },
        setExtraData(response) {
            this.extraData = response;

        },
    },
});