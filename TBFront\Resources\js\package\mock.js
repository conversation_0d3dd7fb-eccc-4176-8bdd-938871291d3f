
export const data_flights = [
    {
        airlineImg: "/assets-tb/img/logos-internacionales/iberia.svg",
        airline: "Iberia",
        airline_code: "avianca",
        price: 2210000,
        hasTaxes: false,
        cheaperPrice: 2210000,
        flight_date_start: new Date(),
        flight_date_end: new Date(),
        families_fare: [
            {
                code: "XS",
            },
            {
                code: "S",
            },
            {
                code: "M",
            },
        ],
        departure_rates: [
            {
                departureTime: "07:20",
                arrivalTime: "18:54",
                totalFlight: "04h 40m",
                flight_number: "4043",
                stops: 1,
                price: 367670,
                code: "XS",
                families_fare: [
                    {
                        code: "M",
                        price: 367670
                    },
                    {
                        code: "S",
                        price: 367670
                    },
                    {
                        code: "XS",
                        price: 367670
                    }
                ]

            },
            {
                departureTime: "07:20",
                arrivalTime: "18:54",
                totalFlight: "04h 40m",
                flight_number: "4044",
                stops: 0,
                price: 1023700,
                code: "XS",
                families_fare: [
                    {
                        code: "XS",
                        price: 367670
                    }
                ]
            }
        ],
        arrival_rates: [
            {
                departureTime: "07:20",
                arrivalTime: "18:54",
                totalFlight: "04h 40m",
                flight_number: "4044",
                stops: 1,
                price: 367670,
                code: "XS",
                families_fare: [
                    {
                        code: "XS",
                        price: 367670
                    }
                ]
            },
            {
                departureTime: "07:20",
                arrivalTime: "18:54",
                totalFlight: "04h 40m",
                flight_number: "4044",
                stops: 0,
                price: 1023700,
                code: "XS",
                families_fare: [
                    {
                        code: "XS",
                        price: 367670
                    }
                ]
            }
        ]
    },
    {
        airlineImg: "/assets-tb/img/tiquetesbaratos/logos/logo-viva.svg",
        airline: "Viva",
        airline_code: "viva",
        price: 2210000,
        hasTaxes: false,
        cheaperPrice: 2210000,
        flight_date_start: new Date(),
        flight_date_end: new Date(),
        families_fare: [
            {
                code: "XS",
            },
            {
                code: "S",
            },
            {
                code: "M",
            },
        ],
        departure_rates: [
            {
                departureTime: "07:20",
                arrivalTime: "18:54",
                totalFlight: "04h 40m",
                stops: 1,
                price: 367670,
                code: "XS",
                families_fare: [
                    {
                        code: "XS",
                        price: 367670
                    }
                ]
            }
        ],
        arrival_rates: [
            {
                departureTime: "07:20",
                arrivalTime: "18:54",
                totalFlight: "04h 40m",
                stops: 1,
                price: 367670,
                code: "XS",
                families_fare: [
                    {
                        code: "XS",
                        price: 367670
                    }
                ]
            },
            {
                departureTime: "07:20",
                arrivalTime: "18:54",
                totalFlight: "04h 40m",
                stops: 0,
                price: 1023700,
                code: "XS",
                families_fare: [
                    {
                        code: "XS",
                        price: 367670
                    }
                ]
            }
        ]
    }
];
