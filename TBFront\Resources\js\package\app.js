/**
 * First we will load all of this project's JavaScript dependencies which
 * includes Vue and other libraries. It is a great starting point when
 * building robust, powerful web applications using Vue.
 */


import ModalCheckPaymentMethods from "./components/modals/home/<USER>";

require('../bootstrap');

import _ from 'lodash';

import { createApp, defineAsyncComponent } from 'vue';
import LazyLoading from 'vue-lazy-loading';
import dayjs from 'dayjs';
import 'dayjs/locale/es';
import pinia from './stores/store'
import { site } from '../main';

import FlightBooker from './components/booker/FlightBooker.vue';
import RecentResearch from './components/research/RecentResearch.vue';
import NewsletterForm from './components/newsletter/NewsletterForm.vue';
import ListPage from './components/list/ListPage.vue';
import CheckoutOnePage from './components/checkout/CheckoutOnePage.vue';
import CheckoutThreePage from './components/checkout/CheckoutThreePage.vue';
import CheckReservation from './components/help_center/CheckReservation.vue';
import PayOnline from './components/help_center/PayOnline.vue';
import PayOnlineData from './components/help_center/PayOnlineData.vue';
import LoaderPage from './components/common/LoaderPage.vue';
import Loading from './components/common/Loading.vue';
import LanguageCurrencyButton from "./components/common/LanguageCurrencyButton.vue";


/*import InternationalModalDetail from './components/modal_detail/InternationalModalDetail.vue';*/
import Skeleton from './components/list/components/Skeleton.vue';
/*import ModalUpsell from './components/modal_upsell/ModalUpsell.vue';*/

import InternationalModalDetail from './components/modals/InternationalModalDetail.vue';
import NationalModalDetail from './components/modals/NationalModalDetail.vue';
import ModalUpsell from './components/modals/ModalUpsell.vue';
import ItemFlightLeg from './components/modals/ItemFlightLeg.vue';
import GroupsForm from './components/help_center/GroupsForm.vue';
import NeedACall from './components/help_center/NeedACall.vue';
import Phones from './components/help_center/Phones.vue';
import ModalMessages from './components/modals/ModalMessages.vue';
import ModalFamilyFare from './components/modals/ModalFamilyFare.vue';
import LanguageCurrencyModal from "./components/modals/LanguageCurrencyModal.vue";

import { defineRule, configure } from 'vee-validate';
import { required, email, alpha_spaces, is, is_not, integer } from '@vee-validate/rules';
import { localize, setLocale } from '@vee-validate/i18n';



const app = createApp({});
const culture = window.__pt.cultureData;
var exchange = window.__pt.exchange || {};
dayjs.locale(culture.language);

app.use(pinia);


app.component('FlightBooker', FlightBooker);
app.component('RecentResearch', RecentResearch);
app.component('NewsletterForm', NewsletterForm);
app.component('ListPage', ListPage);
app.component('CheckoutOnePage', CheckoutOnePage);
app.component('CheckoutThreePage', CheckoutThreePage);
app.component('CheckReservation', CheckReservation);
app.component('PayOnline', PayOnline);
app.component('PayOnlineData', PayOnlineData);
app.component('LoaderPage', LoaderPage);
app.component('ModalCheckPaymentMethods', ModalCheckPaymentMethods);
app.component('Loading', Loading);
app.component('LanguageCurrencyButton', LanguageCurrencyButton);

app.component('ModalFlightDetail', InternationalModalDetail);
app.component('NationalModalDetail', NationalModalDetail);
app.component('Skeleton', Skeleton);
app.component('ModalUpsell', ModalUpsell);
app.component('ItemFlightLeg', ItemFlightLeg);
app.component('GroupsForm', GroupsForm);
app.component('NeedACall', NeedACall);
app.component('HeaderPhones', Phones);
app.component('ModalMessages', ModalMessages);
app.component('ModalFamilyFare', ModalFamilyFare);
app.component('LanguageCurrencyModal', LanguageCurrencyModal);

app.component('FiltersPill', defineAsyncComponent(() =>
    import('./components/list/components/FiltersPill.vue')
));

app.component('NearByDates', defineAsyncComponent(() =>
    import('./components/list/components/NearByDates.vue')
));

app.component('MatrixFlight', defineAsyncComponent(() =>
    import('./components/list/components/MatrixFlight.vue')
));
app.component('InternationalFlightOneWay', defineAsyncComponent(() =>
    import('./components/list/components/InternationalFlightOneWay.vue')
));
app.component('InternationalFlightRoundTrip', defineAsyncComponent(() =>
    import('./components/list/components/InternationalFlightRoundTrip.vue')
));
app.component('NationalFlight', defineAsyncComponent(() =>
    import('./components/list/components/NationalFlight.vue')
));
app.component('TooltipNearDates', defineAsyncComponent(() =>
    import('./components/list/components/TooltipNearDates.vue')
));
//Checkout
app.component('SecurityInformation', defineAsyncComponent(() =>
    import('./components/checkout/components/SecurityInformation.vue')
));

app.component('SummaryCheckout', defineAsyncComponent(() =>
    import('./components/checkout/components/Summary.vue')
));

app.component('CheckoutOneForm', defineAsyncComponent(() =>
    import('./components/checkout/components/CheckoutOneForm.vue')
));

app.component('ContactForm', defineAsyncComponent(() =>
    import('./components/help_center/ContactForm.vue')
));

app.component('FrequentQuestions', defineAsyncComponent(() =>
    import('./components/help_center/FrequentQuestions.vue')
));

app.component('SeoQuestions', defineAsyncComponent(() =>
    import('./components/seo/SeoQuestions.vue')
));

app.component('OriginsSelectForm', defineAsyncComponent(() =>
    import('./components/promotion/originsSelectForm.vue')
));

app.component('PromotionsListCards', defineAsyncComponent(() =>
    import('./components/promotion/promotionsListCards.vue')
));

app.component('CalendarDatesQuotes', defineAsyncComponent(() =>
    import('./components/list/components/CalendarDatesQuotes.vue')
));

app.component('ProgressBar', defineAsyncComponent(() =>
    import('./components/list/components/ProgressBar.vue')
));


app.component('PromotionsCalendar', defineAsyncComponent(() =>
    import('./components/promotion/promotionsCalendar.vue')
));

app.component('Filters', defineAsyncComponent(() =>
    import('./components/list/components/Filters.vue')
));

app.component('LoaderFullPage', defineAsyncComponent(() =>
    import('./components/common/LoaderFullPage.vue')
));

app.component('FlightNotAvailable', defineAsyncComponent(() =>
    import('./components/list/components/FlightNotAvailable.vue')
));

app.component('RequoteModal', defineAsyncComponent(() =>
    import('./components/modals/RequoteModal.vue')
));

app.component('TooltipCharges', defineAsyncComponent(() =>
    import('./components/checkout/components/TooltipCharges.vue')
));

app.component('MultiTicketList', defineAsyncComponent(() =>
	import('./components/list/MultiTicketList.vue')
));

app.component('MultiTicketListItem', defineAsyncComponent(() =>
	import('./components/list/MultiTicketListItem.vue')
));

app.component('MultiTicketListSelected', defineAsyncComponent(() =>
	import('./components/list/MultiTicketListSelected.vue')
));

app.component('AirlineFamiliesNational', defineAsyncComponent(() =>
    import('./components/list/components/nationals/AirlineFamiliesNational.vue')
));

app.component('NotifyCurrency', defineAsyncComponent(() =>
    import('./components/checkout/components/NotifyCurrency.vue')
));

app.component('EmailAutocompleteField', defineAsyncComponent(() =>
    import('./components/checkout/components/EmailAutocompleteField.vue')
));

app.config.globalProperties.$filters = {
    currency(value, maximumFractionDigits = __pt.settings.site.decimalDigits, applyConvertion = true) {
        const formatter = new Intl.NumberFormat(window.__pt.settings.site.culture, {
            style: 'decimal',
            minimumFractionDigits: maximumFractionDigits,
            maximumFractionDigits: maximumFractionDigits,
        });
        return formatter.format(value * (applyConvertion ? exchange.rate : 1));
    },
    currencyBase(value, currency = __pt.exchange.base, maximumFractionDigits = 0, culture = __pt.cultureData.cultureCode) {
        if (typeof value !== "number") {
            return value;
        }

        let formatter = new Intl.NumberFormat(culture, {
            style: 'currency',
            currency,
            currencyDisplay: 'code',
            maximumFractionDigits: maximumFractionDigits
        });
        return (formatter.format(value)).replace(`${currency}`, `${currency}`);
    },
    currencyWithDesc(value, currency = __pt.settings.site.currency) {
        if (typeof value !== "number") {
            return value;
        }
        let formatter = new Intl.NumberFormat(__pt.settings.site.culture, {
            style: 'currency',
            currency,
            currencyDisplay: 'symbol',
            maximumFractionDigits: 0
        });
        return `${formatter.format(value)} ${__pt.settings.site.currency}`;
    },
    date(value, format = 'YYYY-MM-DD HH:mm') {
        if (value) {
            dayjs.locale(culture.language);

            const date = dayjs(value).format(format);

            if (date != null) {
                return date.charAt(0).toUpperCase()
                    + date.slice(1)
            } else {
                return date
            }
        }
    },
    thousandFormat(value) {
        if (typeof value !== "number") {
            return value;
        }
        let formatter = new Intl.NumberFormat(__pt.settings.site.culture, {
            style: 'decimal',
            maximumFractionDigits: 0
        });

        return formatter.format(value);
    },
    thousandCurrencyFormat(value) {
        if (typeof value !== "number") {
            return value;
        }
        let formatter = new Intl.NumberFormat(__pt.settings.site.culture, {
            style: 'currency',
            currency: __pt.settings.site.currency,
            maximumFractionDigits: 0
        });

        return formatter.format(value);
    },
    getIconStars(value) {
        return `icon-${Math.floor(value)}${value % 1 == 0 ? "-" : "-half-"}star`;
    },
    isAfterDate(value) {
        if (value) {
            return dayjs().isAfter(dayjs(value), 'day');
        }

        return false;
    },
    isAfterDateFromDate(from, returne) {
        if (from && returne) {
            return dayjs(returne).isAfter(dayjs(from), 'day') || dayjs(from).isSame(dayjs(returne), 'day');
        }
        return false;
    },
    isSameDate(value) {
        if (value) {
            return dayjs().isSame(dayjs(value), 'day');
        }

        return false;
    },
    isSameDateFromDate(from, returne) {
        if (value) {
            return dayjs(from).isSame(dayjs(returne), 'day');
        }

        return false;
    },
    increaseWeek(val) {
        if (val) {
            return dayjs(val).add(7, 'day');
        }
        return false;
    },
    subtractWeek(val) {
        if (val) {
            return dayjs(val).subtract(7, 'day');
        }
        return false;
    },
    addOrSubstractDays(date, days) {
        if (date && days > 0) {
            return dayjs(date).add(days, 'day');
        } else if (days < 0) {
            return dayjs(date).subtract(Math.abs(days), 'day');
        }
        return false;
    },
    createLocalDateFromISOString(dateString) {
        const localDateString = dateString.replace('Z', '');
        const [year, month, day] = localDateString.split('T')[0].split('-').map(Number);
        return new Date(year, month - 1, day);
    },
    formatNumber(value) {
        const num = Number(value);
        if (isNaN(num)) return '--';
        const formatted = new Intl.NumberFormat(window.__pt.settings.site.culture, {
            notation: 'compact',
            maximumFractionDigits: 1
        }).format(num * (exchange.rate));

        return formatted
            .replace(',', '.')
            .replace(/\s?([km])$/, (_, s) => s.toUpperCase());
    },
}

app.config.globalProperties.__ = (str, values = []) => {
    let formatted = _.get(window.i18n, str, str);

    if (values.length) {
        formatted = _.replace(formatted, /\{\d\}/g, (match) => {
            const index = match.replace(/\{|\}/g, '');
            return values[index] || match;
        });
    }

    return formatted;
}

app.config.globalProperties.time24to12Convert = (time) => {
    // Check correct time format and split into components
    time = time.toString().match(/^([01]\d|2[0-3])(:)([0-5]\d)(:[0-5]\d)?$/) || [time];
    if (time.length > 1) {
        // If time format is correct
        time = time.slice(1); // Remove full string match value
        time[5] = +time[0] < 12 ? ' AM' : ' PM'; // Set AM/PM
        time[0] = +time[0] % 12 || 12; // Adjust hours
    }

    time[3] = "";
    return time.join('');
}

app.directive('click-outside', {
    beforeMount: function (element, binding) {
   
        //  check that click was outside the el and his children
        element.clickOutsideEvent = function (event) {
            // and if it did, call method provided in attribute value
            if (!(element === event.target || element.contains(event.target))) {
                binding.value(event);
            }
        };
        document.body.addEventListener('click', element.clickOutsideEvent)
    },
    unmounted: function (element) {
        document.body.removeEventListener('click', element.clickOutsideEvent)
    }
});

Date.prototype.addDays = function (days) {
    const date = new Date(this.valueOf());
    date.setDate(date.getDate() + days);
    return date;
};

defineRule('uniqueNames', (value, args) => {
	if (!value) return true;

	const { fields, currentIndex } = args;
	const nameCounts = fields.reduce((acc, name, index) => {
		if (index !== currentIndex) {
			acc[name.identityDocument] = (acc[name.identityDocument] || 0) + 1;
		}
		return acc;
	}, {});
	if (nameCounts[value]) {
		return window.i18n.errors.distinct;
	}

	return true;
});

defineRule('required', required);
defineRule('email', email);
defineRule('alpha_spaces', alpha_spaces);
defineRule('is', is);
defineRule('is_not', is_not);
defineRule('integer', integer);
configure({
    generateMessage: localize(culture.language, {
        messages: window?.i18n?.errors,
    }),
});
setLocale(culture.language);

app.config.globalProperties.global_settings = __pt.settings.site || {};
window.app = app;
window.app.config.productionTip = false;
window.app.config.devtools = true;
site.onInit();
app.use(LazyLoading).mount('#app');

