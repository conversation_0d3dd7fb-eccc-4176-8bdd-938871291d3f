<template>
    <!--<button class="btn btn-primary" data-bs-target="#modalAirlineFamiliesNational" data-bs-toggle="modal">Open first</button>-->
    <div class="modal fade modal-slide-left" id="modalAirlineFamiliesNational" aria-hidden="true" aria-labelledby="exampleModalToggleLabel" tabindex="-1">
        <div id="modalDialog" class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header ps-4">
                    <h4 class="modal-title fs-5" id="exampleModalToggleLabel">{{__('flightList.what_each_rate_includes')}}</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <template v-if="!loadingAirlineFamiliesNational">
                    <div class="c-crls">
                        <div class="cc my-auto position-absolute cc-left shadow" @click="scrollLeftButton()" :class="{ 'd-none': isLeftHidden }">
                            <span class="position-relative icon icon-chevron-left"></span>
                        </div>
                        <div class="cc my-auto position-absolute cc-right shadow" @click="scrollRightButton()" :class="{ 'd-none': isRightHidden }">
                            <span class="position-relative icon icon-chevron-right"></span>
                        </div>
                    </div>
                </template>
                <template v-if="loadingAirlineFamiliesNational">
                    <div class="loading-section position-relative">
                        <div id="loader-page" class="loading-page d-center ">
                            <div class="loader__logo"></div>
                        </div>
                    </div>
                </template>
                <div class="modal-body overflow-x-auto overflow-y-hidden px-4">
                    <template v-if="!loadingAirlineFamiliesNational">
                        <div class="c-scroll h-100 position-relative">
                            <div class="cs-int h-100">
                                <template v-for="family in airlineFamiliesNational.families">
                                    <div class="col evt-col cb-blur p-3 " :id="family.familyFareName">
                                        <div class="cm-header px-3 pt-3">
                                            <div class="c-rate-style"></div>
                                            <div class="row mb-2">
                                                <div class="col-12 ps-4 ms-1">
                                                    <h4>{{family.familyFareName}}</h4>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="cm-body py-4 ps-2 pe-3 overflow-y-auto overflow-x-hidden">
                                            <template v-for="(familyFareContent, indexFamilyFareContent) in mappingFamilyFare(family.familyFareContent)">
                                                <div :data-next="family.familyFareContent[indexFamilyFareContent + 1]?.category"
                                                     :data-category="familyFareContent.category" class="mb-3">
                                                    <p class="mb-0 position-relative">
                                                        <span class="icon" :class="familyFareContent.class"></span>
                                                        <span class="font-medium ps-4 d-inline-block">{{familyFareContent.title}}</span>
                                                    </p>
                                                    <p class="ps-24 font-regular"
                                                       :class="{'color-disabled-text': familyFareContent.include === 2 || familyFareContent.include === 0}">
                                                        {{familyFareContent.description}}
                                                    </p>
                                                </div>
                                                <template v-if="indexFamilyFareContent === 1">
                                                    <div class="col-12 border-bottom mb-3 ms-1"></div>
                                                </template>
                                            </template>
                                        </div>
                                    </div>
                                </template>

                            </div>
                        </div>
                    </template>

                </div>
            </div>
        </div>
    </div>
</template>

<style scoped lang="scss">

</style>
<script>
    import {data_flights} from "../../../../mock";
    import {useAirlineFamiliesNationalStore} from "../../../../stores/airlineFamiliesNational";
    import {storeToRefs} from "pinia";
    import { responsiveObserver } from "../../../../../utils/helpers/responsiveObserver";
    import { mappingFamilyFare } from '../../../../../utils/utils';

export default {
    data() {
        return {
            configSite: window.__pt.settings.site.airlineConfiguration.national ?? [],
            data: data_flights,
            step_action: 1,
            pagination: {},
            skeletons: [1, 2, 3, 4],
            pt: window.__pt,
            current_family: 0,
            clicked_action_step: false,
            scrollPosition: 0,
            anchoScroll: 0,
            isLeftHidden: true,
            isRightHidden: false
        }
    },
    computed: {
        airlineFamiliesNational(){
            return this.getAirlineFamiliesNational
        },
        isResponsive() {
            return this.isResponsiveRef;
        },
        loadingAirlineFamiliesNational() {
            return this.loadingAirlineFamiliesNational;
        }
    },
    setup() {
        const airlineFamiliesNationalStore = useAirlineFamiliesNationalStore()
        const { getAirlineFamiliesNational, loadingAirlineFamiliesNational } = storeToRefs(airlineFamiliesNationalStore);
        const isResponsiveRef = responsiveObserver.getResponsiveStatus();
        return {
            getAirlineFamiliesNational,
            isResponsiveRef,
            loadingAirlineFamiliesNational,
            mappingFamilyFare
        }
    },
    mounted() {
        // Llama a la función para hacer la búsqueda inicial y aplicar las clases
        this.checkEvtColDivs();

        // Si deseas que la función se ejecute cada vez que cambie el DOM, puedes usar un MutationObserver
        const observer = new MutationObserver(this.checkEvtColDivs);
        observer.observe(document.body, { childList: true, subtree: true });
        document.querySelector(".modal-body.overflow-x-auto").addEventListener('scroll', this.handleScroll);
     
    },
    methods:{
        // Función para verificar la cantidad de divs con la clase 'evt-col'
        checkEvtColDivs() { // Busca todos los divs con la clase 'evt-col'
            const evtColDivs = document.querySelectorAll('div.evt-col');

            // Selecciona el div con el id 'modalDialog'
            const modalDialog = document.getElementById('modalDialog');

            // Verifica si el div con id 'modalDialog' existe
            if (!modalDialog) {
                console.error("No se encontró el div con id 'modalDialog'");
                return;
            }

            // Remueve todas las clases previas que coinciden con el patrón 'modal-no-'
            modalDialog.classList.remove('modal-no-01', 'modal-no-02', 'modal-no-03', 'modal-no-04');

            // Dependiendo del número de divs encontrados, añade la clase correspondiente
            switch (evtColDivs.length) {
                case 1:
                    modalDialog.classList.add('modal-no-01');
                    break;
                case 2:
                    modalDialog.classList.add('modal-no-02');
                    break;
                case 3:
                    modalDialog.classList.add('modal-no-03');
                    break;
                default:
                    if (evtColDivs.length >= 4) {
                        modalDialog.classList.add('modal-no-04');
                    }
                    break;
            }
        },
        handleScroll() {
            let scrollContainer = document.querySelector("#modalAirlineFamiliesNational").querySelector(".modal-body");
            this.anchoScroll = scrollContainer.scrollWidth - scrollContainer.clientWidth;
            this.scrollPosition = scrollContainer.scrollLeft;
            this.isLeftHidden = Math.ceil(this.scrollPosition) <= 0;
            this.isRightHidden = this.anchoScroll > 0 && Math.ceil(this.scrollPosition) >= this.anchoScroll;
        },
        scrollLeftButton() {
            let scrollContainer = document.querySelector("#modalAirlineFamiliesNational").querySelector(".modal-body");
            let slide =  -((scrollContainer.scrollWidth / (document.querySelectorAll('.evt-col')).length) - 30);
            slide = -scrollContainer.scrollLeft < slide  ? slide : -scrollContainer.scrollLeft
            scrollContainer.scrollBy({ left: (slide), behavior: 'smooth' })
        },
        scrollRightButton() {
            let scrollContainer = document.querySelector("#modalAirlineFamiliesNational").querySelector(".modal-body");
            let slide = (scrollContainer.scrollWidth / (document.querySelectorAll('.evt-col')).length)- 30;
            let anchoScroll = scrollContainer.scrollWidth - scrollContainer.clientWidth;
            let scrollPosition = scrollContainer.scrollLeft;
            slide = (anchoScroll - Math.ceil(scrollPosition)) >= slide  ? slide : anchoScroll - Math.ceil(scrollPosition)
            scrollContainer.scrollBy({ left: (slide), behavior: 'smooth' })
        },
        scrollToElement(element, orientation="right") {
            if (!this.isElementVisible(element)) {
                element.scrollIntoView({ behavior: 'smooth', block: 'end' });
                let elementN = document.querySelector("#modalAirlineFamiliesNational").querySelector(".modal-body");
                if(elementN){
                    setTimeout(()=>{
                        elementN.scrollBy({
                            top: 0, // No scroll vertical
                            left: orientation === 'left'? -120: 120, // "30px" scroll horizontal
                            behavior: 'smooth' // Smooth scrolling
                        });
                    }, 200)
                }
            }
        },
        
        isElementVisible(element) {
            const rect = element?.getBoundingClientRect();
            return rect ? (
                rect.top >= 0 &&
                rect.left >= 0 &&
                rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                rect.right <= (window.innerWidth || document.documentElement.clientWidth)
            ) :  true;
        }
        
    }
}

</script>
<style lang="scss" scoped>
@media (min-width: 576px) {
  .loading-section {
    height: 80% !important;

    .loading-page {
      position: absolute;
    }
  }
}
#modalAirlineFamiliesNational {
    .modal-dialog {
        @media (max-width: 768px) {
            margin: 0;
        }      
    }
    .cm-body {
        .icon {
            position: absolute;
            top: 3px;
        }  
        .icon-carry-on {
            left: 3px;
        }
    }
}

#modalAirlineFamiliesNational .cm-body .icon-left {
    &:after {
        background-color: #ccc;
        border-radius: 50px;
        content: "";
        height: 6px;
        width: 6px;
        display: block;
        right: -14px;
        position: absolute;
        top: 6px;
    }
}
#modalAirlineFamiliesNational .cm-body .icon-left.icon-big-bag, .cm-body .icon-left.icon-carry-on, .cm-body .icon-left.icon-big-bag-out, .cm-body .icon-left.icon-carry-on-bag {
    &:after {
        display: none !important;
    }
    &:before {
        color: #444 !important;
    }
}
#modalAirlineFamiliesNational .cm-body .icon-big-bag-out, #modalAirlineFamiliesNational .cm-body .icon-carry-on-bag {
    &:before {
        color: #999 !important;
        margin-right: 3px;
        margin-left: 4px;
    }    
}



#modalAirlineFamiliesNational .cm-header {
    .c-rate-style {
        left: 0;
    }
}

</style>