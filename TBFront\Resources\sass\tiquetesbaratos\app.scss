﻿// 1. Include functions first (so you can manipulate colors, SVGs, calc, etc)
@import "../../../node_modules/bootstrap/scss/functions";

// 2. Include any default variable overrides here
@import "./variables_custom";

// 3. Include remainder of required Bootstrap stylesheets
@import "../../../node_modules/bootstrap/scss/variables";
@import "../../../node_modules/bootstrap/scss/variables-dark";

// 4. Include any default map overrides here
@import "./variables";
// 5. Include remainder of required parts
@import "../../../node_modules/bootstrap/scss/maps";
@import "../../../node_modules/bootstrap/scss/mixins";
@import "../../../node_modules/bootstrap/scss/root";
@import "../../../node_modules/bootstrap/scss/utilities";


// 6. Optionally include any other parts as needed
@import "../../../node_modules/bootstrap/scss/alert";
@import "../../../node_modules/bootstrap/scss/buttons";
@import "../../../node_modules/bootstrap/scss/card";
@import "../../../node_modules/bootstrap/scss/close";
@import "../../../node_modules/bootstrap/scss/containers";
@import "../../../node_modules/bootstrap/scss/forms";
@import "../../../node_modules/bootstrap/scss/grid";
@import "../../../node_modules/bootstrap/scss/helpers";
@import "../../../node_modules/bootstrap/scss/images";
@import "../../../node_modules/bootstrap/scss/list-group";
@import "../../../node_modules/bootstrap/scss/nav";
@import "../../../node_modules/bootstrap/scss/navbar";
@import "../../../node_modules/bootstrap/scss/modal";
@import "../../../node_modules/bootstrap/scss/reboot";
@import "../../../node_modules/bootstrap/scss/transitions";
@import "../../../node_modules/bootstrap/scss/type";
@import "../../../node_modules/bootstrap/scss/accordion";
@import "../../../node_modules/bootstrap/scss/bootstrap.scss";

// 7. Optionally include utilities API last to generate classes based on the Sass map in `_utilities.scss`
@import "../../../node_modules/bootstrap/scss/utilities/api";
@import "../../../node_modules/bootstrap/scss/utilities";

// 8. Add additional custom code here

@import './base/_icons';
@import 'components/_buttons';
@import './dsdestiny.scss';
@import './components/faq.scss';
@import "components/_links.scss";
// @import "components/_helpers.scss";

.d-center{
    display: flex;
    justify-content:center;
    align-items: center;
}

.cursor-pointer{
    cursor: pointer;
}

.first_letter::first-letter {
    text-transform: uppercase;
}

.cursor-context-menu{
    cursor: context-menu !important;
}

.text-shadow {
    text-shadow: 2px 2px 6px rgba(0,0,0,.4);
}

.color-gray-300 {
    color: #a6acb4;
}

.color-blue {
    color: #003b98 !important;
}

.color-blue-ligth {
    color: rgb(33, 150, 243);
}

.color-yellow {
    color: #ffcd00;
}
.color-subtle {
    color: #696770 !important;
}

.color-red {
    color: #dc2626 !important;
}

.c-gray {
    color: #908e8e;
}

.text-shadow {
    text-shadow: 1px 1px 2px black;
}

.modal {
    --bs-modal-zindex: 1050;
    --bs-modal-width: 740px;
    --bs-modal-padding: 1rem;
    --bs-modal-margin: 0.5rem;
    --bs-modal-bg: #fff;
    --bs-modal-border-color: rgba(0, 0, 0, 0.2);
    --bs-modal-border-width: 1px;
    --bs-modal-border-radius: 0.3rem;
    --bs-modal-box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.5);
    --bs-modal-inner-border-radius: calc(0.3rem - 1px);
    --bs-modal-header-padding-x: 1rem;
    --bs-modal-header-padding-y: 1rem;
    --bs-modal-header-padding: 1rem 1rem;
    --bs-modal-header-border-color: #dee2e6;
    --bs-modal-header-border-width: 1px;
    --bs-modal-title-line-height: 1.5;
    --bs-modal-footer-gap: 0.5rem;
    --bs-modal-footer-border-color: #dee2e6;
    --bs-modal-footer-border-width: 1px;
    position: fixed;
    top: 0;
    left: 0;
    z-index: var(--bs-modal-zindex);
    display: none;
    width: 100%;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    outline: 0;
}

.a-link-1 {
    color: #186cdf !important;
    font-family: Roboto-Regular;
    text-decoration: none;

    &:hover {
        cursor: pointer;
        text-decoration: underline;
    }

    span {
        color: #186cdf;
    }
}



/* start common styles */

.w-100-md {
    @media ($tablet) {
        width: 100%;
    }
}

.w-100-xs {
    @media ($phone) {
        width: 100%;
    }
}



@media (min-width: 576px) {
    .modal-change .modal-md {
        max-width: 468px !important;
    }
}
.border-hard {
    border-color: #ccc;
}




/* colors tb */
.lh-1-1 {
    line-height: 1.1;
}

.font-bold {
    font-weight: bold;
}
.font-semibold {
    font-weight: 500;
}

.font-medium {
    font-family: Roboto-Medium;
}

.font-regular {
    font-family: Roboto-Regular;
}

/* colors */
.color-green {
    color: var(--text-semantic-text-success);
}
.color-gray-100 {
    color: #696770;
}
.color-orange {
    color: #F2BD0D;
}
.text-danger {
    color: #D93616 !important;
}

.color-desibled {}

@media (min-width: 576px) {
    .modal-sm {
        max-width: 390px !important;
    }
}

strong {
    font-weight: bold;
}

/*loader*/
.modal-loading {
    .ca-load {
        height: 140px;
    }

    .cn-loader {
        border: 16px solid #f3f3f3;
        border-top: 16px solid #2196f3;
        border-radius: 50%;
        width: 120px;
        height: 120px;
        animation: spin 4s linear infinite;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        margin: auto;
        z-index: 999;
    }

    .icon-loader {
        width: 120px;
        height: 120px;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        margin: auto;
        z-index: 999;
    }

    .icon-loader::before {
        color: #2196f3;
        content: "\e91a";
        display: block;
        position: absolute;
        top: 53%;
        left: 53%;
        transform: translate(-50%, -60%);
        opacity: 0;
        animation: fade 4s ease-in-out infinite;
        font-family: "icomoon";
        font-size: 50px;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }

    @keyframes fade {
        0%, 100% {
            opacity: 0;
        }

        50% {
            opacity: 1;
        }
    }


    .container-loading {
        border: 1px solid #2196f3;
        border-radius: 10px;
        bottom: 0;
        height: 340px;
        left: 0;
        margin: auto;
        position: absolute;
        right: 0;
        top: 0;
        width: 450px;

        @media ($phone) {
            height: 325px;
            width: 95%;
        }
    }
    .cl-01 {
        width: 70%;
    }

    .cl-img {
        width: 30%;
    }
    /*.clb-footer {
        .c-date {
            color: #555;
        }
    }*/
}

.accordion-button-custom {
    padding: 1.75rem 1.25rem;
    background-color: $background-button-accordion;

    &:not(.collapsed) {
        background-color: $background-button-accordion;
    }
}

.page-link.active {
    z-index: 1;
    color: $color_white;
    background-color: $background-button-pagination;
    border-color: $border-color-pagination;
}

.active {
    > .page-link {
        z-index: 1;
        color: $color_white;
        background-color: $background-button-pagination;
        border-color: $border-color-pagination;
    }
}

.c-recapcha .bg-recapcha {
    background-color: #f3f3f3;
    border-radius: 10px;
}

.c-recapcha .cr-title * {
    color: #757575
}

.c-recapcha .gray-200 {
    color: #848484
}

.c-recapcha .icon {
    color: #2196f3;
    font-size: 20px;
    font-weight: 900;
    position: relative;
    top: 1px
}

.c-recapcha .border-bottom {
    border-bottom-width: 3px !important
}

.c-banner-mobile {
    position: relative !important;
    z-index: 1 !important;
    p {
        font-family: Roboto-Regular !important;
        text-align: left;
        strong {
            font-family: Roboto-Bold;            
        }
    }
    .d-flex.justify-content-center {
        padding-left: 30px;
    }    
}

input[type="checkbox"] {
    cursor: pointer;
}

.c-nav-md {
    padding-left: 15px;
    padding-right: 15px;
    .fmd-12 {
        @media ($tablet) {
            font-size: 12px;
        }
    }
}


/* start 404 */
.c-404 {
    @media ($desktop), ($tablet) {
        .color-gray {
            color: #696770;
        }
        h1 {
            font-size: 36px;
        }
    }
    @media ($phone) {
        * {
            text-align: center;
        }
        h1 {
            font-size: 24px;
        }
    }
}

.ts-center {    
    @media ($phone) {
        list-style-position: inside;  
    }
}

/* start mensajes */
.c-message-full {
    span {
        color: $text-info-strong;
    }    
    background-color: $bg-info-subtle; 
}
/*respomsive*/
.hide-md-pro {
    @media (min-width:992px) and (max-width:1024px) {
        display: none;
    }
}

.hide-xs {    
    @media ($phone) {
        display: none; 
    }
}

.hide-md {    
    @media ($tablet) {
        display: none; 
    }
}

.hide-lg {    
    @media ($desktop) {
        display: none;
    }
}
/* start mensajes */
.c-message-full {
    span {
        color: $text-info-strong;
    }    
    background-color: $bg-info-subtle; 
}
/*respomsive*/
.hide-md-pro {
    @media (min-width:992px) and (max-width:1024px) {
        display: none;
    }
}

/* modal upsell */
.modal-slide-left .modal-dialog {
    position: fixed;
    top: 0;
    /*right: -100%;*/
    height: 100%;
    width: 100%;
    max-width: none;
    transition: transform 0.9s ease;
    @media (min-width: 768px) {
        padding: 5%;
        .modal-content {
            border-radius: 10px !important;
        }       
    }
}  

  @media (max-width: 767px), (min-width: 768px) and (max-width: 990px) {       
      .modal-no-01 {
          .c-crls {
              display: none;
          }
          .c-scroll {
              display: initial;
          }
          .col.evt-col {
              margin: 0 !important;
              max-width: 100%;
              width: 100% !important;                        
          }                    
      }
  }

  @media (min-width: 991px) and (max-width: 1199px) {       
      .modal-no-01 {
          .c-scroll {
              display: initial !important;
          }
      }
      
      .modal-no-02 {
          .c-crls {
              .cc-right {
                  right: 40%;                    
              }                
          }           
      }
  }


  @media (min-width: 1200px) {        
      .modal-no-01 {
          .c-crls {
              display: none;
          }          
      }
      .modal-no-02 {
          .c-crls {
              display: none;
          }         
      }
  }
  .modal-no-04 {
    .c-crls {
        @media (min-width: 1429px) {
            display: none;            
        }
    }
  }
  .modal-no-03 {
    .c-crls {
        @media (min-width: 1400px) {
            display: none;            
        }
    }
  }


  


  .modal-slide-left .modal-content {
    height: 100%;
    border-radius: 0;
    border: none;
  }
  /*.modal {
      --bs-modal-margin: 0;
  }*/

  .c-scroll {
      display: inline-block;
      white-space: nowrap;
      .cs-int {
          display: flex;
          flex-wrap: nowrap;
          .col { 
              border: 2px solid #ccc;
              border-radius: 8px;
              height: 100%;
              margin-right: 15px;
              overflow: hidden;
              position: relative;
              width: 296px;
              @media (max-width: 767px) {
                  width: 260px;
              }                           
          }
            .col:nth-child(1) {
                .c-rate-style {
                    background-color: #428e1e;
                }
                h4 {
                    color: #428e1e;                    
                }
            }
            .col:nth-child(2) {
                .c-rate-style {
                    background-color: #dc1f2f;
                }
                h4 {
                    color: #dc1f2f;                    
                }
            }
            .col:nth-child(3) {
                .c-rate-style {
                    background-color: #5c469c;
                }
                h4 {
                    color: #5c469c;                    
                }
            }
            .col:nth-child(4) {
                .c-rate-style {
                    background-color: #2193e9;
                }
                h4 {
                    color: #2193e9;                    
                }
            }
            .col:nth-child(5) {
                .c-rate-style {
                    background-color: #0a7e8c;
                }
                border: 2px solid #0a7e8c;
                h4 {
                    color: #0a7e8c;                    
                }
            }
            .col:nth-child(6) {
                .c-rate-style {
                    background-color: #9c7c38;
                }
                border: 2px solid #9c7c38;
                h4 {
                    color: #9c7c38;                    
                }
            }
            .col:last-child {
                margin-right: 0; 
            } 
      }                    
  }
  .c-crls {
      position: absolute;
      height: 1px;
      margin: auto;
      left: 0;
      right: 0;
      bottom: 0;
      top: -60px;
      z-index: 1;
      @media (min-width: 768px) {
          left: 0%;
          right: 0%;
      }
      .cc {
        background: rgba(33, 150, 243, .7);
        color: #fff;
          border-radius: 200px;
          bottom: 0;
          height: 43px;
          top: 0;
          width: 43px;
          &:hover {
            background: rgba(33, 150, 243, .6);
            color: #fff;
              cursor: pointer;
          }
          .icon {
              font-size: 36px;
              top: 4px;
          }
          .icon-chevron-left {
              left: 3px;
          }
          .icon-chevron-right {
              left: 5px;
          }
      }
      .cc-left {
          left: 5px;
      }
      .cc-right {
          right: 5px;
      }
  }   

  .cm-header {
      height: 40px;
      left: 0;
      position: absolute;
      right: 0;
      top: 0;
      .form-check {
          height: 18px;
          width: 18px;
      }        
  }
  .cm-body {
      bottom: 2px;
      left: 0;
      position: absolute;
      right: 0;
      top: 40px;
      p {
          white-space: normal;            
      }
      .icons {
          font-size: 22px;
          position: relative;
          top: 4px;
          width: 22px;
          display: inline-block;
      }
      .icons-check {
          font-size: 30px;
          left: -2px;
          top: 10px;
      }
      .icon-left {
          display: inline-block;
      }        
  }
  .bgu-rate {
      background-color: #F5F5F7;        
      margin-left: -24px;
      margin-right: -24px;
  }
  
  .color-green {
      color: #1C8207;
  }
  .color-disabled {
      color: #94979C;
  }
    .color-disabled-icon {
        color: #94979C;
    }
    .color-disabled-text {
        color: #676B70;
    }

  .col-selected {
      border: 3px solid #2BA612 !important;
      .bgu-rate {
          background-color: #F5F5F7;
      }
      .cb-info {
          p {
              color: #0F4704;                
          }

          h3 {
              color: #1C8207;            
          }
      }
      .bgu-rate {
          background-color: #F2FAF0 !important;
      }        
  }
  .mf-upsell {
      h3 {
          span {
              font-family: "Roboto", sans-serif;
              font-size: 16px;
          }
      }
      .btn {
          @media (max-width: 767px) {
              width: 100%;
          }            
      }
  }

.ps-24 {
    padding-left: 24px; 
}

  .ps-26 {
      padding-left: 26px; 
  }

  .cb-blur {
      &:before {
          background: linear-gradient(0deg, rgba(255,255,255,1) 0%, rgba(255,255,255,0) 100%);
          bottom: 0;
          content: ""; 
          height: 60px;
          left: 0;
          position: absolute;
          right: 0;
          z-index: 1;                       
      }
  }

  /*Styles for tiquetesbaratos */
  .c-upsell-tb {
      .form-check > input:first-child:checked + label::before,
      .form-check > input[type=radio]:first-child:checked + label::after {
          background-color: #00a0e4;
          border-color: #00a0e4;
      }
      .btn-tiquetes {
          background: #00a0e4 !important;
          border: 1px solid #00a0e4 !important;            
      }
      .btn-tiquetes:hover {
          background: #00a0e4 !important;
          border: 1px solid #00a0e4 !important;            
      }        
  }

  
.modal-no-01 {      
    .modal-content {
        @media (min-width: 768px) {
            margin: auto;  
            width: 344px;
            .c-scroll .cs-int .col {
                margin-right: 0;
            }
        }       
                    
    }         
}
.modal-no-02 {      
    .modal-content {
        @media (min-width: 768px) {
            margin: auto;
            width: 655px;         
        }
        .c-crls {
            @media (min-width: 555px) {
                display: none;                
            }            
        }                     
    }        
}
.modal-no-03 {      
    .modal-content {
        @media (min-width: 768px) and (max-width: 1428px) {
            margin: auto;
            width: 76%;
        }
        @media (min-width: 1429px) {
            margin: auto; 
            width: 969px;            
        }             
    }       
}
.modal-no-04 {      
    .modal-content {
        @media (min-width: 768px) and (max-width: 1428px) {
            margin: auto;
            width: 96%;
        }
        @media (min-width: 1429px) {
            margin: auto;
            width: 1278px;            
        }        
    }       
}

/* colores para familias en modals */
.cm-family-01 {
    .c-rate-style {
        background-color: #428e1e;
    }
    .cm-txt {
        color: #428e1e !important;        
    }    
}
.cm-family-02 {
    .c-rate-style {
        background-color: #dc1f2f;
    }
    .cm-txt {
        color: #dc1f2f;        
    }     
}
.cm-family-03 {
    .c-rate-style {
        background-color: #5c469c;
    }
    .cm-txt {
        color: #5c469c;        
    }    
}
.cm-family-04 {
    .c-rate-style {
        background-color: #2193e9;
    }
    .cm-txt {
        color: #2193e9;        
    }     
}
.cm-family-05 {
    .c-rate-style {
        background-color: #0a7e8c;
    }
    .cm-txt {
        color: #0a7e8c;        
    }     
}
.cm-family-06 {
    .c-rate-style {
        background-color: #9c7c38;
    }
    .cm-txt {
        color: #9c7c38;        
    }     
}


.modal-info-family-details{

    .icon-left {
        &:after {
            background-color: #ccc;
            border-radius: 50px;
            content: "";
            height: 6px;
            width: 6px;
            display: block;
            right: -14px;
            position: absolute;
            top: 6px;
        }
    }
    .icon-left.icon-big-bag, .icon-left.icon-carry-on, .icon-left.icon-big-bag-out, .icon-left.icon-carry-on-bag {
        &:after {
            display: none !important;
        }
        &:before {
            color: #444 !important;
        }
    }
    .icon-big-bag-out, .icon-carry-on-bag {
        &:before {
            color: #999 !important;
            margin-right: 3px;
            margin-left: 4px;
        }
    }
    .icon {
        position: absolute !important;
        top: 3px !important;
    }
    .icon.icon-left.ic-in {
        top: -1px !important;
    }
    
    .icon-carry-on {
        left: 3px;
    }
    .icon-big-bag {
        left: 3px;
    }

    .icon.icon-left.ic-in.icon-big-bag {
        left: 4px;
    }

    
}