﻿using Microsoft.AspNetCore.WebUtilities;
using System.Net.Mime;
using System.Text.Json;
using System.Web;
using TBFront.Infrastructure.HttpService.APIB2CService.Dtos;
using TBFront.Interfaces;
using TBFront.Models.HotelFacade.Request;
using TBFront.Models.HotelFacade.Response;
using TBFront.Models.MKTCollection.Request;
using TBFront.Models.MKTCollection.Response;


namespace TBFront.Infrastructure.HttpService.APIB2CService
{
    public class APIB2CService : IAPIB2CService
    {

        private readonly HttpClient _httpClient;
        private readonly APIFrontConfiguration _configuration;
        private readonly static JsonSerializerOptions _jsonSerializerOptions = new() { PropertyNameCaseInsensitive = true };
        private readonly ICacheService _cacheService;
        public APIB2CService(HttpClient httpClient, APIFrontConfiguration configuration, ICacheService cacheService)
        {
            _configuration = configuration;
            _httpClient = httpClient;
            _httpClient.DefaultRequestHeaders.Add("Accept", MediaTypeNames.Application.Json);
            _httpClient.BaseAddress = new Uri(_configuration.Uri);
            _cacheService = cacheService;
        }



        public async Task<MKTCollectionResponse> QueryAsync(MTKCollectionRequest request, CancellationToken ct)
        {
            var key = "collection";
            var response = await _cacheService.RedisGetCache<MKTCollectionResponse>(key, ct); ;

            if (response != null && !request.Cache)
            {
                return response;
            }

            var query = new Dictionary<string, string>()
            {
                ["profileId"] = request.ProfileId,
                ["isProd"] = request.IsProduction ? "true" : "false",
            };

            var uriService = QueryHelpers.AddQueryString($"{_configuration.CollectionPath}/{request.Page}", query);

            var httpResponseMessage = await _httpClient.GetAsync(uriService, ct);

            using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync();
            response = await JsonSerializer.DeserializeAsync<MKTCollectionResponse>(contentStream, _jsonSerializerOptions, ct);

            if (response != null && response.Status && response.Data is not null && response.Data.Sections.Any())
            {
                _cacheService.RedisSetCache(key, response);
            }

            return response;
        }

        public async Task<ContentHotelResponse> QueryAsync(ContentHotelRequest request, CancellationToken ct)
        {
            var hotelKey = string.IsNullOrEmpty(request.HotelUri) ? request.HotelId : request.HotelUri;
            var key = $"HotelContent_{hotelKey}_{request.Culture}";
            var response = await _cacheService.GetCache<ContentHotelResponse>(key, ct);

            if (response == null || request.Cache)
            {
                var query = new Dictionary<string, string>()
                {
                    ["uri"] = request.HotelUri,
                    ["hotelId"] = request.HotelId,
                    ["culture"] = request.Culture,
                    ["organizationId"] = request.OrganizationId.ToString(),
                    ["propertyId"] = request.PropertyId,
                    ["imageProfileId"] = request.ImageProfileId,
                    ["query"] = HttpUtility.UrlEncode(request.Query)
                };

                var uriService = $"{_configuration.HotelDetailPath}";
                uriService = QueryHelpers.AddQueryString(uriService, query);

                var httpResponseMessage = await _httpClient.GetAsync(uriService, ct);

                using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync(ct);

                response = await JsonSerializer.DeserializeAsync<ContentHotelResponse>(contentStream, _jsonSerializerOptions, ct);


                if (response  != null)
                {
                    _cacheService.SetCache(key, response);
                }
            }

            return response;
        }
    }
}
