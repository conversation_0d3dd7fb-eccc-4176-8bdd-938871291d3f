import { getUser<PERSON><PERSON> } from "../helpers/usercom";

export const algoliaClickSuggestion = (item, position, type, algoliaIndex = window.__pt.settings.site.algoliaFlightIndex) => {
    if (item && item.queryID && item.objectID && window.aa) {
        let queryid = item.queryID;
        let objectid = item.objectID;
        let indexName = item.indexName || algoliaIndex || "tb_searchvuelos";

        let userToken = getUserKey();
        if (window.sessionStorage) {
            var algoliaObject = {
                objectIDs: objectid,
                queryID: queryid,
                indexName: indexName,
                userToken,
                position
            };

            sessionStorage.setItem('_key_argo_object_flight_' + type, JSON.stringify(algoliaObject));
        }
        window.aa('clickedObjectIDsAfterSearch', {
            userToken: userToken,
            eventName: 'Buscador Click',
            eventType: 'click',
            index: indexName,
            queryID: queryid,
            objectIDs: [objectid],
            positions: [position],
        });
    }
};


export const algoliaEvent = (eventName, algoliaIndex = window.__pt.settings.site.algoliaFlightIndex) => {
    if (window.sessionStorage) {
        let objectSessionAlgolia = JSON.parse(sessionStorage.getItem("_key_argo_object_flight_to"));
        if (objectSessionAlgolia) {
            if (objectSessionAlgolia.queryID && objectSessionAlgolia.objectIDs && objectSessionAlgolia.indexName) {
                window.aa('clickedObjectIDsAfterSearch', {
                    userToken: objectSessionAlgolia.userToken,
                    index: objectSessionAlgolia.indexName || algoliaIndex,
                    eventName: eventName,
                    eventType: 'conversion',
                    queryID: objectSessionAlgolia.queryID,
                    objectIDs: [objectSessionAlgolia.objectIDs],
                });
            }
        }
    }
};

