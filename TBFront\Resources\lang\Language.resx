<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="author" xml:space="preserve">
    <value />
  </data>
  <data name="consultReservation" xml:space="preserve">
    <value />
  </data>
  <data name="contactUsNumbersTitle" xml:space="preserve">
    <value />
  </data>
  <data name="contactUsTitle" xml:space="preserve">
    <value />
  </data>
  <data name="customer_service" xml:space="preserve">
    <value />
  </data>
  <data name="description_app" xml:space="preserve">
    <value />
  </data>
  <data name="destinationPromotionsTitle" xml:space="preserve">
    <value />
  </data>
  <data name="meta_description_destination" xml:space="preserve">
    <value />
  </data>

  <data name="destinationsPromotionsSubtitle" xml:space="preserve">
    <value />
  </data>
  <data name="destinationsSubtitle" xml:space="preserve">
    <value />
  </data>
  <data name="destinationsTitle" xml:space="preserve">
    <value />
  </data>
  <data name="email" xml:space="preserve">
    <value />
  </data>
  <data name="email_callme_out_time" xml:space="preserve">
    <value />
  </data>
  <data name="email_callme_out_time_2" xml:space="preserve">
    <value />
  </data>
  <data name="email_callme_user_data" xml:space="preserve">
    <value />
  </data>
  <data name="email_contact_description" xml:space="preserve">
    <value />
  </data>
  <data name="email_contact_description_2" xml:space="preserve">
    <value />
  </data>
  <data name="email_contact_footer_description" xml:space="preserve">
    <value />
  </data>
  <data name="email_contact_footer_email" xml:space="preserve">
    <value />
  </data>
  <data name="email_contact_footer_promotions" xml:space="preserve">
    <value />
  </data>
  <data name="email_contact_footer_title" xml:space="preserve">
    <value />
  </data>
  <data name="email_contact_message" xml:space="preserve">
    <value />
  </data>
  <data name="email_contact_name" xml:space="preserve">
    <value />
  </data>
  <data name="email_contact_phone" xml:space="preserve">
    <value />
  </data>
  <data name="email_contact_site" xml:space="preserve">
    <value />
  </data>
  <data name="email_contact_title" xml:space="preserve">
    <value />
  </data>
  <data name="email_groups_adults" xml:space="preserve">
    <value />
  </data>
  <data name="email_groups_car" xml:space="preserve">
    <value />
  </data>
  <data name="email_groups_contact" xml:space="preserve">
    <value />
  </data>
  <data name="email_groups_contact_email" xml:space="preserve">
    <value />
  </data>
  <data name="email_groups_contact_phone" xml:space="preserve">
    <value />
  </data>
  <data name="email_groups_description_1" xml:space="preserve">
    <value />
  </data>
  <data name="email_groups_description_2" xml:space="preserve">
    <value />
  </data>
  <data name="email_groups_description_3" xml:space="preserve">
    <value />
  </data>
  <data name="email_groups_destination" xml:space="preserve">
    <value />
  </data>
  <data name="email_groups_email" xml:space="preserve">
    <value />
  </data>
  <data name="email_groups_group" xml:space="preserve">
    <value />
  </data>
  <data name="email_groups_group_type" xml:space="preserve">
    <value />
  </data>
  <data name="email_groups_hotel" xml:space="preserve">
    <value />
  </data>
  <data name="email_groups_kids" xml:space="preserve">
    <value />
  </data>
  <data name="email_groups_name" xml:space="preserve">
    <value />
  </data>
  <data name="email_groups_origin" xml:space="preserve">
    <value />
  </data>
  <data name="email_groups_outbound_date" xml:space="preserve">
    <value />
  </data>
  <data name="email_groups_reservation_code" xml:space="preserve">
    <value />
  </data>
  <data name="email_groups_returning_date" xml:space="preserve">
    <value />
  </data>
  <data name="email_groups_shuttle" xml:space="preserve">
    <value />
  </data>
  <data name="email_groups_title" xml:space="preserve">
    <value />
  </data>
  <data name="email_groups_titular_name" xml:space="preserve">
    <value />
  </data>
  <data name="expertsService" xml:space="preserve">
    <value />
  </data>
  <data name="expertsServiceLinkTitle" xml:space="preserve">
    <value />
  </data>
  <data name="expertsServiceSubtitle" xml:space="preserve">
    <value />
  </data>
  <data name="findSalesTitle" xml:space="preserve">
    <value />
  </data>
  <data name="frequentQuestionsTitle" xml:space="preserve">
    <value />
  </data>
  <data name="groups" xml:space="preserve">
    <value />
  </data>
  <data name="img" xml:space="preserve">
    <value />
  </data>
  <data name="img_home" xml:space="preserve">
    <value />
  </data>
  <data name="meta-checkin" xml:space="preserve">
    <value />
  </data>
  <data name="meta-groups" xml:space="preserve">
    <value />
  </data>
  <data name="meta-payment-online" xml:space="preserve">
    <value />
  </data>
  <data name="meta-search-reservation" xml:space="preserve">
    <value />
  </data>
  <data name="meta-title-checkout" xml:space="preserve">
    <value />
  </data>
  <data name="meta-title-voucher" xml:space="preserve">
    <value />
  </data>
  <data name="meta-write-us" xml:space="preserve">
    <value />
  </data>
  <data name="meta-faq" xml:space="preserve">
    <value />
  </data>
  <data name="meta-helpcenter_checkin" xml:space="preserve">
    <value />
  </data>
  <data name="meta-helpcenter_search-reservation" xml:space="preserve">
    <value />
  </data>
  <data name="meta-helpcenter_payment-online" xml:space="preserve">
    <value />
  </data>
  <data name="meta-helpcenter_payment-online-data" xml:space="preserve">
    <value />
  </data>
  <data name="meta-helpcenter_groups" xml:space="preserve">
    <value />
  </data>
  <data name="meta-helpcenter_write-us" xml:space="preserve">
    <value />
  </data>
  <data name="meta-helpcenter_faq" xml:space="preserve">
    <value />
  </data>
  <data name="meta_description" xml:space="preserve">
    <value />
  </data>
  <data name="meta_description_airlines" xml:space="preserve">
    <value />
  </data>
  <data name="meta_description_offers" xml:space="preserve">
    <value />
  </data>
  <data name="meta_description_aeromexico" xml:space="preserve">
    <value />
  </data>
  <data name="meta_description_aircanada" xml:space="preserve">
    <value />
  </data>
  <data name="meta_description_airfrance" xml:space="preserve">
    <value />
  </data>
  <data name="meta_description_american" xml:space="preserve">
    <value />
  </data>
  <data name="meta_description_avianca" xml:space="preserve">
    <value />
  </data>
  <data name="meta_description_boletosaereos" xml:space="preserve">
    <value />
  </data>
  <data name="meta_description_clicair" xml:space="preserve">
    <value />
  </data>
  <data name="meta_description_continental" xml:space="preserve">
    <value />
  </data>
  <data name="meta_description_copa" xml:space="preserve">
    <value />
  </data>
  <data name="meta_description_easyfly" xml:space="preserve">
    <value />
  </data>
  <data name="meta_description_iberia" xml:space="preserve">
    <value />
  </data>
  <data name="meta_description_jetblue" xml:space="preserve">
    <value />
  </data>
  <data name="meta_description_jetsmart" xml:space="preserve">
    <value />
  </data>
  <data name="meta_description_lan" xml:space="preserve">
    <value />
  </data>
  <data name="meta_description_latam" xml:space="preserve">
    <value />
  </data>
  <data name="meta_description_ofertavuelos" xml:space="preserve">
    <value />
  </data>
  <data name="meta_description_satena" xml:space="preserve">
    <value />
  </data>
  <data name="meta_description_tiquetesaereos" xml:space="preserve">
    <value />
  </data>
  <data name="meta_description_tiquetesbogota" xml:space="preserve">
    <value />
  </data>
  <data name="meta_description_tiquetesnacionales" xml:space="preserve">
    <value />
  </data>
  <data name="meta_description_tiquetesoferta" xml:space="preserve">
    <value />
  </data>
  <data name="meta_description_united" xml:space="preserve">
    <value />
  </data>
  <data name="meta_description_vivaaerobus" xml:space="preserve">
    <value />
  </data>
  <data name="meta_description_volaris" xml:space="preserve">
    <value />
  </data>
  <data name="meta_description_vuelosenpromocion" xml:space="preserve">
    <value />
  </data>
  <data name="meta_description_wingo" xml:space="preserve">
    <value />
  </data>
  <data name="meta_destination_title" xml:space="preserve">
    <value />
  </data>
  <data name="meta_list_title" xml:space="preserve">
    <value />
  </data>
  <data name="meta_title_aeromexico" xml:space="preserve">
    <value />
  </data>
  <data name="meta_title_aircanada" xml:space="preserve">
    <value />
  </data>
  <data name="meta_title_airfrance" xml:space="preserve">
    <value />
  </data>
  <data name="meta_title_american" xml:space="preserve">
    <value />
  </data>
  <data name="meta_title_avianca" xml:space="preserve">
    <value />
  </data>
  <data name="meta_title_boletosaereos" xml:space="preserve">
    <value />
  </data>
  <data name="meta_title_clicair" xml:space="preserve">
    <value />
  </data>
  <data name="meta_title_continental" xml:space="preserve">
    <value />
  </data>
  <data name="meta_title_copa" xml:space="preserve">
    <value />
  </data>
  <data name="meta_title_home" xml:space="preserve">
    <value />
  </data>
  <data name="meta_title_iberia" xml:space="preserve">
    <value />
  </data>
  <data name="meta_title_jetblue" xml:space="preserve">
    <value />
  </data>
  <data name="meta_title_jetsmart" xml:space="preserve">
    <value />
  </data>
  <data name="meta_title_latam" xml:space="preserve">
    <value />
  </data>
  <data name="meta_title_lan" xml:space="preserve">
    <value />
  </data>
  <data name="meta_title_easyfly" xml:space="preserve">
    <value />
  </data>
  <data name="meta_title_ofertavuelos" xml:space="preserve">
    <value />
  </data>
  <data name="meta_title_promotion_destino" xml:space="preserve">
    <value />
  </data>
  <data name="meta_title_promotion_origen" xml:space="preserve">
    <value />
  </data>
  <data name="meta_description_destino" xml:space="preserve">
    <value />
  </data>
  <data name="meta_description_origen" xml:space="preserve">
    <value />
  </data>
  <data name="meta_title_satena" xml:space="preserve">
    <value />
  </data>
  <data name="meta_title_tiquetesaereos" xml:space="preserve">
    <value />
  </data>
  <data name="meta_title_tiquetesbogota" xml:space="preserve">
    <value />
  </data>
  <data name="meta_title_tiquetesnacionales" xml:space="preserve">
    <value />
  </data>
  <data name="meta_title_tiquetesoferta" xml:space="preserve">
    <value />
  </data>
  <data name="meta_title_united" xml:space="preserve">
    <value />
  </data>
  <data name="meta_title_vivaaerobus" xml:space="preserve">
    <value />
  </data>
  <data name="meta_title_vivacolombia" xml:space="preserve">
    <value />
  </data>
  <data name="meta_title_volaris" xml:space="preserve">
    <value />
  </data>
  <data name="meta_title_vuelosenpromocion" xml:space="preserve">
    <value />
  </data>
  <data name="meta_title_wingo" xml:space="preserve">
    <value />
  </data>
  <data name="methodPayments" xml:space="preserve">
    <value />
  </data>
  <data name="methodPaymentsLinkTitle" xml:space="preserve">
    <value />
  </data>
  <data name="methodPaymentsSubtitle" xml:space="preserve">
    <value />
  </data>
  <data name="newsletterSubtitle" xml:space="preserve">
    <value />
  </data>
  <data name="newsletterTitle" xml:space="preserve">
    <value />
  </data>
  <data name="oneFly" xml:space="preserve">
    <value />
  </data>
  <data name="oneFlyLinkTitle" xml:space="preserve">
    <value />
  </data>
  <data name="oneFlySubtitle" xml:space="preserve">
    <value />
  </data>
  <data name="operating_system" xml:space="preserve">
    <value />
  </data>
  <data name="paymentOnline" xml:space="preserve">
    <value />
  </data>
  <data name="itineraryLink" xml:space="preserve">
    <value />
  </data>
  <data name="promotions" xml:space="preserve">
    <value />
  </data>
  <data name="promotionsTitle" xml:space="preserve">
    <value />
  </data>
  <data name="question0" xml:space="preserve">
    <value />
  </data>
  <data name="question1" xml:space="preserve">
    <value />
  </data>
  <data name="question10" xml:space="preserve">
    <value />
  </data>
  <data name="question11" xml:space="preserve">
    <value />
  </data>
  <data name="question12" xml:space="preserve">
    <value />
  </data>
  <data name="question13" xml:space="preserve">
    <value />
  </data>
  <data name="question14" xml:space="preserve">
    <value />
  </data>
  <data name="question15" xml:space="preserve">
    <value />
  </data>
  <data name="question16" xml:space="preserve">
    <value />
  </data>
  <data name="question17" xml:space="preserve">
    <value />
  </data>
  <data name="question18" xml:space="preserve">
    <value />
  </data>
  <data name="question19" xml:space="preserve">
    <value />
  </data>
  <data name="question2" xml:space="preserve">
    <value />
  </data>
  <data name="question20" xml:space="preserve">
    <value />
  </data>
  <data name="question21" xml:space="preserve">
    <value />
  </data>
  <data name="question22" xml:space="preserve">
    <value />
  </data>
  <data name="question23" xml:space="preserve">
    <value />
  </data>
  <data name="question24" xml:space="preserve">
    <value />
  </data>
  <data name="question25" xml:space="preserve">
    <value />
  </data>
  <data name="question26" xml:space="preserve">
    <value />
  </data>
  <data name="question27" xml:space="preserve">
    <value />
  </data>
  <data name="question28" xml:space="preserve">
    <value />
  </data>
  <data name="question29" xml:space="preserve">
    <value />
  </data>
  <data name="question3" xml:space="preserve">
    <value />
  </data>
  <data name="question30" xml:space="preserve">
    <value />
  </data>
  <data name="question31" xml:space="preserve">
    <value />
  </data>
  <data name="question32" xml:space="preserve">
    <value />
  </data>
  <data name="question33" xml:space="preserve">
    <value />
  </data>
  <data name="question34" xml:space="preserve">
    <value />
  </data>
  <data name="question4" xml:space="preserve">
    <value />
  </data>
  <data name="question5" xml:space="preserve">
    <value />
  </data>
  <data name="question6" xml:space="preserve">
    <value />
  </data>
  <data name="question7" xml:space="preserve">
    <value />
  </data>
  <data name="question8" xml:space="preserve">
    <value />
  </data>
  <data name="question9" xml:space="preserve">
    <value />
  </data>
  <data name="ratingCount" xml:space="preserve">
    <value />
  </data>
  <data name="rating_value" xml:space="preserve">
    <value />
  </data>
  <data name="recentSearchesSubtitle" xml:space="preserve">
    <value />
  </data>
  <data name="recentSearchesTitle" xml:space="preserve">
    <value />
  </data>
  <data name="response0" xml:space="preserve">
    <value />
  </data>
  <data name="response1" xml:space="preserve">
    <value />
  </data>
  <data name="response10" xml:space="preserve">
    <value />
  </data>
  <data name="response11" xml:space="preserve">
    <value />
  </data>
  <data name="response12" xml:space="preserve">
    <value />
  </data>
  <data name="response13" xml:space="preserve">
    <value />
  </data>
  <data name="response14" xml:space="preserve">
    <value />
  </data>
  <data name="response15" xml:space="preserve">
    <value />
  </data>
  <data name="response16" xml:space="preserve">
    <value />
  </data>
  <data name="response17" xml:space="preserve">
    <value />
  </data>
  <data name="response18" xml:space="preserve">
    <value />
  </data>
  <data name="response19" xml:space="preserve">
    <value />
  </data>
  <data name="response2" xml:space="preserve">
    <value />
  </data>
  <data name="response20" xml:space="preserve">
    <value />
  </data>
  <data name="response21" xml:space="preserve">
    <value />
  </data>
  <data name="response22" xml:space="preserve">
    <value />
  </data>
  <data name="response23" xml:space="preserve">
    <value />
  </data>
  <data name="response24" xml:space="preserve">
    <value />
  </data>
  <data name="response25" xml:space="preserve">
    <value />
  </data>
  <data name="response26" xml:space="preserve">
    <value />
  </data>
  <data name="response27" xml:space="preserve">
    <value />
  </data>
  <data name="response28" xml:space="preserve">
    <value />
  </data>
  <data name="response29" xml:space="preserve">
    <value />
  </data>
  <data name="response3" xml:space="preserve">
    <value />
  </data>
  <data name="response30" xml:space="preserve">
    <value />
  </data>
  <data name="response31" xml:space="preserve">
    <value />
  </data>
  <data name="response32" xml:space="preserve">
    <value />
  </data>
  <data name="response33" xml:space="preserve">
    <value />
  </data>
  <data name="response34" xml:space="preserve">
    <value />
  </data>
  <data name="response4" xml:space="preserve">
    <value />
  </data>
  <data name="response5" xml:space="preserve">
    <value />
  </data>
  <data name="response6" xml:space="preserve">
    <value />
  </data>
  <data name="response7" xml:space="preserve">
    <value />
  </data>
  <data name="response8" xml:space="preserve">
    <value />
  </data>
  <data name="response9" xml:space="preserve">
    <value />
  </data>
  <data name="salesSubtitle" xml:space="preserve">
    <value />
  </data>
  <data name="salesSubtitleaeromexico" xml:space="preserve">
    <value />
  </data>
  <data name="salesSubtitleaircanada" xml:space="preserve">
    <value />
  </data>
  <data name="salesSubtitleairfrance" xml:space="preserve">
    <value />
  </data>
  <data name="salesSubtitleamerican" xml:space="preserve">
    <value />
  </data>
  <data name="salesSubtitleavianca" xml:space="preserve">
    <value />
  </data>
  <data name="salesSubtitleboletosaereos" xml:space="preserve">
    <value />
  </data>
  <data name="salesSubtitleclicair" xml:space="preserve">
    <value />
  </data>
  <data name="salesSubtitlecontinental" xml:space="preserve">
    <value />
  </data>
  <data name="salesSubtitlecopa" xml:space="preserve">
    <value />
  </data>
  <data name="salesSubtitleiberia" xml:space="preserve">
    <value />
  </data>
  <data name="salesSubtitlejetblue" xml:space="preserve">
    <value />
  </data>
  <data name="salesSubtitlejetsmart" xml:space="preserve">
    <value />
  </data>
  <data name="salesSubtitlelatam" xml:space="preserve">
    <value />
  </data>
  <data name="salesSubtitleofertavuelos" xml:space="preserve">
    <value />
  </data>
  <data name="salesSubtitlesatena" xml:space="preserve">
    <value />
  </data>
  <data name="salesSubtitletiquetesaereos" xml:space="preserve">
    <value />
  </data>
  <data name="salesSubtitletiquetesbogota" xml:space="preserve">
    <value />
  </data>
  <data name="salesSubtitletiquetesnacionales" xml:space="preserve">
    <value />
  </data>
  <data name="salesSubtitletiquetesoferta" xml:space="preserve">
    <value />
  </data>
  <data name="salesSubtitleunited" xml:space="preserve">
    <value />
  </data>
  <data name="salesSubtitlevivaerobus" xml:space="preserve">
    <value />
  </data>
  <data name="salesSubtitlevolaris" xml:space="preserve">
    <value />
  </data>
  <data name="salesSubtitlevuelosenpromocion" xml:space="preserve">
    <value />
  </data>
  <data name="salesSubtitlewingo" xml:space="preserve">
    <value />
  </data>
  <data name="salesTitle" xml:space="preserve">
    <value />
  </data>
  <data name="salesTitleaeromexico" xml:space="preserve">
    <value />
  </data>
  <data name="salesTitleaircanada" xml:space="preserve">
    <value />
  </data>
  <data name="salesTitleairfrance" xml:space="preserve">
    <value />
  </data>
  <data name="salesTitleamerican" xml:space="preserve">
    <value />
  </data>
  <data name="salesTitleavianca" xml:space="preserve">
    <value />
  </data>
  <data name="salesTitleboletosaereos" xml:space="preserve">
    <value />
  </data>
  <data name="salesTitleclicair" xml:space="preserve">
    <value />
  </data>
  <data name="salesTitlecontinental" xml:space="preserve">
    <value />
  </data>
  <data name="salesTitlecopa" xml:space="preserve">
    <value />
  </data>
  <data name="salesTitleiberia" xml:space="preserve">
    <value />
  </data>
  <data name="salesTitlejetblue" xml:space="preserve">
    <value />
  </data>
  <data name="salesTitlejetsmart" xml:space="preserve">
    <value />
  </data>
  <data name="salesTitlelatam" xml:space="preserve">
    <value />
  </data>
  <data name="salesTitleofertavuelos" xml:space="preserve">
    <value />
  </data>
  <data name="salesTitlesatena" xml:space="preserve">
    <value />
  </data>
  <data name="salesTitletiquetesaereos" xml:space="preserve">
    <value />
  </data>
  <data name="salesTitletiquetesbogota" xml:space="preserve">
    <value />
  </data>
  <data name="salesTitletiquetesnacionales" xml:space="preserve">
    <value />
  </data>
  <data name="salesTitletiquetesoferta" xml:space="preserve">
    <value />
  </data>
  <data name="salesTitleunited" xml:space="preserve">
    <value />
  </data>
  <data name="salesTitlevivaerobus" xml:space="preserve">
    <value />
  </data>
  <data name="salesTitlevolaris" xml:space="preserve">
    <value />
  </data>
  <data name="salesTitlevuelosenpromocion" xml:space="preserve">
    <value />
  </data>
  <data name="salesTitlewingo" xml:space="preserve">
    <value />
  </data>
  <data name="same_as" xml:space="preserve">
    <value />
  </data>
  <data name="sendMessageBy" xml:space="preserve">
    <value />
  </data>
  <data name="separator" xml:space="preserve">
    <value />
  </data>
  <data name="subscriberBtnTile" xml:space="preserve">
    <value />
  </data>
  <data name="subtitleHome" xml:space="preserve">
    <value />
  </data>
  <data name="subtitleHomeaeromexico" xml:space="preserve">
    <value />
  </data>
  <data name="subtitleHomeaircanada" xml:space="preserve">
    <value />
  </data>
  <data name="subtitleHomeairfrance" xml:space="preserve">
    <value />
  </data>
  <data name="subtitleHomeamerican" xml:space="preserve">
    <value />
  </data>
  <data name="subtitleHomeavianca" xml:space="preserve">
    <value />
  </data>
  <data name="subtitleHomeboletosaereos" xml:space="preserve">
    <value />
  </data>
  <data name="subtitleHomeclicair" xml:space="preserve">
    <value />
  </data>
  <data name="subtitleHomecontinental" xml:space="preserve">
    <value />
  </data>
  <data name="subtitleHomecopa" xml:space="preserve">
    <value />
  </data>
  <data name="subtitleHomeiberia" xml:space="preserve">
    <value />
  </data>
  <data name="subtitleHomejetblue" xml:space="preserve">
    <value />
  </data>
  <data name="subtitleHomejetsmart" xml:space="preserve">
    <value />
  </data>
  <data name="subtitleHomelatam" xml:space="preserve">
    <value />
  </data>
  <data name="subtitleHomeofertavuelos" xml:space="preserve">
    <value />
  </data>
  <data name="subtitleHomesatena" xml:space="preserve">
    <value />
  </data>
  <data name="subtitleHometiquetesaereos" xml:space="preserve">
    <value />
  </data>
  <data name="subtitleHometiquetesbogota" xml:space="preserve">
    <value />
  </data>
  <data name="subtitleHometiquetesnacionales" xml:space="preserve">
    <value />
  </data>
  <data name="subtitleHometiquetesoferta" xml:space="preserve">
    <value />
  </data>
  <data name="subtitleHomeunited" xml:space="preserve">
    <value />
  </data>
  <data name="subtitleHomevivaerobus" xml:space="preserve">
    <value />
  </data>
  <data name="subtitleHomevolaris" xml:space="preserve">
    <value />
  </data>
  <data name="subtitleHomevuelosenpromocion" xml:space="preserve">
    <value />
  </data>
  <data name="subtitleHomewingo" xml:space="preserve">
    <value />
  </data>
  <data name="talkAsessorSubtitle" xml:space="preserve">
    <value />
  </data>
  <data name="talkAsessorTitle" xml:space="preserve">
    <value />
  </data>
  <data name="PriceTravel" xml:space="preserve">
    <value />
  </data>
  <data name="PriceTravelaeromexico" xml:space="preserve">
    <value />
  </data>
  <data name="PriceTravelaircanada" xml:space="preserve">
    <value />
  </data>
  <data name="PriceTravelairfrance" xml:space="preserve">
    <value />
  </data>
  <data name="PriceTravelamerican" xml:space="preserve">
    <value />
  </data>
  <data name="PriceTravelavianca" xml:space="preserve">
    <value />
  </data>
  <data name="PriceTravelboletosaereos" xml:space="preserve">
    <value />
  </data>
  <data name="PriceTravelclicair" xml:space="preserve">
    <value />
  </data>
  <data name="PriceTravelcontinental" xml:space="preserve">
    <value />
  </data>
  <data name="PriceTravelcopa" xml:space="preserve">
    <value />
  </data>
  <data name="PriceTraveliberia" xml:space="preserve">
    <value />
  </data>
  <data name="PriceTraveljetblue" xml:space="preserve">
    <value />
  </data>
  <data name="PriceTraveljetsmart" xml:space="preserve">
    <value />
  </data>
  <data name="PriceTravellatam" xml:space="preserve">
    <value />
  </data>
  <data name="PriceTravelofertavuelos" xml:space="preserve">
    <value />
  </data>
  <data name="PriceTravelsatena" xml:space="preserve">
    <value />
  </data>
  <data name="PriceTraveltiquetesaereos" xml:space="preserve">
    <value />
  </data>
  <data name="PriceTraveltiquetesbogota" xml:space="preserve">
    <value />
  </data>
  <data name="PriceTraveltiquetesnacionales" xml:space="preserve">
    <value />
  </data>
  <data name="PriceTraveltiquetesoferta" xml:space="preserve">
    <value />
  </data>
  <data name="PriceTravelunited" xml:space="preserve">
    <value />
  </data>
  <data name="PriceTravelvivaaerobus" xml:space="preserve">
    <value />
  </data>
  <data name="PriceTravelvolaris" xml:space="preserve">
    <value />
  </data>
  <data name="PriceTravelvuelosenpromocion" xml:space="preserve">
    <value />
  </data>
  <data name="PriceTravelwingo" xml:space="preserve">
    <value />
  </data>
  <data name="webCheckIn" xml:space="preserve">
    <value />
  </data>
  <data name="writeUsMessageSubtitle" xml:space="preserve">
    <value />
  </data>
  <data name="writeUsMessageTitle" xml:space="preserve">
    <value />
  </data>
  <data name="writeUsTitle" xml:space="preserve">
    <value />
  </data>
  <data name="textCheckin" xml:space="preserve">
    <value />
  </data>
  <data name="subtitleHomelan" xml:space="preserve">
    <value />
  </data>
  <data name="subtitleHomevivaaerobus" xml:space="preserve">
    <value />
  </data>
  <data name="subtitleHomevivacolombia" xml:space="preserve">
    <value />
  </data>
  <data name="PriceTravellan" xml:space="preserve">
    <value />
  </data>
  <data name="PriceTravelvivacolombia" xml:space="preserve">
    <value />
  </data>
  <data name="footerCheckoutAddress" xml:space="preserve">
    <value />
  </data>
  <data name="footerCheckoutRights" xml:space="preserve">
    <value />
  </data>
  <data name="footerCheckoutSubtitle" xml:space="preserve">
    <value />
  </data>
  <data name="footerCheckoutText" xml:space="preserve">
    <value />
  </data>
  <data name="mailCreateBookingIdBooking" xml:space="preserve">
    <value />
  </data>
  <data name="mailCreateBookingNames" xml:space="preserve">
    <value />
  </data>
  <data name="mailCreateBookingMail" xml:space="preserve">
    <value />
  </data>
  <data name="mailCreateBookingPhoneHome" xml:space="preserve">
    <value />
  </data>
  <data name="mailCreateBookingPhone" xml:space="preserve">
    <value />
  </data>
  <data name="mailCreateBookingImportant" xml:space="preserve">
    <value />
  </data>
  <data name="mailCreateBookingMessageConfirm" xml:space="preserve">
    <value />
  </data>
  <data name="mailCreateBookingFlightStart" xml:space="preserve">
    <value />
  </data>
  <data name="mailCreateBookingDepurate" xml:space="preserve">
    <value />
  </data>
  <data name="mailCreateBookingDate" xml:space="preserve">
    <value />
  </data>
  <data name="mailCreateBookingTime" xml:space="preserve">
    <value />
  </data>
  <data name="mailCreateBookingTerminal" xml:space="preserve">
    <value />
  </data>
	<data name="mailCreateBookingAirport" xml:space="preserve">
    <value />
  </data>
  <data name="mailCreateBookingFlightNumber" xml:space="preserve">
    <value />
  </data>
  <data name="mailCreateBookingArrival" xml:space="preserve">
    <value />
  </data>
  <data name="mailCreateBookingFlightReturn" xml:space="preserve">
    <value />
  </data>
  <data name="mailCreateBookingTotal" xml:space="preserve">
    <value />
  </data>
  <data name="mailCreateBookingQuestions" xml:space="preserve">
    <value />
  </data>
  <data name="mailCreateBookingAttention" xml:space="preserve">
    <value />
  </data>
  <data name="mailCreateBookingImportantMgs" xml:space="preserve">
    <value />
  </data>
  <data name="mailCreateBookingPolicyTitle" xml:space="preserve">
    <value />
  </data>
  <data name="mailCreateBookingPolicyDetail" xml:space="preserve">
    <value />
  </data>
  <data name="mailCreateBookingAditionalTitle" xml:space="preserve">
    <value />
  </data>
  <data name="mailCreateBookingAditionalDetail" xml:space="preserve">
    <value />
  </data>
  <data name="meta_description_delta" xml:space="preserve">
    <value />
  </data>
  <data name="meta_title_delta" xml:space="preserve">
    <value />
  </data>
  <data name="salesTitledelta" xml:space="preserve">
    <value />
  </data>
  <data name="subtitleHomedelta" xml:space="preserve">
    <value />
  </data>
  <data name="PriceTraveldelta" xml:space="preserve">
    <value />
  </data>
  <data name="flights" xml:space="preserve">
    <value />
  </data>
  <data name="about_us" xml:space="preserve">
    <value />
  </data>
  <data name="cancel_booking" xml:space="preserve">
    <value />
  </data>
  <data name="check_reservation" xml:space="preserve">
    <value />
  </data>
  <data name="contact_header_pt" xml:space="preserve">
    <value />
  </data>
  <data name="contact_us" xml:space="preserve">
    <value />
  </data>
  <data name="customer_services" xml:space="preserve">
    <value />
  </data>
  <data name="error_flight" xml:space="preserve">
    <value />
  </data>
  <data name="error_hotel" xml:space="preserve">
    <value />
  </data>
  <data name="error_internal_error" xml:space="preserve">
    <value />
  </data>
  <data name="error_main_page" xml:space="preserve">
    <value />
  </data>
  <data name="error_not_found" xml:space="preserve">
    <value />
  </data>
  <data name="error_offer" xml:space="preserve">
    <value />
  </data>
  <data name="error_package" xml:space="preserve">
    <value />
  </data>
  <data name="error_sections" xml:space="preserve">
    <value />
  </data>
  <data name="flight" xml:space="preserve">
    <value />
  </data>
  <data name="flights_to_from" xml:space="preserve">
    <value />
  </data>
  <data name="flights_to" xml:space="preserve">
    <value />
  </data>
  <data name="footer_legal" xml:space="preserve">
    <value />
  </data>
  <data name="forgot_password" xml:space="preserve">
    <value />
  </data>
  <data name="general_error" xml:space="preserve">
    <value />
  </data>
  <data name="get_booking" xml:space="preserve">
    <value />
  </data>
  <data name="help" xml:space="preserve">
    <value />
  </data>
  <data name="destinations" xml:space="preserve">
    <value />
  </data>
  <data name="url_destination" xml:space="preserve">
    <value />
  </data>
  <data name="link_pdv" xml:space="preserve">
    <value />
  </data>
  <data name="high_standard" xml:space="preserve">
    <value />
  </data>
  <data name="hotel" xml:space="preserve">
    <value />
  </data>
  <data name="hotel_register" xml:space="preserve">
    <value />
  </data>
  <data name="invoices" xml:space="preserve">
    <value />
  </data>
  <data name="legals_login_one" xml:space="preserve">
    <value />
  </data>
  <data name="legals_login_three" xml:space="preserve">
    <value />
  </data>
  <data name="legals_login_two" xml:space="preserve">
    <value />
  </data>
  <data name="links_co_country" xml:space="preserve">
    <value />
  </data>
  <data name="links_mx_country" xml:space="preserve">
    <value />
  </data>
  <data name="links_oc_country" xml:space="preserve">
    <value />
  </data>
  <data name="link_covid" xml:space="preserve">
    <value />
  </data>
  <data name="mail_contact" xml:space="preserve">
    <value />
  </data>
  <data name="my_account" xml:space="preserve">
    <value />
  </data>
  <data name="or_back_to" xml:space="preserve">
    <value />
  </data>
  <data name="our_history" xml:space="preserve">
    <value />
  </data>
  <data name="packages" xml:space="preserve">
    <value />
  </data>
  <data name="phone_phone_whatsapp" xml:space="preserve">
    <value />
  </data>
  <data name="press_room" xml:space="preserve">
    <value />
  </data>
  <data name="pricetravel_magazine" xml:space="preserve">
    <value />
  </data>
  <data name="privacy_terms" xml:space="preserve">
    <value />
  </data>
  <data name="providers" xml:space="preserve">
    <value />
  </data>
  <data name="sic_resolution" xml:space="preserve">
    <value />
  </data>
  <data name="terms_and_coditions" xml:space="preserve">
    <value />
  </data>
  <data name="terms_conditions" xml:space="preserve">
    <value />
  </data>
  <data name="travel_agency" xml:space="preserve">
    <value />
  </data>
  <data name="update_booking" xml:space="preserve">
    <value />
  </data>
  <data name="url_flights" xml:space="preserve">
    <value />
  </data>
  <data name="url_hotels" xml:space="preserve">
    <value />
  </data>
  <data name="url_offers" xml:space="preserve">
    <value />
  </data>
  <data name="url_packages" xml:space="preserve">
    <value />
  </data>
  <data name="url_privacy" xml:space="preserve">
    <value />
  </data>
  <data name="url_sic" xml:space="preserve">
    <value />
  </data>
  <data name="url_terms" xml:space="preserve">
    <value />
  </data>
  <data name="use_other_login_methods" xml:space="preserve">
    <value />
  </data>
  <data name="hi" xml:space="preserve">
    <value />
  </data>
  <data name="log_out" xml:space="preserve">
    <value />
  </data>
  <data name="for_reservations_call" xml:space="preserve">
    <value />
  </data>
  <data name="path_seo_airlines" xml:space="preserve">
    <value />
  </data>
  <data name="path_seo_flight_from" xml:space="preserve">
    <value />
  </data>
  <data name="path_seo_flight_to" xml:space="preserve">
    <value />
  </data>
  <data name="email_subject_confirmation" xml:space="preserve">
    <value />
  </data>
  <data name="oneway" xml:space="preserve">
    <value />
  </data>
  <data name="people" xml:space="preserve">
    <value />
  </data>
  <data name="person" xml:space="preserve">
    <value />
  </data>
  <data name="roundtrip" xml:space="preserve">
    <value />
  </data>
  <data name="to" xml:space="preserve">
    <value />
  </data>
  <data name="mailCreateBookingImportantListOne" xml:space="preserve">
    <value />
  </data>
  <data name="mailCreateBookingImportantListThree" xml:space="preserve">
    <value />
  </data>
  <data name="mailCreateBookingImportantListTwo" xml:space="preserve">
    <value />
  </data>
  <data name="mailCreateBookingImportantListFour" xml:space="preserve">
    <value />
  </data>
  <data name="sign_in" xml:space="preserve">
    <value />
  </data>
  <data name="save_up_to_10_percent" xml:space="preserve">
    <value />
  </data>
  <data name="alternate_path_flights" xml:space="preserve">
    <value />
  </data>
  <data name="alternate_path_generic" xml:space="preserve">
    <value />
  </data>
  <data name="flights_from" xml:space="preserve">
    <value />
  </data>
  <data name="create_account" xml:space="preserve">
    <value />
  </data>
  <data name="favorites" xml:space="preserve">
    <value />
  </data>
  <data name="my_trips" xml:space="preserve">
    <value />
  </data>
  <data name="to_reserve" xml:space="preserve">
    <value />
  </data>
  <data name="to_reserve_mobile" xml:space="preserve">
    <value />
  </data>
  <data name="hotels" xml:space="preserve">
    <value>Hoteles</value>
  </data>
  <data name="followUsTitle" xml:space="preserve">
    <value>Follow us on our social media!</value>
  </data>
  <data name="featuredAirlinesTitle" xml:space="preserve">
    <value>Featured Airlines</value>
  </data>
  <data name="tiquetesBaratos" xml:space="preserve">
    <value />
  </data>
  <data name="tiquetesBaratosaeromexico" xml:space="preserve">
    <value />
  </data>
  <data name="tiquetesBaratosaircanada" xml:space="preserve">
    <value />
  </data>
  <data name="tiquetesBaratosairfrance" xml:space="preserve">
    <value />
  </data>
  <data name="tiquetesBaratosamerican" xml:space="preserve">
    <value />
  </data>
  <data name="tiquetesBaratosavianca" xml:space="preserve">
    <value />
  </data>
  <data name="tiquetesBaratosboletosaereos" xml:space="preserve">
    <value />
  </data>
  <data name="tiquetesBaratosclicair" xml:space="preserve">
    <value />
  </data>
  <data name="tiquetesBaratoscontinental" xml:space="preserve">
    <value />
  </data>
  <data name="tiquetesBaratoscopa" xml:space="preserve">
    <value />
  </data>
  <data name="tiquetesBaratosiberia" xml:space="preserve">
    <value />
  </data>
  <data name="tiquetesBaratosjetblue" xml:space="preserve">
    <value />
  </data>
  <data name="tiquetesBaratosjetsmart" xml:space="preserve">
    <value />
  </data>
  <data name="tiquetesBaratoslatam" xml:space="preserve">
    <value />
  </data>
  <data name="tiquetesBaratosofertavuelos" xml:space="preserve">
    <value />
  </data>
  <data name="tiquetesBaratossatena" xml:space="preserve">
    <value />
  </data>
  <data name="tiquetesBaratostiquetesaereos" xml:space="preserve">
    <value />
  </data>
  <data name="tiquetesBaratostiquetesbogota" xml:space="preserve">
    <value />
  </data>
  <data name="tiquetesBaratostiquetesnacionales" xml:space="preserve">
    <value />
  </data>
  <data name="tiquetesBaratostiquetesoferta" xml:space="preserve">
    <value />
  </data>
  <data name="tiquetesBaratosunited" xml:space="preserve">
    <value />
  </data>
  <data name="tiquetesBaratosvivaaerobus" xml:space="preserve">
    <value />
  </data>
  <data name="tiquetesBaratosvolaris" xml:space="preserve">
    <value />
  </data>
  <data name="tiquetesBaratosvuelosenpromocion" xml:space="preserve">
    <value />
  </data>
  <data name="tiquetesBaratoswingo" xml:space="preserve">
    <value />
  </data>
  <data name="tiquetesBaratoslan" xml:space="preserve">
    <value />
  </data>
  <data name="tiquetesBaratosvivacolombia" xml:space="preserve">
    <value />
  </data>
  <data name="tiquetesBaratosdelta" xml:space="preserve">
    <value />
  </data>
  <data name="descripcion_error_not_found" xml:space="preserve">
    <value />
  </data>
  <data name="error-interno" xml:space="preserve">
    <value />
  </data>
  <data name="descripcion-error-interno" xml:space="preserve">
    <value />
  </data>
  <data name="regresar-a-la-pagina-principal" xml:space="preserve">
    <value />
  </data>
  <data name="puedes-intentar-lo-siguiente" xml:space="preserve">
    <value />
  </data>
  <data name="reiniciar-la-pagina" xml:space="preserve">
    <value />
  </data>
  <data name="acceder-nuevamente-mas-tarde" xml:space="preserve">
    <value />
  </data>
  <data name="vive-tu-proxima-experiencia-de-viaje-con-nosotros" xml:space="preserve">
    <value />
  </data>
  <data name="busqueda-de-vuelos" xml:space="preserve">
    <value />
  </data>
  <data name="busqueda-de-hoteles" xml:space="preserve">
    <value />
  </data>
  <data name="ver-paquetes" xml:space="preserve">
    <value />
  </data>
  <data name="ver-promociones" xml:space="preserve">
    <value />
  </data>
  <data name="para-reservar" xml:space="preserve">
    <value />
  </data>
  <data name="reserva-por-teléfono" xml:space="preserve">
    <value />
  </data>
  <data name="ahorrar_hasta" xml:space="preserve">
    <value />
  </data>
  <data name="llamar-a-un-asesor" xml:space="preserve">
    <value />
  </data>
  <data name="sericio_cliente" xml:space="preserve">
    <value />
  </data>
  <data name="ayuda" xml:space="preserve">
    <value />
  </data>
  <data name="preguntas-frecuentes" xml:space="preserve">
    <value />
  </data>
  <data name="whatsapp" xml:space="preserve">
    <value />
  </data>
  <data name="messenger" xml:space="preserve">
    <value />
  </data>
  <data name="mis-viajes" xml:space="preserve">
    <value />
  </data>
  <data name="mis-favoritos" xml:space="preserve">
    <value />
  </data>
  <data name="facturacion" xml:space="preserve">
    <value />
  </data>
  <data name="hoteles" xml:space="preserve">
    <value />
  </data>
  <data name="disney" xml:space="preserve">
    <value />
  </data>
  <data name="ofertas" xml:space="preserve">
    <value />
  </data>
  <data name="viajes-en-grupo" xml:space="preserve">
    <value />
  </data>
  <data name="favoritos" xml:space="preserve">
    <value />
  </data>
  <data name="legals" xml:space="preserve">
    <value />
  </data>
  <data name="sign_in_2" xml:space="preserve">
    <value />
  </data>
  <data name="links_us_simple_country" xml:space="preserve">
    <value />
  </data>
  <data name="hotel + vuelo" xml:space="preserve">
    <value />
  </data>
  <data name="secure_payments" xml:space="preserve">
    <value />
  </data>
  <data name="check_methods_pay" xml:space="preserve">
    <value />
  </data>
  <data name="payment_option_depend" xml:space="preserve">
    <value />
  </data>
  <data name="most_view" xml:space="preserve">
    <value />
  </data>
  <data name="bogota_tickets" xml:space="preserve">
    <value />
  </data>
  <data name="air_tickets" xml:space="preserve">
    <value />
  </data>
  <data name="fligth_offer" xml:space="preserve">
    <value />
  </data>
  <data name="national_tickets" xml:space="preserve">
    <value />
  </data>
  <data name="offer_tickets" xml:space="preserve">
    <value />
  </data>
  <data name="air_tickets_footer" xml:space="preserve">
    <value />
  </data>
  <data name="about" xml:space="preserve">
    <value />
  </data>
  <data name="who_are" xml:space="preserve">
    <value />
  </data>
  <data name="about_anato" xml:space="preserve">
    <value />
  </data>
  <data name="privacy_policy" xml:space="preserve">
    <value />
  </data>
  <data name="secure_purchase" xml:space="preserve">
    <value />
  </data>
  <data name="superintendence" xml:space="preserve">
    <value />
  </data>
  <data name="transportation_intendance" xml:space="preserve">
    <value />
  </data>
  <data name="aerocivil" xml:space="preserve">
    <value />
  </data>
  <data name="aginst_child" xml:space="preserve">
    <value />
  </data>
  <data name="rights_passenger" xml:space="preserve">
    <value />
  </data>
  <data name="withdrawal" xml:space="preserve">
    <value />
  </data>
  <data name="sustainability_policy" xml:space="preserve">
    <value />
  </data>
  <data name="colombian_air" xml:space="preserve">
    <value />
  </data>
  <data name="resources" xml:space="preserve">
    <value />
  </data>
  <data name="companies_tb" xml:space="preserve">
    <value />
  </data>
  <data name="register_hotel" xml:space="preserve">
    <value />
  </data>
  <data name="promotions_destination" xml:space="preserve">
    <value />
  </data>
  <data name="hotels_packages_offers" xml:space="preserve">
    <value />
  </data>
  <data name="bog_colombia" xml:space="preserve">
    <value />
  </data>
  <data name="telephones" xml:space="preserve">
    <value />
  </data>
  <data name="all_rights" xml:space="preserve">
    <value />
  </data>
  <data name="footer_subtitle" xml:space="preserve">
    <value />
  </data>
  <data name="question35" xml:space="preserve">
    <value />
  </data>
  <data name="response35" xml:space="preserve">
    <value />
  </data>
  <data name="question36" xml:space="preserve">
    <value />
  </data>
  <data name="response36" xml:space="preserve">
    <value />
  </data>
  <data name="question37" xml:space="preserve">
    <value />
  </data>
  <data name="response37" xml:space="preserve">
    <value />
  </data>
  <data name="question38" xml:space="preserve">
    <value />
  </data>
  <data name="response38" xml:space="preserve">
    <value />
  </data>
  <data name="question39" xml:space="preserve">
    <value />
  </data>
  <data name="response39" xml:space="preserve">
    <value />
  </data>
  <data name="question40" xml:space="preserve">
    <value />
  </data>
  <data name="response40" xml:space="preserve">
    <value />
  </data>
  <data name="question41" xml:space="preserve">
    <value />
  </data>
  <data name="response41" xml:space="preserve">
    <value />
  </data>
  <data name="question42" xml:space="preserve">
    <value />
  </data>
  <data name="response42" xml:space="preserve">
    <value />
  </data>
  <data name="question43" xml:space="preserve">
    <value />
  </data>
  <data name="response43" xml:space="preserve">
    <value />
  </data>
  <data name="question44" xml:space="preserve">
    <value />
  </data>
  <data name="response44" xml:space="preserve">
    <value />
  </data>
  <data name="question45" xml:space="preserve">
    <value />
  </data>
  <data name="response45" xml:space="preserve">
    <value />
  </data>
  <data name="question46" xml:space="preserve">
    <value />
  </data>
  <data name="response46" xml:space="preserve">
    <value />
  </data>
  <data name="question47" xml:space="preserve">
    <value />
  </data>
  <data name="response47" xml:space="preserve">
    <value />
  </data>
  <data name="question48" xml:space="preserve">
    <value />
  </data>
  <data name="response48" xml:space="preserve">
    <value />
  </data>
  <data name="question49" xml:space="preserve">
    <value />
  </data>
  <data name="response49" xml:space="preserve">
    <value />
  </data>
  <data name="question50" xml:space="preserve">
    <value />
  </data>
  <data name="response50" xml:space="preserve">
    <value />
  </data>
  <data name="question51" xml:space="preserve">
    <value />
  </data>
  <data name="response51" xml:space="preserve">
    <value />
  </data>
  <data name="question52" xml:space="preserve">
    <value />
  </data>
  <data name="response52" xml:space="preserve">
    <value />
  </data>
  <data name="question53" xml:space="preserve">
    <value />
  </data>
  <data name="response53" xml:space="preserve">
    <value />
  </data>
  <data name="question54" xml:space="preserve">
    <value />
  </data>
  <data name="response54" xml:space="preserve">
    <value />
  </data>
  <data name="question55" xml:space="preserve">
    <value />
  </data>
  <data name="response55" xml:space="preserve">
    <value />
  </data>
  <data name="question56" xml:space="preserve">
    <value />
  </data>
  <data name="response56" xml:space="preserve">
    <value />
  </data>
  <data name="question57" xml:space="preserve">
    <value />
  </data>
  <data name="response57" xml:space="preserve">
    <value />
  </data>
  <data name="question58" xml:space="preserve">
    <value />
  </data>
  <data name="response58" xml:space="preserve">
    <value />
  </data>
  <data name="question59" xml:space="preserve">
    <value />
  </data>
  <data name="response59" xml:space="preserve">
    <value />
  </data>
  <data name="question60" xml:space="preserve">
    <value />
  </data>
  <data name="response60" xml:space="preserve">
    <value />
  </data>
  <data name="question61" xml:space="preserve">
    <value />
  </data>
  <data name="response61" xml:space="preserve">
    <value />
  </data>
  <data name="question62" xml:space="preserve">
    <value />
  </data>
  <data name="response62" xml:space="preserve">
    <value />
  </data>
  <data name="question63" xml:space="preserve">
    <value />
  </data>
  <data name="response63" xml:space="preserve">
    <value />
  </data>
  <data name="question64" xml:space="preserve">
    <value />
  </data>
  <data name="response64" xml:space="preserve">
    <value />
  </data>
  <data name="question65" xml:space="preserve">
    <value />
  </data>
  <data name="response65" xml:space="preserve">
    <value />
  </data>
  <data name="question66" xml:space="preserve">
    <value />
  </data>
  <data name="response66" xml:space="preserve">
    <value />
  </data>
  <data name="question67" xml:space="preserve">
    <value />
  </data>
  <data name="response67" xml:space="preserve">
    <value />
  </data>
  <data name="question68" xml:space="preserve">
    <value />
  </data>
  <data name="response68" xml:space="preserve">
    <value />
  </data>
  <data name="question69" xml:space="preserve">
    <value />
  </data>
  <data name="response69" xml:space="preserve">
    <value />
  </data>
  <data name="question70" xml:space="preserve">
    <value />
  </data>
  <data name="response70" xml:space="preserve">
    <value />
  </data>
  <data name="question71" xml:space="preserve">
    <value />
  </data>
  <data name="response71" xml:space="preserve">
    <value />
  </data>
  <data name="question72" xml:space="preserve">
    <value />
  </data>
  <data name="response72" xml:space="preserve">
    <value />
  </data>
  <data name="meta-check-reservation" xml:space="preserve">
    <value />
  </data>
   <data name="alternate_path_a_airlines" xml:space="preserve">
    <value>{0}/airlines/{1}</value>
  </data>
      <data name="alternate_path_a_flights" xml:space="preserve">
    <value>{0}/flights/airlines/{1}</value>
  </data>
     <data name="alternate_path_d_tiquetes" xml:space="preserve">
    <value>{0}/tiquetes/{1}</value>
  </data>
      <data name="alternate_path_d_flights" xml:space="preserve">
    <value>{0}/flights/tiquetes/{1}</value>
  </data>
    <data name="alternate_flights" xml:space="preserve">
    <value>flights</value>
  </data>
      <data name="to_reservate" xml:space="preserve">
    <value>Para reservar</value>
  </data>
   <data name="session_login_session" xml:space="preserve">
    <value>Iniciar sesión</value>
  </data>
    <data name="login_and_get" xml:space="preserve">
    <value>Inicia sesión y obtén hasta un</value>
  </data>
  <data name="10_percent_dsc" xml:space="preserve">
    <value>10% de descuento en hoteles</value>
  </data>
  <data name="on_your_next_trip" xml:space="preserve">
    <value>en tu próximo viaje.</value>
  </data>
  <data name="trip_group" xml:space="preserve">
    <value>Viajes en grupo</value>
  </data>
    <data name="start_session_or_create" xml:space="preserve">
    <value>Inicia sesión o crea una cuenta</value>
  </data>
    <data name="email_welcome_user" xml:space="preserve">
    <value>¡Te damos la bienvenida a {0}!</value>
  </data>
    <data name="reserve_by_phone" xml:space="preserve">
    <value>Reserva por teléfono</value>
  </data>
    <data name="account" xml:space="preserve">
    <value>Cuenta</value>
  </data>
    <data name="trips_account" xml:space="preserve">
    <value>Viajes y cuenta</value>
  </data>
    <data name="to_reservate" xml:space="preserve">
    <value>For booking</value>
  </data>
    <data name="session_login_session" xml:space="preserve">
    <value>Sign in</value>
  </data>
    <data name="login_and_get" xml:space="preserve">
    <value>Log in and get up to</value>
  </data>
  <data name="10_percent_dsc" xml:space="preserve">
    <value>10% off on hotels</value>
  </data>
  <data name="on_your_next_trip" xml:space="preserve">
    <value>on your next trip.</value>
  </data>
  <data name="trip_group" xml:space="preserve">
    <value>Group trips</value>
  </data>
  
    <data name="web_check_in" xml:space="preserve">
    <value>Web Check-in</value>
  </data>
    <data name="payment_online" xml:space="preserve">
    <value>Pago en línea</value>
  </data>
      <data name="header_tb_fact" xml:space="preserve">
    <value>Facturación</value>
  </data>
      <data name="write_to_us" xml:space="preserve">
    <value>Write to us</value>
  </data>
    <data name="faq" xml:space="preserve">
    <value>Frequently Asked Questions</value>
  </data>
    <data name="call_advisor" xml:space="preserve">
    <value>Llamar a un asesor</value>
  </data>
   <data name="seo_question" xml:space="preserve">
    <value>Preguntas frecuentes {0}</value>
  </data>
  <data name="seo_question_info" xml:space="preserve">
    <value>sobre</value>
  </data>
    <data name="seo_info" xml:space="preserve">
    <value> Acerca de {0}</value>
  </data>
</root>