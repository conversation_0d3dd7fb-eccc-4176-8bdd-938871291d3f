﻿
using TBFront.Application.Mappers;
using TBFront.Interfaces;
using TBFront.Models.Datadog;

namespace TBFront.Application.Implementations
{
    public class DataDogHandler : IDataDogHandler
    {
        private readonly IDataDogService _dataDogService;
        public DataDogHandler(IDataDogService dataDogService)
        {
            _dataDogService = dataDogService;

        }

        public Task<DataDogResponse> QueryAsync(DataDogRequest request, CancellationToken ct)
        {
            var dataDogRequest = DataDogMapper.Error(request);
            return _dataDogService.QueryAsync(dataDogRequest, ct);
        }
    }
}
