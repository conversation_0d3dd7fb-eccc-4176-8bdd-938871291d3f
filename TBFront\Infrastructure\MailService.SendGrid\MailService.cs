﻿using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Extensions.Options;
using TBFront.Models.Request;
using TBFront.Models.Response;
using TBFront.Options;
using System.Text.Json;
using SendGrid.Helpers.Mail;
using SendGrid;
using System.Net;
using TBFront.Models.Login;
using TBFront.Helpers;

namespace TBFront.Infrastructure.MailService.SendGrid
{
	public class MailService
	{
		private readonly IHttpClientFactory _clientFactory;
		private readonly SettingsOptions _options;
		private readonly ViewToStringRenderer _razorViewRenderer;

		public MailService(IHttpClientFactory clientFactory, IOptions<SettingsOptions> options, ViewToStringRenderer razorViewRenderer)
		{
			_clientFactory = clientFactory;
			_razorViewRenderer = razorViewRenderer;
			_options = options.Value;
		}
		public async Task<MailResponse> QueryAsync(Dictionary<string, string> query, string mailType)
		{

			var uriService = $"{_options.MailUrl}{mailType}";

			var httpClient = _clientFactory.CreateClient();

			var uri = QueryHelpers.AddQueryString(uriService, query);

			var httpResponseMessage = await httpClient.GetAsync(uri);

			using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync();

			var response = await JsonSerializer.DeserializeAsync<MailResponse>(contentStream, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

			return response;
		}

		public async Task<ApiResponse<T>> SendMail<T>(EmailRequest emailRequest, object model)
		{
			try
			{
				var client = new SendGridClient(_options.ApiKeySendGrid);
				var from = new EmailAddress(string.IsNullOrEmpty(emailRequest.From) ? _options.ContactEmailFrom : emailRequest.From, string.IsNullOrEmpty(emailRequest.FromName) ? _options.ContactEmailFromName : emailRequest.FromName);
				var to = new EmailAddress(emailRequest.ToEmail, emailRequest.Name);
				var htmlString = await _razorViewRenderer.RenderViewToStringAsync("~/Views/Emails/" + emailRequest.NameView, model);
				var msg = MailHelper.CreateSingleEmail(from, to, emailRequest.Subject, "", htmlString);

				if (emailRequest.BCCs is not null && emailRequest.BCCs.Count > 0)
				{
					foreach (var bcc in emailRequest.BCCs)
					{
						msg.AddBcc(bcc);

					}
				}

				var response = await client.SendEmailAsync(msg);

				if (response.StatusCode == HttpStatusCode.Accepted || response.StatusCode == HttpStatusCode.OK)
				{
					return new ApiResponse<T>((T)(object)true, "Correo electrónico enviado exitosamente.");
				}
				else
				{
					return new ApiResponse<T>((T)(object)false, "Error al enviar el correo electrónico.");
				}
			}
			catch (Exception ex)
			{
				var errors = new List<string> { ex.Message };
				HttpStatusCode status = HttpStatusCode.InternalServerError;
				return new ApiResponse<T>((T)(object)false, "Error al enviar el correo electrónico.", status, errors);
			}
		}

		public Dictionary<string, string> GetParams(MailRequest request)
		{

			return new Dictionary<string, string>()
			{
				["BookingId"] = request.BookingId.ToString(),
				["ChannelId"] = request.ChannelId.ToString(),
				["CustomerName"] = request.CustomerName.ToString(),
				["CustomerEmail"] = request.CustomerEmail,
				["Language"] = request.Language.ToString()
			};
		}

		public Dictionary<string, string> GetParamsBookNow(int Id)
		{

			return new Dictionary<string, string>()
			{
				["MasterLocatorId"] = Id.ToString()
			};
		}

	}
}
