﻿/*--------------------------------------- DESIGN SYSTEM ---------------------------------------*/

// BRAND COLORS ----------------------------------------------
$purple-100: #F0EDF7 !default;
$purple-200: #E1DCEF !default;
$purple-300: #B6ABD9 !default;
$purple-400: #8672C0 !default;
$purple-500: #5C469C !default;
$purple-600: #3E2F6A !default;

$pink-100: #FFF0F7 !default;
$pink-200: #FFCCE5 !default;
$pink-300: #FF85C1 !default;
$pink-400: #FF5CAD !default;
$pink-500: #EA0074 !default;
$pink-600: #99004C !default;

$navy-100: #F0F6FF !default;
$navy-200: #CCE0FF !default;
$navy-300: #85B4FF !default;
$navy-400: #5C9BFF !default;
$navy-500: #186CDF !default;
$navy-600: #003B98 !default;
$navy-650: #186BDF !default;





$orange-100: #FEE2B3 !default;
$orange-200: #FDCF81 !default;
$orange-300: #FDBC4F !default;
$orange-400: #FCA91C !default;
$orange-500: #E39003 !default;
$orange-600: #C97F03 !default;
// BRAND COLORS ----------------------------------------------


// BASE VARIABLES ----------------------------------------------
$brand-primary: $pink-500;
$brand-primary-hover: $pink-600;
$brand-secondary: $purple-500;
$brand-secondary-hover: $purple-600;
// BASE VARIABLES ----------------------------------------------


// NAV / LINKS ----------------------------------------------
$color-nav: #186CDF !default;
$color-nav-hover: #001580 !default;
$color-nav-disabled: #CCCCD3 !default;
// NAV / LINKS ----------------------------------------------


// NEUTRALS ----------------------------------------------
$white: #fff !default;
$black: #000 !default;
$grey-100: #FCFCFC !default;
$grey-200: #E4E4E7 !default;
$grey-300: #CCCCD3 !default;
$grey-400: #73737A !default;
$grey-500: #4D4D52 !default;
$grey-600: #333338 !default;
$grey-700: #1A1A1F !default;
// NEUTRALS ----------------------------------------------


// SEMANTIC COLORS ----------------------------------------------
$green-100: #F2F9EC !default;
$green-200: #D9ECC5 !default;
$green-300: #B2D98C !default;
$green-400: #78B43C !default;
$green-500: #5E8E2F !default;
$green-600: #456722 !default;

$red-100: #FCEBE8 !default;
$red-200: #F7C4BA !default;
$red-300: #F08A75 !default;
$red-400: #CD3517 !default;
$red-500: #AA2C13 !default;
$red-600: #7C200E !default;

$yellow-100: #FEF6E7 !default;
$yellow-200: #FBE4B6 !default;
$yellow-300: #F7CA6E !default;
$yellow-400: #E49C0C !default;
$yellow-500: #B37B09 !default;
$yellow-600: #825907 !default;

$blue-100: #E7F7FD !default;
$blue-200: #B7E7FA !default;
$blue-300: #87D6F7 !default;
$blue-400: #57C6F4 !default;
$blue-500: #0E9CD7 !default;
$blue-600: #085778 !default;
// SEMANTIC COLORS ----------------------------------------------

$color_1: #fff;
$color_2: #003b98;
$color_3: #312e81;
$color_4: #333132;
$color_5: #d2d2d2;
$color_6: #003ba6;
$color_7: #65657b;
$color_8: #808097;
$color_9: #2196f3;
$color_10: inherit;
$color_11: #94a3b8;
$color_12: #0e213a;
$color_13: #64748b;
$color_14: initial;
$color_15: #818b97;
$color_16: #5c5a5b;
$color_17: #000;
$color_18: #186cdf;
$font-family_1: Roboto-Bold;
$font-family_2: Roboto-Light;
$font-family_3: sans-serif;
$font-family_4: Roboto-Medium;
$font-family_5: Roboto-Regular;
$background-color_1: #fff;
$background-color_2: #f7f7fc;
$background-color_3: rgba(0,0,0,.8);
$background-color_4: #f1f1f1;
$background-color_5: #f9f9f9;
$background-color_6: #f2f8ff;
$background-color_7: inherit;
$background-color_8: transparent;
$background-color_9: #efefef;
$background-color_10: #f8fafc;
$background-color_11: #0d98dc;
$background-color_12: rgba(92,70,156,.08);
$background-color_13: #2196f3;
$background-color_14: #d2d2d2;
$background-color_15: #f59f00;
$background-color_16: #f1f5f9;
$background-color_17: #fcfcfc;
$background-color_18: #003b98;
$background-color_19: hsla(0,0%,100%,.77);
$background-color_20: #028eaa;
$background-color_21: rgba(2,142,170,.05);
$background-color_22: rgba(2,142,170,.1);
$background-color_23: rgba(2,142,170,.2);

$background-color_24: #da291c;
$background-color_25: rgba(218,41,28,.05);
$background-color_26: rgba(218,41,28,.1);
$background-color_27: rgba(218,41,28,.15);
$background-color_28: rgba(218,41,28,.2);
$background-color_29: #2a0088;
$background-color_30: rgba(42,0,136,.01);
$background-color_31: rgba(42,0,136,.03);
$background-color_32: rgba(42,0,136,.05);
$background-color_33: rgba(42,0,136,.1);
$background-color_34: rgba(42,0,136,.15);

$background-color_35: rgba(42,0,136,.2);
$background-color_36: #366086;
$background-color_37: rgba(254,118,17,.05);

$background-color_38: rgba(254,118,17,.1);
$background-color_39: rgba(254,118,17,.15);
$background-color_40: rgba(254,118,17,.2);

$background-color_41: rgba(54,96,134,.05);
$background-color_42: rgba(54,96,134,.1);
$background-color_43: rgba(54,96,134,.2);

$background-color_44: #ffff;
$background-color_45: #f2f2f2;
$background-color_46: #f5f5f5;
$background-color_47: rgba(0,0,0,.77);
$background-color_48: #f3f3f3;
$background-color_49: rgba(0,0,0,.25);
$background-color_50: #e2e8f0;
$background-color_51: #f0f9ff;
$background-color_52: #cbd5e1;
$border-color_1: transparent transparent #fff;
$border-color_2: transparent transparent rgba(0,0,0,.099);
$border-color_3: #0e213a;
$border-color_4: #2196f3;
$border-color_5: #d2d2d2;
$border-color_6: #000;
$border-color_7: #ffffffb3;
$border-color_8: #028eaa;
$border-color_9: #da291c;
$border-color_10: #2a0088;
$border-color_11: #366086;
$border-color_12: #efefef;

/* colors tb */
$color-green: #28A745;