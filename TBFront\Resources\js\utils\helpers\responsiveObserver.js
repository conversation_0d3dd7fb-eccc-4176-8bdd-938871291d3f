import { ref } from 'vue';

/**
 * Class representing a Responsive Observer.
 * @description This class  function is change if the window page is mobile or desktop.
 * Not working this class if the project is not a component vue
 * @example 
 * setup() {
 *      const isResponsiveRef = responsiveObserver.getResponsiveStatus();
 *      return {
 *          isResponsiveRef,
 *      };
 * },
 * 
 * computed: {
 *      isResponsive() {
 *          return this.isResponsiveRef;
 *      }
 * }
 */
export default class ResponsiveObserver {
    constructor() {
        this.currentScreen = ref(0);
        this.isResponsive = ref(false);
        this.init();
    }

    init() {
        this.checkResponsive();
        window.addEventListener('resize', this.checkResponsive.bind(this));
        window.addEventListener('orientationchange', this.checkResponsive.bind(this));
    }

    checkResponsive() {
        if (this.currentScreen.value !== window.innerWidth) {
            if ((window.innerWidth < 993) || this.isMobileDevice()) {
                this.isResponsive.value = true;
            } else {
                this.isResponsive.value = false;
            }
            this.currentScreen.value = window.innerWidth;
        }
    }

    isMobileDevice() {
        // Detección de dispositivos móviles excluyendo iPad y tabletas Android
        return /Android(?!.*(Tablet|tab|SCH-I800|SGH-T849|SPH-P100|SGH-T849|SHW-M180S|SHW-M180W))|webOS|iPhone|iPod|BlackBerry|IEMobile|Opera Mini|Mobile|mobile|Touch|Windows Phone|HarmonyOS/i.test(navigator.userAgent) &&
            !/iPad|Tablet|Macintosh/i.test(navigator.userAgent);
    }

    getResponsiveStatus() {
        return this.isResponsive;
    }

    stopObserving() {
        window.removeEventListener('resize', this.checkResponsive.bind(this));
        window.removeEventListener('orientationchange', this.checkResponsive.bind(this));
        
    }
}

/**
 * Class representing a responsive observer.
 * This class allows for observing changes in responsiveness of a web page or application.
 */
export const responsiveObserver = new ResponsiveObserver()