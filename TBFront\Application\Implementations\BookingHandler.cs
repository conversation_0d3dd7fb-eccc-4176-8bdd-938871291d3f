﻿using Microsoft.Extensions.Options;
using TBFront.Models;
using TBFront.Models.Request;
using TBFront.Models.Response;
using TBFront.Options;
using TBFront.Services;
using TBFront.Types;
using TBFront.Infrastructure.HttpService.BookingService;
using TBFront.Models.BookingItinerary;

namespace TBFront.Application.Implementations
{
    public class BookingHandler
    {

        private HashService _hash;
        private BookingService _booking;
        private readonly SettingsOptions _options;



        public BookingHandler(HashService hash, BookingService bookingService, IOptions<SettingsOptions> options)
        {
            _hash = hash;
            _booking = bookingService;
            _options = options.Value;
        }
        public async Task<List<ClientTokenInfoResponse>> GetClientTokenInfo(string keyValidation)
        {

            var clientInfo = await _booking.QueryAsync(keyValidation);
            return clientInfo;
        }

        public async Task<ItineraryResponse> GetBooking(VoucherInfo request)
        {
            // todo: descomentar peticion real
            //var itinerary = await _booking.QueryAsync("122059514", "<EMAIL>");

            var itinerary = await _booking.QueryAsync(request.Id, request.Email);
            return itinerary;
        }

        public VoucherInfo DecryptParams(GetBookingRequest request)
        {
            var voucher = new VoucherInfo();

            if (string.IsNullOrEmpty(request.Id) || string.IsNullOrEmpty(request.Em))
            {
                voucher.Valid = false;
                throw SetError(StatusType.ERROR);
            }

            voucher.Id = _hash.Decrypt(OverWriteHash(request.Id));
            voucher.Email = _hash.Decrypt(OverWriteHash(request.Em));

            voucher.Valid = true;

            if (string.IsNullOrEmpty(voucher.Id) || string.IsNullOrEmpty(voucher.Email))
            {
                voucher.Valid = false;
                throw SetError(StatusType.VOUCHER_NOT_FOUND);
            }


            return voucher;
        }

        public string GetLinkVoucher(string id, string email)
        {

            if (string.IsNullOrEmpty(id) || string.IsNullOrEmpty(email))
            {
                throw SetError(StatusType.BOOKING_CREATE_ERROR);
            }

            return $"{_options.SiteUrl}{_options.RedirectToPath}?id={id}&em={email}";
        }


        private string OverWriteHash(string hashStr)
        {

            if (hashStr is null)
            {
                return string.Empty;
            }

            hashStr = hashStr.Replace("%253D", "=").Replace("%252F", "/").Replace("%2F","/").Replace("%3D", "=").Replace("%25253D", "=").Replace("%25252F","/").Replace("%2525253D", "=").Replace("%2525252F", "/");
            return hashStr;
        }

        private ArgumentException SetError(string error)
        {
            throw new ArgumentException(error);
        }
    }
}
