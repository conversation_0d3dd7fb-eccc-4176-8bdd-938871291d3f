﻿using StatsdClient;
using TBFront.Infrastructure.Datadog.Dtos;
using TBFront.Interfaces;
using TBFront.Models.Datadog;

namespace TBFront.Infrastructure.Datadog
{
    public class DataDogService : IDataDogService
    {
        private readonly StatsdConfig _statsdConfig = new();
        private readonly DatadogConfiguration _configuration;
        private static readonly DataDogResponse _dataDogResponse = new();
        public DataDogService(DatadogConfiguration configuration)
        {
            _configuration = configuration;
            _statsdConfig = new StatsdConfig
            {
                StatsdServerName = _configuration.StatsdServerName,
                StatsdPort = _configuration.StatsPort
            };
        }
        public async Task<DataDogResponse> QueryAsync(DataDogRequest request, CancellationToken ct)
        {
            using var dogStatsdService = new DogStatsdService();

            if (_configuration.Active && dogStatsdService.Configure(_statsdConfig))
            {
                dogStatsdService.Histogram(request.Event, request.Value, tags: request.Tags.ToArray());
                //dogStatsdService.Set(request.Event, request.Value, tags: request.Tags.ToArray());
            }

            return _dataDogResponse;
        }
    }
}
