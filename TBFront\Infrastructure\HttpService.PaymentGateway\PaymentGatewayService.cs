﻿using Microsoft.AspNetCore.WebUtilities;
using System.Net.Http.Headers;
using System.Net.Mime;
using System.Text.Json;
using TBFront.Infrastructure.HttpService.PaymentGateway.Dtos;
using TBFront.Interfaces;
using TBFront.Models.PaymentGateway;

namespace TBFront.Infrastructure.HttpService.PaymentGateway
{
    public class PaymentGatewayService : IPaymentGatewayService
    {

        private readonly HttpClient _httpClient;
        private readonly PaymentGatewayConfiguration _configuration;
        private readonly static JsonSerializerOptions _jsonSerializerOptions = new() { PropertyNameCaseInsensitive = true };
        private readonly ICacheService _cacheService;

        public PaymentGatewayService(HttpClient httpClient, PaymentGatewayConfiguration configuration, ICacheService cacheService)
        {
            _configuration = configuration;
            _httpClient = httpClient;
            _httpClient.DefaultRequestHeaders.Add("Accept", MediaTypeNames.Application.Json);
            _httpClient.BaseAddress = new Uri(_configuration.Uri);
            _cacheService = cacheService;
        }



        public async Task<PaymentGatewayResponse> QueryAsync(PaymentGatewayRequest request, CancellationToken ct)
        {
            var response = new PaymentGatewayResponse();

            try
            {
                var uriService = $"{_configuration.PathPayment}";
                var payload = JsonSerializer.Serialize(request);
                var body = new StringContent(payload);
                body.Headers.ContentType = new MediaTypeHeaderValue("application/json");
                var httpResponseMessage = await _httpClient.PostAsync(uriService, body, ct);
                using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync(ct);
                response = await JsonSerializer.DeserializeAsync<PaymentGatewayResponse>(contentStream, _jsonSerializerOptions, ct);
            }
            catch (Exception)
            {
                response = new PaymentGatewayResponse();
            }
            

            return response;
        }

        public async Task<List<PaymentGatewayConfigurationResponse>> QueryAsync(PaymentGateweyConfigurationRequest request, CancellationToken ct)
        {
            var key = $"PaymentGateweyConfiguration_Channel:{request.ChannelId}";
            var response = await _cacheService.GetCache<List<PaymentGatewayConfigurationResponse>>(key, ct);

            if (response == null)
            {
                var query = new Dictionary<string, string>()
                {
                    ["ChannelId"] = request.ChannelId,
                };

                var uriService = $"{_configuration.PathSearchPaymentGatewayConfiguration}";
                uriService = QueryHelpers.AddQueryString(uriService, query);

                var httpResponseMessage = await _httpClient.GetAsync(uriService, ct);

                using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync(ct);

                response = await JsonSerializer.DeserializeAsync<List<PaymentGatewayConfigurationResponse>>(contentStream, _jsonSerializerOptions, ct);

                if (response != null)
                {
                    _cacheService.SetCache(key, response);
                }
            }

            return response;
        }
    }
}
