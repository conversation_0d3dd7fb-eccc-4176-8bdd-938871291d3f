import { Logger } from '../../utils/helpers/logger';
import { apiRequestService } from '../../utils/http';

const config = window.__pt.settings.site;

export const getPromotionsCalendar = async (params) => {
    let response = null;

    const resource = {
        method: 'get',
        uri: `${config.apiFlights.domain}${config.apiFlights.path}${config.apiFlights.pathPromotions}`
    };

    try {
        response = await apiRequestService(resource, params);
    }
    catch (e) {
        Logger.error(e.message);
    }

    return response;
};


export const getParamsPromotions = (request, extraParams) => {
    let params = {
        Site: request.site,
        Origin: request.origin,
        Destination: request.destination,
        StartingFromDateTime: request.startDate,
        ReturningFromDateTime: request.returnDate,
        Adults: request.adults,
        TripMode: request.tripMode,
        ...extraParams
    };
    return params;
};