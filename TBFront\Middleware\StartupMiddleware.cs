﻿using System.Globalization;

namespace TBFront.Middleware
{
    public class StartupMiddleware
    {

        public static StaticFileOptions StaticFileOptionsMiddleware()
        {

            return new StaticFileOptions
            {
                OnPrepareResponse = ctx =>
                {
                    ctx.Context.Response.Headers.Append("Cache-Control", "public,max-age=31536000");
                    ctx.Context.Response.Headers.Append("Expires", DateTime.UtcNow.AddDays(365).ToString("R", CultureInfo.InvariantCulture));
                }
            };
        }

        public static RequestLocalizationOptions RequestLocalizationMiddleware(IConfiguration Configuration)
        {
            var cultures = Configuration.GetSection("Settings:CulturesAllowed").Value;
            var supportedCultures = cultures.Split("|");

            var defaultCulture = Configuration.GetSection("Settings:CultureApp").Value;
            var localizationOptions = new RequestLocalizationOptions().SetDefaultCulture(defaultCulture)
                .AddSupportedCultures(supportedCultures)
                .AddSupportedUICultures(supportedCultures);

            localizationOptions.RequestCultureProviders.Insert(0, new CustomUrlRequestCultureProvider());

            return localizationOptions;
        }

    }
}
