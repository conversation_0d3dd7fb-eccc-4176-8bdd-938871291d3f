<template>
    <!-- Modal -->
    <div class="modal fade" id="modal-loader-checkout" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content container-loading bg-white">
                <div class="cl-body d-flex flex-column h-100">
                    <div class="clb-header">
                        <p class="text-center py-2 font-22 f-semibold color-blue mt-2 mt-md-3">
                            {{ __("checkout.modal_ready") }}
                        </p>
                    </div>

                    <div class="clb-footer mt-auto px-3 mb-4" v-if="summary.flightItinerary">
                        <div class="row mb-2">
                            <div class="col-12">
                                <p class="mb-1 font-18 f-semibold">{{ __("checkout.modal_detail") }}</p>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-9 align-self-center">
                                <p class="m-0 font-14 lh-1">
                                    <span class="f-semibold">{{ summary.flightItinerary.starting.departure.airport }} - {{ summary.flightItinerary.starting.arrival.airport }}</span> 
                                    {{ __("checkout.modal_family") }} - {{ getFareNameStart }}
                                    <span class="c-date">
                                        {{ $filters.date(summary.flightItinerary.starting.departure.date, 'ddd - DD MMM')}} 
                                    </span>
                                </p>
                            </div>
                            <div class="col-3 pl-0">
                                <img height="auto" width="100%" :src="summary.flightItinerary.starting.logoUri"
                                    class="d-block ml-auto">
                            </div>
                        </div>
                        <div class="row" v-if="summary.rate.isRoundtrip">
                            <div class="col-12">
                                <hr class="mt-1 mb-2 pb-1">
                            </div>
                        </div>
                        <div class="row" v-if="summary.rate.isRoundtrip">
                            <div class="col-9 align-self-center">
                                <p class="m-0 font-14 lh-1">
                                    <span class="f-semibold">{{ summary.flightItinerary.returning.departure.airport }} - {{ summary.flightItinerary.returning.arrival.airport }}</span> 
                                    {{ __("checkout.modal_family") }} - {{ getFareNameReturn }}
                                    <span class="c-date">
                                        {{ $filters.date(summary.flightItinerary.returning.departure.date, 'ddd - DD MMM')}} 
                                    </span>
                                </p>
                            </div>
                            <div class="col-3 pl-0">
                                <img height="auto" width="100%" :src="summary.flightItinerary.returning.logoUri"
                                    class="d-block ml-auto">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="icon-loader">
                    <div class="c-loader"></div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import _ from 'lodash';

const configSite = {};

export default {
    props: {
        summary: {},
        itineary:{}
    },
    data() {
        return {
            config: configSite,
        }
    },
    mounted() {
    },
    computed: {
        getFareNameStart(){
            let name = _.get(this.itineary, ["detailStarting","detailFamilyFare","familyFareName"], this.summary.flightItinerary.starting.fareGroup);
            return name;
        },
        getFareNameReturn(){
            let name = _.get(this.itineary, ["detailReturning","detailFamilyFare","familyFareName"], this.summary.flightItinerary.returning.fareGroup);
            return name;
        }
    },
    methods: {
        sum(){
            var modalCheckout = bootstrap.Modal.getOrCreateInstance(document.getElementById('modal-lodaer-checkout')); // new bootstrap.Modal(document.getElementById('modal-lodaer-checkout'));

        }
    }
}
</script>