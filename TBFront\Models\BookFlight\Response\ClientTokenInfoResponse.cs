﻿
namespace TBFront.Models.Response
{

    public class ClientTokenInfoResponse
    {
        public string Token { get; set; }
        public int PaymentPlan { get; set; }
        public int FeesPlan { get; set; }
        public string PayOption { get; set; }
        public string PayProcessOptionType { get; set; }
        public string CardType { get; set; }
        public string CardHolderName { get; set; }
        public string CardNumberLastDigits { get; set; }
        public string CardIdentityDocumentType { get; set; }
        public bool IsPointRedemption { get; set; }
        public int RedemptionPointsBankId { get; set; }
        public DateTime DtCreated { get; set; }
    }
}
