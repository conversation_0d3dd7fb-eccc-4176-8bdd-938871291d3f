﻿using ProtoBuf;

namespace TBFront.Models.PaymentGateway
{
    [ProtoContract]
    public class PaymentGatewayConfigurationResponse
    {
        [ProtoMember(1)]
        public int PaymentGatewayConfigurationId { get; set; }

        [ProtoMember(2)]
        public int ChannelId { get; set; }

        [ProtoMember(3)]
        public int ChannelGroupId { get; set; }

        [ProtoMember(4)]
        public int AffiliateId { get; set; }

        [ProtoMember(5)]
        public int AffiliateSiteId { get; set; }

        [ProtoMember(6)]
        public int AgencyId { get; set; }

        [ProtoMember(7)]
        public string Description { get; set; } = string.Empty;

        [ProtoMember(8)]
        public string ApplicationName { get; set; } = string.Empty;

        [ProtoMember(9)]
        public string HeaderColor { get; set; } = string.Empty;

        [ProtoMember(10)]
        public string ImageLogoUri { get; set; } = string.Empty;

        [ProtoMember(11)]
        public bool ShowPrivacyPolicy { get; set; }

        [ProtoMember(12)]
        public string PrivacyPolicyUrl { get; set; } = string.Empty;

        [ProtoMember(13)]
        public string CurrencyCode { get; set; } = string.Empty;

        [ProtoMember(14)]
        public string CultureCode { get; set; } = string.Empty;

        [ProtoMember(15)]
        public string RedirectUrl { get; set; } = string.Empty;
        
    }
}
