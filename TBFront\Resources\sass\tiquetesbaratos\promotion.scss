@import './app', './base/_variables', './_variables.scss', 'base/_core', 'components/footer', 'components/header';
@import "./components/_loading.scss";



.c-box-promo {
    z-index: 10;
    color: #222f7d;
    text-shadow: none;
    background-position: 100%;
    background-size: contain;
    background-repeat: no-repeat;

    &.origin {
        background-repeat: no-repeat;
        background-size: cover;
        background-position: 50%;
        position: relative;
    }

    &.destination {
        &:after {
            content: "";
            background-image: url(/assets-tb/img/tiquetesbaratos/mask-01.svg);
            background-position: 0 55%;
            width: 100%;
            position: absolute;
            background-repeat: no-repeat;
            right: 30%;
            top: 0;
            bottom: 0;
            background-size: cover;

            @media (min-width: 1550px) {
                right: 20%;
            }

        }
    }

    &.calendar {
        color: #fff;

        &:after {
            content: "";
            background-image: url(/assets-tb/img/tiquetesbaratos/mask-01-b.svg);
            background-position: 0 55%;
            width: 95%;
            position: absolute;
            background-repeat: no-repeat;
            right: 30%;
            top: 0;
            bottom: 0;
            background-size: cover;

            @media (min-width: 1800px) {
                background-position: 0 69%;
                width: 72%;
            }
        }
    }

    &:before {
        background: linear-gradient(180deg, rgba(0, 0, 0, .4416141456582633), hsla(0, 0%, 100%, 0) 5%);
        content: "";
        height: 90%;
        left: 0;
        position: absolute;
        right: 0;
        top: 0;
        z-index: 0;
    }

    .cb-in {
        z-index: 1;
    }

    .nav-tabs {
        z-index: 0;

        .nav-link {
            background-color: $background-color_1;
            border-radius: 4px 4px 0 0;
        }

        .nav-link.active {
            background-color: $background-color_15;
            color: $color_1;
        }
    }

    .tab-content {
        z-index: 1;

        .c-input {
            border: none;
            border-bottom: 1px solid #525051;
            padding-bottom: 8px;

            &:focus {
                outline: none;
            }
        }

        .c-select {
            -webkit-appearance: none;
            -moz-appearance: none;
            background-color: $background-color_1;
            border: none;
            border-bottom: 1px solid #525051;
            padding-bottom: 9px;

            &:focus {
                outline: none;
            }
        }

        .c-input-box {
            input {
                font-size: 14px;
            }

            .icon {
                position: absolute;
                color: $color_6;
            }
        }
    }

    .a-link {
        color: #fff !important;
        text-decoration: underline !important;
    }
}

@media (max-width: 767px) {
    .c-box-promo {
        &.destination {
            &:after {
                background-image: none !important;
            }

            background-size: cover;
            min-height: 100px;

            &:after {
                background-image: none;
            }
        }

        .cb-in {
            display: none;
        }

        &.calendar {
            background-size: cover;
            min-height: 94px;

            &:after {
                background-image: none;
            }
        }
    }

    .cb-in {
        .color-primary {
            color: #003b98 !important;
        }
    }
}

.cpbp-titles {
    @media (min-width: 768px) {
        display: none;
    }

    @media (max-width: 767px) {

        h1,
        h2 {
            font-size: 26px !important;
        }
    }
}


.c-slide-items {
    .cs-img {
        img {
            position: relative;
            top: -3px;
        }
    }
}

.cp-banner-offers {
    background-color: $background-color_16;
}

.cp-box-promo {
    
    .cp-into {
        border-radius: 10px;
        margin-top: -50px;

        .c-input {
            border: none;
            border-bottom: 1px solid #ccc;
            left: 0;
            position: absolute;
            right: 0;
            z-index: 0;

            &:focus {
                outline: none;
            }
        }

        .icon {
            position: relative;
            top: 2px;
            z-index: 1;
        }
    }

    .c-field {
        .i-exp {
            position: absolute;
            right: 5px;
            transition: transform .5s ease 0s;
        }

        .placeholderr {
            color: #65657b;
            font-family: sans-serif;
            font-size: 14px;
            left: 24px;
            line-height: 14px;
            pointer-events: none;
            position: sticky;
            transform-origin: 0 50%;
            transition: transform .2s, color .2s;
            top: 5px;
        }
    }
}

.cf-select {
    border-radius: 0 0 10px 10px;
    left: 0;
    max-height: 327px;
    overflow-x: hidden;
    overflow-y: auto;
    position: absolute;
    right: 0;
    top: 30px;
    z-index: 99;

    li {
        border-color: $border-color_12 !important;
        transition: all .2s linear;

        &:hover {
            background-color: $background-color_9;
            cursor: pointer;
        }
    }
}

.placeholder {
    display: inline-block;
    color: #65657b;
    font-family: sans-serif;
    vertical-align: middle;
    font-size: 12px;
    cursor: pointer;
    background-color: unset;
    opacity: 0.6;
    position: absolute;
    left: 4%;
    top: -2vh;
    z-index: 999;
}

.icono-bed-outline {
    background-image: url('/assets-tb/img/tiquetesbaratos/icon-bed-outline.svg');
    width: 74px;
    height: 74px;
    display: inline-block;
}


.input-select {
    border: none;
    border-bottom: 1px solid #ccc;
    border-radius: 0;

    &:focus {
        outline: none !important;
        border-color: inherit;
        -webkit-box-shadow: none;
        box-shadow: none;
    }
}

.input-group {
    position: relative;
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    width: 100%;
    justify-content: center;
}

.cp-banner-full {
    background-repeat: no-repeat;
    background-size: cover;
    background-position: 0 50%;
    position: relative;
}

figure {
    border: 1px solid #ccc;
    border-radius: 10px;
    margin: 0 0 1rem;
}


@media (min-width: 768px) and (max-width: 1024px),
(min-width: 1025px) {

    .list-1 {
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }
}

@media (max-width: 767px) {
    .c-slide-items {
        h3 {
            font-size: 22px !important;
        }
    }
}

.cb-promo {
    .easepick-wrapper {
        right: 600px;

        @media (min-width: 1188px) and (max-width: 1279px) {
            right: 500px;
        }
    }
}

.mobile-mask {
    display: none;
  
    @media (max-width: 767px) {
      display: block;
      background-color: #fddb2f; /* Fondo amarillo */
      width: 100%;
      height: 100px; /* Puedes ajustar la altura según necesites */
      position: relative;
    }
  
    img {
      display: none; // Por si queda alguna referencia
    }
  }
  

.booker-overlay {
    pointer-events: none;
  }
  .booker-overlay > * {
    pointer-events: all;
  }

  .mobile-search-container {
    margin-bottom: -30px;
    margin-top: -30px;
    position: relative;
    z-index: 1100;
  
    .mobile-search-box {
      background-color: #fff;
      border-radius: 12px;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15); // sombra más notoria
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 1rem 1.5rem;
      cursor: pointer;
  
      span {
        color: #1976d2; // Azul más moderno
        font-size: 1.5rem; // Texto más grande
        font-weight: 600;
        font-family: 'Poppins', sans-serif;
      }
  
      i {
        color: #1976d2;
        font-size: 1.8rem; // Ícono más grande también
      }
    }
  
    .mt-3 {
      margin-top: 1rem;
    }
  }
  
  // Para ocultarlo en desktop:
  @media (min-width: 768px) {
    .mobile-search-container {
      display: none;
    }
  }

  .mobile-booker{
    z-index: 1000;
    top: 380px;
    left: 50%;
    //transform: translate(-50%, -50%);
    margin: 20px;
  }
  
  .mobile-search-box {
    cursor: pointer;
  }
  
  .mobile-search-box i {
    font-size: 1.2rem;
    color: #555; 
  }
  

  @media (max-width: 767px) {
    .bg-booker .card-body[data-v-69ef18ed] {
      box-shadow: none !important;
    }
  }
  