import { defineStore } from "pinia";

export const useFlightRevalidateStore = defineStore({
    id: "flightRevalidate",
    state: () => ({
        flightRevalidateResponse: {
            fares: [],
            faresLegs: {},
            flights: {},
        },
        loading: false
    }),
    getters: {
        getFlightRevalidate: (state) => {
            return state.flightRevalidateResponse;
        },
        getTotalAmount: (state) => {
            let amount = 0;
            for (let fare in state.flightRevalidateResponse.fares) {
                amount += state.flightRevalidateResponse.fares[fare].amount;
            }
            return amount;
        },
        getRevalidateStatus: (state) => {
            return state.loading;
        },
    },
    actions: {
        setFlightRevalidateResponse(response) {
            this.flightRevalidateResponse = response;
        },
        setRevalidateStatus(response) {
            this.loading = response;
        }
    },
});