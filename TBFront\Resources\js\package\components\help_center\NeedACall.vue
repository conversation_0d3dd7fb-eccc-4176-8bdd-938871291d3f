﻿<template>
	<div class="row">
		<div class="col-12 col-md-6 bg-modal-call d-none d-md-block position-relative">
			<div class="c-animated-call cac-1">
				<span class="icon icon-phone d-block text-center font-30 mt-2"></span>
				<span class="d-block px-3 font-12 text-center mt-2">{{__('need_a_call.available_agents')}}</span>
				<img alt="asesores" width="60" src="/assets-tb/img/tiquetesbaratos/animated-call.svg">
			</div>
		</div>
		<div class="col-12 col-md-6">
			<div class="modal-header border-0 pb-1 pb-md-3">
				<button type="button" class="btn-close" data-bs-target="#staticBackdrop" data-bs-toggle="modal" aria-label="Close"></button>
			</div>
			<div class="modal-body pt-0 px-4">
				<form v-if="!message">
					<h5 class="font-18 font-poppins-semibold mb-2">{{__('need_a_call.need_a_call?')}}</h5>
					<div class="row">
						<div class="col-12">
							<div class="form-group mt-1">
								<input type="text" class="form-control" :class="{'is-invalid': errors.name && submitCount > 0}" :placeholder="__('need_a_call.your_name')" v-model="name">
								<div class="invalid-feedback text-left">
									{{__(`errors.${errors.name}`)}}
								</div>
							</div>
						</div>

						<div class="col-12 mt-2">
							<div class="form-group mt-1">
								<input type="text" class="form-control" :class="{'is-invalid': errors.phone && submitCount > 0}" :placeholder="__('need_a_call.phone_number')" v-model="phone">
								<div class="invalid-feedback text-left">
									{{__(`errors.${errors.phone}`)}}
								</div>
							</div>
						</div>
					</div>
					<div class="row mt-3 px-0">
						<div class="col-12">
							<div class="g-recaptcha-grupos" :class="{'border-error': errorCaptcha}">
								<div class="c-recapcha d-flex justify-content-center g-recaptcha" :data-sitekey="config.recaptchaKey"></div>
							</div>
							<p class="invalid-feedback text-left d-block mb-0" v-if="errorCaptcha && submitCount > 0">
								{{__('errors.recaptcha_error')}}
							</p>
						</div>
					</div>
					<div class="row">
						<div class="col-12 mt-3">
							<button class="btn btn-primary w-100 py-3" type="button" @click="submit()">{{__('need_a_call.send')}}</button>
						</div>
					</div>
					<div class="row c-recapcha mx-0 py-2" v-if="config && config.contactMeAvailableTime && config.contactMeAvailableTime.length">
						<div class="col-12 mt-3 bg-recapcha">
							<p class="font-18 mb-1 fw-bold">{{__('need_a_call.openning_hours')}}</p>
							<p class="font-16 mb-1 gray-200" v-for="availableTime in config.contactMeAvailableTime">
								{{__(`need_a_call.${availableTime.title}`, [time24to12Convert(availableTime.hours.start), time24to12Convert(availableTime.hours.end)])}}
							</p>
						</div>
					</div>
				</form>
				<div class="d-flex flex-column justify-content-center align-items-center" v-else>
					<i v-if="submitError" class="icons icon-warning text-warning font-60"></i>
					<i v-else class="icons icon-check-circle text-success font-60"></i>
					<p>{{__(`need_a_call.${message}`)}}</p>
					<button class="btn btn-primary" @click="reset()">{{__('need_a_call.accept')}}</button>
				</div>
			</div>
		</div>
	</div>

	<LoaderFullPage :show="submitting" />
</template>
<script setup>
	import { ref, onMounted, onBeforeUnmount } from 'vue';
	import { useForm } from 'vee-validate';
	import * as yup from 'yup';
	import { Generic } from '../../../utils/analytics/generics.js'

	import { methods } from '../../../constants';
	import { apiRequestService } from '../../../utils/http';
	import { Logger } from '../../../utils/helpers/logger.js';

	const phoneRegExp = /^((\\+[1-9]{1,4}[ \\-]*)|(\\([0-9]{2,3}\\)[ \\-]*)|([0-9]{2,4})[ \\-]*)*?[0-9]{3,4}?[ \\-]*[0-9]{3,4}?$/;
	const config = window.__pt.settings.site || {};

	const { handleSubmit, errors, submitCount, defineField, resetForm } = useForm({
		validationSchema: yup.object({
			name: yup.string().required(),
			phone: yup.string().required().matches(phoneRegExp, 'phone number is not valid'),
		})
	});

	const [name] = defineField('name');
	const [phone] = defineField('phone');

	const submitError = ref(false);
	const submitting = ref(false);
	const errorCaptcha = ref(false);
	const message = ref("");

    const getQueryParams = () => {
        // obtenemos la url de la página actual con window.location.href
        let myURL = new URL(window.location.href);

        // extraemos los parámetros de consulta
        let myParams = new URLSearchParams(myURL.search);

        // creamos un objeto donde se guardarán los parámetros
        let paramsObj = {};

        for(let param of myParams) {
            // si el valor es 'true' o 'false', lo convertimos a booleano
            if (param[1] === 'true') {
                paramsObj[param[0]] = true;
            } else if (param[1] === 'false') {
                paramsObj[param[0]] = false;
            } else {
                paramsObj[param[0]] = param[1];
            }
        }

        // devolvemos los parámetros como un objeto
        return paramsObj;
    }

	const validateCaptcha = () => {
		const recaptchaRes = grecaptcha.getResponse();
		if (!recaptchaRes) {
			errorCaptcha.value = true;
			submitError.value = true;
		} else {
			errorCaptcha.value = false;
		}

		return recaptchaRes;
	};

	const submit = handleSubmit(async values => {
		submitting.value = true;
		const params = { ...values };

		try {
			const token = validateCaptcha();
			if (token) {
				const resource = { uri: config.formsConfiguration.pathCall, method: methods.POST };
				params.recaptchaToken = token;
				const response = await apiRequestService(resource, {}, params);
				if (response && response.data) {
					message.value = response.data;
					submitError.value = false;
					if (response.errors.length) {
						submitError.value = true;
					}

					resetForm();
				} else {
					submitError.value = true;
				}
			}
		} catch (e) {
			Logger.error(e);
			errorCaptcha.value = false;
		} finally {
			submitting.value = false;
		}
	}, () => {
		validateCaptcha();
	});

	const reset = () => {
		resetForm();
		submitting.value = false;
		message.value = "";
		submitError.value = false;
		submitting.value = false;
		errorCaptcha.value = false;
	};

	const onModalOpen = () => {
		Generic.callMe();
	};
    const init = ()=>{
        setTimeout(()=>{
            let queryParams = getQueryParams();
            const callus = (window.__pt.data || {})?.callus;
            if(queryParams.callus || callus){
                const modalElement = new bootstrap.Modal('#staticBackdrop')
                modalElement.show();
                Generic.callMe();
            }
        }, 1000)
    }
    
	onMounted(() => {
        init()
		const modalElement = document.getElementById('staticBackdrop');
		modalElement.addEventListener('shown.bs.modal', onModalOpen);
	});

	onBeforeUnmount(() => {
		const modalElement = document.getElementById('staticBackdrop');
		modalElement.removeEventListener('shown.bs.modal', onModalOpen);
	});
</script>