﻿using Microsoft.Extensions.Options;
using TBFront.Application.Mappers;
using TBFront.Interfaces;
using TBFront.Models.Places.Request;
using TBFront.Models.Places.Response;
using TBFront.Models.Request;
using TBFront.Options;

namespace TBFront.Application.Implementations
{
    public class PlaceHandler : IPlaceHandler
    {
        private readonly IPlacesAirportService _apiPlace;
        private readonly SettingsOptions _options;
        public PlaceHandler(IPlacesAirportService apiPlace, IOptions<SettingsOptions> options)
        {
            _apiPlace = apiPlace;
            _options = options.Value;
        }

        public async Task<List<PlaceResponse>> QueryAsync(FlightRequest request, CancellationToken ct)
        {
            var responsePlaces = new List<PlaceResponse>();
            var req = PlaceMapper.Request(request.StartingAirportPlace.AirportCode, request.ReturningAirportPlace.AirportCode, request.Culture);

            if (req.Codes.Any())
            {
                var response = await _apiPlace.QueryAsync(req, ct);
                responsePlaces = PlaceMapper.Map(response);
            }

            return responsePlaces;
        }

        public async Task<FrontPlaceResponse> QueryAsync(PlaceRequest request, CancellationToken ct)
        {
            var req = PlaceMapper.Request(request.StartingAirport, request.ReturninAirport, request.InternalCulture);
            var responseFront = new FrontPlaceResponse();
            if (req.Codes.Any())
            {
                var response = await _apiPlace.QueryAsync(req, ct);
                var responsePlaces = PlaceMapper.Map(response);
                responseFront.StartingAirport = request.StartingAirport;
                responseFront.ReturningAirport = request.ReturninAirport;
                responseFront.Culture = request.Culture;
                responseFront.InternalCulture = request.InternalCulture;
                responseFront.PlaceResponses = responsePlaces;
            }

            return responseFront;
        }

    }
}