﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Options;
using System.Text.Json;
using System.Xml.Linq;
using TBFront.Application.Implementations;
using TBFront.Helpers;
using TBFront.Interfaces;
using TBFront.Mappers;
using TBFront.Models.Configuration;
using TBFront.Models.Destination.Request;
using TBFront.Models.Places.Request;
using TBFront.Models.Request;
using TBFront.Options;
using TBFront.Types;

namespace TBFront.Controllers
{
    public class PromotionController : Controller
    {
        private readonly ILogger<HomeController> _logger;
        private readonly IDestinationHandler _destinationHandler;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IUserHandler _userHandler;
        private readonly SettingsOptions _options;
        private readonly ViewHelper _helper;
        private readonly ICommonHandler _commonHandler;
        private readonly IContentDeliveryNetworkHandler _contentDeliveryNetworkHandler;
        private readonly IAlternateHandler _alternateHandler;

        public PromotionController
            (
            ILogger<HomeController> logger,
            IDestinationHandler destinationHandler,
            IOptions<SettingsOptions> options,
            ICommonHandler commonHandler,
            IContentDeliveryNetworkHandler contentDeliveryNetworkHandler,
            IHttpContextAccessor httpContextAccessor,
            ViewHelper helper,
            IUserHandler userHandler,
            IAlternateHandler alternateHandler
            )
        {
            _logger = logger;
            _destinationHandler = destinationHandler;
            _options = options.Value;
            _helper = helper;
            _httpContextAccessor = httpContextAccessor;
            _commonHandler = commonHandler;
            _contentDeliveryNetworkHandler = contentDeliveryNetworkHandler;
            _alternateHandler = alternateHandler;
            _userHandler = userHandler;
        }

        [Route("/vuelos/promociones-tiquetes-aereos-origen")]
        [Route("/flights/promociones-tiquetes-aereos-origen")]
        [Route("{culture}/vuelos/promociones-tiquetes-aereos-origen")]
        [Route("{culture}/flights/promociones-tiquetes-aereos-origen")]
        [HttpGet]
        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> Origen(FlightParamsRequest flightRequest)
        {
            try
            {
                using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(60000));
                
                var route = HomeMapper.Path(Request.Path.Value ?? "");
                var req = new DestinationRequest("origin");
                var destinations = await _destinationHandler.QueryAsync(req, cts.Token);
                var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
                var alternates = await _alternateHandler.QueryAsync(new PlaceRequest { Culture = userSelection.Culture.CultureCode, Id = 0, Path = "vuelos/promociones-tiquetes-aereos-origen", Route = route, Type = PageType.Generic }, cts.Token);
                var meta = MetaMapper.Promotions(_options, _helper, "origen", userSelection);

                var request = FlightParamsHelper.GetFlightRequest(flightRequest, _options, userSelection.Culture.InternalCultureCode);
                var user = await _userHandler.QueryAsync(new Models.User.UserRequest() { }, cts.Token);

                ViewData["Alternates"] = alternates;
                ViewData["destinations"] = destinations;
                ViewData["MetaTag"] = meta;
                ViewData["type"] = "origin";
                ViewData["User"] = user;
                ViewData["CultureData"] = userSelection.Culture;
                ViewData["CurrencyData"] = userSelection.Currency;
                ViewData["Exchange"] = userSelection.ExchangeClient;
                ViewData["UserLocation"] = userSelection.Context.Location;
                ViewData["request"] = request;

                return View("~/Views/Promotion/Index.cshtml");
            }
            catch (Exception e)
            {
                _logger.LogError($"Origen error - {e.Message} ");

                return ErrorPage(e.Message, 500);
            }

        }

        [Route("/index.php/component/promociones_tiquetes_aereos/")]
        [HttpGet]
        public IActionResult RedirectToOrigen()
        {
            return RedirectPermanent($"/vuelos/promociones-tiquetes-aereos-origen");
        }

        [Route("/vuelos/promociones-tiquetes-aereos-destino")]
        [Route("/vuelos/promociones-tiquetes-aereos-a-{destination}/{originCode}")]
        [Route("/flights/promociones-tiquetes-aereos-destino")]
        [Route("{culture}/vuelos/promociones-tiquetes-aereos-destino")]
        [Route("{culture}/vuelos/promociones-tiquetes-aereos-a-{destination}/{originCode}")]
        [Route("{culture}/flights/promociones-tiquetes-aereos-destino")]
        [Route("{culture}/flights/promociones-tiquetes-aereos-a-{destination}/{originCode}")]

        [HttpGet]
        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> Destination(FlightParamsRequest flightRequest, string destination = "", string originCode = "")
        {
            try
            {
                using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(60000));
                var route = HomeMapper.Path(Request.Path.Value ?? "");
                var req = new DestinationRequest("destination");
                var destinations = await _destinationHandler.QueryAsync(req, cts.Token);
                var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);

                var request = FlightParamsHelper.GetFlightRequest(flightRequest, _options, userSelection.Culture.InternalCultureCode);



                string destinationName = string.Empty;

                if (!string.IsNullOrWhiteSpace(originCode))
                {
                     
                    var destinationMatch = destinations.FirstOrDefault(d => d.Code.Equals(originCode, StringComparison.OrdinalIgnoreCase));

                    if (destinationMatch == null)
                    {
                        return ErrorPage("Destination not found", 404);
                    }
                    destinationName = destinationMatch.Name;
                }

               
                var alternatesPath = "vuelos/promociones-tiquetes-aereos-origen";

                if (!string.IsNullOrWhiteSpace(destinationName))
                {
                  
                    string normalizedDestination =  FlightParamsHelper.GenerateSlug(destinationName);
                    alternatesPath = $"vuelos/promociones-tiquetes-aereos-a-{normalizedDestination}/{originCode}";
                }


                var alternates = await _alternateHandler.QueryAsync(new PlaceRequest
                {
                    Culture = userSelection.Culture.CultureCode,
                    Id = 0,
                    Path = alternatesPath,
                    Route = route,
                    Type = PageType.Generic
                }, cts.Token);


                var meta = MetaMapper.Promotions(_options, _helper, "destino", userSelection, destinationName, originCode);
                var user = await _userHandler.QueryAsync(new Models.User.UserRequest() { }, cts.Token);

                ViewData["Alternates"] = alternates;
                ViewData["destinations"] = destinations;
                ViewData["destination"] = destination;
                ViewData["originCode"] = originCode;
                ViewData["MetaTag"] = meta;
                ViewData["type"] = "destination";
                ViewData["User"] = user;
                ViewData["CultureData"] = userSelection.Culture;
                ViewData["CurrencyData"] = userSelection.Currency;
                ViewData["Exchange"] = userSelection.ExchangeClient;
                ViewData["UserLocation"] = userSelection.Context.Location;
                 ViewData["request"] = request;
                return View("~/Views/Promotion/Index.cshtml");
            }
            catch (Exception e)
            {

                _logger.LogError($"Destination error - {e.Message} ");

                return ErrorPage(e.Message, 500);
            }

        }

        [Route("/index.php/component/promociones_tiquetes_aereos2/")]
        [HttpGet]
        public IActionResult RedirectToDestination()
        {
            return RedirectPermanent($"/vuelos/promociones-tiquetes-aereos-destino");
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public ActionResult ErrorPage(string errorMgs, int statusCode)
        {
            ViewData["ErrorMgs"] = errorMgs;
            _httpContextAccessor.HttpContext.Response.StatusCode = statusCode;
            return View("~/Views/Error/Index.cshtml");
        }

        [Route("/vuelos/calendar/")]
        [Route("{culture}/vuelos/calendar/")]
        [Route("/flights/calendar/")]
        [Route("{culture}/flights/calendar/")]
        [HttpGet]
        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> Calendar(CalendarPromotionsRequest promotionRequest)
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(60000));
            var route = HomeMapper.Path(Request.Path.Value ?? "");
            var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
            var alternates = await _alternateHandler.QueryAsync(new PlaceRequest { Culture = userSelection.Culture.CultureCode, Id = 0, Path = "/vuelos/promociones-tiquetes-aereos-origen", Route = route, Type = PageType.Generic }, cts.Token);
            var meta = MetaMapper.Promotions(_options, _helper, "origen", userSelection);

            ViewData["Alternates"] = alternates;
            ViewData["Request"] = promotionRequest;
            ViewData["MetaTag"] = meta;
            ViewData["CultureData"] = userSelection.Culture;
            ViewData["CurrencyData"] = userSelection.Currency;
            ViewData["Exchange"] = userSelection.ExchangeClient;
            ViewData["UserLocation"] = userSelection.Context.Location;

            return View("~/Views/Promotion/Calendar.cshtml");
        }
    }
}
