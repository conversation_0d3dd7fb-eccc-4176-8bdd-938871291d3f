﻿using TBFront.Interfaces;
using TBFront.Infrastructure.HttpService.Itinerary.Dtos;
using TBFront.Models.BookingItinerary;

namespace TBFront.Infrastructure.HttpService.PaymentGateway
{
    public static class ItineraryServiceRegister
    {
        public static void AddItineraryServiceRegister(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddHttpClient<ItineraryService>("");

            services.AddSingleton(s => configuration.GetSection("ItineraryConfiguration").Get<ItineraryConfiguration>());
            services.AddSingleton(s => configuration.GetSection("AuthConfiguration").Get<AuthConfiguration>());

            
            services.AddSingleton<IQueryHandlerAsync<ItineraryRequest, ItineraryResponse>, ItineraryService>();

        }
    }
}
