﻿using ProtoBuf;
namespace TBFront.Models.ContentDeliveryNetwork.LegalContent
{
    [ProtoContract]
    public class LegalContentResponse
    {
        [ProtoMember(1)]
        public string Name { get; set; } = string.Empty;

        [ProtoMember(2)]
        public IEnumerable<string> Country { get; set; } = [];

        [ProtoMember(3)]
        public bool Static { get; set; }

        [ProtoMember(4)]
        public IEnumerable<LegalContent> Content { get; set; } = [];
    }
    [ProtoContract]
    public class LegalContent
    {
        [ProtoMember(1)]
        public string Code { get; set; } = string.Empty;

        [ProtoMember(2)]
        public string ContentHtml { get; set; } = string.Empty;
    }
}
