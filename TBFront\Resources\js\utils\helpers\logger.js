﻿import { consoleType } from "../../constants";

const Log = (type, args) => {
	// Se podría implementar un logger para logs controlados
	if (window.__pt.settings.site.showConsoleLogs) {
		if (type == consoleType.LOG) {
			console.log(...args);
		} else if (type === consoleType.WARN) {
			console.warn(...args);
		}
		else if (type === consoleType.ERROR) {
			console.error(...args);
		}
	}
};

export const Logger = {
	log: (...args) => {
		Log(consoleType.LOG, args);
	},
	warn: (...args) => {
		Log(consoleType.WARN, args);
	},
	error: (...args) => {
		Log(consoleType.ERROR, args);
	},
	info: (...args) => {
		Log(consoleType.LOG, args);
	},
};