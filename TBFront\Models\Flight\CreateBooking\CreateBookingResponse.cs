﻿namespace TBFront.Models.Flight.CreateBooking
{
    public class CreateBookingResponse
    {
        public string Id { get; set; }
        public int AffiliateSiteId { get; set; }
        public int BookingStatus { get; set; }
        public string OxxoReference { get; set; }
        public int Exception { get; set; }
        public ItemsBookingResponse Items { get; set; }
        public double AmountTotal { get; set; }
        public bool IsConverted { get; set; }
        public DateTime ExpirationDate { get; set; }
        public double OldTotalAmount { get; set; }
        public bool SendNotificationg { get; set; }
        public string? ExceptionMessage { get; set; }
        public string? StackTrace { get; set; }
        public List<ItemLocatorDetails>? ItemLocatorDetails { get; set; }


        public CreateBookingResponse()
        {
            ItemLocatorDetails = new List<ItemLocatorDetails>();
        }

    }

    public class ItemsBookingResponse
    {
        public double ExtrasAmount { get; set; }
        public double TotalAmount { get; set; }
    }


    public class ItemLocatorDetails
    {
        public string? ItemLocatorId { get; set; }
        public string? RecordLocator { get; set; }
        public int Engine { get; set; }

    }
}
