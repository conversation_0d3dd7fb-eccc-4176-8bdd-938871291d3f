using TBFront.Interfaces;
using TBFront.Infrastructure.HttpService.BlacklistBookingService.Dtos;

namespace TBFront.Infrastructure.HttpService.BlacklistBookingService
{
    public static class BlinkServiceRegister
    {
        public static void AddBlacklistServiceRegister(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddHttpClient<BlinkService>("");

            services.AddSingleton(s => configuration.GetSection("ItineraryConfiguration").Get<BlacklistBookingConfiguration>());

             services.AddSingleton<IBlinkService, BlinkService>();
        }
    }
}
