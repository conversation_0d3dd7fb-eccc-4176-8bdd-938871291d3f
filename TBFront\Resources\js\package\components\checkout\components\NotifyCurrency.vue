<template>
    <div class="card my-3 card-exchange flex-row gap-1" v-if="getExchange.base != getExchange.currency ">
        <i class="icon icon-hand-holding-circle-dollar font-18"></i>
        <p class="font-14 mb-0">
            {{ __('checkout.note_charge', [ getExchange.base, getExchange.currency ]) }} <br> <span class="fw-500">{{ __('checkout.pay_will_be') }} <currency-display :amount="getTotalAmount" :showCurrencyCode="true" :applyDecimals="true" :currencyCode="getExchange.base" :applyConvertion="false" :applySymbol="true"></currency-display></span>
        </p>
    </div>
</template>

<script>
import { storeToRefs } from 'pinia';
import { useCheckoutStore } from "../../../stores/checkout";
import { useExchangeRateStore } from "../../../stores/exchange";
import CurrencyDisplay from '../../common/CurrencyDisplay.vue';

export default {
    data() {
        return {};
    },
    setup() {
        const exchangeStore = useExchangeRateStore();
        const checkoutStore = useCheckoutStore();

        const { getTotalAmount } = storeToRefs(checkoutStore);
        const { getQuote } = storeToRefs(checkoutStore);
        const { getExchange } = storeToRefs(exchangeStore);

        return {
            getTotalAmount, getQuote, getExchange, 
        }
    },
    async mounted() {
    },
    methods: {

    },
    components: {
        CurrencyDisplay
    }
}
</script>

<style>
.card-exchange {
    padding: 20px 16px;
    border-radius: 0px 0px 4px 4px;
    border: 1px solid var(--border-subtle);
    background-color: var(--bg-base);
    color: var(--text-main);
}

.fw-500 {
    font-weight: 500;
}


</style>