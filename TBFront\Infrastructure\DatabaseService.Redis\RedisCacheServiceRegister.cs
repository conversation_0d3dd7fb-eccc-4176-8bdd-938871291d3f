﻿using StackExchange.Redis;
using TBFront.Infrastructure.DatabaseService.Redis.Dtos;
using TBFront.Interfaces;

namespace TBFront.Infrastructure.DatabaseService.Redis
{
    public static class RedisCacheServicesRegister
    {
        public static void AddRedisCacheDependencies(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddSingleton(s => configuration.GetSection("RedisCache:Configuration").Get<RedisCacheConfiguration>());

            services.AddSingleton<ICacheService, RedisCacheService>();

            services.AddStackExchangeRedisCache(options =>
            {
                options.InstanceName = $"RedisFlightAPI_";
                options.ConfigurationOptions = new ConfigurationOptions()
                {
                    EndPoints = { configuration.GetValue<string>("RedisCache:Configuration:HostContent"), configuration.GetValue<string>("RedisCache:Configuration:Port") },
                    ConnectRetry = 0,
                    ConnectTimeout = configuration.GetValue<int>("RedisCache:Configuration:ConnectTimeout"),
                    SyncTimeout = configuration.GetValue<int>("RedisCache:Configuration:ConnectTimeout"),
                };

            });
        }
    }
}
