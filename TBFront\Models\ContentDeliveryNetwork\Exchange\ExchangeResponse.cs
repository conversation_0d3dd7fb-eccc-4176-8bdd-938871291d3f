﻿using ProtoBuf;

namespace TBFront.Models.ContentDeliveryNetwork.Exchange
{
    [ProtoContract]
    public class ExchangeResponse
    {
        [ProtoMember(1)]
        public DateTime Date { get; set; }
        [ProtoMember(2)]
        public List<ExchangeRate> Currencies { get; set; } = new List<ExchangeRate>();
    }
    [ProtoContract]
    public class ExchangeRate
    {
        [ProtoMember(1)]
        public string Source { get; set; } = string.Empty;
        [ProtoMember(2)]
        public Dictionary<string, decimal> Quotes { get; set; } = new Dictionary<string, decimal>();
    }
    public class ExchangeClient
    {
        public string Currency { get; set; } = string.Empty;
        public string Base { get; set; } = string.Empty;
        public decimal Rate { get; set; }
    }

}