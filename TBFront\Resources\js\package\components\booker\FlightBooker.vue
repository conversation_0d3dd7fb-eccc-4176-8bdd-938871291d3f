<template>
    <div class="card booker px-0 mt-lg-5 bg-booker mt-md-3">
        <div v-if="!isHomeLanding" class="card-header search-mobile-content d-lg-none" @click="toogleBookerForm()">
            <div class="row">
                <div class="col-10 pr-0 font-14">
                    <div class="font-14 c-info-filght-xs">
                        <span class="text-white f-light">{{ userSelectionStore.startingFromAirport || "S/R" }}</span>
                        <span class="text-white f-light"> | </span>
                        <span class="text-white f-light">{{ userSelectionStore.returningFromAirport || "S/R" }}</span>
                    </div>
                    <div class="font-14 c-info-filght-xs">
                        <span class="text-white icon icon-date font-20  me-1"></span>
                        <span class="text-white f-light" id="startDateText">
                            {{$filters.date(userSelectionStore.startingFromDateTime, "DD MMM YYYY")}}
                        </span>
                        <span class="text-white f-light mx-2" v-if="bookerStore.flightType === roundTrip">|</span>
                        <span class="text-white f-light"
                              id="endDateText" v-if="bookerStore.flightType === roundTrip">
                            {{$filters.date(userSelectionStore.returningFromDateTime, "DD MMM YYYY")}}
                        </span>
                        <span class="text-white icon icon-person-black font-20  ms-3"></span>
                        <span class="text-white f-light ms-2" id="total-person-display">{{userSelectionStore.adults + userSelectionStore.ageKids.length}}</span>
                    </div>
                </div>
                <div class="col-2 px-0 d-flex flex-row justify-content-center align-items-center">
                    <div id="SearchIcon" class="c-search icon icon-search text-white font-20 d-inline-block ml-2"></div>
                </div>
            </div>
        </div>
        <div class="card-body d-lg-block px-3 mx-3 px-md-0 pb-md-0 pt-3 bg-white c-booker mx-md-3" :class="{'d-none': !showBookerForm && !isHomeLanding}">
            <div class="row">
                <div class="mb-0">
                    <div class="form-check form-check-inline" @click.stop.prevent="changeToggle(oneWay)">
                        <span class="custom-radio">
                            <input type="radio" autcomplete="off" checked="checked" class="shadow-none" name="flight-type" id="flight-oneway-checkbox" @change="datepicker('home')" :value="oneWay" v-model="bookerStore.flightType" />
                            <label for="flight-oneway-checkbox" class="flight-item-families__item-form-label">
                            </label>
                        </span>
                        <span class="form-check-label" for="flight-oneway">{{__('booker.oneway')}}</span>
                    </div>
                    <div class="form-check form-check-inline" @click.stop.prevent="changeToggle(roundTrip)">
                        <span class="custom-radio">
                            <input type="radio" autcomplete="off" class="shadow-none" name="flight-type" id="flight-roundtrip-checkbox" @change="datepicker('home')" :value="roundTrip" v-model="bookerStore.flightType" />
                            <label for="flight-roundtrip-checkbox" class="flight-item-families__item-form-label">
                            </label>
                        </span>
                        <span class="form-check-label" for="flight-roundtrip">{{__('booker.roundtrip')}}</span>

                    </div>
                </div>
                <div class="mb-2 mb-md-0 position-relative col-12 col-lg-3 pe-md-2">
                    <label class="ms-4 ps-0 font-12" for="origin">{{__('booker.origin')}} </label>

                    <div class="input-group mb-1 mb-md-0" :class="{'border-danger': submitted && errors.startingAirport && !errors.startingAirport.isValid }">
                        <span class="input-group-text bg-transparent border-0 rounded-0 ps-0 pe-1 pt-0">
                            <i class="icon icon-flight-takeoff font-20 text-secondary"></i>
                        </span>
                        <input type="text"
                               id="origin"
                               ref="anyName"
                               autocomplete="off"
                               class="form-control input-booker border-0 shadow-none rounded-0 px-0 suggestion-input-starting pt-0"
                               @input="getSuggestions($event.target.value, 'startingAirport')"
                               @focus="getIsMobile() ? $event.target.blur() : $event.target.select()"
                               v-model="bookerStore.startingAirportText"
                               @click="()=>{ showOrHide('startingAirportSuggestions', true); showOrHide('startingAirportSelector', true); }" />
                    </div>
                    <small class="text-danger" v-if="submitted && errors.startingAirport && !errors.startingAirport.isValid">{{errors.startingAirport.message}}</small>
                    <div class="d-block d-md-none place-mobile bg-white" v-if="isOpen.startingAirportSelector">
                        <div>
                            <div class="container-fluid pt-4 pb-2 ps-4 d-flex justify-content-end align-items-center">
                                <!--<p class="h5 mb-0">{{__('booker.choose_your_origin')}}</p>-->
                                <button class="btn icon-main text-decoration-none" type="button" @click="showOrHide('startingAirportSelector', false)">
                                    <i class="icon icon-close"></i>
                                </button>
                            </div>
                        </div>
                        <div class="container-fluid pt-2">
                            <!--<label for="origin-mobile">{{__('booker.origin')}}: </label>-->
                            <div class="input-group mb-1 mb-md-0 border-0 position-relative ci-mobile" :class="{'border-danger': submitted && errors.startingAirport && !errors.startingAirport.isValid }">
                                <span class="input-group-text bg-transparent border-0 rounded-0 ps-0 pe-1 pt-0">
                                    <i class="font-icons icons-plane-departure text-secondary"></i>
                                </span>
                                <span class="icon icon-search"></span>
                                <input type="text"
                                       placeholder="Cual es tu origen?"
                                       id="origin-mobile"
                                       autocomplete="off"
                                       @focus="cleanInput('start')"
                                       class="form-control input-booker shadow-none rounded px-4 py-2 suggestion-input-starting i-mobile"
                                       @input="getSuggestions($event.target.value, 'startingAirport')"
                                       v-model="bookerStore.startingAirportText"
                                       @click="showOrHide('startingAirportSuggestions', true)" />
                                <span class="icon icon-close" @click.stop.prevent="cleanInput('start')"></span>
                            </div>
                            <FlightSuggestions v-model="bookerStore.startingAirport"
                                               v-if="isOpen.startingAirportSuggestions"
                                               :isOpen="isOpen.startingAirportSuggestions"
                                               @toggle="(value) => { showOrHide('startingAirportSuggestions', value); showOrHide('startingAirportSelector', value); }"
                                               :suggestions="suggestions.startingAirport"
                                               :history="bookerStore.history.from"
                                               :moreDestinations="suggestions.mostSoughtOrigins"
                                               @changeText="(text, value, position)=>{bookerStore.changeSelectedSuggestion('startingAirport', value, position); bookerStore.startingAirportText=text;}"
                                               input="suggestion-input-starting">
                            </FlightSuggestions>
                        </div>
                    </div>
                    <FlightSuggestions class="position-absolute shadow"
                                       v-model="bookerStore.startingAirport"
                                       v-if="isOpen.startingAirportSuggestions"
                                       :isOpen="isOpen.startingAirportSuggestions"
                                       @toggle="(value) => { showOrHide('startingAirportSuggestions', value); showOrHide('startingAirportSelector', value); }"
                                       :suggestions="suggestions.startingAirport"
                                       :history="bookerStore.history.from"
                                       :moreDestinations="suggestions.mostSoughtOrigins"
                                       @changeText="(text, value, position)=>{bookerStore.changeSelectedSuggestion('startingAirport', value, position); bookerStore.startingAirportText=text;}"
                                       input="suggestion-input-starting">
                    </FlightSuggestions>
                </div>
                <div class="mb-2 mb-md-0 col-12 col-lg-3 px-md-2">
                    <label class="ms-4 ps-0 font-12" for="destination">{{__('booker.destination')}} </label>
                    <div class="input-group mb-1 mb-md-0" :class="{'border-danger': submitted && errors.returningAirport && !errors.returningAirport.isValid }">
                        <span class="input-group-text bg-transparent border-0 rounded-0 ps-0 pe-1 pt-0">
                            <i class="icon icon-flight-land font-20 text-secondary"></i>
                        </span>                        
                        <input type="text"
                               id="destination"
                               placeholder="Cual es tu destino?"
                               autocomplete="off"
                               class="form-control input-booker border-0 shadow-none rounded-0 px-0 suggestion-input-returning pt-0"
                               @input="getSuggestions($event.target.value, 'returningAirport')"
                               @focus="getIsMobile() ? $event.target.blur() : $event.target.select()"
                               v-model="bookerStore.returningAirportText"
                               @click="()=>{ showOrHide('returningAirportSuggestions', true); showOrHide('returningAirportSelector', true); }" />
                        
                    </div>
                    <small id="InputDestinationError" class="text-danger" v-if="submitted && errors.returningAirport && !errors.returningAirport.isValid">{{errors.returningAirport.message}}</small>
                    <div class="d-block d-md-none place-mobile bg-white" v-if="isOpen.returningAirportSelector">
                        <div class="">
                            <div class="container-fluid pt-4 pb-2 ps-4 d-flex justify-content-end align-items-center">
                                <!--<p class="h5 mb-0">{{__('booker.choose_your_destination')}}</p>-->
                                <button class="btn icon-main text-decoration-none" type="button" @click="showOrHide('returningAirportSelector', false)">
                                    <i class="icon icon-close"></i>
                                </button>
                            </div>
                        </div>
                        <div class="container-fluid pt-2">
                            <!--<label for="destination-mobile">{{__('booker.destination')}} </label>-->
                            <div class="input-group mb-1 mb-md-0 border-0 position-relative ci-mobile" :class="{'border-danger': submitted && errors.returningAirport && !errors.returningAirport.isValid }">
                                <span class="input-group-text bg-transparent border-0 rounded-0 ps-0 pe-1 pt-0">
                                    <i class="font-icons icons-plane-departure text-secondary"></i>
                                </span>
                                <span class="icon icon-search"></span>
                                <input type="text"
                                       placeholder="Cual es tu destino?"
                                       id="destination-mobile"
                                       autocomplete="off"
                                       @focus="cleanInput('return')"
                                       class="form-control input-booker shadow-none rounded px-4 py-2 suggestion-input-returning i-mobile"
                                       @input="getSuggestions($event.target.value, 'returningAirport')"
                                       v-model="bookerStore.returningAirportText"
                                       @click="()=>{ showOrHide('returningAirportSuggestions', true); showOrHide('returningAirportSelector', true); }" />
                                <span class="icon icon-close" @click.stop.prevent="cleanInput('return')"></span>
                            </div>
                            <FlightSuggestions v-model="bookerStore.returningAirport"
                                               v-if="isOpen.returningAirportSuggestions"
                                               :isOpen="isOpen.returningAirportSuggestions"
                                               @toggle="(value) => { showOrHide('returningAirportSuggestions', value); showOrHide('returningAirportSelector', value); }"
                                               :suggestions="suggestions.returningAirport"
                                               :history="bookerStore.history.to"
                                               :moreDestinations="suggestions.mostSoughtDestinations"
                                               @changeText="(text, value, position)=>{bookerStore.changeSelectedSuggestion('returningAirport', value, position); bookerStore.returningAirportText=text;}"
                                               input="suggestion-input-returning">
                            </FlightSuggestions>
                        </div>
                    </div>
                    <FlightSuggestions class="position-absolute shadow"
                                       v-model="bookerStore.returningAirport"
                                       v-if="isOpen.returningAirportSuggestions"
                                       :isOpen="isOpen.returningAirportSuggestions"
                                       @toggle="(value) => { showOrHide('returningAirportSuggestions', value); showOrHide('returningAirportSelector', value); }"
                                       :suggestions="suggestions.returningAirport"
                                       :history="bookerStore.history.to"
                                       :moreDestinations="suggestions.mostSoughtDestinations"
                                       @changeText="(text, value, position)=>{bookerStore.changeSelectedSuggestion('returningAirport', value, position); bookerStore.returningAirportText=text;}"
                                       input="suggestion-input-returning">
                    </FlightSuggestions>
                </div>
                <div :class="`${isList ? 'col-lg-3' : 'col-lg-2 c-lg-input-1'}`" class="mb-2 mb-md-0 col-12 px-md-2">
                    <div class="w-100">
                        <!-- <div class="row"> -->
                        <label class="ms-4 ps-0 font-12" for="departure-date">{{ bookerStore.flightType !== oneWay ? __('booker.availableDates') : __('booker.dateAvailable')}} </label>
                        <div id="calendarContainer" class="input-group mb-3 mb-md-0">
                        <span class="input-group-text bg-transparent border-0 rounded-0 ps-0 pe-1 pt-0">
                            <i class="icon icon-event font-20 text-secondary"></i>
                        </span>
                            <input type="text" required readonly id="calendar-checkIn-flight" name="calendar-checkIn-flight"
                                   class="form-control input-booker border-0 shadow-none rounded-0 px-0 cursor-pointer open-datepicker form-control-plaintext pt-0"
                                   :value="$filters.date(bookerStore.dates.start, 'DD MMM. YYYY') + (bookerStore.flightType !== oneWay ? ' - ' + $filters.date(bookerStore.dates.end, 'DD MMM. YYYY') : '')" />
                        </div>
                        <!-- </div> -->
                    </div>
                </div>
                <div :class="`${isList ? '' : 'c-lg-input-2'}`" class="mb-2 mb-md-0 col-12 col-lg-2 px-md-2">
                    <div w-100>
                        <label class="ms-4 ps-0 font-12" for="passengers">{{__('booker.passengers')}} </label>
                        <div class="input-group mb-0 mb-md-0 pax-input" :class="{'border-danger': (submitted || paxSelectorHasBeenClosed) && errors.ageKids && !errors.ageKids.isValid}" @click="togglePaxSelector()">
                        <span class="input-group-text bg-transparent border-0 rounded-0 px-1 pax-input pt-0">
                            <i class="icon icon-people font-20 text-secondary pax-input"></i>
                        </span>
                            <input type="text" id="passengers" class="form-control input-booker border-0 shadow-none rounded-0 px-0 cursor-pointer pax-input pt-0" :value="paxText" readonly />
                        </div>
                        <small class="text-danger" v-if="(submitted || paxSelectorHasBeenClosed) && errors.ageKids && !errors.ageKids.isValid">{{errors.ageKids.message}}</small>
                        <div v-if="isOpen.paxSelector" class="search-paxes bg-white row shadow pax " :class="{'d-block': isOpen.paxSelector, 'd-none': !isOpen.paxSelector}">
                            <div class="col-12 pax">
                                <div class="paxes-header pt-3 pax">
                                    <div class="d-flex align-items-center justify-content-between pax d-md-none">
                                        <p class="h5 pax">{{__('booker.choose_your_passengers')}}</p>
                                        <button class="btn icon-main text-decoration-none pax" type="button" @click="hidePaxSelector()"><i class="icon icon-close font-30"></i></button>
                                    </div>
                                </div>
                                <div class="paxes-body pax">
                                    <div class="pax-selector d-flex justify-content-between py-3 pax">
                                        <div class="description pax">
                                            <p class="mb-0 pax">{{__('booker.adults')}}: </p>

                                        </div>
                                        <div class="pax-buttons d-flex align-items-center gap-2 justify-content-end pax">
                                            <button class="btn btn-outline-secondary rounded-circle control shadow-tb border-0 pax d-flex align-items-center" type="button" @click="bookerStore.changePaxes('adults', -1)">
                                                <span class="font-24 pax">-</span>
                                            </button>
                                            <span class="qty pax">{{bookerStore.paxes.adults}}</span>
                                            <button class="btn btn-outline-secondary rounded-circle control shadow-tb border-0 pax d-flex align-items-center" type="button" @click="bookerStore.changePaxes('adults', 1)">
                                                <span class="font-20 pax">+</span>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="pax-selector d-flex justify-content-between py-3 pax">
                                        <div class="description pax">
                                            <p class="mb-0 pax">{{__('booker.children')}}: </p>

                                        </div>
                                        <div class="pax-buttons d-flex align-items-center gap-2 justify-content-end pax">
                                            <button class="btn btn-outline-secondary rounded-circle control shadow-tb border-0 pax d-flex align-items-center" type="button" @click="bookerStore.changePaxes('kids', -1)">
                                                <span class="font-24 pax">-</span>
                                            </button>
                                            <span class="qty pax">{{bookerStore.paxes.kids}}</span>
                                            <button class="btn btn-outline-secondary rounded-circle control shadow-tb border-0 pax d-flex align-items-center" type="button" @click="bookerStore.changePaxes('kids', 1)">
                                                <span class="font-20 pax">+</span>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="pax-selector pb-3 pax" v-if="bookerStore.paxes.kids">
                                        <label class="form-label col-12 pax">{{__('booker.ageLabel')}}</label>
                                        <div class="kid-age row g-2 align-items-center mb-2 pax" v-for="(kid, index) in bookerStore.paxes.kids" :class="{'is-invalid': !bookerStore.paxes.ageKids[index] && paxSelectorHasBeenClosed}">
                                            <select :id="`cMinors_${index}`" class="form-select shadow-none col-6 pax" v-model="bookerStore.paxes.ageKids[index]" :class="{'is-invalid': !bookerStore.paxes.ageKids[index] && (paxSelectorHasBeenClosed || submitted)}">
                                                <option class="pax" :value="undefined" disabled selected hidden>{{__('booker.child')}} {{index + 1}}:</option>
                                                <option :id="`cList_${index}`" class="pax" v-for="n in ageKids" :value="n">                                                    
                                                    {{ n === 1 || n === 0 ? __('booker.year_old') : n + ' ' + __('booker.years_old') }}
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="pax-selector pb-3 pax" v-if="bookerStore.errorPaxes">
                                        <!--<small class="text-danger">{{__('booker.errorPaxes')}}</small>-->
                                        <div class="py-2 px-3 c-message-full rounded font-12">
                                            <span class="icon icon-info me-1 font-16"></span>
                                            <span>Máximo 9 pasajeros por reserva.</span>
                                            <p class="mb-0">Para agregar más pasajeros haz otra reserva o comunícate con atención a cliente.</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="paxes-footer border-top py-2 d-grid gap-2 mx-auto">
                                    <button class="btn btn-primary text-white" type="button" @click="hidePaxSelector()">{{__('booker.ok')}}</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div :class="`${isList ? 'col-lg-1 pe-lg-0 ps-lg-0' : 'col-lg-2 ps-md-1 pe-md-3'}`" class="col-12 d-flex align-items-center pt-md-4 pt-lg-0 justify-content-center">
                    <!-- <div id="searchButton" class="c-search icon icon-search text-white font-20 d-inline-block ml-2"></div>-->
                    <!--<button id="searchButton" class="btn btn-booker text-white d-block w-100 py-3 py-md-2 py-lg-3 f-p-medium font-14 h6 mb-0 mb-md-3" :class="airline" type="button" @click="sendBookerRequest()">{{__('booker.search') }}</button>-->
                    <!--boton para desktop-->
                    <template v-if="isList">
                        <button aria-label="realizar búsqueda" id="searchButton" class="d-none d-lg-block btn btn-booker rounded-circle c-search icon icon-search text-white d-block font-weight-bold p-3 f-p-medium font-24 h6 mb-0 mb-md-3" :class="airline" type="button" @click="sendBookerRequest()"></button>
                    </template>
                    <!--boton para mobile-->
                    <button aria-label="realizar búsqueda" id="searchButton"  :class="airline + `${isList ? ' d-lg-none': ''}`"  class=" btn btn-booker text-white d-block w-100 py-3 py-md-2 py-lg-3 f-p-medium font-14 h6 mb-0 mb-md-3" type="button" @click="sendBookerRequest()">{{__('booker.search') }}</button>
                    
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
    import { computed, onBeforeMount, onMounted, ref } from 'vue';
    import { storeToRefs } from 'pinia';
    import FlightSuggestions from './FlightSuggestions';
    import { isDate } from 'lodash';
    import { oneWay, roundTrip } from '../../../constants';
    import { requestWithoutCors } from '../../../utils/http/service';
    import { datepicker } from '../../../utils/helpers/calendar';
    import { getUserKey } from '../../../utils/helpers/usercom';
    import { __ } from '../../../utils/helpers/translate';
    import { useBookerStore } from '../../stores/booker';
    import { useFlightStore } from '../../stores/flight';
    import { useLoaderPageStore } from '../../stores/loader-page';

    import { distinctTo, isRequired, validateAges } from '../../../utils/helpers/validator';
    import { useUserSelectionStore } from '../../stores/user-selection';
    import { StorageService } from '../../../utils/helpers/storage';
    import { hidenCloak } from '../../../utils/helpers/animates';
    import { Booker } from '../../../utils/analytics/bookers.js';

    defineProps({
        isHomeLanding: Boolean,
        airline: String,
    })
    const bookerStore = useBookerStore();
    const userSelectionStore = useUserSelectionStore();
    const { showLoaderPage } = useLoaderPageStore();

    const { setFfirstLoad2 } = useFlightStore();
    const { setTypeFlight, setStartingAirportText, setReturningAirportText } = useBookerStore();
    const { getExtraInformation } = storeToRefs(bookerStore);
    const siteSettings = window.__pt.settings.site;
    const culture = window.__pt.cultureData;
    const submitted = ref(false);
    const paxSelectorHasBeenClosed = ref(false);

    const errors = ref({
        showError: false,
        startingAirport: {},
        returningAirport: {},
        departureDate: {},
        returnDate: {},
        adults: {},
        kids: {},
        ageKids: [],
    });

    const suggestions = ref({
        startingAirport: [],
        returningAirport: [],
        mostSoughtOrigins: [],
        mostSoughtDestinations: [],
    });

    const isOpen = ref({
        startingAirportSelector: false,
        returningAirportSelector: false,
        startingAirportSuggestions: false,
        returningAirportSuggestions: false,
        paxSelector: false
    });

    const showBookerForm = ref(false);
    const disableBookerForm = ref(true);
    const isList = ref(window.__pt.settings && window.__pt.settings.page == "list");


    const paxText = computed(() => {
        return `${bookerStore.paxes.adults} ${bookerStore.paxes.adults > 1 ? __('booker.adults') : __('booker.adult')}${bookerStore.paxes.kids > 0 ? `, ${bookerStore.paxes.kids} ${bookerStore.paxes.kids > 1 ? __('booker.children') : __('booker.child')}` : ""}`;
    });

    const ageKids = computed(() => {
        const ages = [];
        ages.push(1);
        for (let i = siteSettings.booker.kidsAge.min; i <= siteSettings.booker.kidsAge.max; i++) {
            ages.push(i);
        }
        console.log(ages);
        return ages;
    });

    const togglePaxSelector = () => {
        isOpen.value.paxSelector = !isOpen.value.paxSelector;
    };

    const changeToggle = (type) => {
        setTypeFlight(type);
        datepicker('home');
    }

    const toogleBookerForm = () => {
        showBookerForm.value = !showBookerForm.value;
        disableBookerForm.value = false;
    };

    const hidePaxSelector = () => {
        paxSelectorHasBeenClosed.value = true;
        if (validateAgeKids()) {
            isOpen.value.paxSelector = false;
        }
    };
    const showOrHide = async (attr, value) => {
        isOpen.value[attr] = value;
        if (attr == 'startingAirportSuggestions') {
            suggestions.value.mostSoughtOrigins = await requestSuggestions("", true, "");
            setTimeout(() => {
                document.getElementById("origin-mobile") && document.getElementById("origin-mobile").focus();
            }, "200");
        }
        if (attr == 'returningAirportSuggestions') {
            suggestions.value.mostSoughtDestinations = await requestSuggestions("", true, bookerStore.startingAirport);
            setTimeout(() => {
                document.getElementById("destination-mobile") && document.getElementById("destination-mobile").focus();
            }, "200");
        }
    };

    const sendBookerRequest = () => {
        const isValid = validate();
        submitted.value = true;
        if (isValid) {
            if (Object.keys(getExtraInformation.value).length > 0) {
                Booker.setDataLayerHistory(bookerStore, getExtraInformation.value);
            }
            toogleBookerForm()
            showLoaderPage();
            bookerStore.submitForm();
        }
    };

    const cleanInput = (input) => {
        switch (input) {
            case "start":
                setStartingAirportText();
                break;
            default:
                setReturningAirportText();
                break;
        }
    }

    const validate = () => {
        errors.value = { showErrors: true };

        if (!isRequired(bookerStore.startingAirport)) {
            errors.value.startingAirport = { isValid: false, message: __('errors.required') };
        }

        if (!isRequired(bookerStore.returningAirport)) {
            errors.value.returningAirport = { isValid: false, message: __('errors.required') };
        } else if (!distinctTo(bookerStore.returningAirport, bookerStore.startingAirport)) {
            errors.value.returningAirport = { isValid: false, message: __('errors.distinctTo').replace('{0}', __('booker.origin').toLowerCase()).replace('{1}', __('booker.destination').toLowerCase()) };
        }

        if (!isDate(bookerStore.dates.start)) {
            errors.value.departureDate = { isValid: false, message: __('errors.required') };
        }

        if (bookerStore.flightType === roundTrip && !isDate(bookerStore.dates.end)) {
            errors.value.returnDate = { isValid: false, message: __('errors.required') };
        }

        if (bookerStore.paxes.kids) {
            validateAgeKids();
        }

        return !JSON.stringify(errors.value).includes('"isValid":false');
    };

    const validateAgeKids = () => {
        delete errors.value.ageKids;
        if (!validateAges(bookerStore.paxes.ageKids, bookerStore.paxes.kids)) {
            errors.value.ageKids = { isValid: false, message: __('errors.ageKids') };
            if (!isOpen.value.paxSelector) {
                isOpen.value.paxSelector = true;
            }
            return false;
        }
        return true;
    };

    const getSuggestions = async (query, type) => {
        if (query && query.length >= 3) {

            const response = await requestSuggestions(query);
            if (response) {
                suggestions.value[type] = response;
            }

            if (!isOpen.value[`${type}Suggestions`]) {
                isOpen.value[`${type}Suggestions`] = true;
            }
        } else {
            suggestions.value[type] = [];
            bookerStore.removeAirportSelected(type);
        }
    };

    const requestSuggestions = async (query, recommendation = false, origin = "") => {
        let tags = window.__pt.settings.site.mobile ? "web_mobile, list" : "web_desktop, list";
        let params = {};
        if (origin == "") {
            params = {
                query,
                analyticsTags: tags,
                from: 0,
                language: siteSettings.language,
                placeTypes: siteSettings.booker.placeTypes,
                site: siteSettings.booker.algoliaSiteName,
                size: siteSettings.booker.autocompleteItems,
                usersearch: getUserKey(),
                recommendation,
            };
        } else {
            params = {
                query,
                analyticsTags: tags,
                from: 0,
                language: siteSettings.language,
                placeTypes: siteSettings.booker.placeTypes,
                site: siteSettings.booker.algoliaSiteName,
                size: siteSettings.booker.autocompleteItems,
                usersearch: getUserKey(),
                recommendation,
                origin,
            };
        }
        return await requestWithoutCors({ method: "get", fullUri: siteSettings.booker.serviceUrl + siteSettings.booker.servicePath }, params);
    }

    const isMobileLandScape = () => {
        return siteSettings.isMobileLandScape();
    }

    const getIsMobile = () => {
        return siteSettings.isMobile();
    }

    const setHistorySearches = () => {
        const storedHistory = StorageService.get(siteSettings.booker.historyStorageKey);
        if (storedHistory) {
            if (storedHistory.from && storedHistory.from.length) {
                bookerStore.history.from = storedHistory.from;
            }

            if (storedHistory.to && storedHistory.to.length) {
                bookerStore.history.to = storedHistory.to;
            }
        }

        const storedRecentResearches = StorageService.get(siteSettings.booker.historyResearchStorageKey);
        if (storedRecentResearches) {
            if (storedRecentResearches.from && storedRecentResearches.from.length) {
                bookerStore.recentResearches.from = storedRecentResearches.from;
            }

            if (storedRecentResearches.to && storedRecentResearches.to.length) {
                bookerStore.recentResearches.to = storedRecentResearches.to;
            }
        }
    }

    /**onBeforeMount(() => {
        const paramsFlight = window.__pt.data || null;
        const lastSearchForBooker = StorageService.get(siteSettings.booker.historyResearchStorageKey);
        if (paramsFlight) {
            userSelectionStore.initUserSelection(paramsFlight, lastSearchForBooker);
        }
    });**/

    window.addEventListener('pageshow', (event) => {
        const paramsFlight = window.__pt.data || null;
        const lastSearchForBooker = StorageService.get(siteSettings.booker.historyResearchStorageKey);
        if (paramsFlight) {
            userSelectionStore.initUserSelection(paramsFlight, lastSearchForBooker);
        }

        hidenCloak();
        document.body.addEventListener('click', $event => {
            if (!$event.target.classList.contains("pax") && !$event.target.classList.contains("pax-input") && !$event.target.classList.contains('btn-primary')) {
                showOrHide('paxSelector', false);
            }
        });
        setHistorySearches();
        datepicker("home");
    });

    /**onMounted(() => {
        hidenCloak();
        document.body.addEventListener('click', $event => {
            if (!$event.target.classList.contains("pax") && !$event.target.classList.contains("pax-input") && !$event.target.classList.contains('btn-primary')) {
                showOrHide('paxSelector', false);
            }
        });
        setHistorySearches();
        datepicker("home");
    });**/



</script>

<style lang="scss" scoped>

.kid-age {
    padding: 0 5px;
}

    .c-lg-input-1 {
        @media (min-width: 1280px) {
            width: 18.66666667% !important;            
        }        
    }
    .c-lg-input-2 {
        @media (min-width: 1280px) {
            width: 14.66666667% !important;            
        }        
    }



    .bg-booker {
        @media (min-width: 768px) and (max-width: 990px), (min-width: 1280px), (min-width: 991px) {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);            
        }
        .card-body {
            @media (max-width: 767px) {
                box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            }
        }    
    }
  
    .ci-mobile .icon-search {
        color: #dadce0 !important;
        position: absolute;
        z-index: 50;
        top: 0;
        bottom: 0;
        margin: auto;
        height: 14px;
        left: 12px;            
    }
    .ci-mobile .icon-close {
        color: #dadce0 !important;
        position: absolute;
        z-index: 50;
        top: 0;
        bottom: 0;
        margin: auto;
        height: 14px;
        right: 9px;
        font-size: 16px;            
    }   

    .i-mobile {
        height: 50px;
        &:focus-visible {
            outline: var(--border-info) auto 1px;
        }
    }
    .c-booker {
        .input-group {
            input {
                font-size: 1rem;
            }
        }        
    }
    .btn-booker {
        color: white;
        background:var(--bg-primary);

        &:hover {
            color: white;
            background: var(--bg-primary-hover);
        }

        &.avianca {
            background: #d41520;

            &:hover {
                background: #d41520;
                color: white;
            }
        }

        &.copa {
            background: #12416e;

            &:hover {
                background: #12416e;
                color: white;
            }
        }

        &.latam {
            background: #4303d2;

            &:hover {
                background: #4303d2;
                color: white;
            }
        }

        &.iberia {
            background: #d9272e;

            &:hover {
                background: #d9272e;
                color: white;
            }
        }

        &.airfrance {
            background: #131d41;

            &:hover {
                background: #131d41;
                color: white;
            }
        }

        &.aeromexico {
            background: #003365;

            &:hover {
                background: #003365;
                color: white;
            }
        }

        &.united {
            background: #039;

            &:hover {
                background: #039;
                color: white;
            }
        }

        &.continental {
            background: #005a9f;

            &:hover {
                background: #005a9f;
                color: white;
            }
        }

        &.american {
            background: #02abe4;

            &:hover {
                background: #02abe4;
                color: white;
            }
        }

        &.aircanada {
            background: #d9272e;

            &:hover {
                background: #d9272e;
                color: white;
            }
        }

        &.jetblue {
            background: #203c73;

            &:hover {
                background: #203c73;
                color: white;
            }
        }

        &.satena {
            background: #11304c;

            &:hover {
                background: #11304c;
                color: white;
            }
        }

        &.clicair {
            background: #003c66;

            &:hover {
                background: #003c66;
                color: white;
            }
        }

        &.vivaaerobus {
            background: #004a23;

            &:hover {
                background: #004a23;
                color: white;
            }
        }

        &.volaris {
            background: #a12885;

            &:hover {
                background: #a12885;
                color: white;
            }
        }

        &.wingo {
            background: #321b74;

            &:hover {
                background: #321b74;
                color: white;
            }
        }

        &.jetsmart {
            background: #11304c;

            &:hover {
                background: #11304c;
                color: white;
            }
        }

        &.delta {
            background: #11172b;

            &:hover {
                background: #11172b;
                color: white;
            }
        }
    }

    .autocomplete {
        z-index: 999;
    }

    .row.ng-scope.card.booker {
        margin-bottom: 1rem;
    }

    .clear-button {
        height: 20px;
        width: 20px;
        line-height: 0;

        i {
            font-size: 13px;
        }
    }

    input, .form-control {
        &:focus {
            border-color: inherit;
        }
    }

    .font-icons {
        font-size: 1.5rem;
    }

    .input-group {
        border-bottom: 1px solid #525051;
    }

    label {
        font-size: 14px;
    }

    .search-paxes {
        width: 280px;
        position: absolute;

        @media(max-width: 767px) {
            position: fixed;
            height: 100vh;
            top: 0;
            left: 0;
            right: 0;
            margin: 0;
            width: 100%;
            z-index: 100;
        }

        .paxes-header {
            i {
                @media(max-width: 767px) {
                    position: relative;
                    right: -15px;
                }
            }
        }

        .paxes-body {
            .pax-selector {
                .pax-buttons {
                    .control {
                        width: 33px;
                        height: 33px;
                        display: inline-flex;
                        flex-direction: row;
                        align-items: center;
                        flex-wrap: nowrap;
                        align-content: center;
                        justify-content: center;
                        text-align: center;

                        &:focus {
                            color: #2196f3 !important;
                            background-color: #fff !important;
                        }

                        i {
                            font-size: 24px;
                        }
                    }

                    .pax {
                        span {
                            position: relative;
                            top: -1px;
                            left: -1px;
                        }
                    }
                }
            }
        }
    }

    .place-mobile {
        position: fixed;
        width: 100%;
        height: 100vh;
        top: 0;
        left: 0;
        z-index: 1000;
    }

    .card.shadow.booker {
        border: none;
    }

    i.bi.icons-search.text-secondary.h3 {
        font-size: 30px;
    }

    span.form-check-label {
        bottom: .60rem;
        top: -0.3rem;
        position: relative;
        right: -.3rem;
        cursor: pointer;
        font-family: Roboto-Regular;
    }

    /* Estilo para el radio button */
    .custom-radio {
        position: relative;
        display: inline-block;
        cursor: pointer;
        //margin-right: 15px; /* Espaciado entre radio buttons */
    }

    .custom-radio input[type="radio"] {
        display: none; /* Ocultar el radio button nativo */
    }

    .custom-radio label {
        position: relative;
        display: inline-block;
        width: 1.2em; /* Ancho del radio button */
        height: 1.2em; /* Altura del radio button */
        border-radius: 50%; /* Hacer el radio button circular */
        background-color: #fff;
        border: 1px solid #333132;
        padding: 10px; /* Espaciado entre el borde y el centro */
        transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
        //bottom: -0.7rem;
    }

    /* Estilo para el centro del radio button */
    .custom-radio label::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 80%; /* Ancho del centro */
        height: 80%; /* Altura del centro */
        border-radius: 50%; /* Hacer el centro circular */
        transition: background-color 0.3s ease;
        cursor: pointer;
    }

    /* Estilo cuando el radio button est� seleccionado */
    .custom-radio input[type="radio"]:checked + label {
        background-color: #fff;
        border-color:var(--border-primary);
    }

    .custom-radio input[type="radio"]:checked + label::before {
        background-color: var(--bg-primary);
    }

    .form-check {
        min-height: 1.5rem;
        padding-left: 0;
        margin-bottom: 0.125rem;
    }

    .form-check-inline {
        margin-right: 8rem !important;

        @media(max-width: 767px) {
            &:first-child {
                margin-right: 4rem !important;
            }

            &:nth-of-type(2) {
                margin-right: 0 !important;
            }
        }
    }

    .shadow-tb {
        box-shadow: 0 3px 1px -2px rgb(0 0 0/2%),0 2px 2px 0 rgb(0 0 0/14%),0 1px 5px 0 rgb(0 0 0/12%);
    }

    .control {
        &.pax {
            &:hover {
                color: #2196f3 !important;
                background-color: #fff !important;
            }
        }
    }

    .icon-close {
        font-size: 24px;
    }

    .form-select:focus {
        border-color: #2196f3;
    }

    .btn-outline-secondary {
        color: #2196f3;
    }

    @media (max-width:767px) {
        .card-body {
            .row {
                margin-right: auto !important;
                margin-left: auto !important;
            }
        }
    }
</style>
<script setup lang="ts">
</script>