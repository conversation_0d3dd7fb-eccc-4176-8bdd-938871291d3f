<template>
	<div id="cBarInfloFlights" class="cb-flights bg-white p-3 d-none d-md-block pt-2 pb-4">
		<div id="barLine" class="">
		</div>
		<div id="bottomLine" class=""></div>
		<div class="container pt-2">
			<div class="row">
				<div class="col-5 border-right">
					<div id="vueloIda" class="row">
						<div class="col-2 d-flex align-items-center justify-content-center">
							<img class="w-100" src="/assets-tb/img/logos/logo-viva.svg" />
						</div>
						<div class="col-10 px-0">
							<div class="col-12 px-1">
								<strong>Ida:</strong> Bogota - Medellin (MDE)
							</div>
							<div class="col-12 px-1">
								<span>11:50</span>   <span class="icon icon-plane-right"></span>  <span>12:48 </span>
								<span class="mx-1">|</span>   <span class="cursor-pointer color-black-light">Directo </span>  <span class="mx-1">|</span>  <span class="cursor-pointer color-blue-ligth">Basic</span>  <span class="mx-1">|</span>  <span>$184.600</span>
							</div>
						</div>
					</div>
					<div id="msjVueloIda" class="d-none h-100">
						<div id="msjVueloIda" class="col-12 d-flex align-items-center justify-content-center h-100">
							<span>Selecciona tu  <strong> vuelo de ida-</strong></span>
						</div>
					</div>
				</div>
				<div class="col-5">
					<div id="vueloRegreso" class="row">
						<div class="col-2 d-flex align-items-center justify-content-center">
							<img class="w-100" src="https://www.tiquetesbaratos.com/resources/images/logos/logo-avianca.svg" />
						</div>
						<div class="col-10 px-0">
							<div class="col-12">
								<strong>Regreso:</strong> Medellin (MDE)  - Bogota
							</div>
							<div class="col-12">
								<span>11:50</span>   <span class="icon icon-plane-right"></span>  <span>12:48 </span>
								<span class="mx-1">|</span>   <span class="cursor-pointer color-black-light">Directo </span>  <span class="mx-1">|</span>  <span class="cursor-pointer color-blue-ligth">Basic</span>   <span class="mx-1">|</span>  <span>$184.600</span>
							</div>
						</div>
					</div>
					<div id="msjVueloRegreso" class="d-none h-100">
						<div class="col-12 d-flex align-items-center justify-content-center h-100">
							<span>Selecciona tu  <strong> vuelo de regreso</strong></span>
						</div>
					</div>
				</div>
				<div class="col-2 d-flex align-items-center justify-content-center">
					<button id="BookButton" class="btn btn-primary px-5 py-3">Reservar</button>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
	const configSite = {};

	export default {
		data() {
			return {
				config: configSite,
			}
		},
        props: {
        },
		mounted() {
		},
		methods: {
		}
	}
</script>