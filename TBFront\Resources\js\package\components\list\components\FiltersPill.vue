<template>
    <div class="container cf-overflow pt-3 mb-0 pb-0 px-2 position-relative" id="pillContainer">
        <div id="cfOverlay" class="cf-overlay" v-if="isOverlayVisible" @click="closeFilters"></div>
        <div class="cf-scroll">
            <div class="cfs-int">
                <div class="c-pill me-2 cp-economics" @click.stop.prevent="searchCheapestFlights()">
                    <label class="list-group-item d-block d-sm-none">
                        <input class="form-check-input me-1" type="checkbox" v-model="isCheapestFlightsApplied" @click.stop="searchCheapestFlights()">
                        {{ __("filters.cheapest") }}
                    </label>
                    <label class="list-group-item d-none d-sm-block">
                        <input class="form-check-input me-1" type="checkbox" v-model="isCheapestFlightsApplied" @click.stop="searchCheapestFlights()">
                        {{ __("messages.more_flights") }}
                    </label>
                </div>
                <div class="cf-capsule position-relative pe-2" :key="'all'">
                    <div class="overflow-hidden o-cp">
                        <div class="c-pill filter-button"
                             :class="{ 'f-active': activeFilter === 'all', 'apply-filter': filtersAppliedArray.length > 0 }"
                             @click="toggleFilter('all')">
                            <span class="cp-text ms-1">{{__('filters.filters')}}</span>
                            <span @click.stop="applyFilters(true)" v-show="filtersAppliedArray.length > 0" class="cp-icon icon-close"></span>
                            <span v-show="filtersAppliedArray.length == 0" :class="activeFilter === 'all' ? 'icon-keyboard-up' : 'icon-expand'" class="cp-icon"></span>
                        </div>
                    </div>
                    <span v-if="filtersAppliedArray.length > 0" class="c-apply" :class="{ 'z-3': activeFilter === 'all' && isOverlayVisible }">
                        {{ filtersAppliedArray.length }}
                    </span>
                    <div v-if="activeFilter === 'all'" class="cf-dropdown px-4 pb-0 border filter-container c-all-filters" :class="{'c-return' : getIsStepTwo}">
                        <div class="special-filter position-relative">

                            <div class="cs-head sticky-top bg-white pt-3 ">                            
                                <div class="d-flex justify-content-between align-items-center border-bottom-0 py-0">
                                    <h5 class="mb-0">{{__('filters.all_filters')}}</h5>
                                    <span class="icon icon-close close cursor-pointer" @click="closeFilters"></span>
                                </div>
                            </div>

                            <div class="cs-body">
                                <div class="accordion row" id="accordionExample">
                                    <div class="col-12 col-md-6 mb-3" v-for="(allFilters, index) in getPillFilters.allFilters">
                                        <div class="accordion-item" v-for="(filter, catIndex) in allFilters">
                                            <h2 class="accordion-header" :id="`accordion${index}${catIndex}`">
                                                <button class="accordion-button" type="button" data-bs-toggle="collapse" :data-bs-target="`#collapse${index}${catIndex}`" aria-expanded="true" :aria-controls="`collapse${index}${catIndex}`">
                                                    {{filter.label}}
                                                </button>
                                            </h2>
                                            <div :id="`collapse${index}${catIndex}`" class="accordion-collapse collapse show cursor-pointer" aria-labelledby="headingOne">
                                                <div class="accordion-body filter-container" v-if="filter.id == 'airlines'">
                                                    <div class="d-flex justify-content-between align-items-center"
                                                         @click="allElements(filter.id)">
                                                        <div class="fw-semibold">{{__('filters.allAirlines')}}</div>
                                                        <div class="d-block">
                                                            <div class="form-check">
                                                                <input class="form-check-input" type="checkbox" :checked="!filterModel[filter.id].length" />
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="d-flex justify-content-between align-items-center" v-for="(option, optIndex) in filter.options" :key="optIndex"
                                                         @click.stop="validateFlight(filter.id, option.value)">
                                                        <div>
                                                            <img v-if="filter.id == 'airlines'" class="me-2" height="25" width="25" :src="option.airlineLogoUrl">
                                                            {{ option.name }}
                                                        </div>
                                                        <div class="d-block">
                                                            <div class="form-check">
                                                                <input class="form-check-input ms-3" type="checkbox" :value="option.value"
                                                                       v-model="filterModel[filter.id]" />
                                                                <span class="position-relative top-2 txt-price">{{lowestPrice(option)}}</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="accordion-body filter-container" v-else>
                                                    <div class="d-flex justify-content-between align-items-center"
                                                         @click="allElements(filter.id)">
                                                        <div class="fw-semibold">
                                                            <label class="form-check-label cursor-pointer" for="flexRadioDefault1">
                                                                {{__('filters.allFlights')}}
                                                            </label>
                                                        </div>
                                                        <div class="d-block">
                                                            <div class="form-check">
                                                                <input class="form-check-input position-relative top-4" type="radio"
                                                                       :name="'radioGroup-' + filter.id"
                                                                       :id="'radio-' + filter.id + '-' + '0'"
                                                                       :checked="!filterModel[filter.id]" />
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="d-flex justify-content-between align-items-center" v-for="(option, optIndex) in filter.options" :key="optIndex + 1"
                                                         @click="validateFlight(filter.id, option.value)">
                                                        <div>
                                                            <label class="form-check-label cursor-pointer" :for="'radio-' + filter.id + '-' + (optIndex + 1)">
                                                                {{ option.name }}
                                                            </label>
                                                        </div>
                                                        <div class="d-block">
                                                            <div class="form-check">
                                                                <input class="form-check-input position-relative top-4" type="radio"
                                                                       :name="'radioGroup-' + filter.id"
                                                                       :id="'radio-' + filter.id + '-' + (optIndex + 1)"
                                                                       :value="option.value" v-model="filterModel[filter.id]">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="cs-footer pb-4 pb-md-0 bg-white" :class="{'mb-3': !isMobile}">
                                <div class="col-12 mt-4 px-4 px-md-3">
                                    <button v-show="validating" class="btn btn-primary px-3 py-3 w-100 btn-f-close" type="button">{{__('filters.validating')}}</button>
                                    <button v-show="getExistResults && !validating" class="btn btn-primary px-3 py-3 w-100 btn-f-close" @click="applyFilters()">{{__('filters.apply')}}</button>
                                    <button v-show="!getExistResults && !validating" class="btn btn-secondary px-3 py-3 w-100 btn-f-close" type="button" disabled>{{__('filters.any_flights')}}</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="cf-capsule position-relative me-2" v-if="!getIsStepTwo">
                    <div id="fechasCercanas" class="c-pill" @click="toggleFechasCercanas">
                        <span class="icon icon-date ps-0 me-1"></span>
                        <span>{{__("messages.near_dates")}}</span>
                    </div>
                </div>
                <div v-if="isFechasCercanasVisible && !getIsStepTwo" class="cf-dropdown p-4 border filter-container filter-full">
                    <div class="d-flex justify-content-between align-items-center border-bottom-0 py-0">
                        <h5 class="mb-0">{{__("messages.near_dates")}}</h5>
                        <span class="icon icon-close close" @click="closeFilters"></span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center py-0">
+                        {{ isRoundtrip ? __("multiticket.price_RoundTrip_per_person") : __("multiticket.per_pax")}}
+                    </div>
                    <CalendarDatesQuotes :typeFlight="getTripMode"></CalendarDatesQuotes>
                </div>
            </div>
        </div>
    </div>
</template>
  
<script>
    import { storeToRefs } from 'pinia';
    import { useFlightMatrixStore } from "../../../stores/flightMatrix";
    import { getFilteredList } from '../../../services/ApiFlightFrontServices';
    import { useFlightStore } from '../../../stores/flight';

    import { useUserSelectionStore } from '../../../stores/user-selection';
    import { cheapestFlights } from '../../../../constants';
    import { List } from '../../../../utils/analytics/flightList.js';
    import { sleep } from '../../../../utils/helpers';
    import { useMultiTicketStore } from '../../../stores/multiTicket';
    import { usePromotionStore } from '../../../stores/promotion';
    import { useFlightUpsellStore } from "../../../stores/flightUpsell";

    export default {
        data() {
            return {
                isOverlayVisible: false,
                activeFilter: null,
                isFechasCercanasVisible: false,
                validating: false,
                isRoundtrip : window?.__pt?.data?.isRoundtrip,
            };
        },
        setup() {
            const storeFlightMatrix = useFlightMatrixStore();
            const storeFlight = useFlightStore();
            const storeUserSelection = useUserSelectionStore();
            const useMultiTicket = useMultiTicketStore();
            const promotionStore = usePromotionStore();
            const useFlightUpsell = useFlightUpsellStore();

            const { getLoading, getPillFilters, getAirlines } = storeToRefs(storeFlightMatrix);
            const { getIsStepTwo, getCheckOutDataStepOne, getStopsFiltersApplied, getAirlinesFiltersApplied, getExistResults } = storeToRefs(useMultiTicket);
            const { setAirlinesFiltersPush, setExistResults, setAirlinesFilters, setStopsFilters } = useMultiTicket;
            const { setFlightResponses, setLoading } = storeFlight;
            const { setLoading: setLoadingMatrix, cleanPagination } = storeFlightMatrix;
            const { changeFilters, setFlagFilters, addFilter, removeFilter } = storeUserSelection;
            const { isCheapestFlightsApplied, getFiltersAppliedArray, getFiltersApplied } = storeToRefs(storeUserSelection);
            const { getTripMode } = storeToRefs(promotionStore);
            const { getAllQuoteTokens, getParams, getreturningQuoteToken } = storeToRefs(storeFlight);
            const { getFlightSelected, getFareKey } = storeToRefs(useFlightUpsell);

            return {
                getPillFilters, getIsStepTwo, setLoadingMatrix, setLoading, addFilter,
                getAllQuoteTokens, getFiltersApplied, getFlightSelected, getFareKey,
                setFlagFilters, getTripMode, getFiltersAppliedArray, setFlightResponses,
                isCheapestFlightsApplied, removeFilter, getAirlinesFiltersApplied, getStopsFiltersApplied,
                setAirlinesFiltersPush, getCheckOutDataStepOne, setExistResults,
                changeFilters, setAirlinesFilters, setStopsFilters, getExistResults, getAirlines, cleanPagination, getreturningQuoteToken
            }
        },
        mounted() {
        },
        props: {
            isMobile: { type: Boolean, default: true },
        },
        computed: {
            filterModel: {
                get() {
                    return {
                        airlines: this.getAirlinesFiltersApplied,
                        stops: this.getStopsFiltersApplied
                    };
                },
                set(newValue) {
                    if (newValue.airlines !== undefined) {
                        this.$emit('update:getAirlinesFiltersApplied', newValue.airlines);
                    }
                    if (newValue.stops !== undefined) {
                        this.$emit('update:getStopsFiltersApplied', newValue.stops);
                    }
                }
            },
            airlinesFiltersApplied() {
                return this.getAirlinesFiltersApplied;
            },
            stopsFiltersApplied() {
                return this.getStopsFiltersApplied;
            },
            filtersAppliedArray() {
                return this.getFiltersAppliedArray.filter(item => item !== "cheapestFlights");
            }
        },
        methods: {
            toggleFilter(filterId) {
                if (this.activeFilter === filterId) {
                    this.closeFilters();
                } else {
                    this.activeFilter = filterId;
                    this.isOverlayVisible = true;
                    this.isFechasCercanasVisible = false;
                    this.openToggles();
                }
            },
            toggleFechasCercanas() {
                this.isFechasCercanasVisible = !this.isFechasCercanasVisible;
                this.isOverlayVisible = this.isFechasCercanasVisible;
                this.activeFilter = null;
                if (this.isFechasCercanasVisible) {
                    this.openToggles();
                    List.nearbyDates();
                }
            },
            openToggles() {
                const pillContainer = document.getElementById("pillContainer");
                const containerDatesMobile = document.getElementById("containerDatesMobile");
                const appContainer = document.getElementById("app");
                pillContainer.classList.add("z-md");
                if (this.isMobile) {
                    appContainer.scrollIntoView();
                    document.body.classList.add("overflow-hidden");
                } else {
                    pillContainer.scrollIntoView();
                    const cBarInfloFlights = document.getElementById("cBarInfloFlights");
                    if (window.__pt.data.isNational && !this.isMobile) {
                        cBarInfloFlights.classList.add("z-bar-national");
                    }
                }
            },
            closeFilters() {
                this.activeFilter = null;
                this.isOverlayVisible = false;
                this.isFechasCercanasVisible = false;
                const pillContainer = document.getElementById("pillContainer");
                document.body.classList.remove("overflow-hidden");
                pillContainer.classList.remove("z-md");
                if (window.__pt.data.isNational && !this.isMobile) {
                    cBarInfloFlights.classList.remove("z-bar-national");
                }
            },
            getFunctionFiltersApplied(apply = false, remove = false) {
                const filters = [];
                if (this.airlinesFiltersApplied.length > 0 && !remove) {
                    filters.push(...this.airlinesFiltersApplied);
                } else {
                    this.setAirlinesFilters([]);
                }
                if (this.stopsFiltersApplied && !remove) {
                    filters.push(this.stopsFiltersApplied);
                } else {
                    this.setStopsFilters('');
                }
                if (this.isCheapestFlightsApplied) {
                    filters.push(cheapestFlights);
                }
                this.setFlagFilters(filters.length == 0);
                if (apply) {
                    this.changeFilters(filters);
                }
                return filters.join(',');
            },
            async searchCheapestFlights() {
                this.setLoading(true);
                this.setLoadingMatrix(true);

                if (!this.isCheapestFlightsApplied) {
                    this.addFilter(cheapestFlights);
                } else {
                    this.removeFilter(cheapestFlights);
                }

                const params = {
                    token: this.getAllQuoteTokens.join(','),
                    filterApplied: this.getFiltersApplied,
                    site: window.__pt.settings.site.apiFlights.siteConfig
                };
                if (this.getIsStepTwo) {
                    params.DepartureToken = this.getCheckOutDataStepOne.summary.token;
                    params.FlightQuoteId = this.getCheckOutDataStepOne.summary.fareKey;
                }
                params.tripMode = this.getTripMode;
                params.simpleFlightQuotes = true;
                params.step = true;
                params.roundTripToken= this.getreturningQuoteToken.join(',')
                params.allQuotes = !window.__pt.data.isNational && this.getTripMode == 1;


                const response = await getFilteredList(params);

                this.setFlagFilters(this.getFiltersAppliedArray.length == 0);
                if (this.isCheapestFlightsApplied) {
                    List.cheapestFlights();
                }

                this.setFlightResponses(response.response);
                this.cleanPagination();
                setTimeout(() => {
                    this.setLoading(false);
                }, 500)
                this.setLoadingMatrix(false);
            },
            async validateFlight(type, value, all = false) {
                const selectedValues = type === 'airlines' ? this.filterModel[type] : [];
                if (selectedValues.includes(value)) {
                    let newValues = [];
                    newValues = this.airlinesFiltersApplied.filter(item => item !== value);
                    this.setAirlinesFilters(newValues);
                } else {
                    if (type === 'airlines') {
                        this.setAirlinesFiltersPush(value);
                    } else {
                        this.setStopsFilters(value);
                    }
                }
                this.validating = true;
                let existFlights = !this.stopsFiltersApplied || !this.airlinesFiltersApplied.length > 0;
                if (!existFlights) {
                    let indexStops = [];
                    indexStops.push(this.stopsFiltersApplied == 'nonStopsRates' ? 0 : 1);
                    this.getAirlines.forEach(response => {
                        this.airlinesFiltersApplied.forEach(codes => {
                            if (response.value == codes) {
                                indexStops.forEach(index => {
                                    if (response.stops.stopsInfo[index].rate > 0) {
                                        existFlights = true;
                                    }
                                });
                            }
                        });
                    });
                }
                this.setExistResults(existFlights)
                this.validating = false;
                this.setLoading(false);
            },
            async applyFilters(remove = false) {
                this.setLoading(true);
                this.setLoadingMatrix(true);
                this.closeFilters();
                const params = {
                    token: this.getAllQuoteTokens.join(','),
                    filterApplied: this.getFunctionFiltersApplied(true, remove),
                    site: window.__pt.settings.site.apiFlights.siteConfig,
                    tripMode: this.getTripMode,
                    simpleFlightQuotes: true,
                    step: true
                };
                if (this.getIsStepTwo) {
                    params.DepartureToken = this.getCheckOutDataStepOne.summary.token;
                    params.FlightQuoteId = this.getCheckOutDataStepOne.summary.fareKey;
                }
                params.roundTripToken= this.getreturningQuoteToken.join(',')
                params.allQuotes = !window.__pt.data.isNational && this.getTripMode == 1;

                const response = await getFilteredList(params);
                await sleep(50)
                this.setFlightResponses(response.response);
                this.cleanPagination();
                if (this.getFiltersAppliedArray.length > 0 && this.getFiltersAppliedArray[0] != 'cheapestFlights') {
                    List.flightFilters(this.getFiltersAppliedArray);
                }
                setTimeout(() => {
                    this.setLoading(false);
                }, 500)
                this.setLoadingMatrix(false);
            },
            allElements(type) {
                if (type === 'airlines') {
                    this.setAirlinesFilters([])
                }
                else if (type === 'stops') {
                    this.setStopsFilters('')
                }
                this.getExistResults = true;
                this.setExistResults(true);
                this.validating = false;
            },
            lowestPrice(options) {
                let price = "$ " + this.$filters.formatNumber(options.orderRate);
                if (this.stopsFiltersApplied) {
                    let index = this.stopsFiltersApplied == "nonStopsRates" ? 0 : 1;
                    price = options.stops.stopsInfo[index].rate > 0 ? "$ " + this.$filters.formatNumber(options.stops.stopsInfo[index].rate) : "--";
                }
                return price;
            }
        }
    }
</script>

