﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System.Text.Json;
using TBFront.Application.Implementations;
using TBFront.Helpers;
using TBFront.Interfaces;
using TBFront.Mappers;
using TBFront.Models.Configuration;
using TBFront.Models.ContentDeliveryNetwork.Seo;
using TBFront.Models.Meta.Metatags;
using TBFront.Models.Places.Request;
using TBFront.Models.Places.Response;
using TBFront.Models.Request;
using TBFront.Options;
using TBFront.Types;

namespace TBFront.Controllers
{
    public class ListController : Controller
    {

        private readonly ILogger<ListController> _logger;
        private readonly IPlaceHandler _placeHandler;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly SettingsOptions _options;
        private readonly ViewHelper _helper;
        private readonly ICommonHandler _commonHandler;
        private readonly IAlternateHandler _alternateHandler;
        private readonly IUserHandler _userHandler;
        private readonly IContentDeliveryNetworkHandler _contentDeliveryNetworkHandler;
        public ListController(ILogger<ListController> logger, IPlaceHandler placeHandler, IOptions<SettingsOptions> options, IHttpContextAccessor httpContextAccessor,  ViewHelper helper, ICommonHandler commonHandler, IAlternateHandler alternateHandler, IUserHandler userHandler, IContentDeliveryNetworkHandler contentDeliveryNetworkHandler) {
            _logger = logger;
            _placeHandler = placeHandler;
            _options = options.Value;
            _helper = helper;
            _httpContextAccessor = httpContextAccessor;
            _commonHandler = commonHandler;
            _alternateHandler = alternateHandler;
            _userHandler = userHandler;
            _contentDeliveryNetworkHandler = contentDeliveryNetworkHandler;
        }
            
        [Route("/vuelos/listado")]
        [Route("/vuelos/resultados")]
        [Route("/flights/results")]
        [Route("/flights/list")]
        [Route("/vuelos/viaja-a-{destination}/{destinationCode}")]
        [Route("/{culture}/vuelos/viaja-a-{destination}/{destinationCode}")]
        [Route("/vuelos/viaja-desde-{origin}-a-{destination}/{originCode}/{destinationCode}/")]
        [Route("/vuelos/{airline}/viaja-desde-{origin}-a-{destination}/{originCode}/{destinationCode}/")]
        [Route("/{culture}/vuelos/listado")]
        [Route("/{culture}/vuelos/resultados")]
        [Route("/{culture}/vuelos/viaja-desde-{origin}-a-{destination}/{originCode}/{destinationCode}/")]
        [Route("/{culture}/vuelos/{airline}/viaja-desde-{origin}-a-{destination}/{originCode}/{destinationCode}/")]
        [Route("/{culture}/flights/list")]
        [Route("/{culture}/flights/results")]
        [Route("/{culture}/flights/flights-to-{destination}/{destinationCode}")]       
        [Route("/{culture}/flights/flights-from-{origin}-to-{destination}/{originCode}/{destinationCode}/")]
        [Route("/{culture}/flights/{airline}/flights-from-{origin}-to-{destination}/{originCode}/{destinationCode}/")]
        
        [HttpGet]
        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> Index(FlightParamsRequest flightRequest, string culture, string airline = "", string origin = "", string destination = "", string originCode = "", string destinationCode = "")
        {
            var route = HomeMapper.Path(Request.Path.Value ?? "");
            var path = HomeMapper.GetPath(route);
            try
            {
                using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(120000));
                
                flightRequest = FlightParamsHelper.OverwriteRequest(flightRequest, originCode, destinationCode, airline,_options);
                var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
                var request = FlightParamsHelper.GetFlightRequest(flightRequest, _options, userSelection.Culture.InternalCultureCode);
                var places = await _placeHandler.QueryAsync(request, cts.Token);
                request = FlightParamsHelper.MapFlightItemRequest(request, places,_options);

                var pageType = !string.IsNullOrEmpty(destinationCode) && string.IsNullOrEmpty(originCode)
                ? PageType.DestinationFlightList
                : PageType.FlightList;


                var alternates = await _alternateHandler.QueryAsync(new PlaceRequest { Culture = userSelection.Culture.CultureCode, Id = 0, Path = path, Route = route, Type = pageType, ReturninAirport = request.ReturningFromAirport, StartingAirport = request.StartingFromAirport }, cts.Token);
                var user = await _userHandler.QueryAsync(new Models.User.UserRequest() { }, cts.Token);

                var seoContent = await _contentDeliveryNetworkHandler.QueryAsync(new SeoRequest { Path = $"{alternates.Url}" }, cts.Token);
                var meta = new MetaTag();
                if (places.Count() >= 2 && !places.Any(p => p is null) && places.Select(p => p.Code).Distinct().Count() >= 2)
                {
                      meta = MetaMapper.List(_options, _helper, request, userSelection, alternates,pageType, seoContent);
                }
                else
                {
                    meta = MetaMapper.Generic(_options, userSelection, _helper, seoContent, "");
                    meta.Url = "";
                    _httpContextAccessor.HttpContext.Response.StatusCode = 404;
                }
                ViewData["User"] = user;
                ViewData["Alternates"] = alternates;
                ViewData["places"] = places;
                ViewData["request"] = request;
                ViewData["MetaTag"] = meta;
                ViewData["seoContent"] = seoContent;
                ViewData["PageType"] = pageType;
                ViewData["CultureData"] = userSelection.Culture;
                ViewData["CurrencyData"] = userSelection.Currency;
                ViewData["Exchange"] = userSelection.ExchangeClient;
                ViewData["UserLocation"] = userSelection.Context.Location;

            }
            catch(Exception ex)
            {
                ViewData["places"] = new List<PlaceResponse>();
                ViewData["request"] = new FlightRequest();

                _logger.LogError($"List - {ex.Message} - Request: {JsonSerializer.Serialize(flightRequest)}");

                return await ErrorPage(ex.Message, 500);
            }
            


            return View();
        }



        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<ActionResult> ErrorPage(string errorMgs, int statusCode)
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));
            var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
            ViewData["cultureData"] = userSelection.Culture;
            ViewData["currencyData"] = userSelection.Currency;
            ViewData["exchange"] = userSelection.ExchangeClient;
            ViewData["UserLocation"] = userSelection.Context.Location;
            ViewData["ErrorMgs"] = errorMgs;

            _httpContextAccessor.HttpContext.Response.StatusCode = statusCode;
            return View("~/Views/List/Index.cshtml");
        }


    }
}
