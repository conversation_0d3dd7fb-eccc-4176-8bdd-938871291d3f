<template>
    <section class="container  mt-4 px-3 c-grid-4 c-flights-list mb-3 pb-0 pb-md-5 mb-lg-5 f-round mt-4 px-3 px-md-0">
        <template v-if="isResponsive">
            <InfoTabBarMobileNational :isRoundTrip="isRoundTrip" />
        </template>
        <template v-for="item in groups">
            <template v-if="(!flightSelected.loadingList && !item?.loading) && ((item.departure?.flights.length > 0 && flightSelected?.step_action <= 2) || (item.returning?.flights.length > 0 && flightSelected?.step_action >= 3 && isRoundTrip))">
                <section :id="configFilter(item.departure?.code?.trim() || item.returning?.code?.trim()).airlineName" :class="`c-grid-4 c-flights-list border-1 mb-3 c-${item.departure?.code?.trim() || item.returning?.code?.trim()} pb-0 pb-md-3 f-round p-0`">
                    <div class="row c-sticky mx-0">
                        <div class="col-12 p-0">
                            <div class="row row-round position-relative bg-white w-100 m-auto c-bg-w">
                                <div class="col-6 col-md-2 py-2 py-lg-0 d-flex align-items-center bg-white cf-logo cfl-c z-index-2" :class="['hide-scroll-top-'+(item.departure?.code?.trim() || item.returning?.code?.trim())]">
                                    <div :id="'logoinfoFlight'+(item.departure?.code?.trim() || item.returning?.code?.trim())"><img width="100%" height="40px" :src="'/assets-tb/img/tiquetesbaratos/checkout/'+(item.departure?.code?.trim() || item.returning?.code?.trim())+'.png'" style="max-width: 100px; object-fit: contain;"></div>
                                </div>
                                <!--MENU EN DESKTOP-->
                                <div class="col-12 col-md-10 px-0 px-md-3 position-revert order-md-1">
                                    <div class="sw ch-row" id="chrowinfoFlightWingo">
                                        <div class="col-12">
                                            <div class="row c-color-header">
                                                <div :data-id="item.families.length" class="col-md-2 bg-white border-radius-left"></div>
                                                <!--HEADER -->
                                                <div :class="['col-md-'+(10-item.families.length), 'header-color', 'hide-scroll-top-'+(item.departure?.code?.trim() || item.returning?.code?.trim())]">
                                                    <!--header info ida -->
                                                    <template v-if="((flightSelected?.step_action == 1 || flightSelected?.step_action == 2) )">
                                                        <p class="largeNameFlight py-3 py-lg-2">
                                                            <strong class="strong">
                                                                {{ gDestination('from') ?? information.startingAirportPlace.cityName }}
                                                            </strong>({{ item.departure.departure.code }})
                                                            -  <strong class="strong">
                                                            {{ gDestination('to') ?? information.returningAirportPlace.cityName }}
                                                        </strong>({{  item.departure.arrival?.code }})
                                                        </p>
                                                    </template>
                                                    <!--end header info ida -->
                                                    <!-- header info regreso -->
                                                    <template v-if="((flightSelected?.step_action == 3 || flightSelected?.step_action == 4 ) && isRoundTrip)">
                                                        <p class="largeNameFlight py-3 pt-md-4 py-lg-2">
                                                            <strong class="strong"> {{ gDestination('to') ?? information.returningAirportPlace.cityName }} </strong>
                                                            ({{ item.returning?.departure.code }}) - <strong class="strong"> {{ gDestination('from') ?? information.startingAirportPlace.cityName }}</strong>
                                                            ({{  item.returning?.arrival.code  }})
                                                        </p>
                                                    </template>
                                                    <!--end header info regreso -->
                                                </div>
                                                <div class="col-12 header-color rounded-0 d-none" :class="['show-scroll-top-'+(item.departure?.code?.trim() || item.returning?.code?.trim())]">
                                                    <p class="shortNameFlight py-1 py-lg-3">
                                                        <span class="f-r-medium text-white font-16">
                                                            {{item.departure?.name?.trim() || item.returning?.name?.trim()}}
                                                        </span>
                                                        <span class="icon icon-keyboard-right text-white"></span>
                                                        <span>
                                                            <span class="text-white font-14" id="stickMobileinfoFlightLan">
                                                                {{ __("messages.flight_leg_title_"+(flightSelected?.step_action  < 3 ? "starting" : "returning"))+" " }}
                                                            </span>
                                                            <span class="text-white font-14 pl-2" id="iataStickyIdainfoFlightLan">
                                                                {{ flightSelected?.step_action  < 3 ? item.departure.departure?.code :  item.returning.departure?.code}} -  {{ flightSelected?.step_action  < 3 ? item.departure.arrival?.code :  item.returning.arrival?.code}}
                                                            </span>
                                                        </span>
                                                    </p>
                                                </div>
                                                <div :class="['col-'+item.families.length, 'p-0', 'm-0', 'header-color', 'header-style-list-flights']">
                                                    <div class="d-flex w-100 header-color"></div>
                                                </div>
                                                <!--END HEADER -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- start Ida -->
                    <div class="d-flex c-sticky-fam" v-if=" flightSelected?.step_action <= 2">
                        <div class="col-12 d-flex px-2 bg-white shadow-sm cr-line">
                            <div :class="['col-lg-'+(12-item.families.length)]" class="row">
                                <div class="col-lg-7 px-0 hide-xs hide-md">
                                    <div class="ct-airline my-3">
                                        <span class="ctl-line px-2 py-1 me-2">
                                            <span class="icon icon-plane-right"></span>
                                            <span class="font-poppins-medium font-16">{{ __("flightList.flight_departure") }}</span>
                                        </span>
                                        <span class="font-poppins font-16 color-gray-600">
                                            {{ $filters.date(item.departure.departure.date, 'dddd, DD  MMMM  YYYY') }}
                                        </span>
                                    </div>
                                </div>
                                <div class="col-lg-5 d-flex align-items-center justify-content-end">
                                    <a class="a-link cursor-pointer my-2 my-md-3" @click="openFamilies({name: item.departure.name, image: item.departure.image, group: item}, 'starting')">
                                        {{__('flightList.what_each_rate_includes')}}
                                        <span class="icon icon-info"></span>
                                    </a>
                                </div>
                            </div>
                            <div class="d-flex w-100 col column-list-flights p-0">
                                <div class="d-flex w-100">
                                    <template v-for="(configurations, indexConfig) in item.families" :key="indexConfig">
                                        <div @click="getFlightDetails({name: item.departure.name, image: item.departure.image, flight: findFamilyAirline(item, configurations.familyFareCode), group: item, fare: filterFare(findFamilyAirline(item, configurations.familyFareCode).fares, 'fareGroup', configurations.familyFareCode)}, 'FLIGHT_FAMILY')"
                                                class="hide-xs hide-md show-desktop px-0 pointer w-100 flex-fill cursor-pointer">
                                            <span class="d-flex align-items-center justify-content-center h-100 cf-name">
                                                <strong>{{ configurations.name }}</strong>
                                            </span>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex c-sticky-fam" v-if="((flightSelected?.step_action >= 3) && item.returning && isRoundTrip)">
                        <div class="col-12 d-flex px-2 bg-white shadow-sm cr-line">
                            <div :class="['col-lg-'+(12-item.families.length)]" class="row">
                                <div class="col-lg-7 px-0 hide-xs hide-md">
                                    <div class="ct-airline my-3">
                                        <span class="ctl-line px-2 py-1 me-2">
                                            <span class="icon icon-plane-left"></span>
                                            <span class="font-poppins-medium font-16">{{ __("flightList.flight_arrival") }}</span>
                                        </span>
                                        <span class="font-poppins font-16 color-gray-600">
                                            {{  $filters.date(item.returning?.departure.date, 'dddd, DD  MMMM  YYYY') }}
                                        </span>
                                    </div>
                                </div>
                                <div class="col-lg-5 d-flex align-items-center justify-content-end">
                                    <a class="a-link cursor-pointer my-2 my-md-3" @click="openFamilies({name: item.departure?.name?.trim() || item.returning?.name?.trim(), image: item.departure?.image?.trim() || item.returning.image?.image?.trim(), group: item}, 'starting')">
                                        {{__('flightList.what_each_rate_includes')}}
                                        <span class="icon icon-info"></span>
                                    </a>
                                </div>
                            </div>
                            <div class="d-flex w-100 col column-list-flights p-0">
                                <div class="d-flex w-100">
                                    <template v-for="(configurations, indexConfig) in item.families" :key="indexConfig">
                                        <div @click="getFlightDetails({name: item.departure?.name?.trim() || item.returning?.name?.trim(), image: item.departure?.image?.trim() || item.returning.image?.image?.trim(), flight: findFamilyAirline(item, configurations.familyFareCode), group: item, fare: filterFare(findFamilyAirline(item, configurations.familyFareCode).fares, 'fareGroup', configurations.familyFareCode)}, 'FLIGHT_FAMILY')"
                                                class="hide-xs hide-md show-desktop px-0 pointer w-100 flex-fill cursor-pointer">
                                            <span class="d-flex align-items-center justify-content-center h-100 cf-name">
                                                <strong>{{ configurations.name }}</strong>
                                            </span>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>
                    <template v-if=" flightSelected?.step_action <= 2">
                        <div class="cf-layer evtHideMobile overflow-initial px-4 px-md-0 mx-2"
                             :id="'listaidainfoFlightWingo'+item.departure.code">
                            <!--items-->
                            <div class="col-12 py-3 py-md-0 px-0 list-flights-v-2">
                                <template v-for="(flight, iFlight) in item.departure.flights">
                                    <div class="mb-md-3 mb-lg-0" :class="['c-bar-info-'+iFlight,item.departure.code]" :data-airline="item.departure?.code">
                                        <template v-if="getPagination[item.departure.code].departure >= (iFlight + 1)">
                                            <NationalFlightItem :id="configFilter(item.departure?.code?.trim() || item.returning?.code?.trim()).airlineName+iFlight" :iFlight="iFlight" type="departure" :airline="item" :submit="submit" :flight="flight" :isRoundTrip="isRoundTrip" />
                                        </template>

                                    </div>
                                </template>
                            </div>
                            <!-- start view more -->
                            <template v-if="getPagination[item.departure.code].departure < item.departure.flights.length">
                                <div class="row c-view-more tab-footer-offers mt-0">
                                    <div class="col-12 text-center py-2 border-top" @click="activePaginated(item.departure.code,'departure', item.departure.flights.length)">
                                        <span class="icon icon-sell font-20"></span>
                                        <span class="txt ms-1" @click="activePaginated(item.departure.code,'departure', item.departure.flights.length)">
                                            {{ __("flightList.moreFlightsFrom") }}
                                            <CurrencyDisplay :amount="item.departure.cheapest" :showCurrencyCode="false" />
                                        </span>
                                        <span class="icon icon-expand font-24"></span>
                                    </div>
                                </div>
                            </template>
                            <template v-if="getPagination[item.departure.code].departure >= item.departure.flights.length && item.departure.flights.length > itemsToshow">
                                <div class="row c-view-more tab-footer-offers mt-0">
                                    <div class="col-12 text-center py-2 border-top" @click="activePaginated(item.departure.code,'departure', itemsToshow)">
                                        <span class="icon icon-sell font-20"></span>
                                        <span class="txt ms-1" @click="activePaginated(item.departure.code,'departure', itemsToshow)">
                                            {{ __("flightList.lessFlightsFrom") }}
                                            <CurrencyDisplay :amount="item.departure.cheapest" :showCurrencyCode="false" />
                                        </span>
                                        <span class="i-rotate-180 icon icon-expand font-24"></span>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </template>
                    <!-- start Regreso -->
                    <template v-if="((flightSelected?.step_action >= 3) && item.returning && isRoundTrip)">
                        <div :id="configFilter(item.departure?.code?.trim() || item.returning?.code?.trim()).airlineName+'Regreso'" class="cf-layer evtHideMobile overflow-initial px-4 px-md-0 mx-2">
                            <div class="c-tf leaving-from d-none d-md-block">
                            </div>                          
                            <div class="row pb-2 px-md-3 d-none d-md-block m-auto pt-2">
                                <div class="col-12 p-0"></div>
                            </div>
                            <div class="col-12 py-3 py-md-0 px-0 list-flights-v-2">
                                <template v-for="(flight, iFlight) in item.returning.flights">
                                    <div class="mb-md-3 mb-lg-0" :class="['c-bar-info-'+iFlight,item.returning.code]" :data-airline="item.returning?.code">
                                        <template v-if="getPagination[item.returning.code].returning >= (iFlight + 1)">
                                            <NationalFlightItem :id="configFilter(item.departure?.code?.trim() || item.returning?.code?.trim()).airlineName+iFlight" :iFlight="iFlight" type="returning" :airline="item" :submit="submit" :flight="flight" :isRoundTrip="isRoundTrip" />
                                        </template>
                                    </div>
                                </template>
                                <!-- start line -->
                            </div>
                            <!-- start view more -->
                            <template v-if="getPagination[item.returning.code].returning < item.returning.flights.length">
                                <div class="row c-view-more tab-footer-offers mt-0">
                                    <div class="col-12 text-center py-2 border-top" @click="activePaginated(item.returning.code,'returning', item.returning.flights.length)">
                                        <span class="icon icon-sell font-20"></span>
                                        <span class="txt ms-1" @click="activePaginated(item.returning.code,'returning', item.returning.flights.length)">
                                            {{ __("flightList.moreFlightsFrom") }}
                                            <CurrencyDisplay :amount="item.returning.cheapest" :showCurrencyCode="false" />
                                        </span>
                                        <span class="icon icon-expand font-24"></span>
                                    </div>
                                </div>
                            </template>
                            <template v-if="getPagination[item.returning.code].returning >= item.returning.flights.length && item.returning.flights.length > itemsToshow">
                                <div class="row c-view-more tab-footer-offers mt-0">
                                    <div class="col-12 text-center py-2 border-top" @click="activePaginated(item.returning.code,'returning', itemsToshow)">
                                        <span class="icon icon-sell font-20"></span>
                                        <span class="txt ms-1" @click="activePaginated(item.returning.code,'returning', itemsToshow)">
                                            {{ __("flightList.lessFlightsFrom") }}
                                            <CurrencyDisplay :amount="item.returning.cheapest" :showCurrencyCode="false" />
                                        </span>
                                        <span class="i-rotate-180 icon icon-expand font-24"></span>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </template>
                </section>
            </template>
            <div v-show="item?.loading" class="mb-3">
                <Skeleton :typeSkeleton="'typeFamilyNational'" />
            </div>
        </template>
        <!--<FlightNotAvailable v-if="isDepartureEmpty && !loading" />-->
        <!--loading component-->
        <!--<Skeleton v-show="loading" :typeSkeleton="'typeFamilyNational'" v-for="skeleton in skeletons" />-->
        <!--end loading component-->
        <!--ext components-->
        <NationalModalDetail />
        <!--end ext components-->
    </section>
    <!--TAB FOOTER-->
    <template v-if="Object.keys(getGroups).length > 0 && !isDepartureEmpty">
        <FooterTabBarNational :submit="submit" :isRoundTrip="isRoundTrip" />
    </template>
    <!-- END TAB FOOTER-->
    <AirlineFamiliesNational></AirlineFamiliesNational>
</template>

<script>
    import { storeToRefs } from 'pinia';
    import { data_flights } from "../../../mock";
    import { useFlightStore } from '../../../stores/flight';
    import { getDetail, getFamilyFare, getParamsDetailFlight, getRevalidate, getParamsRevalidate } from "../../../services/ApiFlightFrontServices";
    import { useFlightDetailStore } from '../../../stores/flightDetail';
    import { useFlightFamilyFareStore } from '../../../stores/flightFamilyFare';
    import { useFlightRevalidateStore } from "../../../stores/flightRevalidate";
    import { useFlightUpsellStore } from "../../../stores/flightUpsell";
    import FooterTabBarNational from './nationals/FooterTabBarNational.vue';
    import NationalFlightItem from './nationals/NationalFlightItem.vue';
    import InfoTabBarMobileNational from "./nationals/InfoTabBarMobileNational.vue";
    import { NationalAirlineFlightsMapper } from "../../../mappers/nationalAirlineFlightsMapper"
    import { List } from '../../../../utils/analytics/flightList.js';
    import { useUserSelectionStore } from '../../../stores/user-selection';
    import {responsiveObserver} from "../../../../utils/helpers/responsiveObserver";
    import {useLoaderPageStore} from "../../../stores/loader-page";
    import { useAirlineFamiliesNationalStore } from "../../../stores/airlineFamiliesNational";
    import CurrencyDisplay from '../../common/CurrencyDisplay.vue';
    import { useMultiTicketStore } from '../../../stores/multiTicket';
    import { useFlightMatrixStore } from "../../../stores/flightMatrix";
    const { showLoaderPage } = useLoaderPageStore();

    export default {
        data() {
            return {
                configSite: window.__pt.settings.site.airlineConfiguration.national ?? [],
                data: data_flights,
                step_action: 1,
                pagination: {},
                skeletons: [1, 2, 3, 4],
                pt: window.__pt,
                isDepartureEmpty: false,
                isArrivalEmpty: false,
                culture: window.__pt.cultureData,
                quoteConfiguration: window.__pt.settings.site.quoteConfiguration.national ?? [],
                adjustables: [],
                itemsToshow: 7
            }
        },
        components: {
            FooterTabBarNational,
            NationalFlightItem,
            InfoTabBarMobileNational,
            CurrencyDisplay
        },
        computed: {
            isResponsive() {
                return this.isResponsiveRef;
            },
            loading() {
                return this.flightSelected.loadingList || this.getProgressBar < 100 || this.getLoading;
            },
            groups() {
                return this.filterAirlinesEmpty(this.sortAirlines((NationalAirlineFlightsMapper.generateLoading(this.getGroups, this.configFilter(), this.loading) || []))).map((airline) => {
                    const code = airline.departure?.code?.trim() || airline.returning?.code?.trim();
                    const view = this.flightSelected?.step_action >= 3 ? "returning" : "departure";
                    airline = NationalAirlineFlightsMapper.map(airline, (this.configFilter(code) ?? {}))
                    if (!this.getPagination[code]) {
                        const paginationParams = {
                            departure: this.itemsToshow,
                            returning: this.isRoundTrip ? this.itemsToshow : 0
                        }
                        this.setPagination(code, paginationParams);
                    }
                    return airline
                })
            },
            flightSelected() {
                return this.validateFlightSelected(this.getFlightSelected);
            },
            information() {
                return this.getParams
            },
            isFiltersActivated() {
                return this.getFiltersAppliedArray.length > 0;
            }
        },
        props: {
            isRoundTrip: { type: Boolean, default: false },
        },
        setup() {
            let storeFlight = useFlightStore();
            let storeUser = useUserSelectionStore();
            let storeAirlineFamiliesNationalStore = useAirlineFamiliesNationalStore();
            const useMultiTicket = useMultiTicketStore();
            const useFlightMatrix = useFlightMatrixStore();
            const { getCheckOutDataStepOne } = storeToRefs(useMultiTicket);

            const { getParams, getGroups, getProgressBar, getLoading } = storeToRefs(storeFlight);

            const flightDetailStore = useFlightDetailStore();
            const flightFamilyFareStore = useFlightFamilyFareStore();

            const { setFlightDetailResponse, activeModalDetail } = flightDetailStore;
            const { setFlightFamilyFareResponse } = flightFamilyFareStore;

            const flightRevalidateStore = useFlightRevalidateStore();
            const { getTotalAmount } = storeToRefs(flightRevalidateStore);
            const { setFlightRevalidateResponse } = flightRevalidateStore;

            const useFlightUpsell = useFlightUpsellStore();
            const { setFlightSelected } = useFlightUpsell; //set/actions
            const { getFlightSelected } = storeToRefs(useFlightUpsell);
            const { getShowDetail } = storeToRefs(flightDetailStore);
            const isResponsiveRef = responsiveObserver.getResponsiveStatus();

            const { actionAirlineFamiliesNational, openAirlineFamiliesNationalComponent } = storeAirlineFamiliesNationalStore;
            const { getAirlineFamiliesNational } = storeToRefs(storeAirlineFamiliesNationalStore);
            
            const { showRequoteModal } = storeUser;
            const { getFiltersAppliedArray } = storeToRefs(storeUser);

            const { setPagination } = useFlightMatrix;
            const { getPagination } = storeToRefs(useFlightMatrix);

            return {
                getGroups,
                getParams,
                getProgressBar,
                setFlightDetailResponse,
                setFlightFamilyFareResponse,
                setFlightRevalidateResponse,
                getTotalAmount,
                setFlightSelected,
                getFlightSelected,
                getLoading,
                showRequoteModal,
                activeModalDetail,
                getShowDetail,
                isResponsiveRef,
                actionAirlineFamiliesNational,
                openAirlineFamiliesNationalComponent,
                getAirlineFamiliesNational,
                getCheckOutDataStepOne,
                getFiltersAppliedArray,
                setPagination,
                getPagination
            }

        },
        mounted() {
            window.addEventListener('scroll', this.scrollEvent);
            document.querySelector("body").classList.add("c-multiticket")
            if (this.flightSelected) {
                this.setFlightSelected({
                    departure_flight: [],
                    returning_flight: [],
                    departure: null,
                    departure_fare: null,
                    returning: null,
                    returning_fare: null,
                    departure_token: null,
                    returning_token: null,
                    step_action: 1,
                    loadingList: false,
                    barLineBottomShow: false,
                    firstClick: true,
                    returningQuoteToken: null
                })
            }
            this.eventsModalDetail()
        },
        methods: {
            filterAirlinesEmpty(airlines){
                let nAirlines = airlines
                this.isDepartureEmpty = true
                this.isArrivalEmpty = true
                nAirlines = (nAirlines || []).filter((airline)=>{
                    let isValid = airline?.departure?.flights && ((airline?.departure?.flights ?? [])).length
                    if(isValid){
                        this.isDepartureEmpty  = false
                    }
                    if(this.isRoundTrip){
                        isValid = airline?.returning?.flights && ((airline?.returning?.flights ?? [])).length
                    }
                    if(isValid){
                        this.isArrivalEmpty  = false
                    }
                    return isValid
                })
                return nAirlines
            },
            validateFlightSelected(flightSelected){
                let response = flightSelected
                if( response.returning_flight && response.returning_flight.departure?.code && response.returning_flight.arrival?.code ){
                    if((response.returning_flight.departure?.code === response.departure_flight.departure?.code) || (response.returning_flight.arrival?.code === response.departure_flight.arrival?.code)){
                        this.showRequoteModal(`repeated_flights :: ${this.information.checkIn} :: ${this.information.checkOut} :: ${this.flightSelected.departure_flight.airline.code}|${this.flightSelected.returning_flight.airline.code} :: ${this.flightSelected.departure_flight.flightNumbers.join("-")}|${this.flightSelected.returning_flight.flightNumbers.join("-")}`, "error_lista");
                    }
                }
                return response
            },
            configFilter(code=null) {
               let config = (this.configSite || []);
                return code ? ((config).find(item => (item.airlineCode || []).find(aCode => (String(aCode)).toUpperCase() === (String(code)).toUpperCase())) ?? {}) : config 
            },
            activePaginated(code, type, lenghtI) {
                const otherView = type == "departure" ? "returning" : "departure";
                const paginationParams = {};
                paginationParams[type] = lenghtI;
                paginationParams[otherView] = this.getPagination[code][otherView];
                this.setPagination(code, paginationParams);
            },
            async revalidate() {
                let paramsRevalidate = {
                    fareKey: `${this.flightSelected.departure_fare.fareKey}`,
                    outboundFlightId: this.flightSelected.departure_flight.id,
                    outputFareId: this.flightSelected.departure_fare.fareId,
                    token: this.flightSelected.departure_token,
                    SimpleFlightQuotes: true,
                    site: this.pt.settings.site.apiFlights.siteConfig
                }
                if (this.isRoundTrip) {
                    paramsRevalidate.returnFlightId = this.flightSelected.returning_flight.id;
                    paramsRevalidate.ReturnFareId = this.flightSelected.returning_fare.fareId;
                    paramsRevalidate.fareKey += `|${this.flightSelected.returning_fare.fareKey}`
                    if(this.flightSelected.returning_fare?.isRoundTrip){
                        delete paramsRevalidate.SimpleFlightQuotes
                        paramsRevalidate.token = this.flightSelected.returningQuoteToken  || this.flightSelected.returning_token
                        //paramsRevalidate.outboundFlightId = this.flightSelected.outboundFlightId
                        //paramsRevalidate.outputFareId = this.flightSelected.outputFareId
                    }else{
                        paramsRevalidate.token += `,${this.flightSelected.returning_token}`
                    }
                }
                const responseRevalidate = await getRevalidate(paramsRevalidate);
                this.setFlightRevalidateResponse(responseRevalidate);
                return responseRevalidate;

            },
            async submit() {
                showLoaderPage();
                let revalidate = await this.revalidate();
                if (!revalidate.quoteTaskID || !revalidate.flights) {
                    return this.showRequoteModal("invalid-key");
                }else if(!this.isValidRevalidate(revalidate)){
                    return this.showRequoteModal(`repeated_flights_revalidate :: ${this.information.checkIn} :: ${this.information.checkOut} :: ${this.flightSelected.departure_flight.airline.code} :: ${this.flightSelected.departure_flight.flightNumbers.join("-")}|${this.flightSelected.returning_flight.flightNumbers.join("-")}`, "error_lista");
                }
                
                const params = {
                    site: null,
                    isBookNowPayLater: false,
                    lastFlightRate: this.getTotalAmount,
                    isPackage: false,
                    idAgentCall: 0,
                    checkIn: "0001-01-01T00:00:00",
                    checkOut: "0001-01-01T00:00:00",
                    hasSpecialDiscountAplied: false,
                    chkSource: 1,
                    rdmCksrreal: 1,
                    airline: this.flightSelected.departure_flight.airline.name,
                    tripMode: this.pt.data.isRoundtrip ? "RoundTrip" : "OneWay",
                    tripCabin: "N",
                    startingFromAirport: this.flightSelected.departure_flight.departure.airportCode,
                    startingFromDateTime: this.flightSelected.departure_flight.departureTime,
                    startingFromTime: "Anytime",
                    returningFromAirport: this.flightSelected.departure_flight.arrival.airportCode,
                    returningFromDateTime: this.pt.data.isRoundtrip
                        ? this.flightSelected.returning_flight.arrivalTime : this.flightSelected.departure_flight.arrivalTime,
                    returningFromTime: "Anytime",
                    nonStopOnly: false,
                    selectedOutboundFlight:  revalidate.flights[1]?.id ?? this.flightSelected.departure_flight.id,
                    selectedReturnFlight: this.pt.data.isRoundtrip ? revalidate.flights[2]?.id ?? this.flightSelected.returning_flight.id : 0,
                    fareKeyQuote: `${this.flightSelected.departure_fare.fareKey}${this.isRoundTrip ? `|${this.flightSelected.returning_fare.fareKey}` : ''}`,
                    quoteFlight: false,
                    quoteList: false,
                    referralUrl: window.location.pathname + window.location.search,
                    adults: this.pt.data.adults,
                    kids: this.pt.data.kids,
                    ageKids: this.pt.data.agekids,
                    infants: 0,
                    seniors: 0,
                    isDomesticRoute: true,
                    paxes: this.mapPaxes(),
                    flights: JSON.stringify(revalidate.flights),
                    fares: JSON.stringify((revalidate.fares)),
                    isRoundTrip: this.flightSelected?.returning_fare?.isRoundTrip ?? false,
                    checkoutData: JSON.stringify(this.mapCheckOutData(revalidate)),
                };
                this.evtLayerIsRountrip();
                const urlCheckout = `/${this.culture.cultureCode}${this.culture.pathCheckout}`;
                this.sendObjectPostForm(urlCheckout, params);
            },
            isValidRevalidate(revalidate){
                let isValid = true 
                if(revalidate.flights[1] && revalidate.flights[2]){
                    if( (revalidate.flights[1].departure.code === revalidate.flights[2].departure?.code) || (revalidate.flights[1].arrival.code === revalidate.flights[2].arrival?.code) ){
                        isValid = false;
                    }
                }
                return isValid
            },
            evtLayerIsRountrip(){
                if(this.flightSelected.returning_fare?.isRoundTrip){
                    List.selectRoundtripNationalFlight(this.flightSelected.departure_fare, this.flightSelected.returning_flight, this.flightSelected.returning_fare)
                }
            },
            async getFlightsDetails(group, rq){
                let familiesFare = [];
                for (const family of (group.families || [])) {
                    const flight = this.findFamilyAirline(group, family.familyFareCode)
                    const fare = this.filterFare(flight.fares, 'fareGroup', family.familyFareCode)
                    rq.flightId = flight.id
                    rq.fareId = fare?.fareId
                    rq.familyFare = fare?.fareGroup
                    let responseFamilyFare = await getFamilyFare(rq);
                    familiesFare.push(responseFamilyFare)
                    //this.findFamilyAirline(group, configurations.familyFareCode)   
                }
                return familiesFare
            },
            async getFlightDetails({ name, image, flight, group, fare }, type) {
                let flightType = flight.flightType ?? "starting"
                if (this.getShowDetail) return;
                this.activeModalDetail();
                const modalElement = document.getElementById('modalDetailNational');
                const modal = new bootstrap.Modal(modalElement);
                modal.show();
                this.paramsDetail = {
                    token: ((group.departure?.token) ? group.departure?.token : "") + ((group.departure?.token) ? "," : "") + ((this.isRoundTrip && group.returning.token) ? group.returning.token : ""),
                    //token: ((group.departure.token ) ? group.departure.token : group.returning.token) ,
                    flightId: 0,
                    airlineLogoUri: 0,
                    airlineName: name,
                    fareId: 0,
                    flightType: flightType,
                    familyFare: fare?.fareGroup,
                    fareKey: fare?.fareKey,
                };
                let rq = getParamsDetailFlight(this.paramsDetail);

                switch ((String(type)).toUpperCase()) {
                    case "FLIGHT_DETAIL":
                        let responseDetail = await getDetail(rq);
                        this.setFlightDetailResponse(responseDetail);
                        this.setFlightFamilyFareResponse({});
                        List.modalDetail(group.departure?.code?.trim() || group.returning?.code?.trim(), "", "",false);
                        break;
                    case "FLIGHT_FAMILY":
                        let responseFamilyFare = [];
                        if (!this.paramsDetail.fareKey) {
                            await this.openFamilies({ name, image, group }, this.flightSelected?.step_action >= 3 ? "returning" : "starting", false);
                            responseFamilyFare = (this.getAirlineFamiliesNational.families || []).find(
                                family => family.familyFareCode.find(
                                    fareCode => flight.familyFareCode.includes(fareCode)
                                )
                            );
                            rq.familyFare = responseFamilyFare.familyFareName;
                        } else {
                            responseFamilyFare = await getFamilyFare(rq);
                        }
                        this.setFlightFamilyFareResponse({...responseFamilyFare,params: rq});
                        List.modalDetail(group.departure?.code?.trim() || group.returning?.code?.trim(), responseFamilyFare.familyFareName, "", true);
                        this.setFlightDetailResponse({});
                        break;
                }
                this.activeModalDetail();
            },
            async openFamilies({ name, image, group }, flightType, open = true){
                let paramsDetail = {
                    token: [group.departure?.token, this.isRoundTrip ? group.returning.token : null]
                        .filter(token => token)
                        .join(','),
                    //token: ((group.departure.token ) ? group.departure.token : group.returning.token) ,
                    flightType: flightType,
                    airlineLogoUri: image,
                    airlineName: name,
                    type: "national",
                    code: group.departure?.code?.trim() || group.returning?.code?.trim()
                };
                let rq = getParamsDetailFlight(paramsDetail);
                await this.actionAirlineFamiliesNational({ group, rq });
                if (open) {
                    this.openAirlineFamiliesNationalComponent();
                    List.modalDetail(group.departure?.code?.trim() || group.returning?.code?.trim(), "TODAS", "TODAS", true);
                }
            },
            filterFlightByAirlineCode(flights, familyFareCode) {
                return (flights || []).find(flight => this.filterFare((flight?.fares || []), 'fareGroup', familyFareCode))
            },
            findFamilyAirline(airline, familyFareCode) {
                let flightType = "starting";
                let flight = this.filterFlightByAirlineCode((airline.departure?.flights ?? []), familyFareCode)
                if (!flight) {
                    flight = this.filterFlightByAirlineCode((airline.returning?.flights ?? []), familyFareCode) ?? {}
                    flightType = "returning"
                }
                flight.flightType = flightType
                flight.familyFareCode = familyFareCode
                return flight
            },
            filterFare(fares, key, value) {
                return fares?.find(fareKey => value.some(code => fareKey[key].toUpperCase() === (code).toUpperCase()))
            },
            mapPaxes() {
                let paxes = [
                    {
                        adults: this.pt.data.paxes[0]['adults'],
                        children: []
                    }
                ];
                for (let i = 0; i < this.pt.data.paxes[0]['children'].length; i++) {
                    paxes[0]['children'][i] = {
                        year: this.pt.data.paxes[0]['children'][i]
                    };
                }
                return paxes;
            },
            mapCheckOutData(revalidate) {
                let routeRates = [];
                routeRates[0] = {
                    departureFullName: this.pt.data.startingAirportPlace.cityFullName,
                    arrivalFullName: this.pt.data.returningAirportPlace.cityFullName
                };
                let quoteTaskID = [{ taskID: this.flightSelected.departure_quoteTokenFQS }];
                if (this.pt.data.isRoundtrip) {
                    routeRates[1] = {
                        departureFullName: this.pt.data.returningAirportPlace.cityFullName,
                        arrivalFullName: this.pt.data.startingAirportPlace.cityFullName
                    };
                    quoteTaskID.push({
                        taskID: this.flightSelected.returning_quoteTokenFQS
                    });
                }
                let token = this.flightSelected.departure_token + (this.isRoundTrip ? ',' + this.flightSelected.returning_token : '')
                if(this.flightSelected.returning_fare?.isRoundTrip && this.isRoundTrip){
                    token = this.flightSelected.returningQuoteToken  || this.flightSelected.returning_token
                }
                
                const data = {
                    flights: revalidate.flights,
                    fares: revalidate.faresLegs,
                    summary: {
                        origin: {
                            ...this.flightSelected.departure_flight,
                            ...routeRates[0],
                            id: revalidate.flights[1]?.id ?? this.flightSelected.departure_flight.id,
                            fares: [revalidate.fares && revalidate.fares[0] ? revalidate.fares[0] : this.flightSelected.departure_fare]
                        },
                        destination: this.pt.data.isRoundtrip ?
                            {
                                ...this.flightSelected.returning_flight,
                                ...routeRates[1],
                                id: revalidate.flights[2]?.id ?? this.flightSelected.returning_flight.id,
                                fares: [revalidate.fares && revalidate.fares[1] ? revalidate.fares[1] : this.flightSelected.returning_fare]
                            } : {},
                        fareKey: `${this.flightSelected.departure_fare.fareKey}${this.isRoundTrip ? `|${this.flightSelected.returning_fare.fareKey}` : ''}`,
                        token: token,
                        departureName: this.pt.data.startingAirportPlace.cityName,
                        arrivalName: this.pt.data.returningAirportPlace.cityName,
                        quoteTaskID: quoteTaskID
                    },
                }
                return data;
            },
            sendObjectPostForm(url, obj) {
                let form = document.createElement("form");
                form.setAttribute("method", "POST");
                form.setAttribute("action", url);
                for (let key in obj) {
                    if (obj[key] != null) {
                        let hiddenField = document.createElement("input");
                        hiddenField.setAttribute("type", "hidden");
                        hiddenField.setAttribute("name", key);
                        hiddenField.setAttribute("value", obj[key]);
                        form.appendChild(hiddenField);
                    }
                }
                document.body.appendChild(form);
                setTimeout(()=>{
                    form.submit();
                }, 1000)
            },
            firstAirline(value) {
                value = value.toUpperCase()
                return (this.configSite || []).sort((a, b) => ((a.airlineCode || []).find(code => code.toUpperCase() === value) || (a.airlineName).toUpperCase() === value) ? -1 : 1);
            },
            sortAirlines(airlines) {
                let addFirst = false;
                if (this.information.addFirst && this.information.addFirst !== 'home') {
                    this.configSite = this.firstAirline(this.information.addFirst)
                    addFirst = true;
                }
                else if (this.information.landing && this.information.landing !== 'home') {
                    this.configSite = this.firstAirline(this.information.landing)
                }
                const view = this.flightSelected?.step_action >= 3 ? "returning" : "departure";
                const priorityOrder = (this.quoteConfiguration?.priorityOrder || []);
                this.adjustables = this.configSite.filter((airlines, index) => (addFirst && index == 0) || (airlines.adjustable == true));
                airlines.sort((a, b) => {
                    if (a[view] && b[view]) {
                        const isAAdjustable = this.adjustables.some(adj => adj.airlineCode.includes(a[view].code));
                        const isBAdjustable = this.adjustables.some(adj => adj.airlineCode.includes(b[view].code));
                        // Priorizar ajustables por patrocinio o por addFirst
                        if (isAAdjustable && !isBAdjustable) return -1; // `a` va antes
                        if (!isAAdjustable && isBAdjustable) return 1;  // `b` va antes
                        if (isAAdjustable && isBAdjustable) {
                            return this.getAdjustableIndex(a[view].code) - this.getAdjustableIndex(b[view].code);
                        }
                        // Ordenar primero por precio
                        if (a[view].cheapest !== b[view].cheapest) {
                            return a[view].cheapest - b[view].cheapest;
                        }
                        // Si los precios son iguales, ordenar por la prioridad del código
                        return priorityOrder.indexOf(a[view].code) - priorityOrder.indexOf(b[view].code);
                    }
                });
                return airlines;
            },
            getAdjustableIndex(code) {
                return this.adjustables.findIndex(adj => adj.airlineCode.includes(code));
            },
            scrollEvent() {
                if (!this.isResponsive) {
                    var elementos = document.querySelectorAll(".list-flights-v-2");
                    elementos.forEach(elemento => {
                        var rect = elemento.getBoundingClientRect();
                        let all_c_types = document.querySelectorAll(".c-type-fly-v-2");
                        // Si el top del elemento es menor o igual a 0 y el bottom es mayor que 0, el elemento está tocando el borde superior.
                        if (rect.bottom <= 0) {
                            all_c_types.forEach(c_type_fly => {
                                c_type_fly.style.display = "none";
                            })
                        } else if (rect.top <= 0 && rect.bottom > 0) {
                            let c_type = elemento.querySelector(".c-type-fly-v-2");
                            if (c_type) {
                                c_type.style.display = "block";
                            }
                        }

                        if (all_c_types.length) {
                            all_c_types[0].style.display = "none";
                        }
                    })
                }
                this.scrollCBarInfo()//scroll responsivo de la barra de informacion
            },
            scrollCBarInfo() {
                document.querySelectorAll(".c-bar-info-0").forEach(element => {
                    var rect = element.getBoundingClientRect();
                    if (rect.top <= 10 && this.isResponsive) {
                        document.querySelectorAll(".hide-scroll-top-" + (element.getAttribute("data-airline"))).forEach(top => {
                            if (!top.classList.contains("d-none")) {
                                top.classList.add("d-none");
                            }
                        })
                        document.querySelectorAll(".show-scroll-top-" + (element.getAttribute("data-airline"))).forEach(top => {
                            if (top.classList.contains("d-none")) {
                                top.classList.remove("d-none");
                            }
                        })
                    } else {
                        document.querySelectorAll(".hide-scroll-top-" + (element.getAttribute("data-airline"))).forEach(top => {
                            if (top.classList.contains("d-none")) {
                                top.classList.remove("d-none");
                            }
                        })
                        document.querySelectorAll(".show-scroll-top-" + (element.getAttribute("data-airline"))).forEach(top => {
                            if (!top.classList.contains("d-none")) {
                                top.classList.add("d-none");
                            }
                        })
                    }
                })
            },
            eventsModalDetail() {
                const modalElement = document.getElementById('modalDetail');
                modalElement.addEventListener('hidden.bs.modal', (event) => {
                    document.querySelectorAll(".modal-backdrop").forEach(element => {
                        element.remove()
                    })
                    document.querySelector("body").removeAttribute('style');
                })
            },
            gDestination(key){
                return undefined
            }
        }

    }
</script>
<style>
.column-list-flights{
    .cf-name strong{
        color: #3B3A40 !important;
    }
}
.c-sticky-fam{
    .a-link{
        color: #186BDF !important;
        font-weight: 500 !important;
    }
}

@media (min-width: 992px) and (max-width: 1279px) {
    .c-multiticket {         
        .c-flights-list .ct-airline {
            padding-left: 15px;
            span {            
                font-size: 14px !important;
            }
        }
        .c-sticky-fam {
            .a-link {
                font-size: 15px !important;            
            }        
        }

        .column-list-flights {
            .cf-name {
                strong {
                    font-size: 12px !important;
                }                        
            }        
        }
        .cr-line {
            padding: 0 !important;
            .column-list-flights {
                padding-left: 25px !important;                
            }
        }
    }
}
@media (min-width: 992px) and (max-width: 1199px) {
    .c-multiticket {
        .ch-row .header-color p span {
            display: contents !important;            
        }  
        
        .shortNameFlight {
            .icon-keyboard-right {
                &:before {
                    position: relative;
                    top: 5px;
                    margin: 0 4px;              
                }
            }
        }
    }
}
.c-sticky-fam {
    background-color: #fff;
    position: sticky;
    top: 37px;
    z-index: 1;
    margin-left: 9px;
    margin-right: 6px;
    margin-top: 0;
    .cr-line {
        border: 2px solid #dee2e6;
        border-bottom: none;
        border-radius: 6px 6px 0 0;
        margin-top: 10px;
        @media (max-width: 767px) {
            border: 0;
            border-radius: 0;
            margin-top: 0;
        }
        @media (min-width: 768px) and (max-width: 990px) {
            margin-top: 0;
        }        
    }
    @media (max-width: 767px), (min-width: 768px) and (max-width: 990px) {
        margin: 0;
        top: 28px;
    }
}


@media (min-width: 1280px) {
    .c-multiticket {
        .cf-overflow {
            padding: 0 !important;
        }
        .cf-scroll {
            position: absolute !important;
            right: -5px;
            top: 20px;
        }
        .cf-scroll .c-all-filters {
            left: -581%;
            top: 46px !important;
        }       
        .cf-scroll .filter-full {
            left: -856px;
            top: 46px !important;
            width: 1280px;
        }
        .c-return {
            left: -753% !important;
        }       
        .c-sencillo {
            padding-top: 34px;
            .col-12 {
                .mb-md-3.mb-lg-0 {
                    position: relative;
                    top: -16px;
                }
            }            
        }
    }
}


</style>