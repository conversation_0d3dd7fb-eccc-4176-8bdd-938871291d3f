﻿using Microsoft.Extensions.Options;
using TBFront.Interfaces;
using TBFront.Models.Destination.Request;
using TBFront.Models.Destination.Response;
using TBFront.Options;

namespace TBFront.Application.Implementations
{
    public class DestinationHandler : IDestinationHandler
    {
        private readonly IQueryHandlerAsync<DestinationRequest, List<DestinationResponse>> _apiDestination;
        private readonly SettingsOptions _options;
        public DestinationHandler(IQueryHandlerAsync<DestinationRequest, List<DestinationResponse>> apiDestination, IOptions<SettingsOptions> options)
        {
            _apiDestination = apiDestination;
            _options = options.Value;
        }

        public async Task<List<DestinationResponse>> QueryAsync(DestinationRequest request, CancellationToken ct)
        {

            var response = await _apiDestination.QueryAsync(request, ct);

            return response;
        }
    }
}
