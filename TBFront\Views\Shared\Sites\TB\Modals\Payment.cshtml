﻿@using TBFront.Helpers
@inject ViewHelper viewHelper

@{
    var page = ViewData["page"];
}


<div class="modal fade modal-payform" id="modal-payform" tabindex="-1" role="dialog">
    <div class="modal-dialog  modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header d-flex align-items-center justify-content-between">
                <p class="modal-title h5">@viewHelper.Localizer("modal_payment_title")</p>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true" class="font-icons icons-close"></span>
                </button>
            </div>
            <div class="modal-body">

                <div class="row" ng-hide="vm.responsePayment.loading">




                    <div class="col-md-12 border-bottom">
                    @*     <div class="row">
                            <div class="col-12">
                                <p>*Las cuotas varían dependiendo de la entidad financiera que cobra los intereses.</p>
                            </div>
                        </div> *@

                      @*   <div class="row pb-2">
                            <div class="col-md-12 border-bottom" ng-repeat="item in vm.responsePayment.monthInterestFree">
                                <div class="row py-2">
                                    <div class="col-4 d-center">
                                        <span class="icon-bank-brand icon-brand-{{item.icon}}"></span>
                                    </div>
                                    <div class="col-8 d-center p-0">
                                        <p>
                                            <span>@viewHelper.Localizer("modal_payment_until") </span> <strong> {{item.paymentPlans[item.paymentPlans.length - 1 ]}} {{item.description.toLowerCase()}}</strong>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div> *@
                        <div class="row pb-2">
                            <div class="col-12 d-center ">
                                <p><strong>Recibimos todas las tarjetas de crédito y débito</strong></p>
                            </div>
                            <div class="col-12 d-center logos-ba">
                                <i class="icon-brand-visa"></i>
                                <i class="icon-brand-amex-short"></i>
                                <i class="icon-brand-dinersclub"></i>
                                <i class="icon-brand-mastercard"></i>
                            </div>
                        </div>
                    </div>



                    <div class="col-md-12">
                        <div class="row py-2">
                            <div class="col-md-12">
                                <p class="h6 m-0">
                                    <strong>¿No cuentas con tarjeta de crédito?</strong>
                                </p>
                                <p class="m-0 mt-1 text-sm gray-dark-color"><b>Tenemos más formas de pago:</b></p>
                            </div>
                        </div>

                    </div>

              

                    <div class="col-md-12">
                        <div class="row py-2 gray-black-color">
                            <div class="col-12 col-md-4 d-center my-3 my-md-0 content-banks-brand">
                                <span class="icon-bank-brand ptw-icon-bancolombia"></span>
                            </div>
                            <div class="col-12 col-md-8">
                                <p class="m-0"><strong class="gray-dark-color"> Botón Bancolombia</strong></p><p class="m-0">Paga en linea con tu cuenta de ahorro o corriente de Bancolombia</p>
                            </div>
                        </div>
                    </div>

                    

                    <div class="col-md-12">
                        <div class="row py-2 gray-black-color">
                            <div class="col-12 col-md-4 d-center my-3 my-md-0 content-banks-brand">
                                <span class="icon-bank-brand ptw-icon-efecty"></span></div><div class="col-12 col-md-8"><p class="m-0"><strong class="gray-dark-color">Pago con Efecty</strong></p><p class="m-0 ">
                                    Paga en efectivo con el código de recaudo 112232 y la referencia de pago en cualquier punto efecty a nivel nacional
                                </p>
                            </div>
                        </div>
                    </div>


                    <div class="col-md-12">
                        <div class="row py-2 gray-black-color">
                        <div class="col-12 col-md-4 d-center my-3 my-md-0 content-banks-brand">
                        <span class="icon-bank-brand ptw-icon-creditcard"></span></div>
                        <div class="col-12 col-md-8"><p class="m-0"><strong class="gray-dark-color"> Pago en línea con PSE</strong></p>
                        <p class="m-0 ng-binding">Paga en línea con tu cuenta corriente o de ahorros</p></div></div>
                    </div>

                    <div class="col-md-12">
                        <div class="row py-2 gray-black-color">
                            <div class="col-12 col-md-4 d-center my-3 my-md-0 content-banks-brand">
                                <span class="icon-bank-brand ptw-icon-multiple-co"></span>
                            </div>
                            <div class="col-12 col-md-8">
                                    <p class="m-0">
                            <strong class="gray-dark-color">Depósito Bancario</strong></p>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="row" ng-show="vm.responsePayment.loading">
                    <div class="col-12 d-center">
                        @await Html.PartialAsync("_Loading")
                    </div>
                </div>


            </div>



            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-dismiss="modal">Cerrar</button>
            </div>

        </div>
    </div>
</div>
