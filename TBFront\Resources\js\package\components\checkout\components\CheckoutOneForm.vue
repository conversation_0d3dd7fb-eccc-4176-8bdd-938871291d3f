<template>

	<!-- start forms 01 -->
	<div class="c-forms mb-3" v-for="(pax, idx) in user.passengers">
		<div class="pax-title">
				<label> {{ __(`messages.${pax.typePassenger}`) }} {{ pax.idx + 1 }}</label>
		</div>
		<div class="d-flex flex-wrap justify-content-between gap-3 px-3">
			<div class="flex-grow-1">
				<div class="form-group position-relative">
					<Field :validateOnInput="true" v-model="user.passengers[idx][`firstname`]"
						   :name="`first_name_${idx}`" rules="required|alpha_spaces" :ref="`first_name_${idx}`"
						   v-slot="{ field, errors, errorMessage }">

						<label class="font-12 px-1 label-xs">{{ __("checkout.first_name") }}</label>
						<input type="text" :id="`first_name_${idx}`" @change="validateFields(`first_name_${idx}`)"
							   class="form-control form__input" :class="{ 'is-invalid': errors && errors.length }"
							   v-bind="field" />
						<span v-if="errors[0]" class="invalid-feedback">{{ errors[0] }}</span>
					</Field>
				</div>
			</div>
			<div class="flex-grow-1">
				<div class="form-group position-relative">
					<Field :validateOnInput="true" v-model="user.passengers[idx][`lastname`]" :name="`last_name_${idx}`"
						   rules="required|alpha_spaces" :ref="`last_name_${idx}`"
						   v-slot="{ field, errors, errorMessage }">

						<label class="font-12 px-1 label-xs">{{ __("checkout.last_name") }}</label>
						<input type="text" :id="`last_name_${idx}`" class="form-control form__input"
							   :class="{ 'is-invalid': errors && errors.length }" v-bind="field" />
						<span v-if="errors[0]" class="invalid-feedback">{{ errors[0] }}</span>
					</Field>


				</div>
			</div>
		</div>
		<div class="d-flex flex-wrap justify-content-between gap-3 p-3 align-items-end">
			<div class="flex-grow-1">
						<div class="form-group position-relative">

							<Field :validateOnInput="true" v-model="user.passengers[idx][`identityDocument`]"
								   :name="`identityDocument_${idx}`" :rules="{required:true, uniqueNames: { fields : user.passengers, currentIndex: idx } }"
								   :ref="`identityDocument_${idx}`" v-slot="{ field, errors, errorMessage }">

								<label class="font-12 px-1 label-xs" v-if="pax.typePassenger == 'adult'">
									{{
									__("checkout.identification_code")
									}}
								</label>
								<label class="font-12 px-1 label-xs" v-else>
									{{ __("checkout.identification_code_minor") }}
								</label>
								<input type="text" :id="`identityDocument_${idx}`"
									   @change="validateFields(`identityDocument_${idx}`)" class="form-control form__input"
									   :class="{ 'is-invalid': errors && errors.length }" v-bind="field" />
								<span v-if="errors[0]" class="invalid-feedback">{{ errors[0] }}</span>

							</Field>
						</div>
			</div>
			<div class="flex-grow-1">
				<label style="padding-bottom: 8px;">{{ __("checkout.date_of_birth") }}</label>
				<div class="d-grid" style="grid-template-columns: 1fr 1fr 1fr; grid-column-gap: 8px;">
					<div class="form-group position-relative">
							<label class="font-12 px-1 label-xs">{{ __("checkout.day") }}</label>
							<Field :validateOnInput="true" v-model="user.passengers[idx].day_selected"
								   :name="`day_${idx}`" rules="required|is_not:0" :ref="`day_${idx}`"
								   v-slot="{ field, errors, errorMessage }">

								<select v-bind="field" :id="`day_${idx}`" :name="`day_${idx}`"
										class="form-control custom-select"
										:class="{ 'is-invalid': errors && errors.length }">
									<option v-for="option in user.passengers[idx].day_options" :value="option.value">
										{{ option.text }}
									</option>
								</select>
								<span v-if="errors[0]" class="invalid-feedback">{{ errors[0] }}</span>
							</Field>
					</div>
					<div class="form-group position-relative">
						<label class="font-12 px-1 label-xs">{{ __("checkout.month") }}</label>

						<Field :validateOnInput="true" v-model="user.passengers[idx].month_selected"
								   :name="`month_${idx}`" rules="required|is_not:0" :ref="`month_${idx}`"
								   v-slot="{ field, errors, errorMessage }">

								<select v-bind="field" :id="`month_${idx}`" :name="`month_${idx}`"
										class="form-control custom-select"
										:class="{ 'is-invalid': errors && errors.length }">
									<option v-for="option in user.passengers[idx].month_options" :value="option.value">
										{{ option.text }}
									</option>
								</select>
								<span v-if="errors[0]" class="invalid-feedback">{{ errors[0] }}</span>
							</Field>
					</div>
					<div class="form-group position-relative">
						<label class="font-12 px-1 label-xs">{{ __("checkout.year") }}</label>
						<Field :validateOnInput="true" v-model="user.passengers[idx].year_selected"
								   :name="`year_${idx}`" rules="required|is_not:0" :ref="`year_${idx}`"
								   v-slot="{ field, errors, errorMessage }">

								<select v-bind="field" :id="`year_${idx}`" :name="`year_${idx}`"
										class="form-control custom-select"
										:class="{ 'is-invalid': errors && errors.length }">
									<option v-for="option in user.passengers[idx].year_options" :value="option.value">
										{{ option.text }}
									</option>
								</select>
								<span v-if="errors[0]" class="invalid-feedback">{{ errors[0] }}</span>
							</Field>
					</div>
					<div class="col-12">
						<small class="invalid-feedback d-block" v-if="user.passengers[idx].isInvalidDate">
							{{ __('errors.date_invalid') }}
						</small>
					</div>
				</div>

			</div>
		</div>
	</div>


	<!-- start forms 3 -->
	<div class="c-forms mb-3">
		<div class="pax-title">
			<label>{{ __("checkout.contact_information") }}</label>
		</div>
		<div class="d-flex flex-wrap justify-content-between gap-3 p-3 pt-0">
			<div class="flex-grow-1">
				<div class="form-group position-relative">

					<Field :validateOnInput="true" v-model="user.phone" :name="`phone`" rules="required|integer"
						   :ref="`phone`" v-slot="{ field, errors, errorMessage }">

						<label class="font-12 px-1 label-xs" id="tel-label">{{ __("checkout.phone") }}</label>
						<input type="tel" :id="`phone`" @change="validateField(`phone`)"
							   class="form-control form__input" style="padding-left: 52px;"
							   :class="{ 'is-invalid': errors && errors.length }" v-bind="field" />
						<span v-if="errors[0]" class="invalid-feedback  d-block">{{ errors[0] }}</span>
					</Field>

				</div>
			</div>
			<div class="flex-grow-1">
				<div class="form-group position-relative">
					<Field :validateOnInput="true" v-model="user.email" :name="`email`" rules="required|email"
						   :ref="`email`" v-slot="{ field, errors, errorMessage }">
						<EmailAutocompleteField v-bind="field"
												:error-message="errorMessage"
												:errors="errors"
												@validate="validateField"/>
					</Field>
				</div>
			</div>
		</div>
	</div>

	<!-- start button -->
	<div class="row mb-3">
		<div class="col-12 col-md-7">
			<p class="font-14">
				{{ __('checkout.checkout_legals') }}
				<a href="javascript:void(0)" class="a-link-1" data-bs-toggle="modal" data-bs-target="#modal-policy">
					{{ __('checkout.checkout_legals_terms') }}
				</a>
			</p>
		</div>
		<div class="col-12 col-md-5">
			<button id="bookAndPay" :disabled="disabledSubmitButton" class="btn btn-primary w-100 py-3">{{ __("checkout.booknow") }}</button>
		</div>
	</div>


	<security-information :css="'d-block d-sm-none'"></security-information>



</template>

<script>
	import intlTelInput from 'intl-tel-input';
	import { Field } from 'vee-validate';
	import { isAdult, isMinor, isInfant } from '../../../../utils/utils';
	import { Logger } from '../../../../utils/helpers/logger';

	const configSite = {};
	const culture = window.__pt.cultureData;
	const nationality = window.__pt.settings.formData.Nationality;
	export default {
		props: ['user', 'data', 'disabledSubmitButton'],
		components: {
			Field,
		},
		data() {
			return {
				config: configSite,

			}
		},
		mounted() {
			this.onInit();

		},
		methods: {

			onInit() {
				const input = document.querySelector("#phone");
				let iti = intlTelInput(input, {
					initialCountry: nationality,
					preferredCountries: nationality
				});

				input.addEventListener("countrychange", () => {
					const selected = iti.getSelectedCountryData();
					if (selected) {
						this.user.dial_code = selected.dialCode;
					}
				});

				var countryData = iti.getSelectedCountryData();

				if (countryData) {
					this.user.dial_code = countryData.dialCode;
				}

				let label = document.getElementById("tel-label");
				if (input) {
					input.after(label)
				}
			},
			async validateFields(field) {
				const provider = this.$refs[field][0];
				let result = await provider.validate();
				if (result && !result.valid) {
					Logger.error("PackageAnalytic.trackUserError");
					//PackageAnalytic.trackUserError(field, result, this.__('messages.enter_your_data'))
				}
			},
			async validateField(field) {
				const provider = this.$refs[field];
				let result = await provider.validate();
				if (result && !result.valid) {
					Logger.error("PackageAnalytic.trackUserError");
					//FlightsAnalytic.trackUserError(field, result, this.__('messages.enter_your_data'))
				}
			},
			async validateAges() {
				let isInvalidDate = false;
				let dataUser = this.user;
				let isValidDate = false;
				for (let index = 0; index < dataUser.passengers.length; index++) {
					let dateStr = `${dataUser.passengers[index].year_selected}-${dataUser.passengers[index].month_selected}-${dataUser.passengers[index].day_selected}`;

					if (dataUser.passengers[index].typePassenger == "adult") {
						isValidDate = isAdult(dateStr);

					} else if (dataUser.passengers[index].typePassenger == "child") {
						isValidDate = isMinor(dateStr, this.data.extraInfoFlight.startingFromDateTime);

					} else { // infant
						isValidDate = isInfant(dateStr, this.data.extraInfoFlight.startingFromDateTime);
					}


					if (!isValidDate) {
						dataUser.passengers[index].isInvalidDate = true;

						isInvalidDate = true;
					} else {
						dataUser.passengers[index].isInvalidDate = false;
					}
				}
				return { isInvalidDate, dataUser };
			}
		}
	}
</script>