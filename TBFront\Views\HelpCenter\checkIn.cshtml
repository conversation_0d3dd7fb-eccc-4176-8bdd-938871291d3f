﻿@using TBFront.Helpers
@using Microsoft.Extensions.Options
@using TBFront.Options;
@using TBFront.Types;
@using TBFront.Models.Meta.Metatags;
@using TBFront.Models.Request;

@inject IOptions<SettingsOptions> settingOptions
@inject IOptions<SiteOptions> siteOptions
@inject IOptions<CurrencyOptions> currencyOptions

@inject ViewHelper viewHelper
@inject StaticHelper staticHelper

@inject ViewHelper _
@inject StaticHelper staticHelper
@{
    var resolution = _.GetImageResolution();
    var isMobile = resolution.Device == DeviceType.Mobile;
    var isRobot = _.IsRobot();
    var airlinesList = siteOptions.Value.airlinesCheckIn;
    var metaTag = ViewData["MetaTag"] as MetaTag;
    var culture = ViewData["CultureData"] as Culture;
    string languaje = (string)culture.Language;
    ViewData["Page"] = "checkin";

    ViewData["IsRobot"] = isRobot;
    ViewData["isMobile"] = isMobile;
    ViewData["Resolution"] = resolution;

}

@await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.Code}/_Header.cshtml", new ViewDataDictionary(ViewData) { { "navs", true }, { "login", isRobot } })


<div class="container c-web-checkin py-3 pt-md-5">
    <div class="title">
        <b>@_.Localizer("titleWebCheckin")</b>
    </div>

    <div class="row mb-4">
        <div class="col-12">
            <p class="text-center">
                @_.Localizer("textCheckin")
            </p>
        </div>
    </div>

    <div class="row">
        <div class="airlines-container" style="margin-bottom:100px;">
            @foreach (var airline in airlinesList.OrderBy(a => a.name))
        {
            <label class="airline-option">
                <input type="radio" name="airline" value="@airline.name">
                <img src="@airline.img" alt="@airline.name">
                    <a href="@(airline.url?.ContainsKey(languaje) == true ? airline.url[languaje] : airline.url["es"])"
                       target="_blank"
                       title="@airline.name">@airline.name</a>
            </label>
            
        }
        </div>
    </div>

</div>

<div>
    @await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.Code}/_Footer.cshtml", new ViewDataDictionary(ViewData) { { "newsletter", false } })
</div>

@section Css {
    <link type="text/css" rel="stylesheet"
      href="@staticHelper.GetVersion($"/{settingOptions.Value.SiteName}/css/checkin.css", settingOptions.Value.Assets)">
}


@section Meta {
    @await Html.PartialAsync("_MetaMain", new ViewDataDictionary(ViewData) { { "Meta", ViewData["MetaTag"] }})
}


@section Scripts {
    <script>
        window.__pt.cultureData = @Json.Serialize((ViewData["CultureData"]));
        window.__pt.exchange = @Json.Serialize((ViewData["Exchange"]));
        window.__pt.alternates = @Json.Serialize((ViewData["Alternates"]));
        window.__pt.currency = @Json.Serialize((ViewData["CurrencyData"]));
        window.__pt.currencies = @Json.Serialize((currencyOptions.Value.Currencies));
    </script>
    <script src="@staticHelper.GetVersion("/js/vue/app.js", settingOptions.Value.Assets)"></script>
}

 