﻿using TBFront.Models.Common;
using TBFront.Models.Flight.Quote;

namespace TBFront.Models.Flight.CreateBooking
{
    public class CreateBookingRequest
    {
        public string ExternalId { get; set; }
        public string KeyValidation { get; set; }
        public int Channel { get; set; }
        public CustomerInformation CustomerInformation { get; set; }
        public ReservationSetting ReservationSettings { get; set; }
        public ServiceFlightItem ServiceItems { get; set; }
        public List<ExtraInformation> ExtraInformation { get; set; }

        public CreateBookingRequest()
        {
            CustomerInformation = new CustomerInformation();
            ReservationSettings = new ReservationSetting();
            ExtraInformation = new List<ExtraInformation>();
        }
    }

    public class ServiceFlightItem
    {
        public double TotalAmount { get; set; }
        public List<FlightItem> Flights { get; set; }

        public ServiceFlightItem()
        {
            Flights = new List<FlightItem>();
        }

    }

    public class CustomerInformation
    {
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
        public string MobilePhone { get; set; }
    }
    public class ExtraInformation
    {
        public int Type { get; set; }
        public string Value { get; set; }
       
    }



    public class ReservationSetting
    {
        public string Currency { get; set; }
        public string Language { get; set; }
        public string IpAddress { get; set; }
        public int OrganizationId { get; set; }

    }

    public class FlightItem
    {
        public int TripMode { get; set; }
        public int TripCabin { get; set; }
        public string FareKey { get; set; }
        public bool GetAllFares { get; set; }
        public bool NonStopOnly { get; set; }
        public int Seniors { get; set; }
        public string PromotionalCode { get; set; }
        public bool IsPackageRate { get; set; }
        public int Type { get; set; }
        public double TotalAmount { get; set; }
        public int Adults { get; set; } //<-- validar tipo de dato
        public List<int> ChildAges { get; set; }
        public int Infants { get; set; } //<-- validar tipo de dato
        public int Infant { get; set; } //<-- validar tipo de dato
        public EmergencyContact EmergencyContact { get; set; }
        public List<PassengerItem> FlightPassengers { get; set; }

        public RevalidateCreateBookingRequest Revalidate { get; set; }



        public FlightItem()
        {
            ChildAges = new List<int>();
            EmergencyContact = new EmergencyContact();
            FlightPassengers = new List<PassengerItem>();
        }

    }


    public class EmergencyContact
    {
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string PhoneNumber { get; set; }
        public string Title { get; set; }
        public bool WasProvided { get; set; }

        public EmergencyContact()
        {
            FirstName = string.Empty;
            LastName = string.Empty;
            PhoneNumber = string.Empty;
            Title = string.Empty;
        }

    }


    public class RevalidateCreateBookingRequest
    {

        public string FareKey { get; set; }
        public string TaskID { get; set; }
        public Context Context { get; set; }
/*        public bool IsPackage { get; set; }
        public string Currency { get; set; }
        public bool ShowDetailAmounts { get; set; }
        public bool ShowRevenueByLeg { get; set; }
        public string ValidatingCarrierCode { get; set; }
        public Dictionary<string, Quote.Flight> Flights { get; set; }
        public Dictionary<string, FareLeg> Fares { get; set; }
        public Dictionary<string, Dictionary<string, BookingInfo>> BookingInfos { get; set; }
        public Recommendation Recommendation { get; set; }
        public List<PassengerBooking> Passengers { get; set; }

        public RevalidateCreateBookingRequest()
        {
            Passengers = new List<PassengerBooking>();
        }*/
    }

    public class PassengerItem
    {
        public string Title { get; set; }
        public int Type { get; set; }
        public string Names { get; set; }
        public string LastNames { get; set; }
        public int PassengerIndex { get; set; }
        public int PassengerNumber { get; set; }
        public bool RequireAdditionalUsaInformation { get; set; }
        public int Sex { get; set; }
        public int BirthDateDay { get; set; }
        public int BirthDateMonth { get; set; }
        public int BirthDateYear { get; set; }
        public string Nationality { get; set; }
        public int CustomerIdentityDocumentType { get; set; }
        public string CustomerDocumentNumber { get; set; }
        public string FrequentFlyerNumber { get; set; }
        public string FrequentFlyerProgram { get; set; }
        public object SpecialAssist { get; set; } //<- preguntar por modelo
        public object CustomerId { get; set; } //<- preguntar por modelo
        public object ExtraServicesCodes { get; set; } //<- preguntar por modelo
        public EmergencyContact EmergencyContact { get; set; }

        public PassengerItem()
        {
            EmergencyContact = new EmergencyContact();
        }
    }


    public class PassengerBooking
    {
        public int Type { get; set; }
        public int Quantity { get; set; }
        public int Age { get; set; }
    }

    public class PassengerRequest
    {
        public string? Firstname { get; set; }
        public string? Lastname { get; set; }
        public string? Type { get; set; }
        public int? Age { get; set; }
        public string? Prefix { get; set; }
        public bool? IsChild { get; set; }
        public int Gender { get; set; }
        public string? Nationality { get; set; }
        public string? Birthday { get; set; }
        public string? IdentityDocument { get; set; }
    }
}
