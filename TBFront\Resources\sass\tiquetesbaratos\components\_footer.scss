@import "../_variables.scss";

$font-family_1: Roboto,sans-serif;
$background-color_1: unset;
$background-color_2: rgba(255, 255, 255, .77);
$background-color_3: #2a6acf;
$border-color_1: #ffffffb3;
$background-color_18: #003b98;

.c-banner-full {
    background-repeat: no-repeat;
    background-size: cover;
    background-position: 0 10%;
    position: relative;
}

.imgcomprasegura {
    background: url(/assets-tb/img/tiquetesbaratos/comprasegura.png);
    background-repeat: no-repeat;
    width: 100%;
    height: 122px;
    background-position: center;
    background-size: contain;
}

.footer-group-heading {
    border-bottom: 1px solid #c2c2c2;
    padding: 10px 5px;
    cursor: pointer;
    font-family: Roboto-Regular;
}

.c-footer {
    background-color: $background-color_18;

    ul {
        li {
            color: $color_1;
            font-family: $font-family_2;
            font-size: 12px;
            margin-bottom: 15px;
        }
    }

    .accordion-item {
        color: unset;
        background-color: $background-color_1;
    }

    .social {
        @media ($tablet) {
            text-align: right;
        }
    }

    .c-newsletter {
        border-radius: 10px;
        position: relative;
        top: -30px;

        h3 {
            color: inherit;
            font-size: 26px;

            @media ($desktop) {
                font-size: 26px;
            }
        }


        ul {
            li {
                color: $white;
                font-family: $font-family_1;
                font-size: 12px;
                margin-bottom: 15px;
            }

            .navbar-brand {
                img {
                    width: 100px;
                }
            }

            .c-social {
                a {
                    background-color: $white;
                    display: inline-block;
                    border-radius: 50px;
                    margin: 0 5px;
                    padding: 4px;
                    transition: all .2s linear;
                    vertical-align: middle;

                    span {
                        color: $white;
                        display: block;
                        text-align: center;
                    }

                    hr {
                        border-color: $border-color_1;
                    }
                }

                & {
                    &:hover {
                        text-decoration: none;
                        background-color: $background-color_2;
                    }
                }

                footer {
                    h4 {
                        height: 40px;
                        font-size: calc(.8875rem + .15vw);
                    }

                    .font-icons {
                        font-size: 24px;
                    }
                }

                @media (max-width: 767px) {
                    .footer-group-heading {
                        border-bottom: 1px solid #2a6acf;

                        &:hover {
                            background-color: $background-color_3;
                        }
                    }

                    .c-banner-full {
                        background-size: inherit;
                        background-position: 60% 0;
                    }
                }
            }
        }
    }

    .c-social {
        text-align: left;

        a {
            background-color: #fff;
            display: inline-block;
            border-radius: 50px;
            margin: 0 5px;
            padding: 10px;
            transition: all .2s linear;
            width: 37px;

            span {
                color: #003b98;
                display: block;
                text-align: center;
            }

            &:hover {
                text-decoration: none;
                background-color: $background-color_19;
            }
        }
    }

    .navbar-brand {
        img {
            width: 100px;
        }
    }

    hr {
        border-color: $border-color_7;
    }
}
