﻿using System.Net;

namespace TBFront.Models.Login
{
    public class ApiResponse<T>
    {
        public T Data { get; set; }
        public string Response { get; set; }
        public HttpStatusCode Status { get; set; }
        public List<string> Errors { get; set; }

        public ApiResponse(T data, string response, HttpStatusCode status = HttpStatusCode.OK, List<string> errors = null)
        {
            Data = data;
            Response = response;
            Status = status;
            Errors = errors ?? new List<string>();
        }
    }
}
