@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@600&family=Roboto:ital,wght@0,100..900;1,100..900&display=swap');

@font-face {
	font-family: Roboto-Regular;
	src: url(/assets-tb/fonts/Roboto-Regular.woff2?id=0946062e-5336-437f-84a9-f57653d8e341) format("woff2");
	font-weight: 400;
	font-style: normal;
	font-display: swap
}

@font-face {
	font-family: Roboto-Light;
	src: url(/assets-tb/fonts/Roboto-Light.woff2?id=0946062e-5336-437f-84a9-f57653d8e341) format("woff2");
	font-weight: 300;
	font-style: normal;
	font-display: swap
}

@font-face {
	font-family: Roboto-Medium;
	src: url(/assets-tb/fonts/Roboto-Medium.woff2?id=0946062e-5336-437f-84a9-f57653d8e341) format("woff2");
	font-weight: 500;
	font-style: normal;
	font-display: swap
}

@font-face {
	font-family: Roboto-Bold;
	src: url(/assets-tb/fonts/Roboto-Bold.woff2?id=0946062e-5336-437f-84a9-f57653d8e341) format("woff2");
	font-weight: 700;
	font-style: normal;
	font-display: swap
}

@font-face {
	font-family: Poppins;
	src: url(/assets-tb/fonts/Poppins-Regular.woff2?id=0946062e-5336-437f-84a9-f57653d8e341) format("woff2");
	font-weight: 400;
	font-style: normal;
	font-display: swap
}

@font-face {
	font-family: Poppins-semibold;
	src: url(/assets-tb/fonts/Poppins-SemiBold.woff2?id=0946062e-5336-437f-84a9-f57653d8e341) format("woff2");
	font-weight: 600;
	font-style: normal;
	font-display: swap
}

@font-face {
	font-family: Poppins-medium;
	src: url(/assets-tb/fonts/Poppins-Medium.woff2?id=0946062e-5336-437f-84a9-f57653d8e341) format("woff2");
	font-weight: 500;
	font-style: normal;
	font-display: swap
}

.f-r-medium, .f-r-semibold {
	font-family: Roboto-Medium;
}

.text-shadow-2 {
	text-shadow: 2px 2px 6px rgba(0,0,0,.4);
}

.font-roboto-medium {
	font-family: Roboto-Medium;
}

.font-weight-bold {
	font-weight: bold;
}

h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6 {
	font-family: Poppins-semibold;
}

h1, .h1 .display-1 {
	font-size: 32px;
	font-weight: 600;
}

h4, .display-2 {
	font-size: 20px;
	font-weight: 600;
}

.f-p-semibold {
    font-family: Poppins-semibold;
}

.fs-5 {
	font-size: 18px !important;
}

strong, b, .fw-bold {
	font-weight: bold !important;
}

.small, small {
	font-size: 12px;
}


.font-10 {
	font-size: 10px !important;
}

.font-12 {
	font-size: 12px !important;
}

.font-14 {
	font-size: 14px !important;
}
.font-15 {
	font-size: 15px !important;
}

.font-16 {
	font-size: 16px !important;
}

.font-18 {
	font-size: 18px !important;
}

.font-20 {
	font-size: 20px;
}

.font-22 {
	font-size: 22px !important;
}

.font-24 {
	font-size: 24px !important;
}

.font-28 {
	font-size: 28px !important;
}

.font-26 {
	font-size: 26px !important;
}

.font-30 {
	font-size: 30px !important;
}

.font-34 {
	font-size: 34px !important;
}

.font-40 {
	font-size: 40px !important;
}

.font-50 {
	font-size: 50px !important;
}

.font-60 {
	font-size: 60px !important;
}

.font-poppins {
	font-family: 'Poppins';
}

.font-poppins-medium {
	font-family: 'Poppins-medium';
}

.c-vg-form {
	background-color: #f8fafc;
	margin-bottom: 100px;
	padding: 25px 60px;
}
.font-roboto {
	font-family: Roboto-Regular;
}
.fw-100 {
	font-weight: 100;
}