﻿using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Extensions.Options;
using TBFront.Models.Request;
using TBFront.Models.Response;
using TBFront.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text.Json;
using System.Threading.Tasks;
using TBFront.Infrastructure.HttpService.PaymentGateway.Dtos;
using TBFront.Models.BookingItinerary;

namespace TBFront.Infrastructure.HttpService.BookingService
{
    public class BookingService
    {
        private readonly IHttpClientFactory _clientFactory;
        private readonly SettingsOptions _options;
        private readonly PaymentGatewayConfiguration _paymentGatewayconfiguration;

        public BookingService(IHttpClientFactory clientFactory, IOptions<SettingsOptions> options, PaymentGatewayConfiguration paymentGatewayconfiguration)
        {
            _clientFactory = clientFactory;
            _options = options.Value;
            _paymentGatewayconfiguration = paymentGatewayconfiguration;
        }

        public async Task<ItineraryResponse> QueryAsync(string id, string email)
        {
            var uriService = $"{_options.UrlBookingService}";

            var httpClient = _clientFactory.CreateClient();

            var request = GetQueryItynerary(id, email);

            var token = await AuthToken();

            var payload = JsonSerializer.Serialize(request);

            var body = new StringContent(payload);

            body.Headers.ContentType = new MediaTypeHeaderValue("application/json");

            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token.AuthenticationResult.Token);

            var httpResponseMessage = await httpClient.PostAsync(uriService, body);

            using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync();

            var response = await JsonSerializer.DeserializeAsync<ItineraryResponse>(contentStream, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

            return response;
        }

        public async Task<List<ClientTokenInfoResponse>> QueryAsync(string keyValidation)
        {

            var httpClient = _clientFactory.CreateClient();

            var query = new Dictionary<string, string>()
            {
                ["token"] = keyValidation

            };

            var url = $"{_paymentGatewayconfiguration.Uri}{_paymentGatewayconfiguration.PathPaymentGetClientInfo}";
            var uri = QueryHelpers.AddQueryString(url, query);

            var httpResponseMessage = await httpClient.GetAsync(uri);

            using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync();

            var response = await JsonSerializer.DeserializeAsync<List<ClientTokenInfoResponse>>(contentStream, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

            return response;
        }

        private async Task<AuthTokenResponse> AuthToken()
        {
            var uriService = $"{_options.UrlAuthToken}";

            var httpClient = _clientFactory.CreateClient();

            var request = GetAuthTokenParams();

            var body = new StringContent(JsonSerializer.Serialize(request));
            body.Headers.ContentType = new MediaTypeHeaderValue("application/json");

            var httpResponseMessage = await httpClient.PostAsync(uriService, body);

            using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync();

            var response = await JsonSerializer.DeserializeAsync<AuthTokenResponse>(contentStream, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

            return response;
        }


        private AuthTokenRequest GetAuthTokenParams()
        {
            var data = new AuthTokenRequest
            {
                ClientId = _options.ClientId,
                ClientSecret = _options.ClientSecret,
                Organization = "PTH",
                GrantType = "client_credential"
            };

            return data;
        }

        private ItineraryRequest GetQueryItynerary(string id, string email)
        {
            var itineraryRequest = new ItineraryRequest();
            /*var fields = new List<string>();
            var query = "travelItinerary(bookingId:{id},customerEmail:\"{email}\")";

            fields.Add("bookingId");
            fields.Add("organizationId");
            fields.Add("isQuote");
            fields.Add("customerFirstName");
            fields.Add("customerLastName");
            fields.Add("customerEmail");
            fields.Add("createdDate");*/



            var query = "{ travelItinerary(bookingId:{id},customerEmail:\"{email}\") { bookingId organizationId isQuote customerName customerFirstName customerLastName customerEmail correlationId createdDate minServiceDate channelId branchId tags currency bookingServices{ serviceId description serviceType adults kids confirmationCode startDate endDate serviceCarrierName serviceCarrierCode serviceProviderId serviceCarrierDescription isCancelled specialRequest mealPlan cancellationDate isOnRequest mealPlanCode collectType startHour endHour providerCancellationPolicies { isDefault limitDays chargePercentage chargeNights chargeAmount chargeCurrency startDate endDate } rate { taxScheme } serviceCharge { amountDiscount serviceAmountTotal serviceAmountBalance serviceAmountPaid } serviceInfo { engine serviceNumber luggage segments{ departureCode departureName departureDate arrivalCode arrivalName arrivalDate operatingCode operatingName } passengers { name seat ticketNumber identityDocument birthDate } } } bookingPayments { paymentType paymentDescription paymentNumber paymentAmount chargedAmount } } }";
            query = query.Replace("{id}", id).Replace("{email}", email);

            itineraryRequest.Query = query;

            return itineraryRequest;
        }

    }
}
