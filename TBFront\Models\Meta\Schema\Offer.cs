﻿using System.Text.Json.Serialization;

namespace TBFront.Models.Meta.Schema
{
    public class Offer
    {
        [JsonPropertyName("@type")]
        public string? Type { get; set; }

        [JsonPropertyName("price")]
        public int Price { get; set; }

        [JsonPropertyName("priceCurrency")]
        public string? PriceCurrency { get; set; }
        [JsonPropertyName("url")]
        public string? Url { get; set; }

        [JsonPropertyName("itemCondition")]
        public string? ItemCondition { get; set; }

        [JsonPropertyName("availability")]
        public string? Availability { get; set; }

        [JsonPropertyName("priceValidUntil")]
        public string? PriceValidUntil { get; set; }
    }
}
