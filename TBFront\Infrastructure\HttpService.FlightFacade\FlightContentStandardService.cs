﻿using Hotel.Content.Standard.Dtos;
using Hotel.Content.Standard.Services;
using Hotel.Content.Standard.Services.Implementations;
using TBFront.Infrastructure.HttpService.FlightFacade.Dtos;
using TBFront.Interfaces;
using TBFront.Models.Places.Request;

namespace TBFront.Infrastructure.HttpService.FlightFacade
{
    public class FlightContentStandardService : IFlightContentStandardService
    {
        private readonly IHotelContentDetailHandler _hotelContentDetailHandler;
        private readonly ILogger<FlightContentStandardService> _logger;
        private readonly ICacheService _cache;
        public FlightContentStandardService(FlightFacadeConfiguration options, ILogger<FlightContentStandardService> logger, ICacheService cache)
        {
            _cache = cache;
            _logger = logger;
            _hotelContentDetailHandler = new HotelContentDetailHandler(options.Production);
        }
        public async Task<FrontFlightContentDetailResponse> QueryAsync(PlaceRequest request, CancellationToken ct)
        {
            var hotelRequest = new HotelContentDetailRequest
            {
                Culture = request.InternalCulture.ToUpper(),
                HotelId = request.Id!.Value
            };
            var response = await _hotelContentDetailHandler.QueryAsync(hotelRequest, ct);
            return new FrontFlightContentDetailResponse
            {
                Culture = request.Culture,
                Hotel = response
            };
        }

    }
}