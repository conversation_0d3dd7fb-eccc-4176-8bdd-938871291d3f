﻿@using TBFront.Helpers

@inject ViewHelper viewHelper


<div class="row">

    <div class="col-12" ng-show="!form_login.$submitted">
        <p class="h4">@viewHelper.Localizer("forgot_password")</p>
        <p>@viewHelper.Localizer("forgot_password_string_1")</p>
    </div>
    <div class="col-12" ng-show="form_login.$submitted">
        <p class="h4">Revisa tu correo</p>
        <p class="line_height">
            Enviamos un correo<strong> {{ vm.userData.email }} </strong>con un link para restablecer tu
            contraseña. Recuerda revisar tu bandeja de spam.
        </p>
    </div>

</div>


<div class="row my-4" ng-show="!form_login.$submitted">
    <div class="col-12">
        <label for="email_login_forget">@viewHelper.Localizer("enter_email")</label>
        <div class="input-group mb-3" ng-class="{'is--danger' : vm.hasError(form_login, 'email_login_forget') }">
            <div class="input-group-prepend">
                <span class="input-group-text">
                   <span class="font-icons icons-email"></span>
                </span>
            </div>
            <input type="email" id="email_login_forget" required name="email_login_forget" ng-model="vm.userData.email"
                   placeholder="@viewHelper.Localizer("enter_email")" class="form-control input-custom">
        </div>
        <div class="invalid-feedback" ng-show="vm.hasError(form_login, 'email_login_forget')">
            @viewHelper.Localizer("enter_email")
        </div>
    </div>

</div>

<div class="row" ng-show="form_login.$submitted">
    <div class="col-12">
        <small>@viewHelper.Localizer("no_received_yet") </small>
        <button type="button" name="button" class="btn btn-link btn-link-inline" ng-click="vm.recoveryPassword()">
            @viewHelper.Localizer("resend")
        </button>
    </div>
</div>


<div class="row" ng-show="!form_login.$submitted">
    <div class="col-12">
        <button type="submit" name="button" class="btn btn-primary btn-block p-3">@viewHelper.Localizer("send")</button>
    </div>
</div>

