﻿namespace TBFront.Models.BookingItinerary
{
    public class ItineraryResponse
    {
        public BookingData Data { get; set; }
        public List<ItineraryErrorResponse> Errors { get; set; } = new List<ItineraryErrorResponse>();
    }

    public class ItineraryErrorResponse
    {
        public string Message { get; set; }
    }


    public class BookingItineraryService
    {
        public string? ServiceId { get; set; }
        public string Description { get; set; } = string.Empty;
        public int ServiceType { get; set; }
        public int Adults { get; set; }
        public int Kids { get; set; }
        public string? ConfirmationCode { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string? ServiceCarrierName { get; set; }
        public string ServiceCarrierCode { get; set; } = string.Empty;
        public int ServiceProviderId { get; set; }
        public string? ServiceCarrierDescription { get; set; }
        public bool IsCancelled { get; set; }
        public string? SpecialRequest { get; set; }
        public string? MealPlan { get; set; }
        public DateTime CancellationDate { get; set; }
        public bool IsOnRequest { get; set; }
        public string? MealPlanCode { get; set; }
        public int CollectType { get; set; }
        public string? StartHour { get; set; }
        public string? EndHour { get; set; }
        public List<ProviderCancellationPolicy> ProviderCancellationPolicies { get; set; }
        public RateItinerary Rate { get; set; }
        public ServiceCharge ServiceCharge { get; set; }
        public ServiceInfo ServiceInfo { get; set; }
    }

    public class BookingData
    {
        public TravelItinerary TravelItinerary { get; set; }
    }

    public class ProviderCancellationPolicy
    {
        public bool IsDefault { get; set; }
        public int LimitDays { get; set; }
        public double ChargePercentage { get; set; }
        //public int ChargeNights { get; set; }
        public double ChargeAmount { get; set; }
        public string? ChargeCurrency { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
    }

    public class RateItinerary
    {
        public int TaxScheme { get; set; }
    }


    public class ServiceCharge
    {
        public double AmountDiscount { get; set; }
        public double ServiceAmountTotal { get; set; }
        public double ServiceAmountBalance { get; set; }
        public double ServiceAmountPaid { get; set; }
    }

    public class ServiceInfo
    {
        public int Engine { get; set; }
        public string ServiceNumber { get; set; }
        public string Luggage { get; set; }
        public List<Segments> Segments { get; set; }
        //public List<object> Passengers { get; set; }
    }

    public class Segments
    {
        public string? DepartureCode { get; set; }
        public string? DepartureName { get; set; }
        public string? DepartureDate { get; set; }
        public string? ArrivalCode { get; set; }
        public string? ArrivalName { get; set; }
        public string? ArrivalDate { get; set; }
        public string? OperatingCode { get; set; }
        public string? OperatingName { get; set; }

    }

    public class TravelItinerary
    {
        public int BookingId { get; set; }
        public int OrganizationId { get; set; }
        public bool IsQuote { get; set; }
        public string CustomerName { get; set; }
        public string CustomerFirstName { get; set; }
        public string CustomerLastName { get; set; }
        public string CustomerEmail { get; set; }
        public string CorrelationId { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime MinServiceDate { get; set; }
        public int ChannelId { get; set; }
        public int BranchId { get; set; }
        public string Tags { get; set; }
        public string Currency { get; set; }
        public List<BookingItineraryService> BookingServices { get; set; }
        public List<BookingPayment> BookingPayments { get; set; }

        public List<string> TagsList => GetTags();

        private List<string> GetTags()
        {
            var tags = new List<string>();

            if (!string.IsNullOrEmpty(this.Tags))
            {
                tags = this.Tags.Split(",").ToList();

            }

            return tags; ;
        }
    }

    public class BookingPayment
    {
        public int PaymentType { get; set; }
        public string PaymentDescription { get; set; }
        public int PaymentNumber { get; set; }
        public double PaymentAmount { get; set; }
        public double ChargedAmount { get; set; }
    }
}
