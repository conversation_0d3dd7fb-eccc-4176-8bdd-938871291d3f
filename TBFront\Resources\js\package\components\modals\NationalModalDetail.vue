<template>
    <div class="modal fade c-md-national" id="modalDetailNational" tabindex="-1" aria-labelledby="exampleModalDetail01" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-md">
            <div class="modal-content">
                <div class="modal-header">
                    <p v-if="flightDetail.flightSegments && configData.isRoundtrip" class="font-24 font-poppins-medium mb-0">
                        {{getExtraData.view}}
                    </p>
                    <h4 class="mb-0" v-if="flightFamilyFare.familyFareName">{{__('messages.what_includes')}}</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div :class="['cm-family-0'+(indexFamily() + 1)]" class=" modal-body pt-0 px-3 c-view-detail">
                    <template v-if="!showDetail">
                        <div class="int-xs px-2">
                            <div id="detailInter" class="col-12  px-md-0 pr-0 pr-md-2">
                                <div class="row mx-0">
                                    <template v-if="flightDetail.flightSegments">
                                        <div id="flight-info" class="p-2 pb-3  mt-0 w-100 bg-white">
                                            <div class="row">
                                                <div class="col-12 my-3 px-4">
                                                    <span>
                                                        <img height="30" v-lazy="getExtraData.airlineLogoUri" v-if="!imgErrors.some((an)=> an == getExtraData.airlineName)" @error="handleImgError(getExtraData.airlineName)" />
                                                    </span>
                                                    <span class="font-16 position-t-1 ps-2">{{ getExtraData.airlineName }}</span>
                                                </div>
                                            </div>
                                            <template v-for="detail in flightDetail.flightSegments">
                                                <p v-if="detail.generalInfo.operator" class="mb-2 text-left font-14 font-weight-bold d-block pl-2">
                                                    <i class="icon-info mr-2"></i>
                                                    {{__("messages.flight_operated_by")}} {{detail.generalInfo.operator}}, {{__("messages.to_document")}}.
                                                </p>
                                                <div class="info-flight">
                                                    <div class="content-info">
                                                        <div class="line-steps">
                                                            <div class="circle-top1"></div>
                                                            <div class="line1"></div>
                                                        </div>
                                                        <div class="info-travel align-top">
                                                            <div class="d-flex">
                                                                <p class="mb-0 text-left font-14 d-block back-p">
                                                                    {{ __("messages.flight")+" "+detail.generalInfo.flightNumber}}
                                                                    {{ detail.generalInfo.providerCabin != null && detail.generalInfo.providerCabin != "" ? "| " + __("messages.cabin") + ": " + detail.generalInfo.providerCabin : "" }}
                                                                </p>
                                                                <p class="mb-0 text-left font-12 gray d-block title-color-gray ps-2 mt-1">
                                                                    {{detail.generalInfo.airEquipType != null && detail.generalInfo.airEquipType != "" ? __("messages.aircraft")+" "+detail.generalInfo.airEquipType : ""}}
                                                                </p>
                                                            </div>
                                                            <div class="d-flex">
                                                                <span class="icon icon-flight-takeoff font-20 align-top icon-strong"></span>
                                                                <p class="mb-0 text-left p01 font-18 fw-medium title-color-gray font-poppins">{{ detail.generalInfo.departureCity }}</p>
                                                            </div>
                                                            <p class="mb-0 text-left font-16 gray d-block ps-4 title-color-gray font-poppins">{{ $filters.date(detail.generalInfo.departureDate, 'ddd, DD MMM, YYYY') }}</p>
                                                            <p class="mb-0 text-left font-21 gray d-block ps-4 time-color-gray font-poppins-medium">
                                                                {{ detail.generalInfo.departureTime }} hrs
                                                            </p>
                                                            <p class="ps-4 mb-0 text-left font-12 gray d-block ps-4 title-color-gray">
                                                                {{ detail.generalInfo.departureAirportName }} ({{ detail.generalInfo.departureAirportCode }}) {{ detail.generalInfo.terminalDeparture != null && detail.generalInfo.terminalDeparture != "" ? "| " + __("messages.terminal") + " " + detail.generalInfo.terminalDeparture : "" }}
                                                            </p>

                                                            <div class="c-capsule-light rounded-pill font-14 d-block ms-4 p-2 my-3 text-center alert alert-secondary border-0 text-black">
                                                                <span class="icon icon-clock position-relative"></span>
                                                                {{ durationFormat(detail.generalInfo.flightDuration)}}
                                                                <template v-for="technicalStop in detail.intermediatePoints">
                                                                    <br>
                                                                    {{ __('messages.wait_on_board').replace("{0}", technicalStop.destination) }} {{ durationFormat(technicalStop.duration)}}
                                                                </template>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="content-info">
                                                        <div class="line-steps">
                                                            <div class="circle-top1"></div>
                                                        </div>
                                                        <div class="info-travel align-top">
                                                            <div class="d-flex">
                                                                <span class="icon icon-flight-land font-20 align-top icon-strong"></span>
                                                                <p class="mb-0 text-left p01 font-18 fw-medium title-color-gray font-poppins">{{ detail.generalInfo.arrivalCity }}</p>
                                                            </div>
                                                            <p class="mb-0 text-left font-16 gray d-block ps-4 title-color-gray font-poppins">
                                                                {{ $filters.date(detail.generalInfo.arrivalDate, 'ddd, DD MMM, YYYY') }}
                                                            </p>
                                                            <p class="mb-0 text-left font-21 gray d-block ps-4 time-color-gray font-poppins-medium">
                                                                {{ detail.generalInfo.arrivalTime }} hrs
                                                            </p>
                                                            <p class="mb-0 text-left font-12 gray d-block ps-4 title-color-gray">
                                                                {{ detail.generalInfo.arrivalAirportName }} ({{ detail.generalInfo.arrivalAirportCode }}) {{ detail.generalInfo.terminarArrival != null && detail.generalInfo.terminarArrival != "" ? "| " + __("messages.terminal") + " " + detail.generalInfo.terminarArrival : "" }}
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="ps-2 ps-md-0 mt-2 mt-md-0"
                                                     v-for="stop in detail.stopInfo">
                                                    <div class="ps-md-0 pe-4 pe-md-0">
                                                        <hr />
                                                    </div>
                                                    <div class=" ps-md-0 text-center text-secondary">
                                                        {{ __("messages.connection_in") }} {{ stop.destination }} {{ __("messages.with_waiting") }} {{ stop.duration }}
                                                    </div>
                                                    <div class=" ps-md-0 pe-4 pe-md-0">
                                                        <hr />
                                                    </div>
                                                </div>
                                            </template>
                                        </div>
                                    </template>
                                    <template v-if="flightFamilyFare.familyFareName">
                                        <div id="package-info" class="modal-info-family-details w-100  mt-0 bg-white pt-2 rounded tabcontent cpi-info border rounded mt-3 position-relative ps-4 pb-2">
                                            <div class="c-rate-style"></div>
                                            <div class="pl-3 font-20 my-2 cm-txt fw-bold">
                                                {{getFamilyName(flightFamilyFare.familyFareName)}}
                                            </div>
                                            <template v-for="(familyFareContent, indexFamilyFareContent) in mappingFamilyFare(flightFamilyFare.familyFareContent)">
                                                <div :data-next="flightFamilyFare.familyFareContent[indexFamilyFareContent + 1]?.category"
                                                     :data-category="familyFareContent.category" class="mb-3">
                                                    <p class="mb-0 position-relative">
                                                        <span class="icon" :class="familyFareContent.class"></span>
                                                        <span class="font-medium ps-4 d-inline-block">{{familyFareContent.title}}</span>
                                                    </p>
                                                    <p class="ps-24 font-regular"
                                                       :class="{'color-disabled-text': familyFareContent.include === 2 || familyFareContent.include === 0}">
                                                        {{familyFareContent.description}}
                                                    </p>
                                                </div>
                                                <template v-if="indexFamilyFareContent === 1">
                                                    <div class="col-12 border-bottom mb-3 ms-1"></div>
                                                </template>
                                            </template>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </template>
                    <template v-else>
                        <div class="loading-section position-relative">
                            <div id="loader-page" class="loading-page d-center ">
                                <div class="loader__logo"></div>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import { storeToRefs } from 'pinia';
    import { useFlightDetailStore } from '../../stores/flightDetail';
    import { useFlightFamilyFareStore } from '../../stores/flightFamilyFare';
    import { mappingFamilyFare } from '../../../utils/utils';

    export default {
        data() {
            return {
                paramsDetail: {},
                configSite: window.__pt.settings.site.airlineConfiguration.national ?? [],
                imgErrors: [],
                configData: window.__pt.data
            }
        },
        setup() {
            const flightDetailStore = useFlightDetailStore();
            const flightFamilyFareStore = useFlightFamilyFareStore();

            const { getFlightDetail, getExtraData } = storeToRefs(flightDetailStore);
            const { getFlightFamilyFare } = storeToRefs(flightFamilyFareStore);

            const { durationFormat } = flightDetailStore;
            const { getShowDetail } = storeToRefs(flightDetailStore);

            return { getFlightDetail, getFlightFamilyFare, durationFormat, getShowDetail, getExtraData, mappingFamilyFare }
        },
        mounted() {
        },

        computed: {
            showDetail(){
                return this.getShowDetail;
            },
            flightDetail() {
                return this.getFlightDetail;
            },
            flightFamilyFare() {
                return this.getFlightFamilyFare;
            },
        },
        methods: {
            getFamilyName(n_code){
                let families = ((window.__pt.settings.site.airlineConfiguration.national ?? []).find(
                    config => {
                        return (String(config.airlineName)).toUpperCase() === (String(this.flightFamilyFare.params?.airlineName)).toUpperCase()
                    })?.families ?? []).find(
                        family=>(family.familyFareCode || []).some(
                            code => (String(code)).toUpperCase() === (String(n_code)).toUpperCase()
                        )
                ) ?? {}
                n_code = families?.name ?? n_code
                return n_code
            },
            configFilter(name=null) {
                let config = (this.configSite || []);
                return name ? ((config).find(item => (String(item.airlineName)).toUpperCase() === (String(name)).toUpperCase()) ?? {}) : config
            },
            indexFamily(){
                let index = -1
                if(this.flightFamilyFare?.params){
                    let config = this.configFilter(this.flightFamilyFare.params?.airlineName);
                    if(config){
                        index = (config?.families ?? []).findIndex(item => (item.familyFareCode ?? []).filter(code => (String(code)).toUpperCase() === (String(this.flightFamilyFare.params?.familyFare)).toUpperCase()).length > 0);
                    }
                }
                return index
            },
            handleImgError(airline) {
                this.imgErrors.push(airline);
            }
        }
    }
</script>
<style lang="scss" scoped>
    .c-flights-list p{
        color: #0e213a !important;
    }

    @media (min-width: 576px) {
        #modalDetailNational .modal-dialog {
            max-width: 500px !important;
        }
    }

    .loading-section {
        height: 150px !important;

        .loading-page {
            position: absolute;
        }
    }

    .c-view-detail {
        color: #0e213a;

        .circle-top {
            border: 2px solid #c4c4c4;
            border-radius: 50px;
            height: 10px;
            left: 0;
            margin: auto;
            position: absolute;
            top: 0;
            right: 0;
            width: 10px;
            z-index: 1;
        }
    }

    @media (min-width: 1025px), (min-width: 768px) and (max-width: 1024px) {
        #detailInter {
            .row {
                justify-content: space-between;
            }

            #flight-info {
                width: 60% !important;
            }

            /*#package-info {
                width: 38% !important;
            }*/

            #flight-info.w-100 {
                width: 100% !important;
            }

            /*#package-info.w-100 {
                width: 100% !important;
            }*/
        }
    }

    .content-info {
        position: relative;
        display: inline-table;
        width: 100%;
        height: auto;
    }

    .line-steps {
        position: relative;
        width: 5%;
        display: table-cell;
        height: 100%;
    }

    .circle-top1 {
        border: 2px solid #c4c4c4;
        border-radius: 50px;
        height: 10px;
        margin: auto;
        width: 10px;
        z-index: 1;
        position: absolute;
        top: 5px;
        left: 6px;
        display: block;
        background-color: #fff;
    }

    .line1 {
        bottom: -9px;
        left: 0;
        top: 11px;
        border: 1px dashed #c4c4c4;
        border-radius: 50px;
        margin: auto;
        position: absolute;
        right: 0;
        width: 1px;
        z-index: 0;
    }

    .info-travel {
        p {
            display: inline-block;
        }

        .p01 {
            position: relative;
            right: -4px;
        }
    }

    .text-secondary {
        color: var(--text-link) !important;
    }

    .c-capsule-light {
        .icon {
            top: -1px;
        }
    }

    .title-color-gray {
        color: #3B3A40;
    }

    .time-color-gray {
        color: #18161C;
    }

    .font-21 {
        font-size: 21px;
    }

    .row {
        margin-right: calc(-0.5* var(--bs-gutter-x)) !important;
        margin-left: calc(-0.5* var(--bs-gutter-x)) !important;
    }

    .ps-2 {
        padding-left: .5rem !important;
    }

    .text-secondary {
        color: var(--text-link) !important;
    }

    .back-p {
        background-color: #F5F5F7; /* Aplica un fondo gris */
        padding-top: 4px;
        padding-bottom: 4px;
        padding-right: 8px;
        padding-left: 8px;
        color: black;
    }
</style>