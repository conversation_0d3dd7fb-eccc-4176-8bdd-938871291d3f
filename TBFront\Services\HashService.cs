﻿using Microsoft.Extensions.Options;
using TBFront.Options;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace TBFront.Services
{
    public class HashService
    {
        private readonly SettingsOptions _options;
        public HashService(IOptions<SettingsOptions> options)
        {
            _options = options.Value;
        }

        public string Encrypt(string value)
        {
            if (string.IsNullOrEmpty(value)) return value;
            try
            {
                var key = Encoding.UTF8.GetBytes(_options.HashKey);

                using (var aesAlg = Aes.Create())
                {
                    using (var encryptor = aesAlg.CreateEncryptor(key, aesAlg.IV))
                    {
                        using (var msEncrypt = new MemoryStream())
                        {
                            using (var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                            using (var swEncrypt = new StreamWriter(csEncrypt))
                            {
                                swEncrypt.Write(value);
                            }

                            var iv = aesAlg.IV;

                            var decryptedContent = msEncrypt.ToArray();

                            var result = new byte[iv.Length + decryptedContent.Length];

                            Buffer.BlockCopy(iv, 0, result, 0, iv.Length);
                            Buffer.BlockCopy(decryptedContent, 0, result, iv.Length, decryptedContent.Length);

                            var str = Convert.ToBase64String(result);
                            var fullCipher = Convert.FromBase64String(str);
                            return str;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return string.Empty;
            }
        }

        public string Decrypt(string value)
        {
            if (string.IsNullOrEmpty(value)) return value;
            try
            {
                value = value.Replace(" ", "+");
                var fullCipher = Convert.FromBase64String(value);

                var iv = new byte[16];
                var cipher = new byte[fullCipher.Length - iv.Length];

                Buffer.BlockCopy(fullCipher, 0, iv, 0, iv.Length);
                Buffer.BlockCopy(fullCipher, iv.Length, cipher, 0, fullCipher.Length - iv.Length);
                var key = Encoding.UTF8.GetBytes(_options.HashKey);

                using (var aesAlg = Aes.Create())
                {
                    using (var decryptor = aesAlg.CreateDecryptor(key, iv))
                    {
                        string result;
                        using (var msDecrypt = new MemoryStream(cipher))
                        {
                            using (var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read))
                            {
                                using (var srDecrypt = new StreamReader(csDecrypt))
                                {
                                    result = srDecrypt.ReadToEnd();
                                }
                            }
                        }

                        return result;
                    }
                }
            }
            catch (Exception ex)
            {
                return string.Empty;
            }
        }

        public async Task<T> GetDecrypt<T>(string encodedStr)
        {
            var decryptString = DecryptHash(encodedStr);
            var data =  JsonSerializer.Deserialize<T>(decryptString, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
            return data;
        }

        /**
         * 
         * Codigo para desencriptar el hash de legacy
         * 
         **/
        public string DecryptHash(string encodedStr = null)
        {
            if (string.IsNullOrEmpty(encodedStr)) return encodedStr;

            var aesCryptoProvider = new AesCryptoServiceProvider();
            aesCryptoProvider.Key = Encoding.UTF8.GetBytes(_options.CheckoutHashKey);

            var textParts = encodedStr.Split(':');
            var iv = FromHexString(textParts[0]);
            aesCryptoProvider.IV = iv;
            var byteBuff = FromHexString(textParts[1]);
            var plaintext = Encoding.UTF8.GetString(aesCryptoProvider.CreateDecryptor().TransformFinalBlock(byteBuff, 0, byteBuff.Length));

            aesCryptoProvider.Clear();
            return plaintext;
        }

        public string EncryptHash(string str)
        {
            var aesCryptoProvider = new AesCryptoServiceProvider();
            aesCryptoProvider.Key = Encoding.UTF8.GetBytes(_options.CheckoutHashKey);
            aesCryptoProvider.GenerateIV();
            aesCryptoProvider.IV = aesCryptoProvider.IV;

            var byteBuff = Encoding.UTF8.GetBytes(str);
            var encoded = aesCryptoProvider.CreateEncryptor().TransformFinalBlock(byteBuff, 0, byteBuff.Length);

            var ivHexString = ToHexString(aesCryptoProvider.IV);
            var encodedHexString = ToHexString(encoded);

            aesCryptoProvider.Clear();
            return ivHexString + ':' + encodedHexString;
        }
        private static string ToHexString(byte[] str)
        {
            var sb = new StringBuilder();

            var bytes = str;
            foreach (var t in bytes)
                sb.Append(t.ToString("X2"));

            return sb.ToString();
        }

        private static byte[] FromHexString(string hexString)
        {
            var bytes = new byte[hexString.Length / 2];
            for (var i = 0; i < bytes.Length; i++)
                bytes[i] = Convert.ToByte(hexString.Substring(i * 2, 2), 16);

            return bytes;
        }
    }


}
