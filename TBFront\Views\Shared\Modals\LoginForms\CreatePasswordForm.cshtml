﻿@using TBFront.Helpers

@inject ViewHelper viewHelper






<div class="row">

    <div class="col-12">
        <p class="h4">@viewHelper.Localizer("create_yout_password")</p>
        <p ng-show="vm.userData.password.length < 8 && form_login.$submitted && form_login.password.$touched" class="line_height on_error_login mb-0">@viewHelper.Localizer("min_8_letters_required")</p>
        <p ng-show="vm.userData.password.length < 8 && form_login.$submitted && form_login.password.$touched" class="line_height on_error_login mb-0">@viewHelper.Localizer("password_weak")</p>
        <p ng-show="form_login.$submitted" class="line_height on_error_login mb-0">{{vm.currentErrorMsg}}</p>
    </div>

</div>


<div class="row my-4">
    <div class="col-12">
        <label for="password_create_login">@viewHelper.Localizer("enter_your_password")</label>
        <div class="input-group mb-3" ng-class="{'is--danger': form_login.$submitted && (vm.hasError(form_login, 'confirmPassword') || vm.hasError(form_login, 'password') || vm.userData.password != vm.userData.pwdconfirm)}">
            <div class="input-group-prepend">
                <span class="input-group-text">
                   <span class="font-icons icons-lock"></span>
                </span>
            </div>
            <input type="password" required ng-model="vm.userData.password" id="password_create_login" name="password" placeholder="@viewHelper.Localizer("enter_your_password")" class="form-control input-custom">
        </div>
        <div class="invalid-feedback" ng-show="vm.hasError(form_login, 'password')">
            @viewHelper.Localizer("enter_your_password")
        </div>
    </div>

    <div class="col-12 mt-3">
        <label for="confirmPassword_login">@viewHelper.Localizer("confirm_password")</label>
        <div class="input-group mb-3" ng-class="{'is--danger': form_login.$submitted && (vm.hasError(form_login, 'confirmPassword') || vm.hasError(form_login, 'password') || vm.userData.password != vm.userData.pwdconfirm)}">
            <div class="input-group-prepend">
                <span class="input-group-text">
                   <span class="font-icons icons-lock"></span>
                </span>
            </div>
            <input type="password" required ng-model="vm.userData.pwdconfirm" id="confirmPassword_login" name="confirmPassword" placeholder="@viewHelper.Localizer("confirm_password")" class="form-control input-custom">
        </div>
        <div class="invalid-feedback" ng-show="vm.hasError(form_login, 'confirmPassword')">
            @viewHelper.Localizer("enter_your_password")
        </div>
        <div class="invalid-feedback" ng-show="form_login.$submitted && vm.userData.password != vm.userData.pwdconfirm">
            @viewHelper.Localizer("login_passwords_error")
        </div>
    </div>

</div>


<div class="row">
    <div class="col-12">
        <button type="submit" name="button" class="btn btn-primary btn-block p-3">@viewHelper.Localizer("create_account")</button>
    </div>
</div>

