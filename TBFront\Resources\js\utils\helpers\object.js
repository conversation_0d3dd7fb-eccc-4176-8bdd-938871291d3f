import _ from 'lodash';

/**
 * @name objectFormatter
 * @description Map any object to a provided object structure
 *
 * @param  {Object}   modelToMapper      required  Object with the desired output structure.
 * @param  {Object}   objectToFormatter  required  Object to format.
 *
 * @returns {Object}  Formated object baset in to structure of modelToMapper.
 */
export const objectFormatter = (modelToMapper, objectToFormatter) => {
    if (_.isEmpty(modelToMapper)) return { mapper: 'invalidOrUndefinedMapper' };
    if (_.isEmpty(objectToFormatter))
        return { response: 'emptyOrUndefinedResponse' };
    let formated = null;

    if (_.isArray(objectToFormatter)) {
        formated = _.map(objectToFormatter, item => {
            const itemMapped = {};

            _.forEach(modelToMapper, (value, key) => {
                itemMapped[key] = _.get(item, _.split(value, '.'), null);
            });

            return itemMapped;
        });
    } else {
        formated = {};
        _.forEach(modelToMapper, (value, key) => {
            formated[key] = _.get(objectToFormatter, _.split(value, '.'), null);
        });
    }

    return formated;
};

/**
 * @name keysToCamelCase
 * @description Rename keys of an object to camelCase format
 *
 * @param  {Object}   object  required Object to rename keys. e.g. { a: 'foo', B: 'bar', AB: 'foo-bar' }
 *
 * @returns {Object}  Formated object baset in to structure of modelToMapper. e.g. { a: 'foo', b: 'bar', aB: 'foo-bar' }
 */
export const keysToCamelCase = object => {
    const keys = _.keys(object);
    const parsedObject = {};

    _.map(keys, key => {
        _.set(parsedObject, [_.camelCase(key)], object[key]);
    });

    return parsedObject;
};

/**
 * @name validateNullValues
 * @description Validate empty, null or undefined of a value and convert to false
 *
 * @param  {any} value  Required value to validate
 *
 * @returns {any}  Returned value or false in case of obtaining an empty, null or undefined
 */
export const validateNullValues = value => {
    const isNullOrUndefined =
        _.isNull(value) || (!_.isDate(value) && _.isEmpty(value));

    return isNullOrUndefined ? false : value;
};
