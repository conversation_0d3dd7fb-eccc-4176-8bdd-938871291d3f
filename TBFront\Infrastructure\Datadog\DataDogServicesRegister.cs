﻿using TBFront.Infrastructure.Datadog.Dtos;
using TBFront.Interfaces;

namespace TBFront.Infrastructure.Datadog
{
    public static class DataDogServicesRegister
    {
        public static void AddDataDogServiceRegister(this IServiceCollection services, IConfiguration configuration)
        {

            services.AddSingleton(s => configuration.GetSection("DatadogConfiguration").Get<DatadogConfiguration>());

            services.AddSingleton<IDataDogService, DataDogService>();

        }
    }
}
