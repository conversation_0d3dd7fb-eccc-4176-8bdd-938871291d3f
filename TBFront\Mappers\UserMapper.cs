﻿using TBFront.Models.Response;
using TBFront.Models.User;
using TBFront.Models;
using TBFront.Models.BookingItinerary;

namespace TBFront.Mappers
{
    public class UserMapper
    {
        public static UserCheckout UserCheckoutData(Customer customer)
        {
            var phone = $"+{customer.DialCode}{customer.Phone}";
            var passenger = customer.PassengersBooking.First().Passengers.First();

            return new UserCheckout
            {
                Email = customer.Email,
                Phone = phone,
                Name = passenger.Firstname,
                LastName = passenger.Lastname,
                HashEmail = Cryptography.CryptographySHA(customer.Email),
                HashPhone = Cryptography.CryptographySHA(phone)
            };

        }

        public static UserCheckout UserCheckoutData(TravelItinerary customer)
        {
            var phone = $"";

            return new UserCheckout
            {
                Email = customer.CustomerEmail,
                Phone = phone,
                Name = customer.CustomerFirstName,
                LastName = customer.CustomerLastName,
                HashEmail = Cryptography.CryptographySHA(customer.CustomerEmail),
                HashPhone = Cryptography.CryptographySHA(phone)
            };

        }


        public static List<Dictionary<string, object>> UserCheckoutEvents(TravelItinerary customer)
        {
            var user = UserMapper.UserCheckoutData(customer);
            var dictionary = new Dictionary<string, object>
            {
                { "event", "UserCollector" },
                { "eventName", "UserCollector" },
                { "UserName", user.Name },
                { "UserLastName", user.LastName },
                { "UserPhone", user.Phone },
                { "UserEmail", user.Email },
                { "UserHashPhone", user.HashPhone },
                { "UserHashEmail", user.HashEmail },
            };

            return new List<Dictionary<string, object>>
            {
                dictionary
            };

        }
    }
}
