﻿namespace TBFront.Types
{
    public static class ProductType
    {
        public const string Hotels = "hoteles";
        public const string Flights = "vuelos";
        public const string FlightsUS = "flights";
        public const string Packages = "paquetes";
        public const string HotelsPage = "hotel";
        public const string FlightsPage = "flights";
        public const string FlightsPageUS = "flights";
        public const string PackagesPage = "package";
        public const string FlightsToken = "flight";
        public const string HotelsToken = "hotel";
        public const string PackagesToken = "package";
        public const string Promotion = "promotion";
        public const string Hotel = "hotel";
        public const string Vacation = "vacation";
        public const string Disney = "disney";
        public const string Destination = "destination";
        public const string Country = "country";
        public const string Countries = "countries";
        public const string DestinationMain = "destinos";
        public const string Airlines = "aeromexico,aircanada,airfrance,avianca,boletosaereos,clicair,continental,copa,iberia,jetblue,jetsmart,latam,satena,united,vivaaerobus,volaris,wingo";
        public const string DestinationTB = "tiquetes";
        public const string ArlinesPage = "aerolineas";
        public const string ArlinesPagesUS = "airlines";


    }
}
