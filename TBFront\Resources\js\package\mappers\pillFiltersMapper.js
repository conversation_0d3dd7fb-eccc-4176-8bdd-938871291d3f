﻿import { __ } from "../../utils/helpers/translate";
class pillFiltersMappers {

    map(responses) {
        let filters = [
            {
                id: "stops",
                label: `${__(`filters.stops`)}`,
                options: Object.values(responses.avilableStops)
            },
            {
                id: "airlines",
                label: `${__(`filters.airlines`)}`,
                options: responses.airlines
            }
        ];
        filters.forEach(response => {
            response.options.forEach(opt => {
                opt.value = response.id == 'stops' ? opt.title : opt.code;
                if (response.id == 'stops') {
                    opt.name = `${__(`filters.${opt.title}`)}`
                }
            });
        });
        let allFilters = filters.reduce((acc, item, index) => {
            if (index % 1 === 0) {
                acc.push([item]); // Crea un nuevo sub-array
            } else {
                acc[acc.length - 1].push(item); // Agrega al último sub-array
            }
            return acc;
        }, []);
        return {
            filters: filters,
            allFilters: allFilters
        };
    }

}
export const pillFiltersMapper = new pillFiltersMappers();