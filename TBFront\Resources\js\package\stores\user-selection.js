import { defineStore } from "pinia";
import { getAirportType, getFlightTypeByMode } from "../../utils/helpers/data";
import { useBookerStore } from "./booker";
import { __ } from "../../utils/helpers/translate";
import { cheapestFlights, placeType, startingAirport } from "../../constants";
import { StorageService } from '../../utils/helpers/storage';
import { List } from '../../utils/analytics/flightList.js';

export const useUserSelectionStore = defineStore('userSelection', {
    state: () => ({
        adults: 0,
        ageKids: [],
        airlineCode: '',
        startingFromAirport: "BOG",
        returningFromAirport: '',
        flightType: '',
        checkIn: '',
        checkOut: '',
        startingFromDateTime: '',
        returningFromDateTime: '',
        quoteFlight: true,
        quoteList: true,
        startingAirportDetail: {
            airport: '',
            airportCode: '',
            city: '',
            cityFullName: ''
        },
        returningAirportDetail: {
            airport: '',
            airportCode: '',
            city: '',
            cityFullName: ''
        },
        tripMode: 1,
        mode: 1,
        filtersApplied: [],
        startingAirportType: getAirportType(placeType.none),
        returningAirportType: getAirportType(placeType.none),
        timeoutQuote: null,
        countryflight: "",
        countryflightreturn: "",
        origin: "",
        destination: "",
        flagFilters: false
    }),
    getters: {
        paxText() {
            return `${this.adults} ${this.adults > 1 ? __('booker.adults') : __('booker.adult')}${this.ageKids && this.ageKids.length > 0 ? `, ${this.ageKids.length} ${this.ageKids.length > 1 ? __('messages.children') : __('messages.child')}` : ""}`;
        },
        getSearchParams() {
            return {
                mode: this.mode,
                tripMode: this.tripMode,
                startingFromAirport: this.startingFromAirport,
                returningFromAirport: this.returningFromAirport,
                startingFromDateTime: this.startingFromDateTime,
                returningFromDateTime: this.tripMode == 1 ? this.returningFromDateTime : this.startingFromDateTime,
                adults: this.adults,
                agekids: this.ageKids.join(','),
                kids: this.ageKids.length ?? 0,
                QuoteList: true,
                QuoteFlight: true,
                filtersApplied: this.filtersApplied.join(','),
                site: window.__pt.settings.site,
            }
        },
        getNewQueryString() {
            const airlinePathURL = window.location.pathname.split("/");
            const queryString = {
                tripMode: this.tripMode === 0 ? "0" : "1",
                originName: this.origin,
                startingFromAirport: this.startingFromAirport,
                destinationName: this.destination,
                returningFromAirport: this.returningFromAirport,
                startingFromDateTime: this.startingFromDateTime,
                returningFromDateTime: this.tripMode == 1 ? this.returningFromDateTime : this.startingFromDateTime,
                adults: this.adults,
                kids: this.ageKids.length
            };

            if (typeof airlinePathURL[2] !== 'undefined' && airlinePathURL[1] === "aerolineas") {
                queryString.addfirst = airlinePathURL[2]
            }

            if (this.ageKids.length) {
                queryString.ageKids = this.ageKids.join(",");
            }

            /*for (var i = 0; i < this.ageKids.length; i++) {
                queryString[`edad${i + 1}`] = this.ageKids[i];
            }*/

            return queryString;
        },
        getFiltersApplied() {
            return this.filtersApplied.join(',');
        },
        getFiltersAppliedArray() {
            return this.filtersApplied;
        },
        getStartingAirportDetail() {
            return this.startingAirportDetail;
        },
        getReturningAirportDetail() {
            return this.returningAirportDetail;
        },
        isCheapestFlightsApplied() {
            return _.includes(this.filtersApplied, cheapestFlights);
        },
        getFlagFilters() {
            return this.flagFilters;
        }
    },
    actions: {
        initUserSelection(params, storedHistory) {
            let isThereDataParams = false;
            const { initBooker } = useBookerStore();
            let startingAirportPlace = {
                "id": "7983",
                "displayText": __('booker.originSimpleName'),
                "displayDestinationHtml": "Colombia",
                "displayHtml": __('booker.originDisplayHtml'),
                "cityFullName": __('booker.originDefaultName'),
                "type": 0,
                "isActive": true,
                "code": "BOG",
                "country": "CO",
                "positions": 1,
                "items": {
                    "hotel": 0,
                    "objectID": "1cdca75fb17ac2_dashboard_generated_id",
                    "queryID": "33ccdaedb0f1bd14e7467afbb08fe21b"
                }

            };

            if (window.__pt.data.startingAirportPlace.airport !== "" && window.__pt.data.returningAirportPlace.airport !== "") {
                startingAirportPlace = window.__pt.data.startingAirportPlace;
                this.startingFromAirport = params.startingFromAirport;
                this.returningFromAirport = params.returningFromAirport;
                isThereDataParams = true;
            }
            let returningAirportPlace = window.__pt.data.returningAirportPlace;

            this.adults = params.adults || 1;

            if (params.agekids) {
                this.ageKids = params.agekids.split(',');
            }

            this.airlineCode = params.airlineCode;
            this.flightType = getFlightTypeByMode(params.tripMode);
            this.checkIn = params.checkIn;
            this.checkOut = params.checkOut;
            this.startingFromDateTime = params.startingFromDateTime;
            this.returningFromDateTime = params.returningFromDateTime;
            this.tripMode = params.tripMode;
            this.page = params.page;
            if (storedHistory && !isThereDataParams && storedHistory.from && storedHistory.from.length && storedHistory.to && storedHistory.to.length) {
                const fromAirport = storedHistory.from && storedHistory.from[0] && !storedHistory.from[0].cityFullName ? `${storedHistory.from[0].displayText} - ${storedHistory.from[0].displayDestinationHtml} (${storedHistory.from[0].code})` : storedHistory.from[0].cityFullName;
                const toAirport = storedHistory.to && storedHistory.to[0] && !storedHistory.to[0].cityFullName ? `${storedHistory.to[0].displayText} - ${storedHistory.to[0].displayDestinationHtml} (${storedHistory.to[0].code})` : storedHistory.to[0].cityFullName;

                const from = {
                    airport: fromAirport,
                    airportCode: storedHistory.from[0].code,
                    airportType: 11,
                    city: fromAirport,
                    cityFullName: fromAirport,
                }

                const to = {
                    airport: toAirport,
                    airportCode: storedHistory.to[0].code,
                    airportType: 11,
                    city: toAirport,
                    cityFullName: toAirport,
                }

                startingAirportPlace = from;
                returningAirportPlace = to;
                this.startingAirportDetail = from;
                this.returningAirportDetail = to;
                this.startingFromAirport = storedHistory.from[0].code;
                this.returningFromAirport = storedHistory.to[0].code;
                this.startingFromDateTime = storedHistory.to[0].startingFromDateTime;
                this.returningFromDateTime = storedHistory.to[0].returningFromDateTime;
                this.flightType = getFlightTypeByMode(storedHistory.to[0].tripMode);
                this.adults = storedHistory.to[0].adults;
                if (storedHistory.to[0].agekids) {
                    this.ageKids = storedHistory.to[0].agekids.split(',');
                }
            } else {
                this.startingAirportDetail = startingAirportPlace;
                this.returningAirportDetail = returningAirportPlace || this.startingFromDateTime;
            }


            this.startingAirportType = getAirportType(startingAirportPlace.airportType);
            this.returningAirportType = getAirportType(returningAirportPlace.airportType);

            const calculatedDates = window.__pt.data.calculatedDates;
            if (calculatedDates) {
                if (calculatedDates.startingFromDateTime) {
                    this.checkIn = calculatedDates.startingFromDateTime;
                    this.startingFromDateTime = calculatedDates.startingFromDateTime;
                }

                if (calculatedDates.returningFromDateTime) {
                    this.checkOut = calculatedDates.returningFromDateTime;
                    this.returningFromDateTime = calculatedDates.returningFromDateTime;
                }
            }

            initBooker(this);
        },
        changeFilters(filtersApplied) {
            this.filtersApplied = filtersApplied;
        },
        addFilter(value) {
            this.filtersApplied.push(value);
        },
        removeFilter(value) {
            _.remove(this.filtersApplied, (v) => v === value);
        },
        initRequoteTimeout() {
            this.timeoutQuote = setTimeout(() => {
                this.showRequoteModal();
            }, window.__pt.settings.site.timeToShowRequoteModal * 1000 * 60);
        },
        resetRequoteTimeout() {
            clearTimeout(this.timeoutQuote);
            this.initRequoteTimeout();
        },
        newQueryStringRecentSearches(history, index) {
            const fligthFrom = history.from[index];
            const flightTo = history.to[index];

            const displayFromText = fligthFrom.displayText;
            const cityCountryFromText = fligthFrom.displayDestinationHtml;
            const codeFromText = fligthFrom.code;

            const displayToText = flightTo.displayText;
            const cityCountryToText = flightTo.displayDestinationHtml;
            const codeToText = flightTo.code;

            const queryString = {
                tripMode: flightTo.tripMode === 0 ? "0" : "1",
                originName: `${displayFromText} - ${cityCountryFromText} (${codeFromText})`,
                startingFromAirport: flightTo.startingFromAirport,
                destinationName: `${displayToText} - ${cityCountryToText} (${codeToText})`,
                returningFromAirport: flightTo.returningFromAirport,
                startingFromDateTime: flightTo.startingFromDateTime,
                returningFromDateTime: flightTo.tripMode == 1 ? flightTo.returningFromDateTime : flightTo.startingFromDateTime,
                adults: flightTo.adults,
                kids: flightTo.kids,
            };

            if (flightTo.agekids.length) {
                queryString.ageKids = flightTo.agekids;
            }

            /*for (var i = 0; i < this.ageKids.length; i++) {
                queryString[`edad${i + 1}`] = this.ageKids[i];
            }*/
            return queryString;
        },
        showRequoteModal(type = "", eventAction = null) {
            const requoteModal = new bootstrap.Modal(document.getElementById('requote-modal'), {});
            List.requoteModal(type, eventAction);
            requoteModal.show();
        },
        setFlagFilters(value) {
            this.flagFilters = value;
        }

    }
});