﻿using TBFront.Interfaces;
using TBFront.Models.Destination.Request;
using TBFront.Models.Destination.Response;
using TBFront.Infrastructure.HttpService.ContentDeliveryNetwork.Dtos;
using TBFront.Models.Blacklist.Request;
using TBFront.Models.Blacklist.Response;

namespace TBFront.Infrastructure.HttpService.ContentDeliveryNetwork
{
    public static class ContentDeliveryNetworkServiceRegister
    {
        public static void AddContentDeliveryNetworkServiceRegister(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddHttpClient<DestinationService>("");
            services.AddHttpClient<BlacklistService>("");
            services.AddHttpClient<ContentDeliveryNetworkService>("");

            services.AddSingleton(s => configuration.GetSection("HttpDestinationServiceConfiguration").Get<DestinationConfiguration>());
            services.AddSingleton(s => configuration.GetSection("HttpBlacklistServiceConfiguration").Get<BlacklistConfiguration>());
            services.AddSingleton(s => configuration.GetSection("HttpCdnServiceConfiguration").Get<ContentDeliveryNetworkConfiguration>());

            services.AddSingleton<IQueryHandlerAsync<DestinationRequest, List<DestinationResponse>>, DestinationService>();
            services.AddSingleton<IQueryHandlerAsync<BlacklistRequest, BlacklistResponse>, BlacklistService>();
            services.AddSingleton<IContentDeliveryNetworkService, ContentDeliveryNetworkService>();

        }

    }
}