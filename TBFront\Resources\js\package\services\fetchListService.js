import {getList, getParamsFlight, getParamsReturningFlight, getReturningList} from "./ApiFlightFrontServices";
import {List} from "../../utils/analytics/flightList";
import {useFlightStore} from "../stores/flight";
import {storeToRefs} from "pinia";
import {Logger} from "../../utils/helpers";


const storeFlight = useFlightStore();
const { setFlightResponse, removeElementResponse, setProgressBar, getElementResponse } = storeFlight;
const { getParams, getProgressBar, getAllQuoteTokens, getGroups } = storeToRefs(storeFlight);
export function getReturningFlights({departureToken,flightQuoteId, code }, configuration, engine=null, finallyCallback = (res)=>res){
    if(configuration?.searchArrival){
        let cGroups = getGroups.value
        const filteredGroups = ((cGroups ?? []).filter(flight => (String(flight?.departure?.code)).toUpperCase() === (String(code)).toUpperCase()))
        //this.resetFlightResponse();
        let progressBar = 100;
        /** Nuevo cambio **/
        let params = getParams.value
        let quoteConfig = window.__pt.settings.site.quoteConfiguration || {}
        /** End nuevo cambio **/
        const currentConfig = params.isNational ? quoteConfig.national : quoteConfig.international;
        const promises = [];
        let promisesCompleted = 0;
        //const filteredQuoteConfigs = currentConfig.quoteConfigurations.filter(x => x.isActive && (!x.blockedCarrierCode.length || !x.blockedCarrierCode.includes(params.airlineCode)));
        (filteredGroups).forEach(airlineList => {
            let codeFlight = airlineList.departure.code ?? ""
            if (configuration?.searchArrival) {
                const backupResponse = getElementResponse(codeFlight)
                removeElementResponse(codeFlight)
                progressBar -= 101 / cGroups.length;
                setProgressBar(progressBar)
                promises.push(new Promise(async (resolve, reject) => {
                    // Changing base request using config from appsettings

                    const rq = getParamsReturningFlight({departureToken: airlineList.departure.token, flightQuoteId});
                    while(getLoadingsFlightsBySimpleFlightQuotes(engine) && engine) {
                        // Espera una pequeña cantidad de tiempo antes de verificar de nuevo.
                        // Esto es para prevenir la verificación continua que puede bloquear el hilo principal en el navegador.
                        await new Promise(resolve => setTimeout(resolve, 100));
                    }
                    const response = await getReturningList(rq);

                    if (response && !response.error) {
                        response.configName = airlineList.departure.name;
                        resolve(response);
                    }

                    reject("NO RETURNING QUOTES" + airlineList.departure.name);
                }).then(res => {
                    // Set response in store
                    setFlightResponse(res);
                    
                    if(finallyCallback){
                        setTimeout(()=>{
                            finallyCallback(res)
                        }, 1000)
                    }
                }).catch(err => {
                    // Error handler when any requesst fails
                    setFlightResponse(backupResponse);
                    Logger.warn(err);
                }).finally(async () => {
                    promisesCompleted++;
                    progressBar += 101 / cGroups.length;
                    if (progressBar >= 100) {
                        let dataGroups = {
                            lengthGroups: cGroups.length,
                            departure: {},
                            returning: {}
                        }
                        let groups = [];
                        for (let index in cGroups) {
                            dataGroups['index'] = index;
                            dataGroups['code'] = cGroups[index].departure?.code?.trim() || cGroups[index].returning?.code?.trim();
                            dataGroups['departure']['price'] = cGroups[index].departure?.cheapest;
                            dataGroups['departure']['view'] = "departing";
                            dataGroups['departure']['total_flights'] = cGroups[index].departure?.flights.length;
                            if (params.isRoundtrip) {
                                dataGroups['returning']['price'] = cGroups[index].returning?.cheapest;
                                dataGroups['returning']['view'] = "returning";
                                dataGroups['returning']['total_flights'] = cGroups[index].returning?.flights.length;
                            }
                            groups.push(dataGroups);
                        }
                        List.flights(groups);
                    }
                    if (promisesCompleted === promises.length && currentConfig.isMatrixActive) {
                        //setMatrix();
                    }
                    setProgressBar(progressBar);
                }));
            }
        });
    }
}

export function configFilter(code, type = 'national') {
    let configSite = window.__pt.settings.site.airlineConfiguration[type] ?? []
    return (configSite || []).find(item => (item.airlineCode ?? []).some(kCode => (String(kCode)).toUpperCase() === (String(code)).toUpperCase())) ?? {}
}


/** Carga de vuelos **/
let loadingsFlightsBySimpleFlightQuotes = []
let loadedFlightsBySimpleFlightQuotes = []
 
export function getLoadingsFlightsBySimpleFlightQuotes(engine = null){
    return (loadingsFlightsBySimpleFlightQuotes.includes(engine) && engine)
}

export async function getFlightsBySimpleFlightQuotes(engine, simpleFlightQuotes = true, ignoreLoaded = false, autoSave=false){
    let response = []
    if((!loadingsFlightsBySimpleFlightQuotes.includes(engine) && ( !loadedFlightsBySimpleFlightQuotes.includes(engine)) || ignoreLoaded)){
        const params = getParams;
        const siteConfig = window.__pt.settings.site || {};
        const quoteConfig = siteConfig.quoteConfiguration || {};
        const currentConfig = params.value.isNational ? quoteConfig.national : quoteConfig.international;
        const filteredQuoteConfigs = currentConfig.quoteConfigurations.filter(x => (x.engine.includes(engine) || !engine) && x.isActive && (!x.blockedCarrierCode.length || !x.blockedCarrierCode.includes(params.value.airlineCode)));
        for (const cc of filteredQuoteConfigs) {
            /**Espera de carga **/
            loadingsFlightsBySimpleFlightQuotes = loadingsFlightsBySimpleFlightQuotes.concat(cc.engine)
            loadedFlightsBySimpleFlightQuotes = loadedFlightsBySimpleFlightQuotes.concat(cc.engine)
            /**End espera de carga **/
            const extraParams = {};
            // Changing base request using config from appsettings
            if (cc.engine && cc.engine.length) {
                extraParams.engine = cc.engine.join(',');
            }

            if (cc.carrierCode && cc.carrierCode.length) {
                extraParams.carrierCode = cc.carrierCode.join(',');
            }

            if (cc.blockedCarrierCode && cc.blockedCarrierCode.length) {
                extraParams.blockedCarrierCode = cc.blockedCarrierCode.join(',');
            }
            extraParams.fareMode = params.value.isNational ? 1 : 0; // MultiplesFares(1) or LowestFares (0)
            extraParams.SimpleFlightQuotes = simpleFlightQuotes // if national will search roundtrip as double oneway
            const rq = getParamsFlight(params.value, extraParams);
            response = await getList(rq);
            if(autoSave) {
                //cGroups[0].departure.flights[0].engine
                let cGroups = getGroups.value
                const filteredGroups = ((cGroups ?? []).find(arline => {
                    return !!arline.departure.flights.find(flight=>(String(flight.engine)).toUpperCase() === (String(engine)).toUpperCase())
                }))
                if(filteredGroups.departure?.code) removeElementResponse(filteredGroups.departure.code)
                setFlightResponse(response);
            }
            
            /**Espera de carga **/
            loadingsFlightsBySimpleFlightQuotes = loadingsFlightsBySimpleFlightQuotes.filter(loading => !cc.engine.includes(loading))
            /**End espera de carga **/
        }
    }
    return response;
}

export function actionFlights(flights, callbacks = {callbackFlight: (flights, $indexFlight)=>flights, callbackFare: (fare, $indexFare, flight, $indexFlight)=>fare} ){
    flights.forEach((flight, $indexFlight)=>{
        if(callbacks.callbackFlight){
            callbacks.callbackFlight(flight, $indexFlight)   
        }
        
        flight.fares.forEach((fare, $indexFare)=>{
            if(callbacks.callbackFare){
                callbacks.callbackFare(fare, $indexFare, flight, $indexFlight)
            }
        })
    })
}