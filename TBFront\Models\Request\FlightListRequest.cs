﻿namespace TBFront.Models.Request
{
    public class FlightListRequest
    {
        public int Mode { get; set; } = 0;
        public int TripMode { get; set; } = 0;
        public string StartingFromAirport { get; set; }
        public string ReturningFromAirport { get; set; }
        public string ReturningFromDateTime { get; set; }
        public string StartingFromDateTime { get; set; }
        public int Adults { get; set; } = 1;
        public int Kids { get; set; } = 0;
        public string? Agekids { get; set; } = string.Empty;
        public string? KeyRedis { get; set; }
    }
}
