﻿@using TBFront.Helpers

@inject ViewHelper viewHelper



<div class="modal fade" id="modal-login" ng-click="vm.clickOutside($event, 'modal-login')">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content" id="modal-login-form">
            <div class="modal-header">
                <p class="modal-title h4">@viewHelper.Localizer("my_account")</p>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" ng-click="vm.hideModal('modal-login')">
                    <span aria-hidden="true"> <span class="font-icons icons-close"></span></span>
                </button>
            </div>
            <div class="modal-body">

                <div class="row">
                    <div class="col-12">

                        <form id="form-user" name="form_login" novalidate="" class="modal-form-content row"
                              ng-submit="vm.submitForm(form_login, 'modal-login')">
                            <div class="col-12" ng-if="vm.stepRegister == 1">
                                @await Html.PartialAsync("~/Views/Shared/Modals/LoginForms/DefaultEmail.cshtml")
                            </div>
                            <div class="col-12" ng-if="vm.stepRegister == 2">
                                @await Html.PartialAsync("~/Views/Shared/Modals/LoginForms/PasswordForm.cshtml")
                            </div>
                            <div class="col-12" ng-if="vm.stepRegister == 3">
                                @await Html.PartialAsync("~/Views/Shared/Modals/LoginForms/CreatePasswordForm.cshtml")
                            </div>
                            <div class="col-12" ng-if="vm.stepRegister == 4">
                                @await Html.PartialAsync("~/Views/Shared/Modals/LoginForms/ForgotPasswordForm.cshtml")
                            </div>
                             <div class="col-12" ng-if="vm.stepRegister == 5">
                                @await Html.PartialAsync("~/Views/Shared/Modals/LoginForms/LoadingLogin.cshtml")
                            </div>
                             <div class="col-12" ng-if="vm.stepRegister == 6">
                                @await Html.PartialAsync("~/Views/Shared/Modals/LoginForms/CongratsLogin.cshtml")
                            </div>
                        </form>


                        <div class="row mt-3" ng-if="vm.stepRegister == 2">
                            <div class="col-12">
                                <button ng-show="vm.settings.forgot" type="button" name="button" class="btn btn-link btn-block"
                                        ng-click="vm.forgotPasswordForm(form_login)">
                                    @viewHelper.Localizer("forgot_password")
                                </button>
                            </div>
                        </div>

                        <div class="row mt-3" ng-if="vm.stepRegister == 1">
                            <div class="col-12 text-center">
                                <small class="separator-text">@viewHelper.Localizer("use_other_login_methods")</small>
                            </div>

                            <div class="col-12 d-flex justify-content-center">
                                <button ng-repeat="provider in vm.loginProviders" type="button" name="button"
                                        class="btn btn-social btn-fb mr-2 p-3 border"
                                        ng-click="vm.loginWithProvider('modal-login', provider.code)">
                                    <span class="btn-social-icon">
                                        <i class="icon-{{provider.icon}}"></i>
                                    </span>
                                        {{provider.name}}
                                </button>
                            </div>
                        </div>

                        <div class="form-group login-legal"  ng-show="vm.stepRegister == 1 || vm.stepRegister == 3">
                            <hr class="separator-line">
                            
                            <p class="text-center line_height small">

                                @viewHelper.Localizer("legals_login_one")
                                <a href="https://www.pricetravel.com.mx/info/politicas-reservacion">
                                    @viewHelper.Localizer("terms_conditions")
                                </a>
                                @viewHelper.Localizer("legals_login_two")
                                <a href="https://www.pricetravel.com.mx/info/politicas-reservacion">
                                    @viewHelper.Localizer("privacy_terms")
                                </a> @viewHelper.Localizer("legals_login_three")
                            </p>
                        </div>

                    </div>
                </div>

            </div>
        </div>
    </div>
</div>