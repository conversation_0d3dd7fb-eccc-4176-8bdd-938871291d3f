.form-text-field,
.form-text-area{
  width: 100%;
  height: 56px;
  position: relative;
  label {
    margin: 0;
    position: absolute;
    top: 0.5rem;
    left: 1rem;
    line-height: 1;
    color: $gray-500;
  //   font-size: $type-font-size-12;
    cursor: text;
  //   // @include transition($transition-base);
  }
  //INPUTS + SELECT
  input,
  select,
  textarea {
    height: 100%;
    width: 100%;
    border: 0;
    padding: 1.5rem 1rem 0.5rem 1rem;
    // border-bottom: 1px solid $border-color-input;
    //LABEL DEFAULT STATE
    &:placeholder-shown:not(:focus) + * {
      top: 1.25rem;
      // font-size: $type-font-size-16;
    }
    &:focus {
      box-shadow: none;
      outline: none;
      // border-bottom: 2px solid $border-color-input-focus;
    }
    &::placeholder {
      opacity: 0;
      // @include transition($transition-base);
    }
    &:placeholder-shown:not(:focus)::placeholder {
      opacity: 0;
    }
  }
  select {
    padding-right: 1em;
    // background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='%23333' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E") no-repeat right .5em bottom .25em;
    background-size: 8px 10px;
    -webkit-appearance: none;
       -moz-appearance: none;
            appearance: none;
  }
  // TEXTAREA
  textarea {
    width: 100%;
    height: auto;
  }
}