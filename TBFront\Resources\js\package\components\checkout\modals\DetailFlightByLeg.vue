<template>
    <div class="modal fade" :id="`modal-DetailFlightByLeg-${idModal}`">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">{{ __("messages.detail_fly") }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <template v-for="(fly, idx) in detailFlightByLeg.flightSegments">
                    <div class="row" :class="{ 'mt-3' : (idx != 0) }">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body px-3">
                                    <div class="row mx-0">
                                        <div class="col-6 font-14 px-0 fw-bold text-start">                                            
                                            <template v-if="idModal == `starting`">
                                                <i class="font-icons icon-plane-right me-1"></i> 
                                                {{ __("messages.departure_flight") }}
                                            </template>
                                            <template v-else>
                                                <i class="font-icons icon-plane-left me-1"></i> 
                                                {{ __("messages.return_flight") }}
                                            </template>
                                        </div>
                                        <div class="col-6 px-0 text-end img-al">
                                            <img class="ml-auto" width="100px" :src="getUrlLogo(fly.generalInfo)" />
                                        </div>
                                    </div>
                                    <hr style="opacity: 1; margin: 0.25rem 0;"/>
                                    <div class="row align-items-center mx-0">
                                        <div class="col-12 font-14 px-0">
                                            {{ $filters.date(fly.generalInfo.departureDate, 'dddd, DD MMMM YYYY') }}
                                        </div>
                                        <div class="col-2 text-center px-0">
                                            <span class="d-block font-10" >
                                                {{ fly.generalInfo.origin }}
                                            </span>
                                            <span class="d-block font-14 fw-bold">
                                                {{ fly.generalInfo.departureTime }}
                                            </span>
                                        </div>
                                        <div class="col-8 text-center px-1">
                                            <div class="row">
                                                <div class="col-12 d-flex align-items-center justify-content-center">
                                                    <div class="flight-list-item__circle"></div>
                                                    <div class="w-100 border-dashed "></div><i
                                                        class="font-14 icon-plane-right"></i>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-2 text-center px-0">
                                            <span class="d-block font-10" >
                                                {{ fly.generalInfo.destination }}
                                            </span>
                                            <span class="d-block font-14 fw-bold">
                                                {{ fly.generalInfo.arrivalTime }}
                                            </span>
                                        </div>
                                    </div>
                                   
                                </div>
                            </div>
                        </div>
                    </div>
                    </template>
                    
                </div>
            </div>
        </div>
    </div>
</template>

<script>


export default {
    props: {
        detailFlightByLeg: {},
        idModal: "",
        flightItinerary: {}
    },
    methods: {
        getUrlLogo(generalInfo){
            let code = generalInfo.airlineCodeOperator ? generalInfo.airlineCodeOperator : generalInfo.airlineCode;
            let url = window.__pt?.settings?.site?.siteUrl || "";
            return `${url}/assets-tb/img/tiquetesbaratos/checkout/${code}.png`
        }
    }

}
</script>

<style lang="scss" scoped>


.flight-list-item__circle {
    background-color: #fff;
}

.flight-list-item__circle {
    border: 1px solid gray;
    border-radius: 50%;
    height: 7px;
    width: 7px;
}

.border-dashed {
    border-bottom: 1px dashed #d5d4d4;
}

.img-al{
    height: 20px;
    line-height: 1.1;
}
.ml-auto{
    object-fit: contain;
    object-position: right;
    height: 100%;
    width: inherit;
}

</style>
