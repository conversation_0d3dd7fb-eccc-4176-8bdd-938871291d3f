﻿using TBFront.Interfaces;
using TBFront.Models.AB;
using TBFront.Application.Mappers;

namespace TBFront.Application.Implementations
{
    public class GrowthBookHandler : IGrowthBookHandler
    {
        private readonly IGrowthBookService _growthBookService;
        private readonly IHttpContextAccessor _httpContextAccessor;
        public GrowthBookHandler(IGrowthBookService growthBookService, IHttpContextAccessor httpContextAccessor = null)
        {
            _growthBookService = growthBookService;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task<GrowthBookResponse> QueryAsync(string request, CancellationToken ct)
        {
            var requestGB = GrowthBookMapper.GetRequest(request);
            var response = await _growthBookService.QueryAsync(requestGB, ct);

            if (!response.NoExperiment)
            {
                var variationId = response.IsExperiment ? "1" : "0";
                _httpContextAccessor.HttpContext.Response.Cookies.Append(response.Experiment.CookieVariation, variationId);
            }

            return response;
        }
    }
}
