<template>
    <div class="email-input-wrapper" v-outside="hideSuggestions">
        <label for="`email`" class="font-12 px-1 label-xs">{{ __("checkout.email") }}</label>

        <div class="input-wrapper">
            <input :id="`email`"
                   :name="`email`"
                   :value="value"
                   @input="onInput"
                   @keydown.down.prevent="moveDown"
                   @keydown.up.prevent="moveUp"
                   @keydown.enter.prevent="selectActive"
                   @focus="showSuggestions = true"
                   class="form-control form__input"
                   :class="{ 'is-invalid': errors?.length }"
                   @change="emitValidate"
                   :autocomplete="value.length > 0 ? 'off' : 'on'" />

            <ul v-if="showSuggestions && filteredSuggestions.length" class="suggestions-list">
                <li v-for="(suggestion, index) in filteredSuggestions"
                    :key="suggestion"
                    :class="{ active: index === activeIndex }"
                    @mousedown.prevent="selectSuggestion(suggestion)">
                    {{ suggestion }}
                </li>
            </ul>
        </div>
        <span v-if="errorMessage" class="invalid-feedback" :style="{ display: 'block' }">{{ errorMessage }}</span>
    </div>
</template>

<script>
	import { Field } from 'vee-validate';

    export default {
        props: {
            value: {
                type: String,
                default: ''
            },
            errorMessage: String,
            errors: Array
        },
        data() {
            return {
                showSuggestions: false,
                activeIndex: -1,
                domains: window.__pt.settings.site?.emailDomains ?? []
            };
        },
        computed: {
            filteredSuggestions() {
                if (!this.value) return [];

                // Si no hay @, mostrar todas las opciones con el texto como prefix
                if (!this.value.includes('@')) {
                    return this.domains.map((d) => `${this.value}@${d}`);
                }

                // Si hay @, separar prefix y domain
                const [prefix, domain] = this.value.split('@');

                return this.domains
                    .filter((d) => d.startsWith(domain))
                    .map((d) => `${prefix}@${d}`);
            },
        },
        watch: {
            filteredSuggestions() {
                this.activeIndex = -1;
            },
        },
        methods: {
            emitValidate() {
                this.$emit('validate', 'email');
            },
            onInput(e) {
                this.$emit('input', e.target.value); // lo que vee-validate espera
                this.showSuggestions = true;
            },
            moveDown() {
                if (!this.filteredSuggestions.length) return;
                this.activeIndex =
                    this.activeIndex < this.filteredSuggestions.length - 1
                        ? this.activeIndex + 1
                        : 0;
            },
            moveUp() {
                if (!this.filteredSuggestions.length) return;
                this.activeIndex =
                    this.activeIndex > 0
                        ? this.activeIndex - 1
                        : this.filteredSuggestions.length - 1;
            },
            selectActive() {
                if (this.activeIndex !== -1) {
                    this.selectSuggestion(this.filteredSuggestions[this.activeIndex]);
                }
            },
            selectSuggestion(suggestion) {
                this.$emit('input', suggestion);
                this.showSuggestions = false;
            },
            hideSuggestions() {
                this.showSuggestions = false;
            },
        },
        directives: {
            outside: {
                beforeMount(el, binding) {
                    el.clickOutsideEvent = (event) => {
                        if (!(el === event.target || el.contains(event.target))) {
                            binding.value();
                        }
                    };
                    document.addEventListener('click', el.clickOutsideEvent);
                },
                unmounted(el) {
                    document.removeEventListener('click', el.clickOutsideEvent);
                },
            },
        },
    };
</script>