﻿using System.Text.Json.Serialization;

namespace TBFront.Models.Meta.Schema
{
    public class BreadcrumbElement
    {

        [JsonPropertyName("@type")]
        public string? Type { get; set; }

        [JsonPropertyName("position")]
        public int Position { get; set; }

        [JsonPropertyName("item")]
        public BreadcrumbItem? Item { get; set; }

        public BreadcrumbElement()
        {
            Item = new BreadcrumbItem();
        }

    }

    public class BreadcrumbItem
    {
        [JsonPropertyName("@id")]
        public string? Id { get; set; }

        [JsonPropertyName("name")]
        public string? Name { get; set; }
    }
}
