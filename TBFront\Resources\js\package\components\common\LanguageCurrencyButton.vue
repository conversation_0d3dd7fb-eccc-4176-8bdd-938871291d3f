<template>
    <div class="d-center" v-if="!mobile">
        <button class="header__btn" type="button" @click="showModal('modal_langcurr')">
            <img :src="'/assets-tb/img/header/' + (culture.cultureCode || '') + '.svg'" width="24" height="24" class="rounded-circle"
                style="object-fit: cover;" alt="Culture Flag">
            {{ (culture?.name || '') }} - {{ (currency?.currencyCode || '') }}
        </button>
    </div>
    <div v-if="mobile">
        <button class="w-100 d-flex align-items-center g-8" @click="showModal('modal_langcurr')" aria-haspopup="true" type="button" aria-controls="modal_langcurr">
            <img width="18" height="18"
                class="rounded-circle"
                 style="object-fit: cover;"
            :src="'/assets-tb/img/header/' + (culture.cultureCode || '') + '.svg'"
                 loading="lazy">
                <span class="flex-grow-1 d-flex align-items-center justify-content-between">
                    <span class="d-flex align-items-center g-base">
                        {{ __("culture.language") }} & {{ __("culture.currency") }} 
                        <i class="icons-angle-right font-24"></i>
                    </span>
                     {{ (culture?.name || '') }}  ({{ (currency?.currencyCode || '') }})
            </span>
        </button>
    </div>


</template>

<script setup>
import { defineProps } from "vue";

const { culture, currency, mobile } = defineProps({
    culture: {
        type: Object,
        default: {}
    },
    currency: {
        type: Object,
        default: {}
    },
    mobile: {
        type: Boolean
    }
});

const showModal = (id) => {
    const modalElement = document.getElementById(id);

    const modal = new bootstrap.Modal(modalElement);
	modal.show();
}
</script>
