<template>
    <div class="modal fade c-detail-modal-desktop" id="modalDetail" tabindex="-1" aria-labelledby="exampleModalDetail01" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-lg">
            <div class="modal-content px-2">
                <div class="modal-header">
                    <p class="font-24 font-poppins-medium mb-0">
                        {{getExtraData.view}}
                    </p>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body pt-0 px-4 c-view-detail">
                    <div class="b-mobile-1">
                        <template v-if="!showDetail">
                            <div class="int-xs pb-4 pt-0 pt-md-2">
                                <div id="detailInter" class="col-12  px-md-0 pr-0 pr-md-2">
                                    <div class="row">
                                        <div class="nav-tab d-block d-md-none px-2 pt-2 w-100 d-block">
                                            <button class="tablinks rounded-top tablinks" :class="{'active': !getIsLuggage}" id="defaultOpen" @click="openTabs('defaultOpen', 'package-info', 'flight-info')">
                                                {{__("messages.flight")}}
                                            </button>
                                            <button class="tablinks rounded-top tablinks" :class="{'active': getIsLuggage}" id="tabFares" @click="openTabs('tabFares', 'flight-info', 'package-info')">
                                                {{__("messages.what_includes")}}
                                            </button>
                                        </div>
                                        <div id="flight-info" class="content60 bg-white p-2 pb-3 border rounded mt-0 tabcontent" :class="{'hide-xs': getIsLuggage}">
                                            <div class="row">
                                                <div class="col-12 my-3 px-4">
                                                    <span>
                                                        <img height="30" v-lazy="getExtraData.airlineLogoUri" v-if="!imgErrors.some((an)=> an == getExtraData.airlineName)" @error="handleImgError(getExtraData.airlineName)" />
                                                    </span>
                                                    <span class="font-16 position-t-1 ps-2">{{ getExtraData.airlineName }}</span>
                                                </div>
                                            </div>
                                            <template v-for="detail in flightDetail.flightSegments">
                                                <p v-if="detail.generalInfo.operator" class="mb-2 text-left font-14 font-weight-bold d-block pl-2">
                                                    <i class="icon-info mr-2"></i>
                                                    {{__("messages.flight_operated_by")}} {{detail.generalInfo.operator}}, {{__("messages.to_document")}}.
                                                </p>
                                                <div class="info-flight">
                                                    <div class="content-info">
                                                        <div class="line-steps">
                                                            <div class="circle-top1"></div>
                                                            <div class="line1"></div>
                                                        </div>
                                                        <div class="info-travel align-top">
                                                            <div class="d-flex">
                                                                <p class="mb-0 text-left font-14 d-block back-p">
                                                                    {{ __("messages.flight")+" "+detail.generalInfo.flightNumber}}
                                                                    {{ detail.generalInfo.providerCabin != null && detail.generalInfo.providerCabin != "" ? "| " + __("messages.cabin") + ": " + detail.generalInfo.providerCabin : "" }}
                                                                </p>
                                                                <p class="mb-0 text-left font-12 gray d-block title-color-gray ps-2 mt-1">
                                                                    {{detail.generalInfo.airEquipType != null && detail.generalInfo.airEquipType != "" ? __("messages.aircraft")+" "+detail.generalInfo.airEquipType : ""}}
                                                                </p>
                                                            </div>
                                                            <div class="d-flex">
                                                                <span class="icon icon-flight-takeoff font-20 align-top icon-strong"></span>
                                                                <p class="mb-0 text-left p01 font-18 fw-medium title-color-gray font-poppins">{{ detail.generalInfo.departureCity }}</p>
                                                            </div>
                                                            <p class="mb-0 text-left font-16 gray d-block ps-4 title-color-gray font-poppins">{{ $filters.date(detail.generalInfo.departureDate, 'ddd, DD MMM, YYYY') }}</p>
                                                            <p class="mb-0 text-left font-21 gray d-block ps-4 time-color-gray font-poppins-medium">
                                                                {{ detail.generalInfo.departureTime }} hrs
                                                            </p>
                                                            <p class="ps-4 mb-0 text-left font-12 gray d-block ps-4 title-color-gray">
                                                                {{ detail.generalInfo.departureAirportName }} ({{ detail.generalInfo.departureAirportCode }}) {{ detail.generalInfo.terminalDeparture != null && detail.generalInfo.terminalDeparture != "" ? "| " + __("messages.terminal") + " " + detail.generalInfo.terminalDeparture : "" }}
                                                            </p>

                                                            <div class="c-capsule-light rounded-pill font-14 d-block ms-4 p-2 my-3 text-center alert alert-secondary border-0 text-black">
                                                                <span class="icon icon-clock position-relative"></span>
                                                                {{ durationFormat(detail.generalInfo.flightDuration)}}
                                                                <template v-for="technicalStop in detail.intermediatePoints">
                                                                    <br>
                                                                    {{ __('messages.wait_on_board').replace("{0}", technicalStop.destination) }} {{ durationFormat(technicalStop.duration)}}
                                                                </template>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="content-info">
                                                        <div class="line-steps">
                                                            <div class="circle-top1"></div>
                                                        </div>
                                                        <div class="info-travel align-top">
                                                            <div class="d-flex">
                                                                <span class="icon icon-flight-land font-20 align-top icon-strong"></span>
                                                                <p class="mb-0 text-left p01 font-18 fw-medium title-color-gray font-poppins">{{ detail.generalInfo.arrivalCity }}</p>
                                                            </div>
                                                            <p class="mb-0 text-left font-16 gray d-block ps-4 title-color-gray font-poppins">
                                                                {{ $filters.date(detail.generalInfo.arrivalDate, 'ddd, DD MMM, YYYY') }}
                                                            </p>
                                                            <p class="mb-0 text-left font-21 gray d-block ps-4 time-color-gray font-poppins-medium">
                                                                {{ detail.generalInfo.arrivalTime }} hrs
                                                            </p>
                                                            <p class="mb-0 text-left font-12 gray d-block ps-4 title-color-gray">
                                                                {{ detail.generalInfo.arrivalAirportName }} ({{ detail.generalInfo.arrivalAirportCode }}) {{ detail.generalInfo.terminarArrival != null && detail.generalInfo.terminarArrival != "" ? "| " + __("messages.terminal") + " " + detail.generalInfo.terminarArrival : "" }}
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="ps-2 ps-md-0 mt-2 mt-md-0"
                                                     v-for="stop in detail.stopInfo">
                                                    <div class="ps-md-0 pe-4 pe-md-0">
                                                        <hr />
                                                    </div>
                                                    <div class=" ps-md-0 text-center text-secondary">
                                                        {{ __("messages.connection_in") }} {{ stop.destination }} {{ __("messages.with_waiting") }} {{ stop.duration }}
                                                    </div>
                                                    <div class=" ps-md-0 pe-4 pe-md-0">
                                                        <hr />
                                                    </div>
                                                </div>
                                            </template>
                                        </div>
                                        <div id="package-info" class="content40 mt-0 bg-white pt-2 border rounded tabcontent" :class="{'hide-xs': !getIsLuggage}">
                                            <p class="pl-3 font-18 my-2">
                                                {{__("messages.rate_type")}}: {{flightFamilyFare.familyFareName}}
                                            </p>
                                            <ul class="ps-4 ul-icons list-unstyled position-relative"
                                                v-if="getFlightFamilyFare.familyFareContent"
                                                v-for="familyFare, index in mappingFamilyFare(getFlightFamilyFare.familyFareContent, true)":key="index">
                                                <li :class="familyFare.class">
                                                    <span class="d-block">
                                                        <span class="font-poppins-medium">                                                        
                                                            {{familyFare.title}}:
                                                        </span>
                                                        <span class="color-gray-800 font-12 mb-2 d-block">{{familyFare.description}}</span>
                                                    </span>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </template>
                        <template v-else>
                            <div class="loading-section position-relative">
                                <div id="loader-page" class="loading-page d-center ">
                                    <div class="loader__logo"></div>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import { storeToRefs } from 'pinia';
    import { useFlightDetailStore } from '../../stores/flightDetail';
    import { useFlightFamilyFareStore } from '../../stores/flightFamilyFare';
    import { mappingFamilyFare } from '../../../utils/utils';

    export default {
        data() {
            return {
                pt: window.__pt,
                imgErrors: [],
            }
        },
        setup() {
            const flightDetailStore = useFlightDetailStore();
            const flightFamilyFareStore = useFlightFamilyFareStore();

            const { getFlightDetail, getShowDetail, getExtraData } = storeToRefs(flightDetailStore);
            const { getFlightFamilyFare, getIsLuggage } = storeToRefs(flightFamilyFareStore);

            const { durationFormat } = flightDetailStore;

            return { getFlightDetail, getFlightFamilyFare, durationFormat, getShowDetail, getExtraData, getIsLuggage, mappingFamilyFare }
        },
        mounted() {
        },

        computed: {
            flightDetail() {
                return this.getFlightDetail;
            },
            flightFamilyFare() {
                return this.getFlightFamilyFare;
            },
            showDetail() {
                return this.getShowDetail;
            },
            showLogo() {
                return this.getAirlineLogoUri
            }
        },
        methods: {
            openTabs(tab, hide, show) {
                document.getElementById(hide).classList.add("hide-xs");
                document.getElementById(show).classList.remove("hide-xs");
                let tablinks = document.getElementsByClassName("tablinks");
                for (let i = 0; i < tablinks.length; i++) {
                    tablinks[i].classList.remove('active');
                }
                document.getElementById(tab).classList.add("active");
            },
            handleImgError(airline) {
                this.imgErrors.push(airline);
            },
            validateMultipleCabins(data) {
                return data.every(item => item.generalInfo.providerCabin === data[0].generalInfo.providerCabin);;
            }
        }

    }
</script>
<style lang="scss" scoped>
    .loading-section {
        height: 150px !important;

        .loading-page {
            position: absolute;
        }
    }

    .h-mobile-1 {
        .icon {
            display: block;
            width: 30px;
            margin-top: 10px;
            right: -20px;
            position: relative;
        }

        @media (max-width: 767px) {
            height: 130px;
            left: 0;
            padding: 0 15px;
            position: absolute;
            right: 0;
            top: 0;
        }
    }

    .c-detail-modal-desktop {
        .icon-close {
            @media (max-width: 767px) {
                color: #333;
                position: absolute !important;
                right: 0 !important;
                top: 0;
                margin: initial !important;
                width: initial !important;
            }
        }

        .modal-body {
            @media (max-width: 767px) {
                height: 100vh;
            }
        }
    }

    .c-view-detail {
        color: #0e213a;

        .circle-top {
            border: 2px solid #c4c4c4;
            border-radius: 50px;
            height: 10px;
            left: 0;
            margin: auto;
            position: absolute;
            top: 0;
            right: 0;
            width: 10px;
            z-index: 1;
        }
    }

    .hide-xs {
        @media (max-width: 767px) {
            display: none;
        }
    }

    .hide-lg {
        @media (min-width: 1280px) {
            display: none;
        }
    }

    .hide-md {
        @media (min-width: 768px) and (max-width: 990px) {
            display: none;
        }
    }

    @media (min-width: 1025px), (min-width: 768px) and (max-width: 1024px) {
        #detailInter {
            .row {
                justify-content: space-between;
            }

            #flight-info {
                width: 65% !important;
            }

            #package-info {
                width: 33% !important;
            }

            #flight-info.w-100 {
                width: 100% !important;
            }

            #package-info.w-100 {
                width: 100% !important;
            }
        }
    }

    .c-info-flight {
        .bg-gray {
            margin: 0 -15px;
        }

        .icon {
            color: #94a3b8;
            position: relative;
            top: -2px;
        }

        .icon-plane-left {
            color: initial;
            position: relative;
            top: 5px;
        }

        .icon-plane-right {
            color: initial;
            position: relative;
            top: 5px;
        }

        .pipe {
            font-size: 8px;
            position: relative;
            top: 5px;
        }
    }

    .c-info-flight {
        .bg-gray {
            margin: 0 1px 0 -14px;
        }
    }

    .content-info {
        position: relative;
        display: inline-table;
        width: 100%;
        height: auto;
    }

    .line-steps {
        position: relative;
        width: 5%;
        display: table-cell;
        height: 100%;
    }

    .circle-top1 {
        border: 2px solid #c4c4c4;
        border-radius: 50px;
        height: 10px;
        margin: auto;
        width: 10px;
        z-index: 1;
        position: absolute;
        top: 5px;
        left: 6px;
        display: block;
        background-color: #fff;
    }

    .line1 {
        bottom: -9px;
        left: 0;
        top: 11px;
        border: 1px dashed #c4c4c4;
        border-radius: 50px;
        margin: auto;
        position: absolute;
        right: 0;
        width: 1px;
        z-index: 0;
    }


    .nav-tab button {
        position: relative;
        width: 40%;
        height: 40px;
        line-height: 40px;
        display: inline-block;
        color: #495057;
        background-color: #fff;
        border: 1px solid #dee2e6;
        text-align: center;
        border-bottom: #fff;
    }

    .nav-tab button.active {
        border-top: 5px solid !important;
        border-color: var(--border-primary) !important;
        color: var(--text-primary) !important;
        position: relative;
        bottom: -1px;
        border-bottom: 1px solid #fff;
        z-index: 2;
        line-height: 100%;
        position: relative;
    }

    @media (max-width: 767px) {
        .nav-tab .active {
            &:before {
                background-color: #fff;
                content: "";
                height: 5px;
                position: absolute;
                bottom: -3px;
                width: 100%;
                left: 0;
                right: 0;
            }
        }
    }

    .info-travel {
        p {
            display: inline-block;
        }

        .p01 {
            position: relative;
            right: -4px;
        }
    }

    .text-secondary {
        color: var(--text-link) !important;
    }

    .c-capsule-light {
        .icon {
            top: -1px;
        }
    }

    .title-color-gray {
        color: #3B3A40;
    }

    .time-color-gray {
        color: #18161C;
    }

    .font-21 {
        font-size: 21px;
    }

    .row {
        margin-right: calc(-0.5* var(--bs-gutter-x)) !important;
        margin-left: calc(-0.5* var(--bs-gutter-x)) !important;
    }

    .ps-2 {
        padding-left: .5rem !important;
    }

    .text-secondary {
        color: var(--text-link) !important;
    }

    @media (max-width: 767px) {
        .b-mobile-1 {
            top: 10px;
        }
    }

    .back-p {
        background-color: #F5F5F7; /* Aplica un fondo gris */
        padding-top: 4px;
        padding-bottom: 4px;
        padding-right: 8px;
        padding-left: 8px;
        color: black;
    }

    .ul-icons:nth-of-type(2) {
        border-bottom: 1px solid #eee;
        padding-bottom: 15px;
        li {
            div {
                margin-bottom: 0 !important;
            }            
        }
    }
    .ul-icons {
        li {
            &:before {
                content: '•';
                color: #ccc;
                font-size: 26px;
                margin-right: 10px;
                position: absolute;
                left: 4px;
                top: -10px;             
            }
        }
    }

    .ul-icons .i-carry-on-ok {
        &:before {
            content: "\e937" !important;
            color: #333 !important;
            font-family: icomoon !important;
            font-size: 14px;
            top: 0;
        }
    }
    .ul-icons .i-carry-on-none {
        &:before {
            content: "\e936" !important;
            color: #999 !important;
            font-family: icomoon !important;
            font-size: 14px;
            top: 0;
        }        
    }

    .ul-icons .i-bag-ok {
        &:before {
            content: "\e935" !important;
            color: #333 !important;
            font-family: icomoon !important;
            font-size: 14px;
            top: 0;
        }
    }
    .ul-icons .i-bag-none {
        &:before {
            content: "\e934";
            color: #999 !important;
            font-family: icomoon !important;
            font-size: 14px;
            top: 0;
        }        
    }
</style>