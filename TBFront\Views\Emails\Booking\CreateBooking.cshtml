﻿@using Microsoft.Extensions.Options
@using TBFront.Helpers
@using TBFront.Options;
@using TBFront.Models.Request;
@inject IOptions<SettingsOptions> settingOptions
@inject ViewHelper _
@{
    Layout = null;
    var Customer = Model.Customer;
    var Passengers = Customer.PassengersBooking[0].Passengers;
    Detail DetailStarting = Model.FlightDetail.DetailStarting.Detail;
    Detail DetailReturning = Model.FlightDetail.DetailReturning.Detail;
    var Rate = Model.Quote.Rate;

}
<!-- Nueva estructura mailing -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Document</title>
</head>
<body>


<meta content="width=device-width, initial-scale=1" name="viewport">


<table cellpadding="0" cellspacing="0" align="center" width="650" 
       style="padding: 10px; background-color: #fff; border-radius: 15px; font-family: arial;">
    <tbody>
    <tr>
        <td>
            <table style="width: 100%; background-color: #F0F6FF; border-radius: 10px; margin-bottom: 10px; padding: 10px; color: #003B98; border: 1px solid #e5e5e5;">
                <tr>
                    <td style="font-size: 18px;">@_.Localizer("mailCreateBookingIdBooking") <strong>@Model.MasterLocatorID</strong></td>
                    <td align="center">
                        <img width="50" src="@($"{@settingOptions.Value.SiteUrl}/assets-tb/img/logo.png")"/>
                    </td>
                </tr>
            </table>



            <table cellpadding="0" cellspacing="0" style="padding: 0; width: 100%; color: #555;">
                <tbody>
            @{
                int index = 0; // Inicializamos una variable para el índice
            }
                @foreach (var Start in DetailStarting.FlightSegments)
                {
                    @if (index == 0)
                    {
                        <tr>
                            <td style="width: 100%;">
                                <table style="width: 100%; padding: 5px 10px 5px 10px; background-color: #f9f9f9; border-radius: 5px 5px 0 0; border: 1px solid #e5e5e5;">
                                    <tr>
                                        <td style="width: 20px;">
                                            <img style="vertical-align:middle;" width="18" src="@($"{@settingOptions.Value.SiteUrl}/assets-tb/img/tiquetesbaratos/checkout/plane-right.jpg")"/>
                                        </td>
                                        <td style="width: 320px;">
                                            <span style="font-weight: bold; margin: 0 15px 0 3px; font-size: 17px;">@_.Localizer("mailCreateBookingFlightStart")</span>
                                        </td>
                                        <td align="right">
                                            <img style="vertical-align:middle;" width="100" src="@($"{@settingOptions.Value.SiteUrl}/assets-tb/img/tiquetesbaratos/checkout/{Start.GeneralInfo.AirlineCode}.png")"/>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    }

                    <tr>
                        <td align="left" style="padding: 0 10px; border: 1px solid #e5e5e5; border-top: none; padding-bottom: 5px; background: #fff; width: 100%; margin-bottom: 20px;">
                            <table cellpadding="0" cellspacing="0" align="left">
                                <tbody>

                                <tr>
                                    <td width="320" align="left">
                                        <table cellpadding="0" cellspacing="0" style="font-size: 15px; width: 100%;">
                                            <tbody>
                                            <tr>
                                                <td align="left">
                                                    <table style="padding-top: 10px; font-size: 15px; color: #555;">
                                                        <tr>
                                                            <td>
                                                                <img style="vertical-align:middle;" width="12" src="@($"{@settingOptions.Value.SiteUrl}/assets-tb/img/tiquetesbaratos/checkout/location-map.jpg")"/>
                                                            </td>
                                                            <td style="padding-left: 4px; font-weight: bold; line-height: 1.2;">@_.Localizer("mailCreateBookingDepurate") <strong>@Start.GeneralInfo.DepartureCity</strong></td>
                                                        </tr>
                                                        <tr>
                                                            <td colspan="3" style="padding: 9px 0 6px 0; line-height: 1.2;">@_.Localizer("mailCreateBookingDate") <strong>@_.DateEmailCheckout(Start.GeneralInfo.DepartureDate)</strong></td>
                                                        </tr>
                                                        <tr>
                                                            <td colspan="3" style="padding: 6px 0; line-height: 1.2;">@_.Localizer("mailCreateBookingTime") <strong>@Start.GeneralInfo.DepartureTime</strong></td>
                                                        </tr>
                                                        <tr>
                                                            <td colspan="3" style="padding: 6px 0; line-height: 1.2;">@_.Localizer("mailCreateBookingAirport") <strong>@Start.GeneralInfo.DepartureAirportInformation</strong></td>
                                                        </tr>
                                                        @if (!string.IsNullOrWhiteSpace(Start?.GeneralInfo?.TerminalDeparture))
                                                        {
                                                            <tr>
                                                                <td colspan="3" style="padding: 6px 0; line-height: 1.2;">@_.Localizer("mailCreateBookingTerminal") <strong>@Start.GeneralInfo.TerminalDeparture</strong></td>
                                                            </tr>
                                                        }
                                                        <tr>
                                                            <td colspan="3" style="padding: 6px 0; line-height: 1.2;">@_.Localizer("mailCreateBookingFlightNumber") <strong>@Start.GeneralInfo.FlightNumber</strong></td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            <table cellpadding="0" cellspacing="0" align="left">
                                <tbody>
                                <tr>
                                    <td width="320" align="left">
                                        <table cellpadding="0" cellspacing="0" style="font-size: 15px; width: 100%;">
                                            <tbody>
                                            <tr>
                                                <td align="left">
                                                    <table style="padding-top: 10px; font-size: 15px; color: #555;">
                                                        <tr>
                                                            <td>
                                                                <img style="vertical-align:middle;" width="12" src="@($"{@settingOptions.Value.SiteUrl}/assets-tb/img/tiquetesbaratos/checkout/location-map.jpg")"/>
                                                            </td>
                                                            <td style="padding-left: 4px; font-weight: bold; line-height: 1.2;">@_.Localizer("mailCreateBookingArrival") <strong>@Start.GeneralInfo.ArrivalCity</strong></td>
                                                        </tr>
                                                        <tr>
                                                            <td colspan="3" style="padding: 9px 0 6px 0; line-height: 1.2;">@_.Localizer("mailCreateBookingDate") <strong>@_.DateEmailCheckout(Start.GeneralInfo.ArrivalDate)</strong></td>
                                                        </tr>
                                                        <tr>
                                                            <td colspan="3" style="padding: 6px 0; line-height: 1.2;">Hora de llegada: <strong>@Start.GeneralInfo.ArrivalTime</strong></td>
                                                        </tr>
                                                        <tr>
                                                            <td colspan="3" style="padding: 6px 0; line-height: 1.2;">@_.Localizer("mailCreateBookingAirport") <strong>@Start.GeneralInfo.ArrivalAirportInformation</strong></td>
                                                        </tr>
                                                        @if (!string.IsNullOrWhiteSpace(Start?.GeneralInfo?.TerminarArrival))
                                                        {
                                                                <tr>
                                                                    <td colspan="3" style="padding: 6px 0; line-height: 1.2;">@_.Localizer("mailCreateBookingTerminal") <strong>@Start.GeneralInfo.TerminarArrival</strong></td>
                                                                </tr>
                                                        }
                                                        <tr>
                                                            <td colspan="3" style="padding: 6px 0; line-height: 1.2;">@_.Localizer("mailCreateBookingFlightNumber") <strong>@Start.GeneralInfo.FlightNumber</strong></td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                    index++;
                }

            @{
                int indexReturn = 0; // Inicializamos una variable para el índice
            }
                @if  (Rate.IsRoundtrip)
                {
                    
                    @foreach (var Return in DetailReturning.FlightSegments)
                    {
                        @if (indexReturn == 0)
                        {
                            <tr>
                                <td style="width: 100%;">
                                    <table style="width: 100%; padding: 5px 10px 5px 10px; margin-top: 20px; background-color: #f9f9f9; border-radius: 5px 5px 0 0; border: 1px solid #e5e5e5; color: #555;">
                                        <tr>

                                            <td style="width: 20px;">
                                                <img style="vertical-align:middle;" width="18" src="@($"{@settingOptions.Value.SiteUrl}/assets-tb/img/tiquetesbaratos/checkout/plane-left.jpg")"/>
                                            </td>
                                            <td style="width: 320px;">
                                                <span style="font-weight: bold; margin: 0 15px 0 3px; font-size: 17px;">@_.Localizer("mailCreateBookingFlightReturn")</span>
                                            </td>

                                            <td align="right">
                                                <img style="vertical-align:middle;" width="100" src="@($"{@settingOptions.Value.SiteUrl}/assets-tb/img/tiquetesbaratos/checkout/{Return.GeneralInfo.AirlineCode}.png")"/>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        }
                        

                        <tr>
                            <td align="left" style="padding: 0 10px; border: 1px solid #e5e5e5; border-top: none; padding-bottom: 5px; background: #fff; width: 100%;">
                                <table cellpadding="0" cellspacing="0" align="left">
                                    <tbody>

                                    <tr>
                                        <td width="320" align="left">
                                            <table cellpadding="0" cellspacing="0" style="font-size: 15px; width: 100%;">
                                                <tbody>
                                                <tr>
                                                    <td align="left">
                                                        <table style="padding-top: 10px; font-size: 15px; color: #555;">
                                                            <tr>
                                                                <td>
                                                                    <img style="vertical-align:middle;" width="12" src="@($"{@settingOptions.Value.SiteUrl}/assets-tb/img/tiquetesbaratos/checkout/location-map.jpg")"/>
                                                                </td>
                                                                <td style="padding-left: 4px; font-weight: bold; line-height: 1.2;">@_.Localizer("mailCreateBookingDepurate") <strong>@Return.GeneralInfo.DepartureCity</strong></td>
                                                            </tr>
                                                            <tr>
                                                                <td colspan="3" style="padding: 9px 0 6px 0; line-height: 1.2;">@_.Localizer("mailCreateBookingDate") <strong>@_.DateEmailCheckout(@Return.GeneralInfo.DepartureDate)</strong></td>
                                                            </tr>
                                                            <tr>
                                                                <td colspan="3" style="padding: 6px 0; line-height: 1.2;">@_.Localizer("mailCreateBookingTime") <strong>@Return.GeneralInfo.DepartureTime</strong></td>
                                                            </tr>
                                                            <tr>
                                                                <td colspan="3" style="padding: 6px 0; line-height: 1.2;">@_.Localizer("mailCreateBookingAirport") <strong>@Return.GeneralInfo.DepartureAirportInformation</strong></td>
                                                            </tr>
                                                            @if (!string.IsNullOrWhiteSpace(Return?.GeneralInfo?.TerminalDeparture))
                                                            {
                                                            <tr>
                                                                <td colspan="3" style="padding: 6px 0; line-height: 1.2;">@_.Localizer("mailCreateBookingTerminal") <strong>@Return.GeneralInfo.TerminalDeparture</strong></td>
                                                            </tr>
                                                            }
                                                            <tr>
                                                                <td colspan="3" style="padding: 6px 0; line-height: 1.2;">@_.Localizer("mailCreateBookingFlightNumber") <strong>@Return.GeneralInfo.FlightNumber</strong></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                                <table cellpadding="0" cellspacing="0" align="left">
                                    <tbody>
                                    <tr>
                                        <td width="320" align="left">
                                            <table cellpadding="0" cellspacing="0" style="font-size: 15px; width: 100%;">
                                                <tbody>
                                                <tr>
                                                    <td align="left">
                                                        <table style="padding-top: 10px; font-size: 15px;">
                                                            <tr>
                                                                <td>
                                                                    <img style="vertical-align:middle;" width="12" src="@($"{@settingOptions.Value.SiteUrl}/assets-tb/img/tiquetesbaratos/checkout/location-map.jpg")"/>
                                                                </td>
                                                                <td style="padding-left: 4px; font-weight: bold;">@_.Localizer("mailCreateBookingArrival") <strong>@Return.GeneralInfo.ArrivalCity</strong></td>
                                                            </tr>
                                                            <tr>
                                                                <td colspan="3" style="padding: 9px 0 6px 0">@_.Localizer("mailCreateBookingDate") <strong>@_.DateEmailCheckout(Return.GeneralInfo.ArrivalDate)</strong></td>
                                                            </tr>
                                                            <tr>
                                                                <td colspan="3" style="padding: 6px 0;">Hora de llegada: <strong>@Return.GeneralInfo.ArrivalTime</strong></td>
                                                            </tr>
                                                            <tr>
                                                                <td colspan="3" style="padding: 6px 0;">@_.Localizer("mailCreateBookingAirport") <strong>@Return.GeneralInfo.ArrivalAirportInformation</strong></td>
                                                            </tr>
                                                            @if (!string.IsNullOrWhiteSpace(Return?.GeneralInfo?.TerminarArrival)){
                                                            <tr>
                                                                <td colspan="3" style="padding: 6px 0;">@_.Localizer("mailCreateBookingTerminal") <strong>@Return.GeneralInfo.TerminarArrival</strong></td>
                                                            </tr>
                                                            }
                                                            <tr>
                                                                <td colspan="3" style="padding: 6px 0;">@_.Localizer("mailCreateBookingFlightNumber") <strong>@Return.GeneralInfo.FlightNumber</strong></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        indexReturn++;
                    }
                }

                <tr>
                    <td>
                        <table style="width: 100%; border-radius: 10px; margin-top: 10px; padding: 5px 10px;">
                            <tr>
                                <td style="font-size: 15px; color: #555;">
                                    <div style="font-weight: bold; margin-bottom: 5px; padding-top: 5px;">@_.Localizer("mailCreateBookingImportant")</div>
                                    <ul style="padding: 0 0 0 10px; margin: 0 0 5px 0; font-size: 14px;">
                                        <li style="margin-bottom: 5px;">@_.Localizer("mailCreateBookingImportantListOne")</li>
                                        <li style="margin-bottom: 5px;">@_.Localizer("mailCreateBookingImportantListTwo")</li>
                                        <li style="margin-bottom: 5px;">@_.Localizer("mailCreateBookingImportantListThree") <a href="@($"{@settingOptions.Value.SiteUrl}/vuelos/pago-en-linea")">@_.Localizer("paymentOnline")</a>, @_.Localizer("mailCreateBookingImportantListFour")</li>
                                    </ul>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>



                </tbody>
</table>
</td>
    </tr>

    <tr>
        <td>
            <table style="margin-top: 10px; width: 100%; border: 1px solid #e5e5e5; background: #fff; border-radius: 5px;">
                <tr>
                    <td align="left" style="padding: 0 10px;  padding-bottom: 5px;  margin-top: 10px;">
                        <table cellpadding="0" cellspacing="0" align="left">
                            <tbody>

                            <tr>
                                <td width="320" align="left">
                                    <table cellpadding="0" cellspacing="0" style="width: 100%; color: #555;">
                                        <tbody>
                                        <tr>
                                            <td align="left">
                                                <table style="font-size: 15px; padding: 10px 0;">
                                                    <tr>
                                                        <td style="line-height: 1;">@_.Localizer("mailCreateBookingNames")</td>
                                                    </tr>
                                                    <tr>
                                                        <td colspan="3" style="padding: 3px 0 3px 0; font-weight: bold;">
                                                            @foreach (var Passenger in Passengers)
                                                            {

                                                                @($"{Passenger.Firstname} {@Passenger.Lastname}")
                                                                <br>

                                                            }
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <table cellpadding="0" cellspacing="0" align="left">
                            <tbody>
                            <tr>
                                <td width="320" align="left">
                                    <table cellpadding="0" cellspacing="0" style="font-size: 15px; width: 100%; color: #555;">
                                        <tbody>
                                        <tr>
                                            <td align="left">
                                                <table style="padding: 10px 0; font-size: 15px;">
                                                    <tr>
                                                        <td style="line-height: 1;">@_.Localizer("mailCreateBookingMail")</td>
                                                    </tr>
                                                    <tr>
                                                        <td colspan="3" style="padding: 3px 0 3px 0; font-weight: bold;">@Customer.Email</td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>

                <tr>
                    <td align="left" style="padding: 0 10px;  padding-bottom: 5px; margin-top: 10px; color: #555;">
                        <table cellpadding="0" cellspacing="0" align="left">
                            <tbody>

                            <tr>
                                <td width="320" align="left">
                                    <table cellpadding="0" cellspacing="0" style="font-size: 15px; width: 100%;">
                                        <tbody>
                                        <tr>
                                            <td align="left">
                                                <table style="font-size: 15px; padding-bottom: 10px;">
                                                    <tr>
                                                        <td style="line-height: 1;">@_.Localizer("mailCreateBookingPhoneHome")</td>
                                                    </tr>
                                                    <tr>
                                                        <td colspan="3" style="padding: 3px 0 3px 0; font-weight: bold;">@Customer.Phone</td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <table cellpadding="0" cellspacing="0" align="left">
                            <tbody>
                            <tr>
                                <td width="320" align="left">
                                    <table cellpadding="0" cellspacing="0" style="font-size: 15px; width: 100%;">
                                        <tbody>
                                        <tr>
                                            <td align="left">
                                                <table style="font-size: 15px;">
                                                    <tr>
                                                        <td style="line-height: 1;">@_.Localizer("mailCreateBookingPhone")</td>
                                                    </tr>
                                                    <tr>
                                                        <td colspan="3" style="padding: 3px 0 3px 0; font-weight: bold;">@Customer.Phone</td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
            </table>
        </td>
    </tr>

    <tr>
        <td>
            <table style="margin-top: 10px; width: 100%; background: #fff;">
                <tr>
                    <td align="left" style="padding: 0 10px;  padding-bottom: 5px;  margin-top: 10px;">
                        <table cellpadding="0" cellspacing="0" align="left">
                            <tbody>

                            <tr>
                                <td width="320" align="left">
                                    <table cellpadding="0" cellspacing="0" style="width: 100%;">
                                        <tbody>
                                        <tr>
                                            <td align="left">
                                                <table style="font-size: 15px; padding-top: 10px;">
                                                    <tr>
                                                        <td style="line-height: 1;">@_.Localizer("mailCreateBookingTotal")</td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <table cellpadding="0" cellspacing="0" align="left">
                            <tbody>
                            <tr>
                                <td width="320" align="left">
                                    <table cellpadding="0" cellspacing="0" style="font-size: 15px; width: 100%;">
                                        <tbody>
                                        <tr>
                                            <td align="right">
                                                <table style="padding: 15px 0 5px 0; font-size: 20px; color: #555;">
                                                    <tr>
                                                        <td colspan="3" style="padding: 3px 0 3px 0; font-weight: bold;">@($"{_.Currency(Rate.TotalAmount)} COP")</td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
            </table>
        </td>
    </tr>

    <tr>
        <td align="left">
            <table cellpadding="0" cellspacing="0" style="border-top: 1px solid #e5e5e5; margin-top: 15px; width: 100%;">
                <tbody>
                <tr>
                    <td width="650" valign="top">
                        <table cellpadding="0" cellspacing="0" style=" width: 100%;">
                            <tbody>
                            <tr>
                                <td align="left" style="padding: 25px 0 4px 0; color: #555;">
                                    <p style="margin: 0; padding: 2px 0; font-size: 16px;"><strong>@_.Localizer("mailCreateBookingQuestions")</strong></p>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
                </tbody>
            </table>
        </td>
    </tr>

    <tr>
        <td align="left">
            <table cellpadding="0" cellspacing="0" style="color: #333333;">
                <tbody>
                <tr>
                    <td  align="center" valign="top" style="width: 100%;">
                        <table cellpadding="0" cellspacing="0" bgcolor="#f9f9f9" style="width: 100%; border-left:1px solid #eee;border-right:1px solid #eee;border-top:1px solid #eee;border-bottom:1px solid #eee;background-color: #f9f9f9; padding: 15px 10px; font-size: 12px; line-height: 1.4;">
                            <tbody>
                            <tr>
                                <td align="center">
                                    <p style="font-size: 14px; margin: 0;">
                                        <span>Bogotá: </span>
                                        <span>
                                                        <a href="tel:6017436620" style="color:#186CDF; text-decoration: none;">************</a>
                                                    </span>

                                        <span>- Barranquilla:</span>
                                        <span style="color:#186CDF;">
                                                        <a href="tel:6053852828" style="color:#186CDF; text-decoration: none;">************</a>
                                                    </span>

                                        <span> - Armenia:</span>
                                        <span>
                                                        <a href="tel:6067359840" style="color:#186CDF; text-decoration: none;">************</a>
                                                    </span>

                                        <span>- Valledupar:</span>
                                        <span>
                                                        <a href="tel:6055894120" style="color:#186CDF; text-decoration: none;">************</a>
                                                    </span>

                                        <span> Bucaramanga:</span>
                                        <span>
                                                        <a href="tel:6076970800" style="color:#186CDF; text-decoration: none;">************</a>
                                                    </span>

                                        <span>  - Cali:</span>
                                        <span>
                                                        <a href="tel:6024850400" style="color:#186CDF; text-decoration: none;">************</a>
                                                    </span>

                                        <span>  - Pasto:</span>
                                        <span>
                                                        <a href="tel:6027365080" style="color:#186CDF; text-decoration: none;">************</a>
                                                    </span>

                                        <span>  - Santa Marta:</span>
                                        <span>
                                                        <a href="tel:6054366151" style="color:#186CDF; text-decoration: none;">************</a>
                                                    </span>

                                        <span> Cúcuta:</span>
                                        <span>
                                                        <a href="tel:6075943050" style="color:#186CDF; text-decoration: none;">************</a>
                                                    </span>

                                        <span> - Medellín:</span>
                                        <span>
                                                        <a href="tel:6046041777" style="color:#186CDF; text-decoration: none;">************</a>
                                                    </span>

                                        <span> - Pereira:</span>
                                        <span>
                                                        <a href="tel:6063400720" style="color:#186CDF; text-decoration: none;">************</a>
                                                    </span>

                                        <span>  - Cartagena:</span>
                                        <span>
                                                        <a href="tel:************" style="color:#186CDF; text-decoration: none;">************</a>                                                                
                                                    </span>

                                        <span> - Manizales: </span>
                                        <span>
                                                        <a href="tel:6068918920" style="color:#186CDF; text-decoration: none;">************</a>                                                                
                                                    </span>
                                    </p>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
                </tbody>
            </table>
        </td>
    </tr>


    <tr>
        <td>
            <table style="margin-top: 10px; width: 100%; background: #fff;">
                <tr>
                    <td align="left" style="padding: 0 10px;  padding-bottom: 5px;  margin-top: 10px;">
                        <table cellpadding="0" cellspacing="0" align="left">
                            <tbody>

                            <tr>
                                <td width="320" align="left">
                                    <table cellpadding="0" cellspacing="0" style="width: 100%;">
                                        <tbody>
                                        <tr>
                                            <td align="center">
                                                <table cellpadding="0" cellspacing="0" style="font-size: 16px; width: 100%;">
                                                    <tbody>
                                                    <tr>
                                                        <td align="center" style="padding-top: 15px; padding-bottom: 15px;">
                                                            <p style="margin: 0; padding: 2px 0">
                                                                <img src="@($"{@settingOptions.Value.SiteUrl}/assets-tb/img/tiquetesbaratos/checkout/whatsapp.jpg")" width="15"/>
                                                                @_.Localizer("mailCreateBookingAttention")
                                                            </p>
                                                            <a href="https://wa.me/573104915803" target="_blank" style="color: rgb(24, 108, 223); margin: 0px; text-decoration: none;">3104915803</a>
                                                        </td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <table cellpadding="0" cellspacing="0" align="left">
                            <tbody>
                            <tr>
                                <td width="240" align="left">
                                    <table cellpadding="0" cellspacing="0" style="font-size: 15px; width: 100%;">
                                        <tbody>
                                        <tr>
                                            <td align="center">
                                                <table cellpadding="0" cellspacing="0" style="width: 100%;">
                                                    <tbody>
                                                    <tr>
                                                        <td align="center" style="font-size: 0px;">
                                                            <a target="_blank"
                                                               href="https://www.tiquetesbaratos.com/?callus=true">
                                                                <img src="@($"{@settingOptions.Value.SiteUrl}/assets-tb/img/tiquetesbaratos/checkout/call.png")"
                                                                     alt="" style="display: block;" width="252">
                                                            </a>
                                                        </td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
            </table>
        </td>
    </tr>

    <tr>
        <td align="left" style="margin-top: 25px;">
            <table cellpadding="0" cellspacing="0" width="650" style="padding: 0px 15px;">
                <tbody>
                <tr>
                    <td align="center" valign="top">
                        <table cellpadding="0" cellspacing="0" width="650">
                            <tbody>
                            <tr>
                                <td align="left">
                                    <p style="font-size: 12px; color: rgb(147, 147, 147); margin: 0px;">
                                        @_.Localizer("mailCreateBookingImportantMgs")</p>
                                    <p style="font-size: 12px; color: rgb(147, 147, 147); margin: 0px;">
                                        <br></p>
                                    <p style="font-size: 12px; color: rgb(147, 147, 147); margin: 0px;">
                                        <strong>@_.Localizer("mailCreateBookingPolicyTitle")</strong> @_.Localizer("mailCreateBookingPolicyDetail")<br></p>
                                    <p style="font-size: 12px; color: rgb(147, 147, 147); margin: 0px;">
                                        <br></p>
                                    <p style="font-size: 12px; color: rgb(147, 147, 147); margin: 0px;">
                                        <strong>@_.Localizer("mailCreateBookingAditionalTitle")</strong> @_.Localizer("mailCreateBookingAditionalDetail")<br></p>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
                </tbody>
            </table>
        </td>
    </tr>
    </tbody>
</table>
@section Preload {
    <link rel="preconnect" href="@settingOptions.Value.SiteUrl">
    <link rel="preconnect" href="https://img.cdnpth.com">
}


</body>
</html>