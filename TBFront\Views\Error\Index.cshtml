﻿@using TBFront.Helpers
@using Microsoft.Extensions.Options
@using TBFront.Models.Request
@using TBFront.Options;

@inject IOptions<SettingsOptions> settingOptions

@inject ViewHelper viewHelper
@inject StaticHelper staticHelper

@{
    var resolution = viewHelper.GetImageResolution();
    ViewData["Resolution"] = resolution;
    Layout = "~/Views/Shared/_LayoutError.cshtml";
    var request = ViewData["request"] as FlightRequest;
    var culture = ViewData["CultureData"] as Culture;
    string code = Context.Response.StatusCode.ToString();
    string url = settingOptions.Value.SiteUrl;
    string ErrorMgsTitle = (code == "404") ? viewHelper.Localizer("error_not_found") : viewHelper.Localizer("error-interno");
    string ErrorMgsText = (code == "404") ? viewHelper.Localizer("descripcion_error_not_found") : viewHelper.Localizer("descripcion-error-interno");
}


@await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.Code}/_Header.cshtml", new ViewDataDictionary(ViewData) { { "navs", true }, { "login", false } })

@switch (code)
{ 
    case "404":
        <!-- start code for error 404 -->
        <div class="container my-4 py-2">
            <div class="row c-404">
                <div class="col-12 col-md-8">
                    <p class="mb-0 color-gray mb-2">Error @(code)</p>
                    <h1>@(ErrorMgsTitle)</h1>
                    <img class="d-block d-sm-none w-100 mb-4" src="@(settingOptions.Value.CloudCdn)/assets-tb/img/tiquetesbaratos/img-404.png"/>
                    <p>@(ErrorMgsText)</p>

                    <a href="@(url)/" class="a-link-1">@(viewHelper.Localizer("regresar-a-la-pagina-principal"))</a>
                    <hr class="my-4"/>
                    <p class="font-bold">@(viewHelper.Localizer("vive-tu-proxima-experiencia-de-viaje-con-nosotros")):</p>
                    <ul class="list-unstyled">
                        <li class="mb-4"><a href="@(url)/" class="a-link-1">@viewHelper.Localizer("busqueda-de-vuelos")</a></li>
                        <li class="mb-4"><a href="@(url)/hoteles" class="a-link-1">@viewHelper.Localizer("busqueda-de-hoteles")</a></li>
                        <li class="mb-4"><a href="@(url)/paquetes" class="a-link-1">@viewHelper.Localizer("ver-paquetes")</a></li>
                        <li class="mb-4"><a href="@(url)/vuelos/promociones-tiquetes-aereos-origen" class="a-link-1">@viewHelper.Localizer("ver-promociones")</a></li>
                    </ul>
                </div>
                <div class="col-12 col-md-4 d-flex align-items-center">
                    <img class="d-none d-md-block d-lg-block w-100" src="@(settingOptions.Value.CloudCdn)/assets-tb/img/tiquetesbaratos/img-404.png"/>
                </div>
            </div>
        </div>
        <!-- end code for error 404 --> 
        break;
    case "500":
        <!-- start code for error 500 -->
        <div class="container my-4 py-2">
            <div class="row c-404">
                <div class="col-12 col-md-8">
                    <p class="mb-0 color-gray mb-2">Error @(code)</p>
                    <h1>@(ErrorMgsTitle)</h1>
                    <img class="d-block d-sm-none w-100 mb-4" src="@(settingOptions.Value.CloudCdn)/assets-tb/img/tiquetesbaratos/img-500.png"/>
                    <p>@(ErrorMgsText)</p>

                    <a href="https://www.tiquetesbaratos.com/" class="a-link-1">@(viewHelper.Localizer("regresar-a-la-pagina-principal"))</a>                
                    <hr class="my-4"/>
                    <p class="font-bold">@(viewHelper.Localizer("puedes-intentar-lo-siguiente")):</p>
                    <ul class="ps-3 ts-center">
                        <li class="mb-2">@(viewHelper.Localizer("reiniciar-la-pagina"))</li>
                        <li class="mb-4">@(viewHelper.Localizer("acceder-nuevamente-mas-tarde"))</li>
                    </ul>                
                </div>
                <div class="col-12 col-md-4 d-flex align-items-center">
                    <img class="d-none d-md-block d-lg-block w-100" src="@(settingOptions.Value.CloudCdn)/assets-tb/img/tiquetesbaratos/img-500.png"/>
                </div>               
            </div>
        </div>
        <!-- end code for error 500 -->
        break;
        
    default:
        <div style="margin-top: 50px;">
            <table border="0" width="100%" align="center" cellpadding="4" cellspacing="0">
                <tbody>
                <tr>
                    <td>
                        <a class="d-center  d-flex justify-content-center" href="@(url)">
                            <img src="@(settingOptions.Value.CloudCdn)@viewHelper.Localizer("img")" alt="">
                        </a>
                    </td>
                </tr>
                <tr>

                    <td valign="middle">
                        <h1 class="d-center mb-5 d-flex justify-content-center"> @(code) - @ErrorMgsTitle</h1>
                        <p class="bodytext d-center mb-5"></p>
                        <div class=" d-flex justify-content-center flex-column">
                            <h2 class="d-center text-center d-flex justify-content-center">@viewHelper.Localizer("Secciones")</h2>

                            <ul class="d-flex d-flex flex-column bd-highlight mb-3 justify-content-center p-0 d-flex justify-content-center">
                                <li class="d-center  d-flex justify-content-center"><a href="@(url)/hoteles" title="Ir a sección de hoteles">@viewHelper.Localizer("Hoteles")</a></li>
                                <li class="d-center  d-flex justify-content-center"><a href="@(url)/paquetes" title="Ir a sección de paquetes">@viewHelper.Localizer("Paquetes")</a></li>
                                <li class="d-center  d-flex justify-content-center"><a href="@(url)/vuelos" title="Ir a sección de vuelos">@viewHelper.Localizer("Vuelos")</a></li>
                                <li class="d-center  d-flex justify-content-center"><a href="@(url)/vuelos/promociones-tiquetes-aereos-origen" title="Ir a sección de ofertas">@viewHelper.Localizer("Ofertas")</a></li>
                            </ul>

                        </div>

                        <p class="d-center  d-flex justify-content-center">@viewHelper.Localizer("Ir atras")&nbsp;<a href="@(url)" title="Página principal">@viewHelper.Localizer("Página principal")</a></p>
    </td>
    </tr>
    </tbody>
    </table>
    </div> 
        break;
        
}

<div style="display: none;">
    @ViewData["ErrorMgs"]
</div>
<div>
    @await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.Code}/_Footer.cshtml", new ViewDataDictionary(ViewData) { { "newsletter", true } })
</div>




@section Meta {
    <title> @(code) -  @(viewHelper.Localizer("author")) </title>
    <meta name="robots" content="noindex,nofollow" />
    <meta name="googlebot" content="noindex,nofollow" />
}


@section Css {
    <link type="text/css" rel="stylesheet"
      href="@staticHelper.GetVersion($"/{settingOptions.Value.SiteName}/css/list.css", settingOptions.Value.Assets)">
}


@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@19.1.3/dist/lazyload.min.js"></script>
    <script>
        var lazyLoadInstance = new LazyLoad({
            use_native: true // <-- there you go
        });
    </script>
}

