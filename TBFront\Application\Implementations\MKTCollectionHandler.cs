﻿using TBFront.Interfaces;
using TBFront.Models.MKTCollection.Request;
using TBFront.Models.MKTCollection.Response;

namespace TBFront.Application.Implementations
{
    public class MKTCollectionHandler : IMKTCollectionHandler
    {
        private readonly IAPIB2CService _apiService;
        public MKTCollectionHandler(IAPIB2CService apiService)
        {
            _apiService = apiService;
        }

        public async Task<MKTCollectionResponse> QueryAsync(MTKCollectionRequest request, CancellationToken ct)
        {

            var response = await _apiService.QueryAsync(request, ct);

            return response;
        }
    }
}
