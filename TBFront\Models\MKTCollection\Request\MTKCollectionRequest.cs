﻿using System.Text.Json.Serialization;

namespace TBFront.Models.MKTCollection.Request
{
    public class MTKCollectionRequest
    {
        [JsonPropertyName("profileId")]
        public string ProfileId { get; set; } = "1";

        [JsonPropertyName("isProd")]
        public bool IsProduction { get; set; } = true;

        [JsonPropertyName("isProd")]
        public bool Cache { get; set; } = true;


        public string Page { get; set; } = "www.tiquetesbaratos.com";

        public string CultureSite { get; set; }

        public MTKCollectionRequest(bool cache, string? page, string? cultureSite)
        {
            Page ??= page;
            Cache = cache;
            CultureSite ??= cultureSite;
        }
    }
}
