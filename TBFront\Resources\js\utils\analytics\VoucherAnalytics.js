import moment from 'moment';
import { normalizeText } from '../utils';

class VoucherAnalytics {

    constructor() {
        this.data = window.__pt.data || {};
        this.site = window.__pt.settings.site || {};
    }


    transactionEvent(args) {
        let reservation = this.getReservation(args);
        
        let event = {
            PaymentType: "CreditCard",
            kenshooRevenue: reservation.totalAmount,
            transactionAffiliation: "1",
            transactionId: reservation.bookingId,
            transactionTax: 0,
            transactionTotal: reservation.totalAmount,
            kenshooRevenue: reservation.totalAmount,
            transactionProducts: []
        };


        let flight = {
            category: "flight",
            name: `Vuelo ${reservation.startingFrom} - ${reservation.returningFrom} ${reservation.tripTypeText}`,
            price: reservation.totalAmount,
            quantity: 1,
            sku: `${reservation.bookingId}-1`
        };

        event.transactionProducts.push(flight);

        this.setDataLayer(event)
    }

    flightTransaction(args) {
        let reservation = this.getReservation(args);

        let event = {
            eventAction: `Domestic | ${reservation.tripType}`,
            eventCategory: "Flights Transaction",
            eventLabel: `${reservation.startingFrom} | ${reservation.returningFrom}`,
            eventValue: Math.trunc(reservation.totalAmount),
            event: "gtmEvent",
            eventExtra:'',
        };

        this.setDataLayer(event)
    }

    remarketing(args) {
        let reservation = this.getReservation(args);
        let event = {
            OriginAirportName: reservation.startingFromName,
            DestinationAirportName: reservation.returningFromName,
            OriginAirport: reservation.startingFrom,
            DestinationAirport: reservation.returningFrom,
            TripType: reservation.tripType,
            TotalChilds: reservation.children,
            TotalAdults: reservation.adults,
            Airline: reservation.startingOperatingCode,
            FlightName: `${reservation.startingFrom} to ${reservation.returningFrom}`,
            ProductId: `${reservation.startingFrom}-${reservation.returningFrom}-${reservation.week}`,
            CheckIn: reservation.checkinFormat,
            CheckOut: reservation.checkoutFormat,
            PayForm: '',
            PayPlan: '',
            TotalAmount: reservation.totalAmount,
            ReservationId: `${reservation.bookingId}`,
            Week: `${reservation.week}`,
            IsWeekend: reservation.isWeekend,
            TripDays: reservation.days,
            Currency: reservation.currency,
            TotAmount: reservation.totalAmount,
            ConversionAmount: reservation.totalAmount,
            ConversionCurrency: reservation.currency,
            EventName: 'GoogleRemarketingTransactionFlight',
            event: 'checkoutStep3-flight'
        };

        this.setDataLayer(event)
    }

    setEcommerce(args){
        let reservation = this.getReservation(args);
        let originName = reservation.startingFromName;
        let destinationName = reservation.returningFromName;
        let originNameNormalize = originName;
        let destinationNameNormalize = destinationName;
        let originIATA = reservation.startingFrom;
        let destinationIATA = reservation.returningFrom;
        let originAirlineCode = reservation.startingOperatingCode;
        let destinationAirlineCode = reservation.returningOperatingCode;
        const arrivalDate = this.getHoursFromDate(args.reservation.flightInformation[0]);
        let items = [
            {
                item_name: `${originNameNormalize}-${destinationNameNormalize}-${originAirlineCode}`,
                item_id: `${originIATA}-${destinationIATA}-${originAirlineCode}`,
                price: reservation.totalAmount,
                item_brand: originAirlineCode,
                item_category: reservation.isRoundtrip ? "roundtrip" : "oneway",
                item_category2: reservation.isDomestic ? "national" : "international",
                item_variant: "departing",
                index: 1,
                quantity: reservation.adults + reservation.children + reservation.infants,
                item_flight_number: args.reservation.flightInformation[0].flightNumber,
                item_time: `${arrivalDate[0]}-${arrivalDate[1]}`,
                item_branded_fare: args.reservation.flightInformation[0].familyFareName
            }
        ];

        if(reservation.isRoundtrip){
            const returnDate = this.getHoursFromDate(args.reservation.flightInformation[1]);
            items.push({
                item_name: `${destinationNameNormalize}-${originNameNormalize}-${destinationAirlineCode}`,
                item_id: `${destinationIATA}-${originIATA}-${destinationAirlineCode}`,
                price: reservation.totalAmount,
                item_brand: destinationAirlineCode,
                item_category: reservation.isRoundtrip ? "roundtrip" : "oneway",
                item_category2: reservation.isDomestic ? "national" : "international",
                item_variant: "returning",
                index: 1,
                quantity: reservation.adults + reservation.children + reservation.infants,
                item_flight_number: args.reservation.flightInformation[1].flightNumber,
                item_time: `${returnDate[0]}-${returnDate[1]}`,
                item_branded_fare: args.reservation.flightInformation[1].familyFareName
            });
        }

        var event ={
            event: "purchase",
            ecommerce: {
                items,
                item_list_name: "flights_home",
                layer: "flights",
                total_flights: 1,
                value: reservation.totalAmount,
                locator: reservation.bookingId,
                transaction_id: reservation.bookingId,
                field_origin_iata: originIATA,
                field_origin_name: originName,
                field_destination_iata: destinationIATA,
                field_destination_name: destinationName,
                field_date1: reservation.checkinFormat,
                field_date2: reservation.checkoutFormat,
                travelers_adults: String(reservation.adults),
                travelers_children: String(reservation.children),
                travelers_infants: String(reservation.infants),
                travelers_infants_onlap: "0",
                travelers_infants_inseat: "0"
            }
        }
        var event2 = {
            event: 'ga4.trackEvent',
            eventName: "purchase",
            eventParams: event.ecommerce
        };

        this.setDataLayer({ecommerce: null})
        this.setDataLayer(event2)
    }

    setVoucherAnalytics(args) {
        this.applyEvents(args);
        this.transactionEvent(args);
        //this.flightTransaction(args);
        this.remarketing(args);
        this.setEcommerce(args);
    }


    getBaseDefault(args = {}) {
        let info = args.info || {};
        return {
            reservationId: info.id,
            email: info.email,
        }
    }

    getReservation(args = {}) {
        let reservation = args.reservation || {};
        let isRoundtrip = reservation.isRoundTrip;
        let isDomestic = reservation.isDomestic;
        let flightInformation = reservation.flightInformation || {};
        let segmentDeparture = flightInformation[0];
        let segmentArrival = flightInformation[1] || {};

        let checkinMoment = moment(segmentDeparture.startDate);
        let checkoutMoment = moment(isRoundtrip ? segmentArrival.startDate : segmentDeparture.endDate);
        let totalAmount = 0;
        reservation.payments.forEach(payment => {
            totalAmount = totalAmount + payment.paymentAmount
        });
        return {
            bookingId: reservation.bookingId,
            channelId: reservation.channelId,
            createdDate: reservation.createdDate,
            currency: reservation.currency,
            customerEmail: reservation.customerEmail,
            customerFirstName: reservation.customerFirstName,
            customerLastName: reservation.customerLastName,
            minServiceDate: reservation.minServiceDate,
            organizationId: reservation.organizationId,
            adults: segmentDeparture.adults,
            children: segmentDeparture.kids,
            infants: segmentDeparture.infants,
            checkout: segmentDeparture.endDate,
            checkin: segmentDeparture.startDate,
            isRoundtrip: isRoundtrip,
            isDomestic: isDomestic,
            tripType: isRoundtrip ? 'roundtrip' : 'oneway',
            tripTypeText: isRoundtrip ? 'roundtrip' : 'oneway',

            startingFrom: segmentDeparture.originAirportCode,
            returningFrom: isRoundtrip ? segmentArrival.originAirportCode : segmentDeparture.destinationAirportCode,

            startingFromName: segmentDeparture.originAirport,
            returningFromName: isRoundtrip ? segmentArrival.originAirport : segmentDeparture.destinationAirport,

            startingOperatingCode: segmentDeparture.airlineCode,
            startingOperatingName: segmentDeparture.serviceCarrierName,

            returningOperatingCode: isRoundtrip ? segmentArrival.airlineCode : segmentDeparture.airlineCode,
            returningOperatingName: isRoundtrip ? segmentArrival.serviceCarrierName: segmentDeparture.serviceCarrierName,

            isWeekend: (checkinMoment.day() === 6) || (checkinMoment.day() === 0),
            week: checkinMoment.week(),
            checkinFormat: checkinMoment.format('YYYY-MM-DD'),
            checkoutFormat: checkoutMoment.format('YYYY-MM-DD'),
            days: checkoutMoment.diff(checkinMoment, 'days'),
            weekDay: checkinMoment.day(),
            totalAmount: totalAmount,
            channelId: reservation.channelId,
            segmentDeparture,
            segmentArrival

        }
    }

    applyEvents(args) {
        let events = args.reservation.events || [];
        let eventsLength = events.length;

        for (var i = 0; i < eventsLength; i++) {
            const event = { ...events[i] } //Para elimininar props que le a�ade vuejs 
            this.setDataLayer(event)
        }

    }

    onError(error, data, step) {
        const error_summary = {
            message: error.message,
            email: error.info.email,
            id: error.info.id,
        }

        const data_summary = {
            em: data.em,
            id: data.id
        }
        const payload = JSON.stringify(error_summary || { results: "none" });
        const quote_params = JSON.stringify(data_summary || { params: "none" });

        const event = {
            event: 'gtmEvent',
            eventName: "gtmEvent",
            eventExtra: "",
            eventCategory: "Flights Error",
            eventAction: quote_params,
            eventLabel: `step:${step} - ${payload}`
        }


        this.setDataLayer(event);
    }


    setDataLayer(args) {
        if (window.dataLayer) {
            dataLayer.push(args)
        }
    }

    getHoursFromDate(date) {
        let dates = [];
        let hours = [];
        dates.push(new Date(date.startDate));
        dates.push(new Date(date.endDate));
        
        for (let index in dates) {
            let  horas = dates[index].getHours();
            let  minutos = dates[index].getMinutes();
            horas = horas < 10 ? '0' + horas : horas;
            minutos = minutos < 10 ? '0' + minutos : minutos;
            hours.push(`${horas}:${minutos}`);
        }

        return hours;
    }

}

export const VoucherAnalytic = new VoucherAnalytics();