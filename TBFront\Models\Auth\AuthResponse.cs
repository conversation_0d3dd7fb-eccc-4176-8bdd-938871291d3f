﻿namespace TBFront.Models.Auth
{
    public class AuthResponse
    {
        public AuthTokenBodyResponse AuthenticationResult { get; set; }
        public string Message { get; set; } = string.Empty;
        public AuthResponse()
        {
            AuthenticationResult = new AuthTokenBodyResponse();
        }
    }

    public class AuthTokenBodyResponse
    {
        public string Token { get; set; } = string.Empty;
        public string AccessToken { get; set; } = string.Empty;
        public string RefreshToken { get; set; } = string.Empty;
        public int ExpireIn { get; set; }
        public string TokenType { get; set; } = string.Empty;

    }
}
