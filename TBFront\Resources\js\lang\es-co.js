﻿window.i18n = {
	"messages": {
		"domain": "www.TiquetesBaratos.com",
		"search_with_all_airlines": "Buscar con todas las aerolíneas",
		"direct": "Directo",
		"scale": "Parada",
		"scales": "Paradas",
		"rate": "Tarifa",
		"arrival": "<PERSON>lega",
		"departure": "Sale",
		"departure_flight": "Ida",
		"return_flight": "Regreso",
		"total": "Total",
		"flight": "Vuelo",
		"adult": "Adulto",
		"child": "Niño",
		"infant": "Infante",
		"upgrade_your_flight": "Mejora tu vuelo",
		"view_flight_details": "Ver detalle del vuelo",
		"we_found": "Encontramos",
		"fares": "tarifas",
		"available": "disponibles",
		"stops_0": "Directo",
		"stops_1": "parada",
		"stops_2": "paradas",
		"message_list_title": "Tarifas por viajero con impuestos y sobrecargos en pesos colombianos (COP$).",
		"near_dates": "Fechas cercanas",
		"cheapest_flights": "Vuelos más económicos",
		"more_flights": "Más Económicos",
		"more": "Más",
		"dates": "Fechas",
		"select": "Seleccionar",
		"rate_type": "Tipo de tarifa",
		"connection_in": "Conexión en",
		"with_waiting": "con espera de",
		"flight_detail": "Detalle de vuelo",
		"what_includes": "¿Qué incluye?",
		"RATE_NOT_FOUND": "Ha ocurrido un error con la cotizacion actual, por favor comunicate al telefono <a href='tel:6017436620'>************ </a> para el seguimiento",
		"ROOM_NOT_FOUND": "Ha ocurrido un error con la cotizacion actual, por favor comunicate al telefono <a href='tel:6017436620'>************ </a> para el seguimiento",
		"HOTEL_NOT_FOUND": "No se ha encontrado el hotel",
		"VOUCHER_NOT_FOUND": "Datos no encontrados",
		"ERROR_DEFAULT": "Se ha producido un error",
		"error_title": "Ha ocurrido un error",
		"error_description": "Nuestras disculpas. No pudimos obtener la información solicitada.",
		"error_list_one": "Los servidores eliminan la información de búsqueda después de un tiempo de inactividad. Esto protege tu privacidad y ayuda a que el sitio funcione mejor.",
		"error_list_two_bold": "Enlace desde favoritos",
		"error_list_two": "Las páginas que agregas a tus favoritos(bookmarks) de tu navegador, están construidas con información que se basa en tus selecciones previas (como tarifas para una fecha específica). Estas páginas no se pueden usar para obtener información actualizada.",
		"go_home": "Regresar al inicio",
		"detail_fly": "Detalles del vuelo",
		"to_destination": "a",
		"duration_fly": "Duracion total del vuelo",
		"fly": "Vuelo",
		"operate_by": "Operado por ",
		"departure_of": "Sale de ",
		"arrival_to": "Llega a",
		"time_flight": "Tiempo entre vuelos",
		"huso_horario_local": "Horarios en hora local de cada ciudad.",
		"fare": "Tarifa: ",
		"unscaled": "Sin paradas",
		"aircraft": "Aeronave",
		"selected_flight": "Vuelo seleccionado",
		"flight_leg_title_starting": "Vuelo de ida",
		"flight_leg_title_returning": "Vuelo de regreso",
		"selected_fare": "Tarifa seleccionada",
		"includes_all_taxes": "Incluye todos los impuestos",
		"continue_button_text": "Continuar",
		"select_button_text": "Seleccionar",
		"success_voucher": "Genial, ¡Prepárate para viajar!",
		"processing_booking": "Estamos procesando tu reservación",
		"reservacion_id": "Localizador de tu reservación",
		"we_send_email": "Enviamos los detalles al correo",
		"description_voucher": "Asegurate de recibir nuestros mensajes",
		"description_voucher_detail": "Si después de unos minutos no ves nuestro mensaje en tu correo, revisa la carpeta de spam, si está ahí, márcalo como correo seguro para que recibar sin problema los correos de Tiquetes Baratos",
		"go_home": "Regresar al inicio",
		"thankyou_page": "Confirmación de compra",
		"please_wait_voucher": "Estamos procesando tu reservación, por favor espere un momento.",
		"voucher_departure_date": "Fecha de salida: ",
		"voucher_hrs": "hrs",
		"voucher_passangers": "Pasajeros: ",
		"voucher_info_mail": "Te enviamos un correo con la información de tu reservación al siguiente correo: ",
		"voucher_info_mail_phone": "En caso de no recibir el correro electrónico de tu reservación por favor comunicate al telefóno: ",
		"voucher_total": "Total pagado: ",
		"voucher_method_payment": "Metodo de pago ",
		"voucher_airline": "Aerolínea: ",
		"voucher_header_one": "¡Hola  ",
		"voucher_header_two": "Gracias por reservar con ",
		"voucher_header_three": "TiquetesBaratos.com ",
		"voucher_header_four": "Tu reservación con número ",
		"voucher_header_five": " se encuentra confirmada y su pago está en proceso de validación.",
		"voucher_adults": " adultos",
		"voucher_kids": " niños",
		"voucher_infants": " infantes",
		"voucher_adult": " adulto",
		"voucher_kid": " niño",
		"voucher_infant": " infante",
		"flight_operated_by": "Vuelo operado por",
		"to_document": "para documentar deberás acudir a su zona",
		"children": "Niños",
		"chage_price": "Se ha detectado un cambio de tarifa",
		"chage_price_package": "El precio de tu vuelo ha",
		"chage_price_down": "bajado ",
		"chage_price_up": "subido ",
		"close": "Cerrar",
		"return_label": "Regresar",
		"error_reserv": "Lo sentimos. La opción seleccionada ya no está disponible.",
		"single_fare": "tarifa",
		"only_available": "disponible",
		"by_passenger": "Por persona",
		"from": "Desde",
		"terminal": "Terminal",
		"flight_not_available": "No se ha encontrado ningún vuelo disponible, por favor seleccione otro vuelo. En caso de requerir ayuda por favor comuníquese con uno de nuestros asesores.",
		"promotions_not_avaible": "No se han encontrado promociones por el momento para el origen seleccionado, seleccione un destino diferente. Porfavor",
		"spent_some_time": "Pasó un tiempo desde tu búsqueda",
		"update_search": "Actualizar búsqueda",
		"wait_on_board": "Espera a bordo en {0} de",
		"hour": "hora",
		"hours": "horas",
		"minute": "minuto",
		"minutes": "minutos",
		"date_validation_roundtrip": "<p class='body'>No es posible seleccionar esa combinación de vuelos. La llegada del vuelo de ida es posterior al vuelo de regreso.</p>",
		"by_adult": "Por adulto",
		"checkout_family_change_your_fare": "Tu tarifa ",
		"checkout_family_change_no_available": "no estaba disponible, y se actualizó a tarifa ",
		"checkout_family_change_no_available_route": "para el viaje de ",
		"enter_your_data": "Ingresa tus datos",
		"change_your_reservation": "Cambios en tu reserva",
		"before": "Antes",
		"today": "Ahora",
		"price_your_flight_has_down": "El precio de tu vuelo ha bajado",
		"finalize_your_reservation": "¡Finaliza la reservación y asegura tu lugar en el vuelo!",
		"ok": "Entendido",
		"the_rate": "La tarifa",
		"is_not_available_html": "<strong class='text-danger'>no está disponible</strong> para el vuelo de ida, hemos actualizado a la tarifa",
		"choose_another_flight": "Elige otro vuelo",
		"cabin": "Cabina",
		"several": "Varias",
		"options": "opciones",
		"flight_recommended": "Detalles de tu vuelo de ida",
		"show": "Mostrar",
		"hide": "Ocultar",
		"choose_another_flight": "Elige otro vuelo",
		"seeLuggage": "Ver equipaje"
	},
	"checkout": {
		"title": "¡Asegure su reserva ahora!",
		"subtitle": "Información de los viajeros",
		"security_title": "Su información personal y su transacción están seguras",
		"security_alert": "No GUARDA INFORMACIÓN ",
		"security_text": "sobre las tarjetas de crédito de los usuarios que realicen las transacciones en nuestro portal. Nuestra plataforma de pago en línea está avalada por VeriSign",
		"your_reservation": "Tu reservación",
		"tax": "Impuestos y tasas",
		"rate_by_adult": "Tarifa por adulto ",
		"phone": "Teléfono o Celular (10 dígitos)",
		"contact_information": "Información del contacto",
		"email": "Correo electrónico",
		"checkout_legals": "Al hacer click en el botón Reservar y pagar, usted está aceptando los ",
		"checkout_legals_terms": "Términos y Condiciones de la compra y la Política de Privacidad.",
		"booknow": "Reservar y pagar",
		"first_name": "Primer nombre",
		"last_name": "Primer apellido",
		"identification_code": "Cédula de ciudadanía",
		"identification_code_minor": "Documento de identidad",
		"frequent_flyer_number_text": "Agregar Numero de Viajero Frecuente",
		"frequent_flyer_number": "Número de viajero frecuente",
		"date_of_birth": "Fecha de Nacimiento",
		"day": "Día",
		"month": "Mes",
		"year": "Año",
		"interest_text": "Me interesa un hotel en {0}",
		"interest_text_body": "Reserve su vuelo y obtenga precios exclusivos para nuestros clientes",
		"off": "Hasta -{0}%",
		"modal_ready": "Tu vuelo está casi listo",
		"modal_detail": "Detalles del vuelo:",
		"modal_family": "Familia"
	},
	"breakdown": {
		"TaxExcluded": "Impuestos no incluidos",
		"TaxExcluded_CO": "IVA residentes locales*",
		"TaxIncluded": "Impuestos incluidos",
		"HotelFees": "Tarifas de hotel",
		"ResortFees": "Tarifas del complejo",
		"HotelMandatoryTax": "Impuestos oblitatorios del hotel",
		"HospitalityTax": "Impuesto de hospitalidad",
		"charges_not_included": "Cargos no incluidos",
		"adults_txt": "Adulto(s)",
		"children_txt": "Niño(s)",
		"infant_txt": "Infante(s)",
		"service_charge_txt": "Cargo por servicio",
		"taxes_and_fees": "Impuestos",
		"all_charges_txt": "Impuestos, tasas y cargos",
	},
	"errors": {
		"default": "El campo no es válido",
		"alpha": "El campo solo puede contener letras",
		"alpha_spaces": "El campo solo puede contener letras",
		"confirmed": "El campo no concuerda",
		"email": "Deber ser un campo email",
		"required": "El campo es requerido",
		"integer": "El campo solo puede ser digitos",
		"distinctTo": "El {0} es igual al {1}",
		"ageKids": "Verifica las edades de los menores",
		"is_not": "El campo no es válido",
		"date_invalid": "La fecha es inválida",
		"name is a required field": "Por favor ingrese un nombre",
		"email is a required field": "Por favor ingrese un correo electrónico",
		"phone number is not valid": "Por favor ingrese un número de teléfono válido",
		"message is a required field": "Por favor agregue un mensaje",
		"email must be a valid email": "Por favor ingrese un correo electrónico válido",
		"origin is a required field": "Por favor ingrese el origen de su viaje",
		"destination is a required field": "Por favor ingrese el destino de su viaje",
		"adults is a required field": "Por favor ingrese el número de adultos para su viaje",
		"groupType is a required field": "Por favor seleccione el tipo de grupo",
		"phone is a required field": "Por favor ingrese un número de teléfono",
		"adults must be greater than or equal to 1": "Debe viajar por lo menos un adulto",
		"kids must be greater than or equal to 0": "La cantidad de niños debe ser igual o mayor a 0",
		"must be a number": "Debe ingresar un número",
		"otherGroup is a required field": "Por favor ingrese el tipo de grupo",
		"recaptcha_error": "Por favor verifique el recaptcha",
		"error_dates": "Verifique las fechas de su viaje",
		"distinct": "Los campos deben ser diferentes"
	},
	"booker": {
		"adult": "Adulto",
		"adults": "Adultos",
		"and": "y",
		"child": "Niño",
		"children": "Niños",
		"age": "Edad",
		"roundtrip": "Ida y regreso",
		"edit_search": "Modificar búsqueda",
		"origin": "Origen",
		"destination": "Destino",
		"departure_date": "Ida",
		"return_date": "Regreso",
		"passengers": "Pasajeros",
		"choose_your_passengers": "Elige tus pasajeros",
		"from_up_to_years": "De {0} hasta {1} años",
		"from_years": "Desde {0} años",
		"ok": "Aceptar",
		"search": "Buscar",
		"year_old": "0 a 24 meses (En brazos)",
		"years_old": "{0} años",
		"child_age": "Edad del menor",
		"choose_your_origin": "Elige tu aeropuerto de origen",
		"choose_your_destination": "Elige tu destino",
		"availableDates": "Fechas",
		"add_room": "Agregar habitación",
		"room": "Habitación",
		"remove": "Quitar",
		"rooms": "Habitaciones",
		"dateAvailable": "Fecha",
		"oneway": "Solo Ida",
		"ageLabel": "¿Cuál es su edad?",
		"oneWayFligth": "Vuelo solo de ida",
		"roundTripFligth": "Vuelo de ida y regreso",
		"day": "día",
		"days": "días",
		"mostOrigins": "Orígenes más buscados",
		"mostDestinations": "Destinos más buscados",
		"originDefaultName": "Bogotá - Colombia (BOG)",
		"originSimpleName": "Bogotá",
		"errorPaxes": "Lo sentimos no soportamos más de 9 pasajeros",
		"originDisplayHtml": "<em>Bogot</em>á",
		"recentResearchs": "Búsquedas recientes"
	},
	"newsletter": {
		"title": "¡No te pierdas ninguna oferta!",
		"subtitle": "Recibirás información para que vivas la mejor experiencia",
		"email": "Correo electrónico",
		"suscribe": "Suscribirse ahora",
		"validatorMessage": "Ingresa un correo electrónico valido",
	},
	"checkReservation": {
		"title": "Consulte el itinerario de su reserva",
		"tab1": "Vuelos",
		"tab2": "Hoteles y Paquetes",
		"title2": "Por favor ingrese su código de reserva.",
		"codeReservation": "Código de reservación o localizador",
		"btnReservation": "Siguiente",
		"codeReservationPackages": "Por favor ingrese su código de localizador (código de 9 caracteres de su reserva, por ejemplo 123455687) y su email para obtener la información detallada de su viaje.",
		"title3": "Localizador",
		"title4": "Email (el que se usó para reservar)",
		"btnReservation2": "Ver Itinerario",
	},
	"recentResearch": {
		"title": "Continúa con tus búsquedas recientes",
		"subtitle": "Encuentra lo que estabas buscando",
	},
	"NearByDates": {
		"EconomicFlights": "Vuelos más económicos",
	},
	"payOnline": {
		"title": "Pago en línea",
		"subtitle": "Ingrese su localizador y pague en línea. (Recibimos todas las tarjetas Débito y Crédito).",
		"code": "Localizador",
		"codePlaceHolder": "Ej. 129668555",
		"email": "Email (el que usó para reservar)",
		"emailPlaceholder": "Ej. <EMAIL>",
		"consult": "Consultar",
		"title_token": "Los datos de su reserva son los siguientes",
		"description": "Sr(a) {0}, en caso de dudas puede contactarnos a nuestra línea de atención:",
		"tel": "<a href='tel:6017436620'>************ </a>",
		"owner_name": "A nombre de",
		"owner_email": "Correo electrónico",
		"total_amount": "Por un valor de",
		"go_back": "Regresar al inicio",
		"create_link": "Proceder a pagar",
		"error": "Ha ocurrido un error al consultar los datos de la reserva",
		"cancelled": "La reservación ha sido cancelada",
		"not_found": "No hemos encontrado la reservación.",
		"success": "Tus datos de reservación",
		"description_cancelled": "Puede intentar creando un nueva reservacion desde nuestro portal, en caso de dudas puede contactarnos a nuestra línea de atención",
		"description_not_found": "Por favor, verifica que los datos ingresados sean correctos. Si necesitas ayuda, no dudes en contactarnos a nuestra línea de atención:",
		"paid_amount": "Valor saldado",
		"balance_amount": "Pendiente por saldar",
		"try_again": "Intenta de nuevo",
		"without_results": "Sin resultados"
	},
	"urls": {
		"url_terms": "https://www.tiquetesbaratos.com/terminos-condiciones",
		"url_best_price": "https://www.pricetravel.com/ayuda/garantia-precios",
		"url_go_home": "https://www.tiquetesbaratos.com/",
		"url_go_itinerary": "https://www.tiquetesbaratos.com/ayuda/tiquetes-baratos/reservacion",
		"url_detail": "https://www.tiquetesbaratos.com/hotel/",
		"url_list": "https://www.tiquetesbaratos.com/hoteles/",
	},
	"filters": {
		"nonStopsRates": "Directo",
		"oneStopRates": "Con paradas",
		"searching": "Buscando",
		"airlines": "Aerolíneas",
		"stops": "Paradas",
		"filters": "Filtros",
		"apply": "Aplicar",
		"allFlights": "Todos los vuelos",
		"allAirlines": "Todas las aerolíneas",
		"validating": "Validando...",
		"any_flights": "¡Ups! No encontramos vuelos",
		"all_filters": "Todos los filtros",
		"cheapest": "Económicos"
	},
	"flightList": {
		"lastChair": "",
		"fairesIncluded": "Incluye todos los Impuestos",
		"moreFlightsFrom": "Más vuelos desde",
		"seeMore": "Ver más",
		"stop": "Parada",
		"stops": "Paradas",
		"flightsEconomies": "Vuelos más económicos",
		"adultsFroms": "Por pasajero, desde:",
		"direct": "Directo",
		"roundTrip": "Redondo",
		"seeMoreFligths": "Ver más vuelos",
		"flight_departure": "Vuelos de ida",
		"flight_arrival": "Vuelos de regreso",
		"seeMoreEconomicFligthsFrom": "Ver más vuelos económicos desde",
		"seeLessFligths": "Ver menos vuelos",
		"book": "Reservar",
		"select_departure_flight": "Seleccionar vuelo de ida",
		"select_your_departure_flight": "<span>Selecciona tu <strong> vuelo de ida</strong></span>",
		"select_your_flight": "<span>Selecciona tu <strong> vuelo</strong></span>",
		"select_your_departure_flight_2": "Selecciona tu <strong> vuelo de ida</strong>",
		"select_your_returning_flight_2": "Selecciona tu <strong> vuelo de regreso</strong>",
		"select_returning_flight": "Seleccionar vuelo de regreso",
		"select_your_returning_flight": "<span>Selecciona tu <strong> vuelo de regreso</strong></span>",
		"origin": "Origen",
		"destination": "Destino",
		"sold_out": "Agotado",
		"seeMoreFligthsDeparture": "Ver más vuelos de ida",
		"seeMoreFligthsReturning": "Ver más vuelos de regreso",
		"seeMoreEconomicFligthsDeparture": "Ver más vuelos económicos de ida",
		"seeMoreEconomicFligthsReturning": "Ver más vuelos económicos de regreso",
		"selected_departure_flight": "Vuelo de ida seleccionado",
		"change_flight": "Cambiar vuelo",
		"departure": "Ida",
		"returning": "Regreso",
		"show_arrival_flights": "Ver vuelos de regreso",
		"what_each_rate_includes": "¿Qué  incluye cada tarifa?"
	},
	"NearByDates": {
		"EconomicFlights": "Vuelos más económicos",
		"select_your_departure_flight": "Elige tu vuelo de ida",
		"select_your_arrival_flight": "Elige tu vuelo de regreso"
	},
	"contactForm": {
		"title": "Envíanos un mensaje",
		"name": "Nombre",
		"entreprises": "Empresas",
		"tel": "Teléfono (opcional)",
		"email": "Correo electrónico",
		"message": "Mensaje",
		"checkText1": "Quiero recibir una copia del mensaje a mi correo electrónico",
		"checkText2": "Quiero recibir promociones exclusivas para mi",
		"sendFormBtn": "Enviar mensaje",
		"sendingFormBtn": "Enviando...",
		"success": "¡Gracias! En breve nos comunicamos contigo.",
		"error": "Hubo un problema al procesar tu solicitud",
	},
	"groupsForm": {
		"mainMessage": "Si quieres viajar con tu familia, amigos o grupo de trabajo y deseas toda la información necesaria sobre tiquetes, traslados o alquiler de auto, solo llena este formulario y un asesor se comunicará en poco tiempo.",
		"origin": "Origen",
		"originPlaceholder": "¿Dónde inicia tu viaje?",
		"destination": "Destino",
		"destinationPlaceholder": "¿Cuál es el destino?",
		"outboundDate": "Fecha de salida",
		"returningDate": "Fecha de regreso",
		"travelDate": "Fecha del viaje",
		"adults": "Adultos",
		"kids": "Niños",
		"groupType": "Tipo de grupo",
		"needHotel": "¿Necesitas hotel?",
		"needShuttle": "¿Necesitas traslados?",
		"needCar": "¿Necesitas auto?",
		"family": "Familia",
		"schools": "Colegios",
		"universities": "Universidades",
		"conventions": "Convenciones",
		"artistic": "Artistico",
		"sports": "Deportivo",
		"others": "Otros",
		"contact": "Contacto",
		"full_name": "Nombre(s) y Apellido(s)",
		"email": "Correo electrónico",
		"email_placeholder": "Ej: <EMAIL>",
		"phone": "Teléfono",
		"observations": "Observaciones (opcional)",
		"send": "Enviar",
	},
	"need_a_call": {
		"need_a_call?": "¿Necesitas que te llamemos?",
		"your_name": "Tu nombre",
		"phone_number": "Número de teléfono (10 dígitos)",
		"send": "Enviar",
		"openning_hours": "Horario de atención",
		"openning_hours_weekdays": "De lunes a viernes de {0} a {1}",
		"openning_hours_weekends_holidays": "Sábados, domingos y festivos {0} a {1}",
		"Ok": "Nos pondremos en contacto contigo a la brevedad",
		"No available": "Uno de nuestros asesores se comunicará lo más pronto posible.",
		"available_agents": "Asesores disponibles",
		"accept": "Aceptar",
	},
	"promotions": {
		"titleForm": "Saliendo desde:",
		"labelPlaceHolder": "Origen",
		"titleFormDestination": "Selecciona tu ciudad de destino",
		"labelPlaceHolderDestination": "Destino",
		"originTitle": "¡VIAJA!",
		"originSubtitle": "Super tarifas a cualquier destino",
		"destinationTitle": "Pensando en viajar a",
		"destinationTitleList": "Selecciona tu ciudad de origen",
		"salesDestinations": "Ofertas en destinos",
		"internationals": "INTERNACIONALES",
		"nationals": "NACIONALES",
		"titleCalendar": "Vuelos de",
		"otherDestination": "Busca otro destino",
		"tab1": "Ida y Regreso",
		"tab2": "Solo Ida",
		"from": "desde",
		"taxesWarning": "Tarifas por adulto con impuestos y sobrecargos",
		"hotelsDestination": "Ver todos los hoteles de",
		"datesFroms": "FECHAS DE",
		"go": "IDA",
		"return": " REGRESO",
		"more": "MAS",
		"dates": "FECHAS",
		"noRatesQuotations": "Lo sentimos pero no se encontraron precios para fechas cercanas para el destino, por favor intenta con nuevas fechas o diferente destino",
		"noDestinationPromotions": "No se encontraron destinos por el momento",
		"noOriginsPromotions": "No se encontraron origenes por el momento",
		"nationalTitle": "Nacionales",
		"internationalTitle": "Internacionales",
		"travelfrom": "Viajando desde",
		"travelto": "Viajando a",
		"searchFlight": "Buscar vuelos",
	},
	"multiticket": {
		"search_flights": "Buscando las mejores ofertas de vuelos...",
		"price_per_pax": "precio por persona",
		"tax_include": "incluye todos los impuestos",
		"price_from": "Por persona desde:",
		"price_RoundTrip_from": "Ida y regreso por persona desde:",
		"price_RoundTrip": "Ida y regreso por persona desde:",
		"per_pax": "por persona",
		"direct": "Directo",
		"stops": "paradas",
		"stop": "parada",
		"show_more_flights_web_cheap_mobile": "Ver vuelos <span class='cheap-active border-0'>más económicos</span>",
		"show_more_flights_web_mobile": "Ver más vuelos",
		"show_more_flights_departure_web": "Ver más vuelos de ida",
		"show_more_flights_departure_web_cheap": "Ver vuelos de ida <span class='cheap-active border-0'>más económicos</span>",
		"show_more_flights_arrival_web": "Ver más vuelos de regreso",
		"show_more_flights_arrival_web_cheap": "Ver vuelos de regreso <span class='cheap-active border-0'>más económicos</span>",
		"change_flight": "Cambiar vuelo",
		"hide_less_flights": "Ver menos vuelos",
		"day": "+{0} día",
		"days": "+{0} días",
		"departure_flight": "Vuelos de ida",
		"returning_flight": "Vuelos de regreso",
		"selected_departure_flight": "Vuelo de ida seleccionado",
		"departure_flight_upsell": "Vuelo de ida <span class='hide-xs'>seleccionado</span>",
		"returning_flight_upsell": "Vuelo de regreso <span class='hide-xs'>seleccionado</span>",
		"notification_flight": "No encontramos vuelos con ",
		"notification_flight_options": "Estas otras opciones podrían interesarte.",
		"price_RoundTrip_per_person":"precio redondo por persona",
		"arrive_on": "Llegas el ",
	},
	"categories": {
		"paymentsAndReservations": "Pagos y Reservas",
		"changesAndCancellations": "Cambios y Cancelaciones",
		"baggageAndProcesses": "Equipaje y Procesos",
		"billing": "Facturación",
		"onlineSecurity": "Seguridad en línea",
		"additionalServices": "Servicios adicionales"
	},
	"faq": {
		"searchPlaceholder": "Ingresa una palabra clave o pregunta",
		"paymentsAndReservations": [
			{
				"question": "¿Cuánto tiempo tengo para pagar mi tiquete después de realizar la reserva?",
				"answer": "Tienes un plazo de 24 horas para realizar el pago después de hacer la reserva."
			},
			{
				"question": "¿Cómo puedo comprar y pagar mi tiquete en línea?",
				"answer": "Puedes comprar y pagar tu tiquete a través de nuestra página web usando tarjeta de crédito o débito."
			},
			{
				"question": "Si ya he hecho una reserva ¿puedo continuar con el proceso de pago de mi tiquete?",
				"answer": "Sí, si ya has reservado, solo tienes que ingresar al área de pagos y continuar con el proceso."
			},
			{
				"question": "¿Puedo realizar el pago en línea de mi reserva con dos (2) tarjetas de crédito o débito?",
				"answer": "No, solo puedes usar una tarjeta de crédito o débito por pago."
			},
			{
				"question": "¿Puedo realizar el pago en línea de dos (2) reservas o más con la misma tarjeta de crédito o débito?",
				"answer": "Sí, puedes realizar el pago de varias reservas con la misma tarjeta."
			},
			{
				"question": "¿Puedo realizar el pago de mis reservas por línea telefónica?",
				"answer": "No, no ofrecemos la opción de pago por línea telefónica, solo en línea."
			},
			{
				"question": "¿Puedo pagar mi reserva con tarjeta de crédito?",
				"answer": "Sí, puedes pagar tu reserva con tarjeta de crédito."
			},
			{
				"question": "¿Cómo pago mi tiquete si la transacción con mi tarjeta sale rechazada o cancelada?",
				"answer": "Si la transacción es rechazada, puedes intentar con otro método de pago o verificar los detalles de tu tarjeta."
			},
			{
				"question": "¿Puedo pagarle un tiquete a un familiar o amigo?",
				"answer": "Sí, puedes pagar el tiquete a nombre de otra persona."
			},
			{
				"question": "¿Puedo pagar un tiquete en la noche?",
				"answer": "Sí, los pagos en línea están disponibles las 24 horas."
			},
			{
				"question": "¿Cómo puedo pagar?",
				"answer": "Puedes pagar en línea con tarjeta de crédito, débito o a través de otros métodos de pago que ofrecemos en el sitio."
			},
			{
				"question": "¿Puedo cambiar la forma de pago?",
				"answer": "No, una vez que se haya realizado el pago no podrás cambiar el método."
			},
			{
				"question": "¿Si vivo fuera de Colombia cómo puedo pagar un tiquete y cómo puedo reclamarlo?",
				"answer": "Puedes pagar desde cualquier país usando métodos de pago internacionales y el tiquete se enviará a tu correo electrónico."
			},
			{
				"question": "¿Puedo pagar un tiquete en otro país y que lo reclame otra persona en otro lugar?",
				"answer": "Sí, puedes pagar desde otro país y la persona que viaja podrá reclamar el tiquete."
			},
			{
				"question": "Si mi empresa está interesada en cuentas corporativas ¿qué debemos hacer?",
				"answer": "Debes ponerte en contacto con nuestro departamento de ventas para cuentas corporativas."
			},
			{
				"question": "¿Cómo es el manejo de las cuentas corporativas?",
				"answer": "Las cuentas corporativas tienen beneficios exclusivos como descuentos, facturación consolidada y más."
			}
		],
		"changesAndCancellations": [
			{
				"question": "¿Qué sucede si cancelo mi vuelo? ¿Cuánto tiempo tengo para hacer dicha cancelación?",
				"answer": "La cancelación depende de la tarifa seleccionada. Generalmente, tienes hasta 24 horas antes del vuelo para cancelar."
			}
		],
		"baggageAndProcesses": [
			{
				"question": "¿Cuánto tiempo antes debo estar en el aeropuerto antes de abordar mi vuelo?",
				"answer": "Te recomendamos llegar al menos 2 horas antes para vuelos nacionales y 3 horas para internacionales."
			},
			{
				"question": "¿Cuánto es el máximo peso permitido en mi equipaje?",
				"answer": "El peso máximo permitido es de 23 kg por maleta en vuelos nacionales y 30 kg en vuelos internacionales."
			},
			{
				"question": "¿Si excedo el peso permitido en mi equipaje qué debo hacer?",
				"answer": "Deberás pagar un cargo adicional por el exceso de peso."
			},
			{
				"question": "¿Cómo me doy cuenta si tengo o no mi reserva?",
				"answer": "Puedes verificar tu reserva ingresando el código de reserva en nuestra página web."
			},
			{
				"question": "¿Qué pasa si me deja el vuelo?",
				"answer": "Si llegas tarde y pierdes el vuelo, deberás contactar con atención al cliente para opciones de reprogramación."
			},
			{
				"question": "Si he comprado mi tiquete en tarifa económica y me deja el avión ¿qué debo hacer?",
				"answer": "Si pierdes el vuelo, deberás pagar una tarifa adicional para reprogramar tu vuelo según disponibilidad."
			}
		],
		"billing": [
			{
				"question": "¿Qué hago para solicitar mi factura o soporte de pago?",
				"answer": "Puedes solicitar tu factura a través de nuestra sección de soporte en la página web."
			}
		],
		"onlineSecurity": [
			{
				"question": "¿Qué seguridad tengo de hacer mi compra online?",
				"answer": "Nuestro sitio web utiliza encriptación SSL para garantizar la seguridad de tus transacciones."
			},
			{
				"question": "¿Qué empresas validan la seguridad en www.TiquetesBaratos.com?",
				"answer": "Contamos con la validación de empresas como Norton y McAfee, que aseguran la seguridad de nuestras transacciones."
			},
			{
				"question": "¿Puedo realizar el pago cualquier día y a cualquier hora?",
				"answer": "Sí, puedes realizar el pago en línea las 24 horas del día, los 7 días de la semana."
			},
			{
				"question": "¿Pagar electrónicamente tiene algún valor para mí como comprador?",
				"answer": "El pago electrónico es rápido, seguro y eficiente, lo que te garantiza un proceso sin contratiempos."
			},
			{
				"question": "¿Qué debo hacer si mi transacción no concluyó?",
				"answer": "Si la transacción no se completa, te sugerimos revisar tu información de pago o intentar con otro método."
			},
			{
				"question": "¿Qué debo hacer si no recibí el comprobante de pago?",
				"answer": "Si no recibiste el comprobante de pago, verifica tu correo electrónico o comunícate con nuestro soporte."
			}
		],
		"additionalServices": [
			{
				"question": "¿Cómo puedo añadir servicios adicionales a mi reserva (selección de asiento, equipaje extra, comidas especiales)?",
				"answer": "Puedes añadir servicios adicionales durante el proceso de reserva o en el área de gestión de tu reserva en línea."
			},
			{
				"question": "¿Puedo hacer una reserva a nombre de un menor de edad que viaja solo?",
				"answer": "Sí, puedes hacer una reserva para un menor de edad, pero requiere una autorización especial."
			},
			{
				"question": "¿Cómo puedo reservar asistencia especial (silla de ruedas, asistencia para personas mayores, etc.)?",
				"answer": "Puedes reservar asistencia especial durante la compra de tu tiquete o contactando a nuestro centro de atención al cliente."
			}
		]
	}
};

window.__pt.settings.formData = {
	"Nationality": "co",
	"nation_options": () => { return [
			{
				"text": "mexico",
				"value": "mx"
			}
		]
	}, /* () => {
        let nations = [];
        const countryData = window.intlTelInputGlobals.getCountryData();
        for (let index = 0; index < countryData.length; index++) {
            nations.push({ "text": countryData[index].name, "value": countryData[index].iso2 });
        }
        return nations;
    } */
	"day_selected": "0",
	"day_options": [
		{
			"text": "--",
			"value": "0"
		},
		{
			"text": "1",
			"value": "01"
		},
		{
			"text": "2",
			"value": "02"
		},
		{
			"text": "3",
			"value": "03"
		},
		{
			"text": "4",
			"value": "04"
		},
		{
			"text": "5",
			"value": "05"
		},
		{
			"text": "6",
			"value": "06"
		},
		{
			"text": "7",
			"value": "07"
		},
		{
			"text": "8",
			"value": "08"
		},
		{
			"text": "9",
			"value": "09"
		},
		{
			"text": "10",
			"value": "10"
		},
		{
			"text": "11",
			"value": "11"
		},
		{
			"text": "12",
			"value": "12"
		},
		{
			"text": "13",
			"value": "13"
		},
		{
			"text": "14",
			"value": "14"
		},
		{
			"text": "15",
			"value": "15"
		},
		{
			"text": "16",
			"value": "16"
		},
		{
			"text": "17",
			"value": "17"
		},
		{
			"text": "18",
			"value": "18"
		},
		{
			"text": "19",
			"value": "19"
		},
		{
			"text": "20",
			"value": "20"
		},
		{
			"text": "21",
			"value": "21"
		},
		{
			"text": "22",
			"value": "22"
		},
		{
			"text": "23",
			"value": "23"
		},
		{
			"text": "24",
			"value": "24"
		},
		{
			"text": "25",
			"value": "25"
		},
		{
			"text": "26",
			"value": "26"
		},
		{
			"text": "27",
			"value": "27"
		},
		{
			"text": "28",
			"value": "28"
		},
		{
			"text": "29",
			"value": "29"
		},
		{
			"text": "30",
			"value": "30"
		},
		{
			"text": "31",
			"value": "31"
		},
	],
	"month_selected": "0",
	"month_options": [
		{
			"text": "--",
			"value": "0"
		},
		{
			"text": "01",
			"value": "01"
		},
		{
			"text": "02",
			"value": "02"
		},
		{
			"text": "03",
			"value": "03"
		},
		{
			"text": "04",
			"value": "04"
		},
		{
			"text": "05",
			"value": "05"
		},
		{
			"text": "06",
			"value": "06"
		},
		{
			"text": "07",
			"value": "07"
		},
		{
			"text": "08",
			"value": "08"
		},
		{
			"text": "09",
			"value": "09"
		},
		{
			"text": "10",
			"value": "10"
		},
		{
			"text": "11",
			"value": "11"
		},
		{
			"text": "12",
			"value": "12"
		},
	],
	"year_selected": "0",
	"year_options": (age = 0) => {

		let today = new Date();
		let year = today.getFullYear();
		let yearUntil = age == 0 ? 1900 : (year - age - 2);
		let years = [];

		years.push({
			"text": "--",
			"value": "0"
		})
		for (let i = year; i > yearUntil; i--) {
			years.push({
				"text": String(i),
				"value": String(i)
			})
		}
		return years;
	}
}