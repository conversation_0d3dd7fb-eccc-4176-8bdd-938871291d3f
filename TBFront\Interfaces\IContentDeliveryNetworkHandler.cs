﻿using TBFront.Models.ContentDeliveryNetwork.Exchange;
using TBFront.Models.ContentDeliveryNetwork.FaqContent;
using TBFront.Models.ContentDeliveryNetwork.LegalContent;
using TBFront.Models.ContentDeliveryNetwork.Seo;

namespace TBFront.Interfaces
{
    public interface IContentDeliveryNetworkHandler : IQueryHandlerAsync<ExchangeRequest, ExchangeResponse>, IQueryHandlerAsync<SeoRequest, SeoResponse>
    {
        Task<FaqContentResponse> QueryAsync(FaqContentRequest request, CancellationToken ct);
        Task<List<LegalContentResponse>> QueryAsync(LegalContentRequest request, CancellationToken ct);
    }
}