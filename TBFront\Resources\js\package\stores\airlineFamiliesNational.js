import { defineStore } from "pinia";
import {getFamilyFare, getLuggage} from "../services/ApiFlightFrontServices";
import {NationalAirlineFlightsMapper} from "../mappers/nationalAirlineFlightsMapper";

export const useAirlineFamiliesNationalStore = defineStore({
    id: "AirlineFamiliesNational",
    state: () => ({
        loadingAirlineFamiliesNational: false,
        showAirlineFamiliesNational: false,
        airlineFamiliesNational: {}
    }),
    getters: {
        getLoadingAirlineFamiliesNational: (state) => {
            return state.loadingAirlineFamiliesNational;
        },
        getShowAirlineFamiliesNational: (state) => {
            return state.showAirlineFamiliesNational;
        },
        getAirlineFamiliesNational: (state) => {
            return state.airlineFamiliesNational;
        }
    },
    actions: {
        changeOpenCloseModalUpsell() {
            this.showAirlineFamiliesNational = !this.showAirlineFamiliesNational;
            if (this.showAirlineFamiliesNational) {
                document.body.classList.add("overflow-y-hidden");
            } else {
                document.body.classList.remove("overflow-y-hidden");
            }
        },
        async actionAirlineFamiliesNational({group, rq, responseFamilyFare}) {
            this.loadingAirlineFamiliesNational = true;
            try {
                //let families = await this.searchFlightsDetails(group, rq)
                let families = await this.getLuggageNationalFlight(group, rq)
                this.setAirlineFamiliesNational({families: families, group, rq, 
                    responseFamilyFare
                })
                this.loadingAirlineFamiliesNational = false;
            }catch(e){
                this.airlineFamiliesNational = []
                this.loadingAirlineFamiliesNational = false;
            }
        },
        openAirlineFamiliesNationalComponent(){
            const modalElement = document.getElementById("modalAirlineFamiliesNational");
            if(modalElement){
                const modal = new bootstrap.Modal(modalElement);
                modal.show();   
            }
        },
        setAirlineFamiliesNational({families, group, rq, responseFamilyFare}){
            this.airlineFamiliesNational = {families, group, rq, responseFamilyFare};
        },
        async searchFlightsDetails(group, rq){
            let familiesFare = [];
            for (const family of (group.families || [])) {
                const flight = this.findFamilyAirline(group, family.familyFareCode)
                const fare = this.filterFare(flight.fares, 'fareGroup', family.familyFareCode)
                rq.flightId = flight.id
                rq.fareId = fare?.fareId
                rq.familyFare = fare?.fareGroup
                let responseFamilyFare = await getFamilyFare(rq);
                familiesFare.push(responseFamilyFare)
                //this.findFamilyAirline(group, configurations.familyFareCode)   
            }
            
            return familiesFare
        },
        async getLuggageNationalFlight(group, rq){
            let families = (window.__pt.settings.site.airlineConfiguration[rq.type] ?? []).find(config => (config.airlineCode || []).some(code => code === rq.code))?.families ?? null
            let excluded = []
            if(families){
                for (const family of (families || [])) {
                    const isValid = ((group.families || []).some(fm=>fm.familyFareCode.some(code=> family.familyFareCode.some(code2=> code2 === code)) && fm.isAvailable))
                    if(!isValid){
                        excluded = [...excluded, ...family.familyFareCode]
                    }
                }
                if(excluded.length){
                    rq.exceptions = excluded.join(",")
                }   
            }
            let responseFamilyFare = await getLuggage(rq);
            
            let res =  responseFamilyFare.luggages
            res = NationalAirlineFlightsMapper.sortFamilies((res || []).map(item=>{
                const code = String(( item.id || "" )).toUpperCase()
                let fam2 = families.find(family=> family.familyFareCode.some(code2=> code2.toUpperCase() === code.toUpperCase()))
                item.familyFareCode = [code]
                item.familyFareName = fam2?.name ?? item.familyFareName
                item.familyFareContent = this.sortFamilyFareContent(item.familyFareContent)
                return item
            }), {families: families})
            return res
        },
        sortFamilyFareContent(familyFareContent){
            return (familyFareContent || []).sort((a, b) => {
                if(a.category === 2) {
                    return -1;
                }
                if(b.category === 2) {
                    return 1;
                }
                if(a.category === 1) {
                    return -1;
                }
                if(b.category === 1) {
                    return 1;
                }
                return a.category - b.category;
            });
        },
        filterFlightByAirlineCode(flights, familyFareCode) {
            return (flights || []).find(flight => this.filterFare((flight?.fares || []), 'fareGroup', familyFareCode))
        },
        findFamilyAirline(airline, familyFareCode) {
            let flightType = "starting";
            let flight = this.filterFlightByAirlineCode((airline.departure?.flights ?? []), familyFareCode)
            if (!flight) {
                flight = this.filterFlightByAirlineCode((airline.returning?.flights ?? []), familyFareCode) ?? {}
                flightType = "returning"
            }
            flight.flightType = flightType
            return flight
        },
        filterFare(fares, key, value) {
            return fares.find(fareKey => value.some(code => fareKey[key].toUpperCase() === (code).toUpperCase()))
        },
        activateLoadingAirlineFamiliesNational() {
            this.loadingAirlineFamiliesNational = true;
        },
    },
});