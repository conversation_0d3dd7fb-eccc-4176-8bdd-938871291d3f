﻿namespace TBFront.Models.PaymentGateway.Dtos
{
    public class PaymentTokenConfig
    {
        public bool Is3DSecureProcessingEnabled { get; set; }

        public List<Dictionary<string, object>>? Analytics { get; set; }

        public string AnalyticsId { get; set; } = string.Empty;
        public string UrlRedirectTo { get; set; } = string.Empty;
        public bool RedirectToPaymentGatewayConfirmation { get; set; }
        public string HostUrl { get; set; } = string.Empty;
        public string SessionId { get; set; } = string.Empty;
        public int ChannelGroupId { get; set; }
        public int ChannelId { get; set; }
        public int PaymentGatewayApp { get; set; }
        public string ContactPhone { get; set; } = string.Empty;
        public List<int> ExternalProvider { get; set; } = new List<int>();
        public List<int> ThirdPartyCheckoutProvider { get; set; } = new List<int>();
        public int AffiliateId { get; set; }
        public int AffiliateSiteId { get; set; }


    }
}
