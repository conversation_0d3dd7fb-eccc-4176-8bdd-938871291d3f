using System.Collections.Generic;

namespace TBFront.Models.Configuration
{
    public class FlightValidationStops
    {
        public NationalStops National { get; set; }
    }

    public class NationalStops
    {
        public List<Route> Routes { get; set; }
        public Default Default { get; set; }
    }

    public class Route
    {
        public string Origin { get; set; }
        public List<AirlineStop> AirlineStops { get; set; }
    }

    public class AirlineStop
    {
        public string Airline { get; set; }
        public int Stops { get; set; }
    }

    public class Default
    {
        public int Value { get; set; }
    }
}