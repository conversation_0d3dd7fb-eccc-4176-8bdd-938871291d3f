export function getCompleteURLFromParts() {
    const protocol = window.location.protocol;
    const hostname = window.location.hostname;
    const port = window.location.port;
    const pathname = window.location.pathname;
    const search = window.location.search;
    const hash = window.location.hash;

    // Construct the full URL
    return `${protocol}//${hostname}${port ? ':' + port : ''}${pathname}${search}${hash}`;
}

export function getUrlDomain() {
    const protocol = window.location.protocol;
    const hostname = window.location.hostname;
    const port = window.location.port;

    // Construct the full URL
    return `${protocol}//${hostname}${port ? ':' + port : ''}`;
}