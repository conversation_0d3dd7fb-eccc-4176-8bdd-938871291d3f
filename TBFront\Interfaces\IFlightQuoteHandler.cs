﻿using TBFront.Models;
using TBFront.Models.Flight.Quote;
using TBFront.Models.Flight.Revalidate;
using TBFront.Models.Flight.Summary;
using TBFront.Models.Flight.Upsell;
using TBFront.Models.Request;
using TBFront.Models.Response;

namespace TBFront.Interfaces
{
    public interface IFlightQuoteHandler :
        IQueryHandlerAsync<FlightListRequest, FlightQuoteResponse>, 
        IQueryHandlerAsync<FlightUpsellRequest, FlightUpsellResponse>,
        IQueryHandlerAsync<CheckoutRevalidateRequest, RevalidateResponse>,
        IQueryHandlerAsync<CheckoutBookingRequest, CheckoutBookingResponse>,
        IQueryHandlerAsync<VoucherInfo, SummaryResponse>
    { }
}
