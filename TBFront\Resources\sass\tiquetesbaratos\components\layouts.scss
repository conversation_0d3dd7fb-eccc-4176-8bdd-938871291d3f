@import '../_variables.scss';

body {
    margin: 0;
    font-family: 'Roboto', sans-serif;
    font-weight: 400;
    color: #333333;

    .d-flex-important {
        display: flex !important;
    }

    .d-flex {
        display: flex !important;
        flex-direction: row;
        flex-wrap: nowrap;
        align-content: center;
        justify-content: center;
        align-items: center;
    }
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Poppins', sans-serif;
    font-weight: 500;
}



a{
    color: $color-primary;
    &:hover{
        color: $color-primary-hover;
    }
}

.d-center{
    display: flex;
    justify-content: center;
    align-items: center;
}

.cursor-pointer{
    cursor: pointer;
}

.header-page {
    padding: 25px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.brand-logo {
    width: 202px;
}



header + svg {
    width: 100%;
    height: 6px;
    display: block;
}

.alert-telephone svg {
    width: 24px;
    vertical-align: middle;
}

.svg_line {
    background-image: url('/assets-tb/img/hr-brand.svg');
    height: 6px;
    background-size: cover;
    background-position: center;
    width: 100%;
}

.svg_line.svg_line_footer {
    height: 3px;
}

.hr-brand {
    display: block;
    background-image: url('/assets-tb/img/hr-brand.svg');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: bottom;
    width: 100%;
    height: 6px;

    &.hr-brand--footer {
        height: 2px;
    }
}

.header-page {
	padding: 25px 0;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.brand-logo {
	width: 202px;
}

header + svg {
	width: 100%;
	height: 6px;
	display: block;
}

.navbar{
    padding-top: 16px;
    padding-bottom: 16px;

}

.avatar-user{
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: $color-primary;
    color: white;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 22px;
    margin: 0 5px;
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
    background-color: $color-secundary-hover;
}

.nav-login{
    .nav-link{
        font-weight: bold;
    }

    .dropdown-item{
        transition: 1s;
        &:hover{
            transition: 1s;
            color: $color-primary-hover;
            background-color: $color-primary-light;
        }
    }
}

.color-pink, .text-pink{
    color: $color-secundary;
}



@media($phone){
    .footer-group-heading{
        border-bottom: 1px solid #c2c2c2;
        padding: 10px 5px 10px 5px;
        cursor: pointer;
        &:hover{
            background-color: $color-primary-light;
        }
    }
    .navbar-tb{
        display: none;
    }
        
        footer .icons-expand-more{
            position: absolute;
            right: 18px;
        }

    .phonenumber {
        font-size: 15px;
        padding: 25px 9px;
    }

}

@media (min-width: 768px) and (max-width: 991px){

    .navbar-tb__menu-contact{
        display: none;
    }

}
.text-secondary{
    color: $color-primary !important;
    position: relative;
}
@media($desktop-lg){

    .navbar-tb__menu-contact-m{
        display: none;
    }

}

@media($desktop){
    .navbar-expand-lg .navbar-nav .nav-link {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    footer .icons-expand-more {
        display: none;
    }

}

.pointer {
    cursor: pointer;
}

.top-0 {
    top: 0;
}
.top-1 {
    top: 1;
}
.z-98 {
    z-index: 98 !important;
}
.z-99 {
    z-index: 99 !important;
}
.z-99-xs {
    @media ($phone) {
        z-index: 99 !important;
    }    
}




