﻿using TBFront.Models.Meta.Schema;
using TBFront.Models.Meta.Alternate;


namespace TBFront.Models.Meta.Metatags
{
    public class MetaTag
    {
        public string SiteTitle { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Separator { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Author { get; set; }  = string.Empty;
        public string Img { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
        public string AppName { get; set; } = string.Empty;
        public string Domain { get; set; } = string.Empty;
        public string Lang { get; set; } = string.Empty;
        public string Root { get; set; } = string.Empty;
        public string UrlList { get; set; } = string.Empty;

        public SchemaMain SchemaMain { get; set; } = new SchemaMain();
        public MobileApplication MobileApp { get; set; } = new MobileApplication();
        public SchemaProduct SchemaProduct { get; set; } = new SchemaProduct();
        public SchemaHotel SchemaHotel { get; set; } = new SchemaHotel();
        public BreadcrumbList BreadCrumbs { get; set; } = new BreadcrumbList();
        public ItemList ItemList { get; set; } = new ItemList();
        public Question Question { get; set; } = new Question();
        public List<AlternateUrl>? Alternate { get; set; }

        public MetaTag()
        {
            BreadCrumbs = new BreadcrumbList();
            Alternate = new List<AlternateUrl>();
        }
    }
}
