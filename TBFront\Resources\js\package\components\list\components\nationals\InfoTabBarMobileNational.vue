<template>
    <div class="container">
        <div class="mt-3 mb-2 row c-title-go mt-3 c-header-flights" id="mobileInfo-section">
            <template v-if="( flightSelected?.step_action >= 3 ) && flightSelected.departure_fare">
                <div class="col-12 pt-1 px-0 pb-2" id="mobileInfo">
                    <div class="c-info-flight-xs py-2 px-1">
                        <div class="col-12 font-16 mb-1 px-2">
                            <span class="f-bold">{{__("flightList.selected_departure_flight")+" "}}</span>
                            <a @click="backSelectedFlight()" class="a-link-1 ml-2 f-light" id="relink">{{__("flightList.change_flight")}}</a>
                        </div>
                        <div class="col-12 font-14 px-2 d-flex align-items-center">
                            <span @click="getFlightDetails('FLIGHT_FAMILY')" id="familyMobile" class=" pointer mr-025">{{flightSelected.departure_fare.fareName}}</span>
                            <span  @click="getFlightDetails('FLIGHT_FAMILY')" class="icon icon-info font-22 pointer" id="modalFamilyMobile"></span>
                            <span class="px-1 pipe mr-05 ml-05" id="mobileSeparador">|</span>
                            <span @click="getFlightDetails('FLIGHT_DETAIL')" class="a-link-1 pointer" id="escalaMobile" data-detailrow="detalleMobileIda">{{ ((flightSelected.departure_flight?.stops >= 1) ? flightSelected.departure_flight?.stops: '')}}{{ __(`messages.stops_${flightSelected.departure_flight.stops >=2 ? 2 : flightSelected.departure_flight.stops}`) + "  " }} {{" "}} </span>
                            <span class="pl-1" id="horarioMobileIda">{{"  "+flightSelected.departure_flight.departure?.time + " "}}</span>
                            <span class="icon icon-plane-right font-12 mr-05 ml-05"></span>
                            <span class="pr-1" id="horarioMobileLlegada">{{flightSelected.departure_flight.arrival?.time}}</span>
                            <span class="px-1 pipe ml-025 mr-025">|</span>
                            <span id="precioMobile">{{$filters.currency(flightSelected.departure_fare.amount)}}</span>
                        </div>
                    </div>
                </div>
            </template>
            <template v-if="isRoundTrip">
                <div class="col-12 px-0">
                    <h3 class="title-list mb-0 mt-2 mt-md-3">
                        <span :class="['icon icon-plane-'+((flightSelected?.step_action <= 2) ? 'right': 'left')]" id="avion"></span>
                        <span class="font-poppins-medium" id="vueloMobile">{{__("flightList.flight_"+((flightSelected?.step_action <= 2)?"departure":"arrival"))}}</span>
                    </h3>
                </div>   
            </template>
        </div>
    </div>
</template>
<script>
import {storeToRefs} from "pinia";
import { useFlightStore } from '../../../../stores/flight';
import { useFlightUpsellStore } from '../../../../stores/flightUpsell';
import {useFlightRevalidateStore} from "../../../../stores/flightRevalidate";
import {getDetail, getFamilyFare, getParamsDetailFlight, getMatrix, getFilteredList} from "../../../../services/ApiFlightFrontServices";
import {List} from "../../../../../utils/analytics/flightList";
import {useFlightDetailStore} from "../../../../stores/flightDetail";
import {useFlightFamilyFareStore} from "../../../../stores/flightFamilyFare";
import {configFilter, getFlightsBySimpleFlightQuotes} from "../../../../services/fetchListService";
import { useUserSelectionStore } from '../../../../stores/user-selection';
import { useMultiTicketStore } from '../../../../stores/multiTicket';
import { usePromotionStore } from '../../../../stores/promotion';
import { useFlightMatrixStore } from '../../../../stores/flightMatrix';
import { sleep } from '../../../../../utils/helpers';
export default {
    data() {
        return {
            configSite: window.__pt.settings.site.airlineConfiguration.national ?? [],
        }
    },
    props: {
        isRoundTrip: { type: Boolean, default: false },
    },
    setup(){
        const storeFlight = useFlightStore()
        const useFlightUpsell = useFlightUpsellStore();
        const flightRevalidateStore = useFlightRevalidateStore();
        const useUserSelection = useUserSelectionStore();
        const useMultiTicket = useMultiTicketStore();
        const storeFlightMatrix = useFlightMatrixStore();
        const promotionStore = usePromotionStore();
        const { getParams, getAllQuoteTokens, getReturnQuoteTokens } = storeToRefs(storeFlight); //get
        const { setFlightResponses, resetFlightResponse } = storeFlight;
        const { getTotalAmount } = storeToRefs(flightRevalidateStore);
        const { getFlightSelected } = storeToRefs(useFlightUpsell);
        const { setFlightSelected } = useFlightUpsell; //set/actions
        const flightDetailStore = useFlightDetailStore();
        const flightFamilyFareStore = useFlightFamilyFareStore();
        const { setFlightDetailResponse, activeModalDetail, setExtraData } = flightDetailStore;
        const { setFlightFamilyFareResponse } = flightFamilyFareStore;
        const { getShowDetail } = storeToRefs(flightDetailStore);
        const { changeFilters } = useUserSelection;
        const { setAirlinesFilters, setStopsFilters, setIsStepTwo } = useMultiTicket;
        const { getTripMode } = storeToRefs(promotionStore);
        const { setLoading, setFlightMatrix } = storeFlightMatrix;
        return {
            getTotalAmount,
            getParams,
            getFlightSelected,
            setFlightSelected,
            setFlightDetailResponse,
            setFlightFamilyFareResponse,
            activeModalDetail,
            getShowDetail,
            setExtraData,
            changeFilters,
            setAirlinesFilters,
            setStopsFilters,
            setIsStepTwo,
            setFlightResponses,
            resetFlightResponse,
            setLoading,
            setFlightMatrix,
            getAllQuoteTokens,
            getReturnQuoteTokens,
            getTripMode
        }
    },
    computed: {
        flightSelected() {
            return this.getFlightSelected;
        },
        information() {
            return this.getParams
        }
    },
    methods:{
        async backSelectedFlight() {
            const modal = new bootstrap.Modal(document.getElementById('LoaderFullPage'), null);
            modal.show();
            await sleep(1000);
            const params = {
                token: this.getAllQuoteTokens.join(','),
                filterApplied: '',
                site: window.__pt.settings.site.apiFlights.siteConfig,
                tripMode: this.getTripMode,
                simpleFlightQuotes: true,
                step: true
            };
            const response = await getFilteredList(params);
            this.resetFlightResponse();
            await sleep(50)
            this.changeFilters([]);
            await this.setFlightResponses(response.response);
            await this.setMatrix();

            this.flightSelected.step_action = 1;
            this.flightSelected.loadingList = true;
            this.flightSelected.returning_flight = {};
            this.flightSelected.returning_fare = null;
            this.flightSelected.departure_flight = {};
            this.flightSelected.departure_fare = null;
            this.flightSelected.departure = null;
            this.flightSelected.returning = null;

            this.setIsStepTwo(false);
            this.flightSelected.loadingList = false;
            this.setAirlinesFilters([]); //-> reiniciamos filtros
            this.setStopsFilters('');
            this.setFlightSelected(this.flightSelected);
            await sleep(1000);
            modal.hide();
        },
        async setMatrix(token, fareKey) {
            if (this.getReturnQuoteTokens.length) {
                this.setLoading(true);
                const response = await getMatrix({ token: (this.getReturnQuoteTokens.join(',')), step: true, departureToken: token, flightQuoteId: fareKey });//
                this.setFlightMatrix(response);
                this.setLoading(false);
            }
        },
        async getFlightDetails(type) {
            const modalElement = document.getElementById('modalDetailNational');
            if (this.getShowDetail) return;
            this.activeModalDetail();
            const modal = new bootstrap.Modal(modalElement);
            modal.show();
            this.setExtraData({
                airlineLogoUri: this.flightSelected.departure.departure.image,
                airlineName: this.flightSelected.departure.departure.name,
                view: this.__(`messages.departure_flight`)
            });
            this.paramsDetail = {
                flightId: 0,
                airlineLogoUri: 0,
                airlineName: this.flightSelected.departure.departure.name,
                fareId: 0,
                token: this.flightSelected.departure.quoteToken,
                //token: ((group.departure.token ) ? group.departure.token : group.returning.token) ,
                flightType: 'starting',
                fareKey: this.flightSelected.departure_fare?.fareKey,
                familyFare: this.flightSelected.departure_fare?.fareGroup
            };
            let rq = getParamsDetailFlight(this.paramsDetail);

            switch ((String(type)).toUpperCase()) {
                case "FLIGHT_DETAIL":
                    let responseDetail = await getDetail(rq);
                    this.setFlightDetailResponse(responseDetail);

                    List.modalDetail(this.flightSelected.departure.departure.code, "", "",false);
                    this.setFlightFamilyFareResponse({});
                    break;
                case "FLIGHT_FAMILY":
                    let responseFamilyFare = await getFamilyFare(rq);
                    this.setFlightFamilyFareResponse({...responseFamilyFare,params: rq});
                    List.modalDetail(this.flightSelected.departure.departure.code, true);
                    this.setFlightDetailResponse({});
                    break;
            }
            this.activeModalDetail();

        },
    }

}
</script>