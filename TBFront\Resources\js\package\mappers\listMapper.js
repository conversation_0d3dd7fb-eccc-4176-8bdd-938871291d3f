
class ListMappers {

    map(responses) {
        let groups = [];

        responses.forEach(response => {
            let airlinesLength = response.airlines.length;

            for (var i = 0; i < airlinesLength; i++) {
                let item = {};

                let code = response.airlines[i];

                let departure = response.departure.flightList[code];

                item.departure = departure;
                item.returning = null;
                item.price = item?.departure?.cheapest;
                item.quoteToken = response.quoteToken;
                item.quoteTokenFQS = response.quoteTokenFQS;

                for (var it = 0; it < item?.departure?.flights?.length; it++) {
                    item.departure.flights[it]['flightDays'] = this.flightDays(item.departure.flights[it]);
                }


                if (response.returning) {
                    item.returning = response?.returning?.flightList?.[code];
                    item.price += item?.returning?.cheapest;
                    for (var it = 0; it < item?.returning?.flights?.length; it++) {
                        item.returning.flights[it]['flightDays'] = this.flightDays(item.returning.flights[it]);
                    }
                }
                if(response.outboundFlightId){
                    item.outboundFlightId = response.outboundFlightId
                }
                
                if(response.outputFareId){
                    item.outputFareId = response.outputFareId
                }

                if(response.returningQuoteToken){
                    item.returningQuoteToken = response.returningQuoteToken
                }

                groups.push(item);
                groups = groups.sort((a, b) => a.price - b.price);
            }
        });
        return groups;
    }

    flightDays(flight) {
        const dateDeparture = new Date(flight.departure.date);
        const dateArrival = new Date(flight.arrival.date);
        const diffInMs = dateArrival - dateDeparture;
        const diffInDays = diffInMs / (1000 * 60 * 60 * 24);
        return Math.abs(diffInDays);
    }
}

export const ListMapper = new ListMappers();