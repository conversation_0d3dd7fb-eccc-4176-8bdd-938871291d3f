﻿using System.Text.RegularExpressions;
using TBFront.Models;
using TBFront.Models.Response;
using TBFront.Options;

namespace TBFront.Mappers
{
    public static class FlightMapper
    {
        private static readonly string _patternCulture = @"^[a-zA-Z]{2}(-[a-zA-Z]{2})?$";
        public static FlightItinerary FlightItineary(Summary summary, bool isRoundTrip, SettingsOptions options)
        {
            var itinerary = new FlightItinerary();
            var flightOrigin = summary.Origin;


            itinerary.Starting = GetFlight(flightOrigin, options);
            itinerary.Starting.ArrivalName = summary.ArrivalName;
            itinerary.Starting.DepartureName = summary.DepartureName;
            itinerary.Starting.airlineLogoUrl = summary.Origin.Airline.AirlineLogoUrl;
            

            if (isRoundTrip)
            {
                var flightReturning = summary.Destination;

                itinerary.Returning = GetFlight(flightReturning, options);
                itinerary.Returning.ArrivalName = summary.DepartureName;
                itinerary.Returning.DepartureName = summary.ArrivalName;
                itinerary.Returning.airlineLogoUrl = summary.Destination.Airline.AirlineLogoUrl;
                if (!string.IsNullOrEmpty(summary.FareKey))
                {
                    itinerary.Starting.FareKey = summary.FareKey.Split("|")[0];
                    itinerary.Returning.FareKey = summary.FareKey.Split("|")[1];
                }

            }

            return itinerary;
        }

        public static ExtraInfoFlight ExtraInfoFlight(Summary summary, bool isRoundTrip)
        {
            var extra = new ExtraInfoFlight();
            var flightOrigin = summary.Origin;
            var fareOrigin = flightOrigin.Fares.FirstOrDefault();


            extra.CheckInWasModified = false;
            extra.Farekey = fareOrigin.FareKey;
            extra.StartingFromDateTime = flightOrigin.Departure.Date;
            extra.StartingFrom = flightOrigin.Departure.AirportCode;
            extra.SelectedOutboundFlight = flightOrigin.FlightNumbers.FirstOrDefault();

            extra.ReturningFromDateTime = flightOrigin.Arrival.Date;
            extra.ReturningFrom = flightOrigin.Arrival.AirportCode;
            extra.SelectedReturnFlight = flightOrigin.FlightNumbers.FirstOrDefault();


            if (isRoundTrip)
            {
                var flightReturning = summary.Destination;
                var fareReturning = flightReturning.Fares.FirstOrDefault();

                extra.Farekey = string.IsNullOrEmpty(summary.FareKey) ? $"{fareOrigin.FareKey}|{fareReturning.FareKey}" : summary.FareKey;
                extra.ReturningFromDateTime = flightReturning.Departure.Date;
                extra.ReturningFrom = flightReturning.Departure.AirportCode;
                extra.SelectedReturnFlight = flightReturning.FlightNumbers.FirstOrDefault();

            }

            return extra;
        }



        private static FlightItemQuote GetFlight(TBFront.Models.FlightCheckout flightSegment, SettingsOptions options)
        {
            var flight = new FlightItemQuote
            {
                Airline = flightSegment.Airline.Name,
                AirlineCode = flightSegment.Airline.Code,
                LogoUri = $"{options.SiteUrl}/assets-tb/img/tiquetesbaratos/checkout/{flightSegment.Airline.Code}.png",
                Scales = flightSegment.Stops,
                FlightDuration = flightSegment.FlightDuration,
                FlightNumber = flightSegment.FlightNumbers.FirstOrDefault(),
                Id = flightSegment.Id,
                ItineraryId = flightSegment.ItineraryId,
                ArrivalFullName = flightSegment.ArrivalFullName,
                DepartureFullName = flightSegment.DepartureFullName,
                FareId = flightSegment.Fares.FirstOrDefault().FareId,
                FareGroup = flightSegment.Fares.FirstOrDefault().FareGroup,
                FareKey = flightSegment.Fares.FirstOrDefault().FareKey,
                NegotiatedFareId = flightSegment.Fares.FirstOrDefault()?.NegotiatedFareId


            };
            flight.Arrival.Date = flightSegment.Arrival.Date;
            flight.Arrival.Time = flightSegment.Arrival.Time;
            flight.Arrival.Airport = flightSegment.Arrival.AirportCode;
            flight.Arrival.Terminal = flightSegment.Arrival.Terminal;
            flight.Departure.Date = flightSegment.Departure.Date;
            flight.Departure.Time = flightSegment.Departure.Time;
            flight.Departure.Airport = flightSegment.Departure.AirportCode;
            flight.Departure.Terminal = flightSegment.Departure.Terminal;

            return flight;
        }


        public static string ToQueryString(IHttpContextAccessor httpContextAccessor)
        {
            var deserialized = GetParamsDictionary(httpContextAccessor);
            var queryString = "";

            if (deserialized.Count > 0)
            {
                queryString = deserialized.Select((kvp) => kvp.Key.ToString() + "=" + Uri.EscapeDataString(kvp.Value)).Aggregate((p1, p2) => p1 + "&" + p2);
                queryString = $"?{queryString}";
            }

            return queryString.ToLower();

        }
        public static string ToQueryString(HttpContext httpContextAccessor)
        {
            var deserialized = httpContextAccessor.Request.Query.ToDictionary(x => x.Key, x => x.Value.ToString());
            var queryString = "";

            if (deserialized.Count > 0)
            {
                queryString = deserialized.Select((kvp) => kvp.Key.ToString() + "=" + Uri.EscapeDataString(kvp.Value)).Aggregate((p1, p2) => p1 + "&" + p2);
                queryString = $"?{queryString}";
            }

            return queryString;
        }

        private static Dictionary<string, string> GetParamsDictionary(IHttpContextAccessor _httpContextAccessor)
        {
            Dictionary<string, string> parameters;

            if (_httpContextAccessor.HttpContext.Request.HasFormContentType)
            {
                parameters = _httpContextAccessor.HttpContext.Request.Form.ToDictionary(x => x.Key, x => x.Value.ToString());
            }
            else
            {
                parameters = _httpContextAccessor.HttpContext.Request.Query.ToDictionary(x => x.Key, x => x.Value.ToString());
            }

            return parameters;
        }

        public static bool IsCultureValid(string culture)
        {
            return Regex.IsMatch(culture, _patternCulture);
        }

    }
}