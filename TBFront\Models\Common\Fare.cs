﻿namespace TBFront.Models.Common
{
    public class FareDetail
    {
        public int Quantity { get; set; }
        public string DisplayText { get; set; }
        public double Amount { get; set; }
        public int Type { get; set; }

    }
    
    public class Fare
    {
        public int FareId { get; set; }
        public int Priority { get; set; }
        public string FareGroup { get; set; }
        public string Fare<PERSON>ey { get; set; }
        public double AverageAmount { get; set; }
        public double Amount { get; set; }
        public double DisplayAmount { get; set; }
        public int Engine { get; set; }
        public List<FareDetail> FareDetails { get; set; }
        public int? NegotiatedFareId { get; set; }
    }

}
