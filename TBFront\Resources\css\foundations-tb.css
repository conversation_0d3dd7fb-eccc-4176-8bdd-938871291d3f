@import "primitives.css";

/*--------------------------------------- DESIGN SYSTEM: SEMANTICOS ---------------------------------------*/
:root {
    /* SPACING ---------------------------------------------- */
    --space-4: var(--space);
    --space-8: calc(var(--space) * 2);
    --space-12: calc(var(--space) * 3);
    --space-16: calc(var(--space) * 4);
    --space-20: calc(var(--space) * 5);
    --space-24: calc(var(--space) * 6);
    --space-28: calc(var(--space) * 7);
    --space-32: calc(var(--space) * 8);
    --space-36: calc(var(--space) * 9);
    --space-40: calc(var(--space) * 10);

    
    /* COLORS ---------------------------------------------- */
    /* BACKGROUNDS */
    --bg-primary: var(--navy-500);
    --bg-primary-hover: var(--navy-600);
    --bg-primary-subtle: var(--navy-100);
    --bg-primary-level1: var(--navy-200);
    --bg-primary-level2: var(--navy-300);

    --bg-secondary: var(--orange-500);
    --bg-secondary-hover: var(--orange-600);
    --bg-secondary-subtle: var(--orange-100);
    --bg-secondary-level1: var(--orange-200);
    --bg-secondary-level2: var(--orange-300);

    --bg-base: var(--white);
    --bg-level1: var(--gray-50);
    --bg-level2: var(--navy-gray-100);
    --bg-level3: var(--navy-gray-200);
    --bg-level4: var(--navy-gray-600);
    
    --bg-error: var(--red-500);
    --bg-error-subtle: var(--red-100);
    --bg-success: var(--green-500);
    --bg-success-subtle: var(--green-100);
    --bg-warning: var(--yellow-500);
    --bg-warning-subtle: var(--yellow-100);
    --bg-info: var(--blue-500);
    --bg-info-subtle: var(--blue-100);

    /* ICONs */
    --icon-primary: var(--navy-500);
    --icon-primary-strong: var(--navy-600);
    --icon-secondary: var(--orange-500);
    --icon-secondary-strong: var(--orange-600);

    --icon-link: var(--color-nav);
    --icon-link-hover: var(--color-nav-hover);

    --icon-main: var(--navy-gray-900);
    --icon-strong: var(--navy-gray-700);
    --icon-subtle: var(--navy-gray-600);
    --icon-disabled: var(--navy-gray-400);
    --icon-oncolor: var(--white);

    --icon-error: var(--red-500);
    --icon-error-strong: var(--red-600);
    --icon-success: var(--green-500);
    --icon-success-strong: var(--green-600);
    --icon-warning: var(--yellow-500);
    --icon-warning-strong: var(--yellow-600);
    --icon-info: var(--blue-500);
    --icon-info-strong: var(--blue-600);


    --icon-xs: var(--space-16);
    --icon-sm: var(--space-20);
    --icon-md: var(--space-24);
    --icon-lg: var(--space-26);
    --icon-xl: var(--space-28);
    --icon-xxl: var(--space-32);
    --icon-xxxl: var(--space-36);


    /* BORDERS */
    --border-primary: var(--navy-500);
    --border-primary-hover: var(--navy-600);
    --border-primary-subtle: var(--navy-300);

    --border-secondary: var(--orange-500);
    --border-secondary-hover: var(--orange-600);
    --border-secondary-subtle: var(--orange-300);

    --border-strong: var(--navy-gray-300);
    --border-strong-hover: var(--navy-gray-400);
    --border-subtle: var(--navy-gray-200);
    --border-subtle-hover: var(--navy-gray-300);
    --border-disabled: var(--navy-gray-100);
    
    --border-error: var(--red-500);
    --border-error-hover: var(--red-600);
    --border-error-subtle: var(--red-300);

    --border-success: var(--green-500);
    --border-success-hover: var(--green-600);
    --border-success-subtle: var(--green-300);

    --border-warning: var(--yellow-500);
    --border-warning-hover: var(--yellow-600);
    --border-warning-subtle: var(--yellow-300);

    --border-info: var(--blue-500);
    --border-info-hover: var(--blue-600);
    --border-info-subtle: var(--blue-300);

    --border-radius-xxs: 4px;
    --border-radius-xs: 8px;
    --border-radius-sm: 12px;
    --border-radius-md: 16px;
    --border-radius-full: 999px;

    --border-width-xs: .5px;
    --border-width-sm: 1px;
    --border-width-md: 1.5px;
    --border-width-lg: 2px;
    --border-width-xl: 4px;
    --border-width-xxl: 6px;

    
    /* TEXTS */
    --text-primary: var(--navy-500);
    --text-primary-strong: var(--navy-600);
    --text-secondary: var(--orange-500);
    --text-secondary-strong: var(--orange-600);

    --text-link: var(--color-nav);
    --text-link-hover: var(--color-nav-hover);

    --text-main: var(--navy-gray-900);
    --text-strong: var(--navy-gray-700);
    --text-subtle: var(--navy-gray-600);
    --text-disabled: var(--navy-gray-400);
    --text-oncolor: var(--white);

    --text-error: var(--retext-secondary col cc-h pointer bg-gray-250 py-2 text-center height border-fd-500);
    --text-error-strong: var(--red-600);
    --text-success: var(--green-500);
    --text-success-strong: var(--green-600);
    --text-warning: var(--yellow-600);
    --text-info: var(--blue-500);
    --text-info-strong: var(--blue-600);

    /* TIPOGRAPHY ---------------------------------------------- */
    --display-xl:600 3.5rem/120% var(--basefontFamily-brand);
    --display-lg:600 3.25rem/120% var(--basefontFamily-brand);
    --display-md:600 3rem/120% var(--basefontFamily-brand);
    --display-sm:600 2.75rem/120% var(--basefontFamily-brand);
    --display-xs:600 2.5rem/120% var(--basefontFamily-brand);
    --display-xxs:600 2.25rem/120% var(--basefontFamily-brand);

    --title-xl:600 2rem/120% var(--basefontFamily-brand);
    --title-lg:600 1.75rem/120% var(--basefontFamily-brand);
    --title-md:600 1.5rem/120% var(--basefontFamily-brand);
    --title-sm:600 1.25rem/120% var(--basefontFamily-brand);
    --title-xs:600 1.125rem/120% var(--basefontFamily-brand);
    --title-xxs:600 1rem/120% var(--basefontFamily);

    --body:400 1rem/150% var(--basefontFamily);
    --body-bold:500 1rem/150% var(--basefontFamily);
    --body-italic:400 1rem/150% var(--basefontFamily);
    --body-strikethrough:400 1rem/150% var(--basefontFamily);

    --body-sm:400 0.875rem/150% var(--basefontFamily);
    --body-sm-bold:500 0.875rem/150% var(--basefontFamily);
    --body-sm-italic:400 0.875rem/150% var(--basefontFamily);
    --body-sm-strikethrough:400 0.875rem/150% var(--basefontFamily);

    --body-xs:400 0.75rem/150% var(--basefontFamily);
    --body-xs-bold:500 0.75rem/150% var(--basefontFamily);
    --body-xs-italic:400 0.75rem/150% var(--basefontFamily);
    --body-xs-strikethrough:400 0.75rem/150% var(--basefontFamily);

    --tight:400 1rem/100% var(--basefontFamily);
    --tight-bold:500 1rem/100% var(--basefontFamily);
    --tight-italic:400 1rem/100% var(--basefontFamily);
    --tight-strikethrough:400 1rem/100% var(--basefontFamily);

    --tight-sm:400 0.875rem/100% var(--basefontFamily);
    --tight-sm-bold:500 0.875rem/100% var(--basefontFamily);
    --tight-sm-italic:400 0.875rem/100% var(--basefontFamily);
    --tight-sm-strikethrough:400 0.875rem/100% var(--basefontFamily);

    --tight-xs:400 0.75rem/100% var(--basefontFamily);
    --tight-xs-bold:500 0.75rem/100% var(--basefontFamily);
    --tight-xs-italic:400 0.75rem/100% var(--basefontFamily);
    --tight-xs-strikethrough:400 0.75rem/100% var(--basefontFamily);

    /* ELEVATION ---------------------------------------------- */
    --shadow-100: 0 calc(0.5px * var(--elevation-level-1)) calc(1px * var(--elevation-level-1)) hsla(var(--elevation-hsl), var(--elevation-lowlevel-opacity-1)), 0 calc(0.5px * var(--elevation-level-1)) calc(1px * var(--elevation-level-1)) hsla(var(--elevation-hsl), var(--elevation-lowlevel-opacity-1));
    --shadow-200: 0 calc(0.5px * var(--elevation-level-2)) calc(1px * var(--elevation-level-2)) hsla(var(--elevation-hsl), var(--elevation-lowlevel-opacity-1)), 0 calc(0.5px * var(--elevation-level-2)) calc(1px * var(--elevation-level-2)) hsla(var(--elevation-hsl), var(--elevation-lowlevel-opacity-1));
    --shadow-300: 0 calc(0.5px * var(--elevation-level-3)) calc(1px * var(--elevation-level-3)) hsla(var(--elevation-hsl), var(--elevation-lowlevel-opacity-1)), 0 calc(0.5px * var(--elevation-level-3)) calc(1px * var(--elevation-level-3)) hsla(var(--elevation-hsl), var(--elevation-lowlevel-opacity-1));
    --shadow-400: 0 calc(0.5px * var(--elevation-level-4)) calc(1px * var(--elevation-level-4)) hsla(var(--elevation-hsl), var(--elevation-highlevel-opacity-1)), 0 0 8px hsla(var(--elevation-hsl), var(--elevation-highlevel-opacity-2));
    --shadow-500: 0 calc(0.5px * var(--elevation-level-5)) calc(1px * var(--elevation-level-5)) hsla(var(--elevation-hsl), var(--elevation-highlevel-opacity-1)), 0 0 8px hsla(var(--elevation-hsl), var(--elevation-highlevel-opacity-2));
}