﻿.filters {
	position: fixed;
	z-index: 98;
	font-size: 18px;
	font-weight: 500;
	color: #003b98;

	.filter {
		cursor: pointer;

		i {
			font-size: 24px;
		}
	}

	&.one-way {
		top: auto;
		bottom: 0px;
		left: 0px;
		background: white;
		box-shadow: rgba(0, 0, 0, 0.15) 0px -8px 16px 0px;

		.filter {
			width: 100%;
			padding: 1.5rem 0;
		}
	}

	&.round {
		top: auto;
		bottom: 84px;

		.filter {
			background: #f0f9ff;
			border-radius: 50px;
			border: 1px solid #003b98;
			width: 85%;
			padding: .5rem 0;
		}
	}
}

#filters-modal {
	z-index: 1110;

	.modal-header {
		button {
			i {
				font-size: 32px;
			}
		}
	}

	.filter-title {
		font-weight: 500;
	}
}

.small-mobile {
	display: none !important;
}
