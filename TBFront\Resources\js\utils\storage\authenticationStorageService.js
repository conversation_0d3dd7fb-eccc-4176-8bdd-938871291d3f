import CryptoJS from 'crypto-js';

const AuthenticationStorageService = (() => {
    let service;
    const tokenKey = 'authenticationPackageToken';

    function getService() {
        if (!service) {
            service = this;

            return service;
        }

        return service;
    }

    function setToken(tokenObj) {
        const { authenticationResult } = tokenObj;
        const now = new Date();
        const tokenItem = {
            value: authenticationResult.token,
            expiry: now.getTime() + authenticationResult.expireIn * 1000
        };
        const tokenEncrypted = encryptToken(tokenItem);

        localStorage.setItem(tokenKey, tokenEncrypted);
    }

    function getToken() {
        try {
            const itemStr = localStorage.getItem(tokenKey);

            if (!itemStr) {
                return null;
            }

            const tokenDecrypted = decryptToken(itemStr);
            const tokenItem = JSON.parse(tokenDecrypted);
            const now = new Date();

            if (now.getTime() > tokenItem.expiry) {
                clearToken();

                return null;
            }

            return tokenItem.value;
        } catch (e) {
            clearToken();

            return null;
        }
    }

    function clearToken() {
        localStorage.removeItem(tokenKey);
    }

    function encryptToken(token) {
        const encryptWordArray = CryptoJS.enc.Utf8.parse(JSON.stringify(token));
        const encryptBase64 = CryptoJS.enc.Base64.stringify(encryptWordArray);

        return encryptBase64;
    }

    function decryptToken(tokenEncrypted) {
        const decryptWordArray = CryptoJS.enc.Base64.parse(tokenEncrypted);
        const token = decryptWordArray.toString(CryptoJS.enc.Utf8);

        return token;
    }

    return {
        getService,
        setToken,
        getToken,
        clearToken
    };
})();

export default AuthenticationStorageService;
