﻿using TBFront.Models.Flight.Quote;
using TBFront.Models.Request;
using TBFront.Models.Common;
using TBFront.Options;
using TBFront.Models.Flight.CreateBooking;
using TBFront.Models;
using TBFront.Types;
using TBFront.Models.Flight.Revalidate;
using TBFront.Models.Response;
using TBFront.Models.PaymentGateway;
using TBFront.Models.Flight.Summary;
using TBFront.Models.Flight.BLink.Request;
using TBFront.Models.Flight.BLink;

namespace TBFront.Application.Mappers
{
    public class CreateBookingMapper
    {
        private static readonly string _defaultIP = "127.0.0.1";


        public static CheckoutBookingResponse Response(CheckoutBookingRequest request, PaymentGatewayResponse paymentGatewayResponse, CreateBookingResponse createbookingResponse, SettingsOptions options, string id, string email, SummaryResponse summaryResponse)
        {
            var response = new CheckoutBookingResponse
            {
                Id = createbookingResponse.Id,
                Detail = CreateBookingMapper.ChangeAmount(createbookingResponse, options),
                FlightInfo = SummaryMapper.FlightInfo(summaryResponse, request) //Validar cambios de vuelos a lo que reservo el cliente
            };


            if (paymentGatewayResponse.IsSuccess)
            {
                response.UrlRedirect = paymentGatewayResponse.RedirectUrl;
            }
            else
            {
                response.UrlRedirect = GetLinkVoucher(id, email, options);
            }


            return response;
        }

        public static CreateBookingRequest CreateBooking(SettingsOptions options, CheckoutBookingRequest request, string ip)
        {
            var req = new CreateBookingRequest
            {
                ExternalId = "",
                KeyValidation = request.Revalidate.QuoteTaskID,
                ReservationSettings = ReservationSetting(request.Quote.Currency, request.Quote.LanguageCode, ip, options),
                CustomerInformation = CustomerInformation(request.Customer),
                ServiceItems = ServiceItem(request, options),
                Channel = request.Quote.ChannelId,
                ExtraInformation = ExtraInformation(ExtraInformationType.FrontFingerprint, request.FingerprintHash)
            };
            return req;
        }

        public static BLinkRequest BookingRequestToBLinkRequest(CheckoutBookingRequest request, SettingsOptions options, string ip)
        {
            var bookingRequest = new BLinkRequest
            {
                ChannelId = request.Quote.ChannelId,
                KeyValidation = Guid.NewGuid().ToString(),
                OrganizationId = request.Quote.Organization,
                ServiceType = 100,
                TotalCost = request.Quote.Rate.Cost,
                TotalAmount = request.Quote.Rate.TotalAmount,
                PayLoad = BookingRequestToBLinkPayload(request, options),
                ExternalProvider = 0 //TODO no se que es 
            };

            return bookingRequest;
        }

        private static BookingBlink BookingRequestToBLinkPayload(CheckoutBookingRequest request, SettingsOptions options)
        {
            var bookingInfo = new BookingBlink();

            bookingInfo.BookingInfo.CustomerInfo = CustomerRequestToCustomerInfo(request.Customer);
            bookingInfo.BookingInfo.AffiliateSettings = GetAffiliate(options);
            bookingInfo.BookingInfo.BookingSettings = BookingRequestToBookingSettings(request.Quote, request.ClientIp, request.FingerprintHash);

            return bookingInfo;
        }

        private static CustomerInfo CustomerRequestToCustomerInfo(Customer customer)
        {

            var passenger = customer.PassengersBooking?.FirstOrDefault()?.Passengers?.FirstOrDefault();
            var customerInfo = new CustomerInfo();
            var phoneNumber = $"({customer.DialCode}) {customer.Phone}";

            customerInfo.FirstName = passenger.Firstname ?? string.Empty;
            customerInfo.LastName = passenger.Lastname ?? string.Empty;
            customerInfo.Email = customer.Email;
            customerInfo.Phone = phoneNumber;
            customerInfo.MobilePhone = phoneNumber;

            return customerInfo;
        }

        private static AffiliateSettings GetAffiliate(SettingsOptions options)
        {
            return new AffiliateSettings
            {
                AffiliateId = options.AffiliateId,
                AffiliateSiteId = options.AffiliateSiteId,
            };
        }

        private static BookingSettings BookingRequestToBookingSettings(QuoteApiResponse request, string ipClient, string token)
        {
            var remoteIpAddress = GetCustomerIp(ipClient);

            var bookingSettings = new BookingSettings();

            bookingSettings.Currency = request.Currency;
            bookingSettings.Language = request.Culture;
            bookingSettings.ChannelId = request.ChannelId;
            bookingSettings.SiteId = request.SiteId;
            bookingSettings.IsPackage = false;
            bookingSettings.AdminUserIdCreatedBy = 0;
            bookingSettings.SendEmail = true;
            bookingSettings.CampaignId = 0;
            bookingSettings.CampaignToken = "";
            bookingSettings.IpAddress = remoteIpAddress;
            bookingSettings.NoteTitle = $"Checkout Frontend";
            bookingSettings.Note = $"Checkout Frontend";
            bookingSettings.Tags.Add(BookingType.EarlyBooking);
            bookingSettings.Tags.Add(BookingType.EarlyBookingInProgress);

            bookingSettings.ExtraInfo = new List<ExtraInfo>
            {
                new() {
                    Type = 31,
                    Info = token
                }
            }; ;


            return bookingSettings;
        }

        private static string GetCustomerIp(string ipRemote)
        {
            if (string.IsNullOrEmpty(ipRemote))
            {
                return _defaultIP;
            }

            var ipList = ipRemote.Split(',');
            ipRemote = ipList.FirstOrDefault();

            if (string.IsNullOrEmpty(ipRemote))
            {
                ipRemote = _defaultIP;
            }

            return ipRemote;
        }


        public static TotalBooking ChangeAmount(CreateBookingResponse response, SettingsOptions options)
        {
            var result = response.AmountTotal - (response.OldTotalAmount != 0 ? response.OldTotalAmount : response.AmountTotal);
            return new TotalBooking
            {
                ChangeAmount = Math.Abs(result) > options.RangeTotalAmountCreateBooking,
                TotalAmount = response.AmountTotal,
                TotalAmountOld = response.OldTotalAmount
            };
        }

        public static string GetLinkVoucher(string id, string email, SettingsOptions _options)
        {
            return $"{_options.SiteUrl}{_options.RedirectToPath}?id={id}&em={email}";
        }

        private static ReservationSetting ReservationSetting(string currency, string lang, string ip, SettingsOptions site)
        {
            var clientIp = string.IsNullOrEmpty(ip) ? _defaultIP : ip.Split(',').FirstOrDefault();
            return new ReservationSetting
            {
                Currency = currency,
                Language = lang,
                IpAddress = clientIp,
                OrganizationId = site.OrganizationId,
            };
        }
        private static CustomerInformation CustomerInformation(Customer customer)
        {
            var owner = customer.PassengersBooking.First().Passengers.First();
            return new CustomerInformation
            {
                FirstName = owner.Firstname,
                LastName = owner.Lastname,
                Email = customer.Email,
                Phone = $"({customer.DialCode}) {customer.Phone}",
                MobilePhone = $"({customer.DialCode}) {customer.Phone}",
            };
        }
        private static List<ExtraInformation> ExtraInformation(ExtraInformationType type, string value)
        {
            return [
                new()
                {
                    Type = (int)type,
                    Value = value
                }
            ];
        }

        private static ServiceFlightItem ServiceItem(CheckoutBookingRequest request, SettingsOptions site)
        {
            return new ServiceFlightItem
            {
                TotalAmount = request.Quote.Rate.TotalAmount,
                Flights = ServiceItemFlight(request, site)
            };
        }

        private static List<Models.Flight.CreateBooking.FlightItem> ServiceItemFlight(CheckoutBookingRequest request, SettingsOptions site)
        {
            var rate = request.Quote.Rate;
            var quote = request.Quote;
            var paxes = request.Quote.Paxes;
            var flightItem = new List<Models.Flight.CreateBooking.FlightItem>();
            var Passengers = request.Customer.PassengersBooking.FirstOrDefault().Passengers;

            foreach (KeyValuePair<int, RevalidateOptions> RevalidateOption in request.RevalidateOptions)
            {
                var childAges = Passengers.Select(c => (int)c.Age).Where(c => c >= 2 && c < 12).ToList();
                var infants = Passengers.Select(c => (int)c.Age).Where(c => c < 2).ToList();
                flightItem.Add(new Models.Flight.CreateBooking.FlightItem
                {
                    TripCabin = 0,
                    FareKey = "",
                    Seniors = 0,
                    PromotionalCode = "",
                    IsPackageRate = false,
                    Type = 2,
                    GetAllFares = false,
                    NonStopOnly = false,
                    TripMode = Convert.ToInt32(rate.IsRoundtrip),
                    Adults = (int)quote.Adults,
                    Infants = infants.Count,
                    Infant = infants.Count,
                    ChildAges = childAges,
                    TotalAmount = (double)RevalidateOption.Value.ta,
                    FlightPassengers = PassengerItems(request.Customer),
                    Revalidate = RevalidateCreateBookingRequest(request, site, RevalidateOption.Value)
                });
            }




            return flightItem;
        }


        private static List<PassengerItem> PassengerItems(Customer customer)
        {
            var passengers = new List<PassengerItem>();

            foreach (var passengersBooking in customer.PassengersBooking)
            {
                var passengerRequest = new List<PassengerRequest>();
                foreach (var passenger in passengersBooking.Passengers)
                {
                    passengers.Add(new PassengerItem
                    {
                        Title = passenger.Prefix.ToUpper(),
                        Type = TypePassengerItem(passenger.Type, passenger.Age),
                        Names = passenger.Firstname,
                        LastNames = passenger.Lastname,
                        RequireAdditionalUsaInformation = true,
                        Sex = (int)passenger.Gender,
                        BirthDateDay = Convert.ToInt32(passenger.Day),
                        BirthDateMonth = Convert.ToInt32(passenger.Month),
                        BirthDateYear = Convert.ToInt32(passenger.Year),
                        Nationality = passenger.Nationality.ToUpper(),
                        CustomerIdentityDocumentType = !string.IsNullOrEmpty(passenger.IdentityDocument) ? 4 : 0,
                        CustomerDocumentNumber = !string.IsNullOrEmpty(passenger.IdentityDocument) ? passenger.IdentityDocument.ToString() : "",
                        FrequentFlyerNumber = "",
                        FrequentFlyerProgram = "",
                    });
                }

            }
            return passengers;
        }

        public static int TypePassengerItem(string passenger, int? age)
        {
            if (string.Equals(passenger, "Adults", StringComparison.OrdinalIgnoreCase))
            {
                return BookingType.Adult;
            }
            else if (string.Equals(passenger, "Minors", StringComparison.OrdinalIgnoreCase))
            {
                if (age < 2)
                { return 3; }
                else { return BookingType.Children; }
            }


            return 0;
        }


        private static RevalidateCreateBookingRequest RevalidateCreateBookingRequest(CheckoutBookingRequest request, SettingsOptions site, RevalidateOptions revalidateOptions)
        {
            var quote = request.Quote;
            /*var revalidate = request.Revalidate;*/

            var revalidateRequest = new RevalidateCreateBookingRequest();
            /*var recommedation = Recommendation(revalidate);

            revalidateRequest.IsPackage = false;
            revalidateRequest.Currency = request.Quote.Currency;
            revalidateRequest.ShowDetailAmounts = true;
            revalidateRequest.ShowRevenueByLeg = true;
            revalidateRequest.BookingInfos = revalidate.BookingInfos;
            revalidateRequest.Recommendation = recommedation;
            revalidateRequest.ValidatingCarrierCode = revalidateRequest.Recommendation.ValidatingCarrier.Code;

            revalidateRequest.Passengers = PassengerItemsRevalidate(request.Customer);
            revalidateRequest.Flights = FlightRevalidate(revalidate, quote.Rate.IsRoundtrip);
            revalidateRequest.Fares = FareRevalidate(revalidate, quote.Rate.IsRoundtrip);*/


            revalidateRequest.TaskID = revalidateOptions.QuoteTaskID;
            revalidateRequest.FareKey = revalidateOptions.FareKey;
            revalidateRequest.Context = ContextOrganization(quote.ChannelId, site);
            return revalidateRequest;
        }


        private static List<PassengerBooking> PassengerItemsRevalidate(Customer customer)
        {
            var passengers = new List<PassengerBooking>();

            foreach (var passengersBooking in customer.PassengersBooking)
            {
                var passengerRequest = new List<PassengerRequest>();
                foreach (var passenger in passengersBooking.Passengers)
                {
                    passengers.Add(new PassengerBooking
                    {
                        Type = string.Equals(passenger.Type, "Adults", StringComparison.OrdinalIgnoreCase) ? 0 : 1,
                        Quantity = 1,
                        Age = 0
                    });
                }

            }
            return passengers;
        }

        private static Recommendation Recommendation(RevalidateRootResponse revalidate)
        {
            Recommendation recommendation;

            if (revalidate.Recommendations.TryGetValue("1", out recommendation))
            {
                return recommendation;
            }

            return new Recommendation();
        }

        private static Context ContextOrganization(int channel, SettingsOptions site)
        {
            return new Context
            {
                ClientID = 4,
                ChannelID = channel,
                OrganizationID = site.OrganizationContent
            };
        }

        private static string FareKeyByRecommendation(Recommendation recommendation)
        {
            var fare = recommendation.FareCombinables.FirstOrDefault();

            if (fare is not null)
            {
                return fare.FareKey;
            }

            return string.Empty;
        }

        private static Dictionary<string, Flight> FlightRevalidate(RevalidateRootResponse revalidate, bool isRoundtrip)
        {
            var fligthItinerary = new Dictionary<string, Flight>();
            var flightRevalidate = revalidate.Flights;

            fligthItinerary.Add("1", flightRevalidate["1"]["1"]);

            if (isRoundtrip)
            {
                fligthItinerary.Add("2", flightRevalidate["2"]["1"]);

            }

            return fligthItinerary;
        }

        private static Dictionary<string, FareLeg> FareRevalidate(RevalidateRootResponse revalidate, bool isRoundtrip)
        {
            var fareItinerary = new Dictionary<string, FareLeg>();
            var fareRevalidate = revalidate.FaresByLeg;

            fareItinerary.Add("1", fareRevalidate["1"]["1"]);

            if (isRoundtrip)
            {
                fareItinerary.Add("2", fareRevalidate["2"]["1"]);
            }

            return fareItinerary;
        }
    }
}
