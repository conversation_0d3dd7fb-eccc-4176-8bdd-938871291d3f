<template>
	<div>

		<section v-if="typee === 'origin'" class="d-md-none hide-md hide-lg d-none">


			<div v-if="typee === 'origin'" class="d-block d-md-none mobile-search-container px-3"
				style="margin-top: -15px; position: relative; z-index: 1100;">
				<div v-if="!showMobileFlightBooker" class="mobile-search-box bg-white rounded-lg shadow-lg p-3">
					<div class="search-trigger d-flex justify-content-between align-items-center w-100"
						@click="showMobileFlightBooker = !showMobileFlightBooker">
						<span class="mb-0 font-16" style="color: #1D81EF;">{{ __('promotions.searchFlight') }}</span>
						<i class="icon icon-search"></i>
					</div>

				</div>
			</div>

		</section>






		<section v-if="typee === 'destination'" class="c-box-promo position-relative container-fluid pb-4 destination"
			style="background-size: cover;" v-bind:style="{ backgroundImage: 'url(' + backgroundImage + ')' }">
			<div class="container position-relative p-0 cbp-title">



				<div v-if="typee === 'destination'" class="position-relative w-100 cp-destination z-1">

					<div class="overlay-content start-0 w-100 h-100 d-flex flex-column justify-content-center px-3"
						style="top: -7px;">
						<div class="row">
							<div class="col-12 px-0">
								<h1 class="pt-4 mb-md-0 chd-1">
									<span class="font-poppins fw-100">{{ __('promotions.destinationTitle')
										}}</span><br />
									<span class="font-poppins-semibold">{{ getSelected.name }}</span>
								</h1>
							</div>
						</div>

					</div>



				</div>

				<div v-if="typee === 'destination'" class="hide-md mt-3 hide-lg mobile-search-container px-0"
					style="margin-top: -15px; position: relative; z-index: 1100;">
					<div v-if="!showMobileFlightBooker" class="mobile-search-box bg-white rounded-lg shadow-lg p-3">
						<div class="search-trigger d-flex justify-content-between align-items-center w-100"
							@click="showMobileFlightBooker = !showMobileFlightBooker">
							<span class="mb-0 font-16" style="color: #1D81EF;">{{ __('promotions.searchFlight')
								}}</span>
							<i class="icon icon-search"></i>
						</div>

					</div>
				</div>

				<div class="container mt-none mt-3 mt-md-3 mb-3 mb-md-none px-0 container-booker z-1 position-relative">
					<flight-booker v-show="!isMobile || showMobileFlightBooker" :is-home-landing="true"></flight-booker>
				</div>


			</div>
		</section>





		<section v-if="typee === 'origin'" class="c-box-promo position-relative container-fluid pb-4 origin"
			v-lazy:bg="`/assets-tb/img/promociones/bg-promociones.jpg`">
			<div class="container position-relative p-0 cbp-title">
				<div class="col-12 p-0 ml-auto text-left">
					<h1 class="pt-4 font-poppins font-60 ch-1">{{ __('promotions.originTitle') }}</h1>
					<h2 class="mb-3 mb-md-0 font-30 pe-5 pe-md-0 font-poppins-semibold">{{
						__('promotions.originSubtitle')
					}}</h2>
				</div>
				<div v-if="typee === 'origin'" class="hide-md hide-lg mobile-search-container px-0 mt-3"
					style="margin-top: -15px; position: relative; z-index: 1100;">
					<div v-if="!showMobileFlightBooker" class="mobile-search-box bg-white rounded-lg shadow-lg p-3">
						<div class="search-trigger d-flex justify-content-between align-items-center w-100"
							@click="showMobileFlightBooker = !showMobileFlightBooker">
							<span class="mb-0 font-16" style="color: #1D81EF;">{{ __('promotions.searchFlight')
								}}</span>
							<i class="icon icon-search"></i>
						</div>

					</div>
				</div>
				<div class="container mt-none mt-md-3 mb-3 mb-md-none px-0 container-booker">
					<flight-booker v-show="!isMobile || showMobileFlightBooker" :is-home-landing="true"></flight-booker>
				</div>
			</div>		
		</section>

		<section class="container-fluid cp-banner-offers py-3">
			<div class="container">
				<!-- <span class="font-bold font-16">{{ __('promotions.travelfrom') }}</span> -->
				<span class="font-bold font-16">
					{{ typee === 'origin' ? __('promotions.travelfrom') : __('promotions.travelto') }}
				</span>


				<div class="row pt-2">
					<div class="col-12">
						<div class="d-flex flex-nowrap align-items-center overflow-auto gap-2">
							<!-- Select de Nacionales -->
							<div class="d-inline-flex align-items-center bg-white rounded-pill shadow-sm px-3 py-2 border"
								style="border: 1px solid #186BDF;">
								<select v-model="selected" class="form-select border-0 bg-transparent w-auto fw-bold"
									@change="changeLocation">
									<optgroup :label="__('promotions.nationalTitle')">
										<option v-for="item in groupedOrigins.nacionales" :value="item.code"
											:key="`nacional-${item.code}`">
											{{ item.name }}
										</option>
									</optgroup>

									<optgroup :label="__('promotions.internationalTitle')">
										<option v-for="item in groupedOrigins.internacionales" :value="item.code"
											:key="`internacional-${item.code}`">
											{{ item.name }}
										</option>
									</optgroup>
								</select>
							</div>


						</div>
					</div>
				</div>
			</div>
		</section>
	</div>
</template>

<script>
import { storeToRefs } from 'pinia';
import { __ } from '../../../utils/helpers/translate';
import { usePromotionStore } from '../../stores/promotion';
import { ref } from 'vue';

const configSite = {};
const showMobileFlightBooker = ref(false);

export default {
	data() {
		return {
			config: configSite,
			selected: (this.selectedOrigin || "BOG").toUpperCase(),
			showMobileFlightBooker: false,
			isMobile: false,
		}
	},
	props: {
		destinations: { type: Array, default: [] },
		typee: { type: String, default: "" },
		selectedOrigin: { type: String, default: "" },
	},
	computed: {
		backgroundImage() {
			return `https://tiquetesbaratos.com/front-vuelos/landings/img/${this.getSelected.code}.jpg`
		},
		groupedOrigins() {
			return {
				nacionales: this.getOrigins
					.filter(item => item.isNational)
					.sort((a, b) => a.name.localeCompare(b.name)),
				internacionales: this.getOrigins
					.filter(item => !item.isNational)
					.sort((a, b) => a.name.localeCompare(b.name))
			};
		}
	},
	setup() {
		const usePromotion = usePromotionStore();
		const { setOriginSelected, setPromotionsList, setSelected } = usePromotion;
		const { getOrigins, getSelected, getPromotions } = storeToRefs(usePromotion);

		return { setOriginSelected, getOrigins, setPromotionsList, getSelected, getPromotions, setSelected }
	},
	mounted() {
		this.checkIfMobile();
		window.addEventListener('resize', this.checkIfMobile);
		this.setPromotionsList(this.destinations);

		const queryString = window.location.search;
		const urlParams = new URLSearchParams(queryString);
		const code = urlParams.get('codigo');
		if (this.selectedOrigin != "" && this.selectedOrigin != null) {

			const indexDestination = this.destinations.findIndex(
				(promotion) => promotion.code.toLowerCase() === this.selectedOrigin.toLowerCase()
			);

			const objectSelected = {
				name: this.destinations[indexDestination].name,
				code: this.destinations[indexDestination].code,
			}

			this.setSelected(objectSelected);
			this.changeLocation();
		}

		if (code != null) {
			this.selected = code.toUpperCase();
			this.changeLocation();
		}
	},
	beforeUnmount() {
		window.removeEventListener('resize', this.checkIfMobile);
	},
	methods: {
		changeLocation() {

			this.setOriginSelected(this.selected);

			const indexDestination = this.getPromotions.findIndex((promotion) => promotion.code.toLowerCase() === this.selected.toLowerCase());
			const objectSelected = {
				name: this.getPromotions[indexDestination].name,
				code: this.getPromotions[indexDestination].code,
			}

			this.setSelected(objectSelected);
		},
		checkIfMobile() {
			this.isMobile = window.innerWidth <= 767;
		},
	}
}
</script>
<style>
.custom-select-icon {
	appearance: none;
	-webkit-appearance: none;
	-moz-appearance: none;
	background-image: url('data:image/svg+xml;utf8,<svg fill="%23186BDF" height="20" viewBox="0 0 24 24" width="20" xmlns="http://www.w3.org/2000/svg"><path d="M7 10l5 5 5-5z"/></svg>');
	background-repeat: no-repeat;
	background-position: right 0.75rem center;
	background-size: 1rem;
	padding-right: 2rem;
	/* espacio para el ícono */
}

.form-select:focus {
	box-shadow: none !important;
	border-color: initial;
}

.card.booker {
	margin-top: 5px !important;

	@media (max-width: 767px) {
		margin-top: 25px !important;
	}

	.card-body {
		@media (max-width: 767px) {
			margin-left: 0 !important;
			margin-right: 0 !important;
			padding-left: 0 !important;
			padding-right: 0 !important;
		}
	}
}



@media (max-width: 767px) {
	.cbp-title {
		.ch-1 {
			font-size: 34px !important;
			margin-bottom: 0;
		}

		.chd-1 {
			font-size: 30px !important;
			margin-bottom: 0;
		}
	}

	.cbp-title {
		h2 {
			font-size: 20px !important;
			margin-bottom: 0 !important;
		}
	}

	.mobile-search-container {
		margin-bottom: 0 !important;
	}

	.c-box-promo.destination {
		background: #ffcd00 !important;
	}
}
</style>