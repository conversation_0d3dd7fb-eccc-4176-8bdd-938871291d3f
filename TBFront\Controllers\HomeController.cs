﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System.Text.Json;
using TBFront.Application.Mappers;
using TBFront.Helpers;
using TBFront.Interfaces;
using TBFront.Mappers;
using TBFront.Models.Configuration;
using TBFront.Models.Places.Request;
using TBFront.Models.Request;
using TBFront.Options;
using TBFront.Services;
using TBFront.Types;

namespace TBFront.Controllers
{
    public class HomeController : Controller
    {
        private readonly ILogger<HomeController> _logger;
        private readonly IMKTCollectionHandler _mktCollectionHandler;
        private readonly IPlaceHandler _placeHandler;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IUserHandler _userHandler;
        private readonly SettingsOptions _options;
        private readonly ViewHelper _helper;
        private readonly HashService _hash;
        private readonly List<string> _optionsRedirect = new List<string> { "com_respuestapago", "com_promociones_tiquetes_aereos2" };
        private readonly IGrowthBookHandler _growthBookHandler;
        private readonly ICommonHandler _commonHandler;
        private readonly IAlternateHandler _alternateHandler;
        private readonly LocalizerHelper _localizer;
        public HomeController(ILogger<HomeController> logger,
            IHttpContextAccessor httpContextAccessor,
            IMKTCollectionHandler mktCollectionHandler,
            IPlaceHandler placeHandler,
            IOptions<SettingsOptions> options,
            ViewHelper helper,
            HashService hash,
            IGrowthBookHandler growthBookHandler,
            ICommonHandler commonHandler,
            IAlternateHandler alternateHandler,
            LocalizerHelper localizer,
            IUserHandler userHandler)
        {
            _logger = logger;
            _mktCollectionHandler = mktCollectionHandler;
            _placeHandler = placeHandler;
            _options = options.Value;
            _helper = helper;
            _httpContextAccessor = httpContextAccessor;
            _hash = hash;
            _commonHandler = commonHandler;
            _alternateHandler = alternateHandler;
            _localizer = localizer;
            _userHandler = userHandler;
        }

        [Route("/")]
        [Route("{culture}/")]
        [Route("/vuelos")]
        [Route("/vuelos/aerolineas/{airlineName}")]
        [Route("/aerolineas/{airlineName}")]
        [Route("{culture}/vuelos")]
        [Route("{culture}/vuelos/aerolineas/{airlineName}")]
        [Route("{culture}/aerolineas/{airlineName}")]
        [Route("{culture}/flights")]
        [Route("/{culture}/flights/airlines/{airlineName}")]
        [Route("/{culture}/airlines/{airlineName}")]
        [HttpGet]
        [HttpPost]
        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> Index(string? airlineName, string culture, FlightParamsRequest flightRequest) 
        {
            try
            {
                /***
                 * Funcionalidad para darle soporte a todas las urls que vengan directamente con index.php 
                 ***/
                if (flightRequest.View is not null || (flightRequest.Option is not null && _optionsRedirect.Contains(flightRequest.Option)))
                {
                    return Redirects(flightRequest);
                }
                /*****/
                var route = HomeMapper.Path(Request.Path.Value ?? "");
                var path = HomeMapper.GetPath(route);


                using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(60000));
                var page = airlineName ?? "home";
                var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
                var request = FlightParamsHelper.GetFlightRequest(flightRequest, _options, userSelection.Culture.InternalCultureCode);
                var mktRequest = new Models.MKTCollection.Request.MTKCollectionRequest(flightRequest.Cache, airlineName, culture ?? _options.Culture);
                var collections = await _mktCollectionHandler.QueryAsync(mktRequest, cts.Token);
                var section = CollectionMapper.Collection(collections, airlineName);
                var alternates = await _alternateHandler.QueryAsync(new PlaceRequest { Culture = culture!, Id = 0, Path = airlineName != null && airlineName != "" ?  airlineName : path, Route = path, Type = airlineName != null && airlineName != "" ? PageType.Airlines : PageType.Home }, cts.Token);

                var meta = MetaMapper.Home(_options, _helper, userSelection, _localizer, page, route, path);
                var user = await _userHandler.QueryAsync(new Models.User.UserRequest() { }, cts.Token);
                if (meta.Title.Contains("meta_title"))
                {
                    return ErrorPage($"Error de contenido", 404);
                }

                ViewData["Alternates"] = alternates;
                ViewData["airline"] = airlineName;
                ViewData["request"] = request;
                ViewData["section"] = section;
                ViewData["isDestinations"] = false;
                ViewData["MetaTag"] = meta;
                ViewData["User"] = user;
                ViewData["CultureData"] = userSelection.Culture;
                ViewData["CurrencyData"] = userSelection.Currency;
                ViewData["Exchange"] = userSelection.ExchangeClient;
                ViewData["UserLocation"] = userSelection.Context.Location;

                return View();
            }
            catch (Exception e)
            {
                _logger.LogError($"Home error - {e.Message} - Request: {JsonSerializer.Serialize(flightRequest)}");
                return ErrorPage(e.Message, 500);
            }
        }

       
        [Route("/aerolineas/{airlineName}/index.php")]
        [Route("/index.php")]
        [HttpGet]
        [HttpPost]
        public IActionResult RedirectToIndex(string airlineName = "")
        {
            if (String.IsNullOrEmpty(airlineName)) { 
                return RedirectPermanent($"/");
            }
            return RedirectPermanent($"/vuelos/aerolineas/{airlineName}");
        }


        
        [Route("/vuelos/tiquetes/{origin}/{destination}/{airports}")]
        [Route("/flights/tiquetes/{origin}/{destination}/{airports}")]
        [Route("/tiquetes/{origin}/{destination}/{airports}")]
        [Route("{culture}/tiquetes/{origin}/{destination}/{airports}")]
        [Route("{culture}/flights/tiquetes/{origin}/{destination}/{airports}")]
        [Route("{culture}/vuelos/tiquetes/{origin}/{destination}/{airports}")]
        [HttpGet]
        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> Destinations(string origin, string destination, string airports, string culture, FlightParamsRequest flightRequest)
        {
             var route = HomeMapper.Path(Request.Path.Value ?? "");
            var path = HomeMapper.GetPath(route);

            try
            {
                using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(60000));


                flightRequest.OriginCode = origin;
                flightRequest.DestinationCode = destination;
                var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
                var request = FlightParamsHelper.GetFlightRequest(flightRequest, _options, userSelection.Culture.InternalCultureCode);
                var alternates = await _alternateHandler.QueryAsync(new PlaceRequest { Culture = culture!, Id = 0, Path = $"{origin}/{destination}/{airports}", Route = path, Type = PageType.DestinationsTB }, cts.Token);
                var places = await _placeHandler.QueryAsync(request, cts.Token);

                if (places.Count < 2 || places.Any(p => p is null))
                {
                    return ErrorPage($"Error de places parametro erroneo | Message: {JsonSerializer.Serialize(places)}", 404);
                }

                request = FlightParamsHelper.MapFlightItemRequest(request, places,_options);

                var mktRequest = new Models.MKTCollection.Request.MTKCollectionRequest(flightRequest.Cache, destination, culture);
                var collections = await _mktCollectionHandler.QueryAsync(mktRequest, cts.Token);
                var section = CollectionMapper.Collection(collections, airports);

                var meta = MetaMapper.Destination(_options, _helper, request, userSelection, path, _localizer);

                ViewData["Alternates"] = alternates;
                ViewData["airline"] = destination;
                ViewData["request"] = request;
                ViewData["section"] = section;
                ViewData["isDestinations"] = true;
                ViewData["MetaTag"] = meta;
                ViewData["CultureData"] = userSelection.Culture;
                ViewData["CurrencyData"] = userSelection.Currency;
                ViewData["Exchange"] = userSelection.ExchangeClient;
                ViewData["UserLocation"] = userSelection.Context.Location;

                return View("~/Views/Home/Index.cshtml");
            }
            catch (Exception e)
            {

                _logger.LogError($"Destinations Airlines - {e.Message} - Request: {JsonSerializer.Serialize(flightRequest)}");

                return ErrorPage(e.Message, 500);
            }

        }


        [Route("/tiquetes/{origin}/{destination}/{airports}/index.php")]
        [HttpGet]
        public IActionResult RedirectToDestinations(string origin, string destination, string airports)
        {
            var queryString = Request.QueryString.Value;
            return RedirectPermanent($"/vuelos/tiquetes/{origin}/{destination}/{airports}{queryString}");
        }

        [Route("/health-check")]
        [HttpPost]
        [HttpGet]
        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public string HealthCheck()
        {
            return "OK";
        }



        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public ActionResult ErrorPage(string errorMgs, int statusCode)
        {
            ViewData["ErrorMgs"] = errorMgs;
            _httpContextAccessor.HttpContext.Response.StatusCode = statusCode;
            return View("~/Views/Error/Index.cshtml");
        }
        
        [Route("/error-page/{errorCode}")]
        [Route("/error-page")]
        [HttpGet]
        public ActionResult ErrorPageView(int errorCode)
        {
            string messageError = "Error page";
            switch (errorCode)
            {
                case 404:
                    messageError = "not found page";
                    break;
                
            }
            return ErrorPage(messageError, errorCode);
        }


        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public ActionResult Redirects(FlightParamsRequest flightRequest)
        {
            var url = "";
            var query = TBFront.Mappers.FlightMapper.ToQueryString(_httpContextAccessor);

            if (flightRequest.View == "calendar")
            {
                url = $"{_options.SiteUrl}/vuelos/calendar{query}";
            }

            if (flightRequest.View == "lowfare")
            {
                url = $"{_options.SiteUrl}{_options.ListUrl}{query}";
            }

            if (flightRequest.Option == "com_promociones_tiquetes_aereos2")
            {
                url = $"{_options.SiteUrl}/vuelos/promociones-tiquetes-aereos-destino{query}";
            }

            if (flightRequest.View == "addinfonacionalfc" && flightRequest.View ==  "addinfointernacional")
            {
                url = $"{_options.SiteUrl}";
            }

            if (flightRequest.Option == "com_respuestapago")
            {
                url = $"{_options.SiteUrl}/vuelos/voucher?rr=legacy&em={_hash.Encrypt(flightRequest.Email)}&id={_hash.Encrypt(flightRequest.MasterLocatorId)}";
            }


            if (string.IsNullOrEmpty(url))
            {
                url = $"{_options.SiteUrl}";
            }

            return RedirectPermanent(url);
        }

    }
}