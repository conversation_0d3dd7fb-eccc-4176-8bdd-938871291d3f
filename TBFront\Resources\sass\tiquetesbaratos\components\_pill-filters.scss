@media (min-width: 768px) {
    .z-md {
        z-index: 99;
    }
}

.cf-overflow {
    @media (max-width: 767px) {
        overflow-x: auto;
        overflow-y: hidden;
    }
    @media (min-width: 768px) and (max-width: 990px) {
        position: relative;
    }
}
.cf-scroll {
    display: inline-block;
    white-space: nowrap;

    .cfs-int {
        display: flex;
        flex-wrap: nowrap;

        @media (min-width: 768px) {
            flex-wrap: wrap;
            display: flex;
        }

        .o-cp {
            border-radius: 50px;
        }

        .c-pill {
            background-color: var(--bg-base);
            border: 1px solid var(--border-primary);
            border-radius: 50px;
            display: flex;
            -webkit-box-align: center;
            align-items: center;
            flex-direction: row;
            padding-left: 10px;
            padding-right: 10px;
            position: relative;
            z-index: 0;

            &:hover {
                /*background-color: #f5f9fd;*/
                /*box-shadow: 0px 0px 4px #74a6eb;*/
            }

            * {
                color: var(--text-primary);
            }

            span {
                cursor: pointer;
                padding: 8px 0 8px 0;
            }

            .icon-expand {
                font-size: 23px;
            }

            .cp-icon {
                position: relative;
                top: 0px;
                width: 18px;
            }

            .icon-close {
                &:before {
                    font-size: 18px;
                    position: relative;
                    right: -2px;
                    top: 1px;
                }
            }

            &:hover {
                border-color: var(--text-primary);
            }

            .form-check-input[type=checkbox] {
                border-color: var(--text-primary);
            }
        }

        .f-active {
            background: var(--bg-primary);

            * {
                color: var(--text-oncolor);
            }

            z-index: 2;

            @media (max-width: 756px) {
                z-index: 0;
            }
        }
    }

    .cf-dropdown {
        background-color: #fff;
        border-radius: 5px;
        overflow-y: auto;
        position: absolute;
        top: 50px;
        width: 350px;
        z-index: 2;

        @media (max-width: 767px) {
            border-radius: 20px 20px 0 0;
            bottom: 0;
            max-height: 90%;
            left: 0;
            position: fixed !important;
            right: 0;
            top: auto;
            width: 100%;
            z-index: 11;
        }

        .form-check-input {
            height: 1.4em;
            width: 1.4em;
        }

        .btn-primary {
            font-size: 16px;
            font-weight: 600;
        }
    }

    .filter-container .align-items-center {
        padding-bottom: 10px;
        padding-top: 10px;
    }

    .filter-container > .align-items-center:has(+ .align-items-center) {
        border-bottom: 1px solid #eee;
    }

    .accordion-button:not(.collapsed), .accordion-button {
        background-color: var(--bg-level2);
        color: var(--text-primary);
    }

    .accordion-button::after {
        font-family: icomoon !important;
        background-image: none !important;
        transition: none !important;
    }

    .accordion-button:not(.collapsed)::after {
        content: "\e908";
        position: relative;
        right: -2px;
        top: 6px;
    }

    .accordion-button::after {
        content: "\e91f" !important;
        font-size: 24px;
    }

    .accordion-button {
        &:focus {
            border-color: initial;
            box-shadow: initial;
        }
    }
}

.cf-capsule {
    margin-bottom: 10px;
}

.close.icon-close {
    color: var(--icon-main);
    font: var(--title-lg);
}


.cf-overlay {
    background-color: rgba(0,0,0,.40);
    bottom: 0;
    cursor: pointer;
    left: 0;
    position: fixed;
    right: 0;
    top: 0;
    z-index: 1;
}

.c-apply {
    background-color: #fff;
    border: 1px solid var(--text-primary);
    border-radius: 50px;
    color: var(--text-primary);
    left: 15px;
    font-size: 12px;
    padding: 0 6px !important;
    position: absolute;
    top: -12px;
    z-index: 0;

    @media (max-width: 756px) {
        z-index: 0 !important;
    }
}

.cf-dropdown {
    .form-check {
        padding-right: 1.5em;

        .form-check-input {
            border: 1px solid #ccc;
            float: right;
            margin-right: -1.5em;
        }
    }

    hr {
        border-top: 1px solid #ccc;
    }

    @media (min-width: 768px) and (max-width: 990px) {
        position: fixed !important;
        left: 23px !important;
        right: 23px;
        bottom: 23px;
        top: 286px !important;
        width: auto !important;
    }

    @media only screen and (max-height: 450px) and (orientation: landscape) {
        top: 40px !important
    }
}

.cfs-int {
    .cp-economics {
        background-color: #ffff54 !important;
        margin-bottom: 10px;
        padding-left: 10px;
        padding-right: 10px;

        &:hover {
            cursor: pointer;
        }

        .list-group-item {
            cursor: pointer;

            .form-check-input {
                border: 1px solid #ccc;
            }
        }
    }
}

.apply-filter {
    background-color: var(--text-primary) !important;
    color: #fff !important;
    z-index: 1;

    .cp-text {
        color: #fff !important;
        padding-right: 8px !important;

        &:hover {
            background-color: var(--text-primary);
            color: #fff;
        }
    }

    .cp-icon {
        color: #fff !important;
        border-left: 1px solid #a2c3f2;

        &:hover {
            background-color: var(--text-primary);
            color: #fff;

            .icon-close {
                color: #fff;
            }
        }
    }
}




.active-filters {
    z-index: 3;
}

.cf-scroll {
    .c-all-filters {
        @media (min-width: 990px) {
            left: -250%;
            width: 800px;
        }

        @media (min-width: 768px) and (max-width: 1230px) {
            left: -177px;
            width: 800px;
        }
        /*@media (max-width: 767px) {
        top: 50px;
    }*/
    }

    .filter-full {
        left: 0;
        top: 80px;
        width: 100%;

        @media (max-width: 767px) {
            top: auto;
        }
    }
}

.fs-12 {
    font-size: 12px;
}

.cs-body {
    @media (max-width: 767px), (min-width: 768px) and (max-width: 990px) {
        padding-bottom: 100px;
    }
}

.cs-footer {
    @media (max-width: 767px) {
        bottom: 0;
        left: 0;
        position: fixed;
        right: 0;
    }
    @media (min-width: 768px) and (max-width: 990px) {
        bottom: 0;
        left: 23px;
        padding-bottom: 25px !important;
        position: fixed;
        right: 23px;
    }
}

.isDisabled {
    pointer-events: none;
    opacity: 0.5;
    cursor: not-allowed;
}

.txt-price {
    color: var(--bs-gray-600);
}


