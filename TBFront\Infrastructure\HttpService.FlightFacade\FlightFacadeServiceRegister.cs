﻿using TBFront.Infrastructure.HttpService.FlightFacade.Dtos;
using TBFront.Interfaces;

namespace TBFront.Infrastructure.HttpService.FlightFacade
{
    public static class FlightFacadeServiceRegister
    {
        public static void AddFlightFacadeServiceRegister(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddSingleton(s => configuration.GetSection("FlightFacadeConfiguration").Get<FlightFacadeConfiguration>());
            services.AddSingleton<IPlaceStandardService, PlaceStandardService>();
        }
    }
}