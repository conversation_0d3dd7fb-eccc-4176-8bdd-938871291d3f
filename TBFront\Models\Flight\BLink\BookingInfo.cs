namespace TBFront.Models.Flight.BLink
{
    public class BookingBlink
    {
        public BookingInfo BookingInfo { get; set; }

        public BookingBlink()
        {
            BookingInfo = new BookingInfo();
        }
    }

    public class AffiliateSettings
    {
        public int PartnerId { get; set; }
        public int AffiliateId { get; set; }
        public int AffiliateSiteId { get; set; }
        public int AgencyId { get; set; }
        public int AgencyClientId { get; set; }
        public int AgencyBranchId { get; set; }
        public int AgencyUserIdCreatedBy { get; set; }
        public int AdminUserRegisteredBy { get; set; }
    }

    public class BookingInfo
    {
        public CustomerInfo CustomerInfo { get; set; }
        public BookingSettings BookingSettings { get; set; }
        public AffiliateSettings AffiliateSettings { get; set; }
        public ServiceItems ServiceItems { get; set; }

        public BookingInfo()
        {
            BookingSettings = new BookingSettings();
            CustomerInfo = new CustomerInfo();
            AffiliateSettings = new AffiliateSettings();
            ServiceItems = new ServiceItems();
        }
    }

    public class BookingSettings
    {
        public string Currency { get; set; }
        public string Language { get; set; }
        public string IpAddress { get; set; }
        public int ChannelId { get; set; }
        public int SiteId { get; set; }
        public int CampaignId { get; set; }
        public string CampaignToken { get; set; }
        public bool IsPackage { get; set; }
        public List<string> Tags { get; set; }
        public int AdminUserIdCreatedBy { get; set; }
        public DateTime QuoteExpirationDate { get; set; }
        public bool SendEmail { get; set; }
        public bool SendConfirmation { get; set; }
        public string? NoteTitle { get; set; }
        public string? Note { get; set; }
        public List<ExtraInfo> ExtraInfo { get; set; }

        public BookingSettings()
        {
            Tags =  new List<string>();
        }

    }

    public class CustomerInfo
    {
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
        public string MobilePhone { get; set; }
    }

    public class ExtraInfo
    {
        public string? Info { get; set; }
        public int Type { get; set; }
    }

    public class HotelBooking
    {
        public int RatePlanId { get; set; }
        public int HotelId { get; set; }
        public DateTime? ArrivalDate { get; set; }
        public DateTime? DepartureDate { get; set; }
        public bool IsPackageRate { get; set; }
        public List<RoomBooking> Rooms { get; set; }
        public int CollectType { get; set; }
        public string? SpecialRequest { get; set; }
        public double TotalAmount { get; set; }

        public HotelBooking()
        {
            Rooms = new List<RoomBooking>();
        }
    }

    public class RoomBooking
    {
        public int? Adults { get; set; }
        public string? ChildAges { get; set; }
        public double TotalAmount { get; set; }
        public double TotalCost { get; set; }

        public RoomBooking()
        {
        }
    }

    public class ServiceItems
    {
        public List<HotelBooking> Hotels { get; set; }

        public ServiceItems()
        {
            Hotels = new List<HotelBooking>();
        }
    }
}

