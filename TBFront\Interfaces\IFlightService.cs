﻿using TBFront.Models.Flight.CreateBooking;
using TBFront.Models.Flight.Quote;
using TBFront.Models.Flight.Revalidate;
using TBFront.Models.Flight.Summary;
using TBFront.Models.Flight.Upsell;


namespace TBFront.Interfaces
{
    public interface IFlightService :
        IQueryHandlerAsync<FlightQuoteRequest, FlightQuoteResponse>, 
        IQueryHandlerAsync<FlightUpsellRequest, FlightUpsellResponse>,
        IQueryHandlerAsync<RevalidateRequest, RevalidateResponse>,
        IQueryHandlerAsync<CreateBookingRequest, CreateBookingResponse>,
        IQueryHandlerAsync<SummaryRequest, SummaryResponse>

    { }
}
