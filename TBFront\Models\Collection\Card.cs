﻿using ProtoBuf;
using System.Text.Json.Serialization;

namespace TBFront.Models.Collection
{
    [ProtoContract]
    public class Card
    {
        [ProtoMember(1)]
        [JsonPropertyName("imageurl")]
        public string? ImageUrl { get; set; }
        [ProtoMember(2)]
        [Json<PERSON>ropertyName("url")]
        public string? Url { get; set; }
        [ProtoMember(3)]
        [JsonPropertyName("promotiontype")]
        public string? PromotionType { get; set; }
        [ProtoMember(4)]
        [JsonPropertyName("name")]
        public string? Name { get; set; }
        [ProtoMember(5)]
        [JsonPropertyName("notes")]
        public string? Notes { get; set; }
        [ProtoMember(6)]
        [<PERSON>son<PERSON><PERSON><PERSON>Name("active")]
        public bool Active { get; set; }
        [ProtoMember(7)]
        [Json<PERSON>ropertyName("order")]
        public int Order { get; set; }
        [ProtoMember(8)]
        [JsonPropertyName("type")]
        public string? Type { get; set; }
        [ProtoMember(9)]
        [Json<PERSON>ropertyName("destination")]
        public string? Destination { get; set; }
        [ProtoMember(10)]
        [<PERSON>son<PERSON><PERSON><PERSON><PERSON><PERSON>("pricenote")]
        public string? PriceNote { get; set; }
        [ProtoMember(11)]
        [JsonPropertyName("price")]
        public string? Price { get; set; }
    }
}