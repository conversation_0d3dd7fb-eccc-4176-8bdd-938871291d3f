import { consoleType } from '../constants';
import { CookieService } from '../services/CookieService';
import { __ } from "../utils/helpers/translate";

const culture = window.__pt.cultureData;
const config = window.__pt.settings.site;

export const getPaxesKeys = (pax, cap, check) => {
	let capitilize = !!cap;
	let checkout = !!check;
	let paxesKeys = {};
	let adults = 0;
	let kids = 0;
	let infants = 0;
	let agekids = [];
	let rooms = pax.length;

	for (let i = 0; i < rooms; i++) {
		const room = pax[i];
		let position = i + 1;

		paxesKeys[`${capitilize ? "R" : "r"}oom${position}.${capitilize ? "A" : "a"}dults`] = room.adults;
		adults += room.adults;

		let childrens = room.children.length;
		let childreJoin = [];
		for (let c = 0; c < childrens; c++) {
			const childrenRoom = room.children[c];
			childreJoin.push(childrenRoom.year);
			agekids.push(childrenRoom.year);
			if (childrenRoom.year > 5) {
				kids += 1;
			} else {
				infants += 1;
			}
		}
		paxesKeys[`${capitilize ? "R" : "r"}oom${position}.${capitilize ? "K" : "k"}ids`] = childrens;
		paxesKeys[`${capitilize ? "R" : "r"}oom${position}.${capitilize ? "A" : "a"}gekids`] = childreJoin.join(",");

	}
	paxesKeys[`${capitilize ? "R" : "r"}ooms`] = rooms;
	paxesKeys[`${capitilize ? "A" : "a"}dults`] = adults;
	paxesKeys[`${capitilize ? "K" : "k"}ids`] = kids;

	if (checkout) {
		paxesKeys[`${capitilize ? "K" : "k"}ids`] = paxesKeys[`${capitilize ? "K" : "k"}ids`] + infants
	} else {
		paxesKeys[`${capitilize ? "I" : "i"}nfants`] = infants;
	}

	paxesKeys[`${capitilize ? "A" : "a"}gekids`] = agekids.join(",");


	return paxesKeys;
};

export const getStringPax = (pax) => {
	let paxstring = "";

	for (var i = 0; i < pax.length; i++) {
		const px = pax[i];
		paxstring += `a:${px.adults}`;
		paxstring += "-c:";
		for (var c = 0; c < px.children.length; c++) {
			let ch = px.children[c];
			paxstring += `${ch.year},`;
		}
		paxstring += "|"
	}


	return paxstring;
};

export const clone = (obj) => {
	return JSON.parse(JSON.stringify(obj));
};

export const saveCookieQuote = (params) => {

	try {
		if (params) {
			let cloneParams = this.clone(params);
			delete cloneParams.checkoutHash;

			let paramstext = JSON.stringify(cloneParams);

			CookieService.setCookie("quoteparams", paramstext)

		}
	} catch { }

};

export const getModelForm = (paxes, settingsForm) => {
	let form = [];
	let formAdults = [];
	let formChildren = [];
	const { Nationality, nation_options, day_selected, day_options, month_selected, month_options, year_selected, year_options } = settingsForm;

	for (let index = 0; index < paxes.length; index++) {
		const adultPerRoom = paxes[index].adults;

		for (let j = 0; j < adultPerRoom; j++) {
			formAdults.push({
				firstname: "",
				lastname: "",
				age: 0,
				IsChild: false,
				Nationality,
				nation_options: nation_options(),
				day_selected,
				day_options,
				month_selected,
				month_options,
				year_selected,
				year_options: year_options(0),
				Birthday: "",
				prefix: "MR",
				gender: 1,
				identityDocument: "",
				typePassenger: 'adult',
				room: 0,
				idx: j
			});
		}

		const arrayChildren = paxes[index].children
		arrayChildren.sort((a, b) => b.year - a.year);

		let idxInfant = -1;

		for (let index = 0; index < arrayChildren.length; index++) {

			if (arrayChildren[index].year == 1) {
				idxInfant = idxInfant + 1;
			}
			let typePass = arrayChildren[index].year == 1 ? "infant" : 'child';
			formChildren.push({
				firstname: "",
				lastname: "",
				age: arrayChildren[index],
				IsChild: true,
				Nationality,
				nation_options: nation_options(),
				day_selected,
				day_options,
				month_selected,
				month_options,
				year_selected,
				year_options: year_options(typePass == "infant" ? 1 : 11),
				Birthday: "",
				prefix: "JR",
				gender: 1,
				identityDocument: "",
				typePassenger: typePass,
				room: 0,
				idx: arrayChildren[index].year == 1 ? idxInfant : index
			});
		}

	}

	form = formAdults.concat(formChildren);

	return form;
};

export const getFamilyFare = async (quote, flightType, authBff, site) => {
	let token = quote.token.split(",");
	let tokenInter = token.length == 1 || (flightType == "starting") ? token[0] : token[1];
	const tramos = quote.flightItinerary[flightType].fareKey.split("|");
	const tramo = flightType == "starting" || tramos.length == 1 ? tramos[0] : tramos[1];
	const familias = tramo
 		.split("^")
 		.map(segmento => segmento.split("~")[2]);
	const params = {
		token: !window.__pt.data.isDomesticRoute ?  (window.__pt.data.isRoundTrip ? tokenInter : quote.token) : tokenInter,
		flightItineraryId: quote.flightItinerary[flightType].itineraryId,
		flightId: quote.flightItinerary[flightType].id,
		fareId: quote.flightItinerary[flightType].fareId,
		airlineLogoUri: quote.flightItinerary[flightType].logoUri,
		airlineName: quote.flightItinerary[flightType].airline,
		flightType: !window.__pt.data.isDomesticRoute || window.__pt.data.isRoundTrip ? flightType :  "starting",
		culture: culture.internalCultureCode,
		fareKey: quote.flightItinerary[flightType].fareKey,
		familyFare: !window.__pt.data.isDomesticRoute ? familias.join(",") : "",
		site: config.code
	}
	if (quote?.quoteToken?.split(",")?.length > 1) {
		params.oneWayToken = quote.quoteToken;
		params.multiple = flightType == "starting" ? quote.multiple[0] : quote.multiple[1];
		params.stepView = flightType;
	}
	let paramsCast = "?" + Object.keys(params).map(function (k) { return encodeURIComponent(k) + '=' + encodeURIComponent(params[k]) }).join('&');

	let flight = {};
	const { domain, path, pathFlightDetail, pathFlightFamilyFare } = site.apiFlights;
	const detail = await fetch(domain + path + pathFlightDetail + paramsCast);
	if (detail && detail.status == 200) {
		flight.detail = await detail.json();
	}
	
	const detailFamilyFare = await fetch(domain + path + pathFlightFamilyFare + paramsCast);
	if (detailFamilyFare && detailFamilyFare.status == 200) {
		flight.detailFamilyFare = { ... (await detailFamilyFare.json()), params: params};
	}
	return flight;
};

export const isAdult = (dateStr) => {
	let birthdate = new Date(dateStr);
	let valid = !isNaN(birthdate.getTime());
	if (valid == false) {
		return false;
	}

	let today = new Date();

	let diffTime = today.getTime() - birthdate.getTime();
	let age = Math.floor(diffTime / (1000 * 60 * 60 * 24 * 365.25));

	if (age >= 12) {
		return true;
	} else {
		return false;
	}
};

export const isMinor = (dateStr, dateFlight) => {
	let birthdate = new Date(`${dateStr}T00:00:00`);
	let valid = !isNaN(birthdate.getTime());

	if (valid == false) {
		return false;
	}

	let today = new Date(`${dateFlight}T00:00:00`);
	let diffTime = today.getTime() - birthdate.getTime();
	let age = Math.floor(diffTime / (1000 * 60 * 60 * 24 * 365));

	if (age >= 2 && age <= 11) {
		return true;
	} else {
		return false;
	}
};

export const isInfant = (dateStr, dateFlight) => {
	let birthdate = new Date(`${dateStr}T00:00:00`);

	if (isNaN(birthdate.getTime())) {
		return false;
	}

	let today = new Date(`${dateFlight}T00:00:00`);
	let diffTime = today.getTime() - birthdate.getTime();
	let age = Math.floor(diffTime / (1000 * 60 * 60 * 24 * 365));

	return age < 2;
};

export const getAge = (dateStr) => {
	let today = new Date();
	let birthdate = new Date(dateStr);

	let edad = today.getFullYear() - birthdate.getFullYear();
	let diffMonth = today.getMonth() - birthdate.getMonth();

	// Si la fecha actual es antes del día de nacimiento, restar un año
	if (diffMonth < 0 || (diffMonth === 0 && today.getDate() < birthdate.getDate())) {
		edad--;
	}

	return edad;
};

export const addPrefix = (arrayObjetos) => {
	const nameCounts = {};

	for (const objeto of arrayObjetos) {
		const fullName = objeto.firstname + objeto.lastname;

		if (nameCounts[fullName] === undefined) {
			nameCounts[fullName] = [objeto];
		} else {
			nameCounts[fullName].push(objeto);
		}
	}

	for (const objeto of arrayObjetos) {
		const fullName = objeto.firstname + objeto.lastname;
		const nameGroup = nameCounts[fullName];

		if (nameGroup.length > 1 && !objeto.IsChild) {
			for (const nameObj of nameGroup) {
				if (nameObj === nameGroup[0]) {
					nameObj.prefix = "MR";
				} else {
					nameObj.prefix = "JR";
				}
			}
		}
		objeto.identityDocument = objeto.identityDocument;
		objeto.gender = 1;
		objeto.type = objeto.IsChild ? "Minors" : "Adults";
		objeto.Birthday = `${objeto.year_selected}-${objeto.month_selected}-${objeto.day_selected}`;
		objeto.age = getAge(`${objeto.year_selected}-${objeto.month_selected}-${objeto.day_selected}`);
	}
	return arrayObjetos;
};


export const normalizeText = (str = "") => {
	if (str == null) {
		return "";
	}
	const newStr = str.normalize('NFD')
		.replace(/([aeio])\u0301|(u)[\u0301\u0308]/gi, "$1$2")
		.normalize().toLowerCase();
	return newStr;
};

export const mappingFamilyFare = (data, detail = false) => {
	const icons = {
		bigbag: {
			icon: !detail ? "icon-big-bag" : "i-bag",
			ok: !detail ? "" : "-ok",
			none: !detail ? "-out" : "-none",
		},
		carryon: {
			icon: !detail ? "icon-carry-on" : "i-carry-on",
			ok: !detail ? "" : "-ok",
			none: !detail ? "-bag" : "-none",
		}
	}
	const map = data ? JSON.parse(JSON.stringify(data)) : [];
	if (map.length > 0) {
		const categoriesToAdd = [
			{ title: `${__("flightList.baggage")}`, description: ` ${__("flightList.notIncluded")}`, category: 1, include: 0, name: `${__("flightList.baggage")}` },
			{ title: `${__("flightList.carryOn")}`, description: ` ${__("flightList.notIncluded")}`, category: 2, include: 0, name: `${__("flightList.carryOn")}` }
		];
		categoriesToAdd.forEach(item => {
			const exists = map.some(existingItem => existingItem.category === item.category);
			if (!exists) {
				map.push(item);
			}
		});
		map.forEach(item => {
			item.class = detail ? "" : "icon-left";
			if (item.category < 3) {
				item.class = item.category == 1 ? icons.bigbag.icon + (item.include != 1 ? icons.bigbag.none : icons.bigbag.ok)
					: icons.carryon.icon + (item.include != 1 ? icons.carryon.none : icons.carryon.ok);
			}
		});
		map.sort((a, b) => {
			const priority = { 2: 1, 1: 2 };
			const priorityA = priority[a.category] || 3;
			const priorityB = priority[b.category] || 3;
			return priorityA - priorityB;
		});
	}
	return map;
}

export const getSiteInformation = () => {
	const culture = window.__pt.cultureData || {};
	const userLocation = window.__pt.userLocation || {};
	const exchange = window.__pt.exchange || {};
	return {
		Country: culture.country,
		CultureCode: culture.cultureCode,
		CultureExternal: culture.cultureExternal,
		InternalCultureCode: culture.internalCultureCode,
		Language: culture.language,
		SiteCode: culture.siteCode,
		SiteName: culture.siteName,
		UserLocation: userLocation.country,
		UserCountry: userLocation.userCountry,
		CurrencyBase: exchange.base,
		CurrencyDisplay: exchange.currency
	}
}
