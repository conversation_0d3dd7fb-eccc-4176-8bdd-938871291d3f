﻿using ProtoBuf;

namespace TBFront.Models.Places.Response
{
    [ProtoContract]
    public class Items
    {
        [ProtoMember(1)]
        public int Airport { get; set; }

        [ProtoMember(2)]
        public int AirportRange { get; set; }

        [ProtoMember(3)]
        public int City { get; set; }

        [ProtoMember(4)]
        public int Hotel { get; set; }

        [ProtoMember(5)]
        public int InterestPoint { get; set; }
    }

    [ProtoContract]
    public class LocationInfo
    {
        [ProtoMember(1)]
        public string CountryISO { get; set; }

        [ProtoMember(2)]
        public string CountryA2 { get; set; }

    }

    [ProtoContract]
    public class PlaceResponse
    {
        [ProtoMember(1)]
        public int Id { get; set; }

        [ProtoMember(2)]
        public string Name { get; set; }

        [ProtoMember(3)]
        public string DisplayText { get; set; }

        [ProtoMember(4)]
        public string DisplayHtml { get; set; }

        [ProtoMember(5)]
        public int Type { get; set; }

        [ProtoMember(6)]
        public LocationInfo LocationInfo { get; set; } = new LocationInfo();

        [ProtoMember(7)]
        public bool IsActive { get; set; }

        [ProtoMember(8)]
        public int Distance { get; set; }

        [ProtoMember(9)]
        public string Uri { get; set; }

        [ProtoMember(10)]
        public string Code { get; set; }

        [ProtoMember(11)]
        public Items Items { get; set; }

        [ProtoMember(12)]
        public string StartingAirport { get; set; }

        [ProtoMember(13)]
        public string ReturningAirport { get; set; }

        [ProtoMember(14)]
        public string Culture { get; set; }
    }

    public class FrontPlaceResponse
    {
        public string StartingAirport { get; set; }
        public string ReturningAirport { get; set; }
        public string Culture { get; set; }
        public string InternalCulture { get; set; }
        public List<PlaceResponse> PlaceResponses { get; set; }
    }

}