﻿using TBFront.Infrastructure.HttpService.PaymentGateway.Dtos;
using TBFront.Models.PaymentGateway.Dtos;
using TBFront.Models.PaymentGateway;
using TBFront.Options;
using TBFront.Models.BookingItinerary;
using TBFront.Types;
using TBFront.Models.Forms.Response;
using TBFront.Models.HotelFacade.Request;
using TBFront.Models.HotelFacade.Response;
using TBFront.Models.Flight.Quote;
using System.Linq;
using TBFront.Models;
using TBFront.Models.Flight.Summary;
using TBFront.Models.Response;


namespace TBFront.Application.Mappers
{
    public class PayOnlineMapper
    {

        public static ContentHotelRequest GetRequestHotelContent(List<BookingItineraryService> items, string culture)
        {
            var hotel = items.Where(h => h.ServiceType == 1).ToList().FirstOrDefault();

            return new ContentHotelRequest
            {
                HotelId = hotel.ServiceCarrierCode,
                Culture = culture,
            };
        }
        public static PaymentGateweyConfigurationRequest GetRequestPaymentConfiguration(TravelItinerary itinerary)
        {
            return new PaymentGateweyConfigurationRequest
            {
                ChannelId = itinerary.ChannelId.ToString(),
            };
        }

        public static OnlinePaymentClient Client(TravelItinerary response, string type)
        {
            return new OnlinePaymentClient
            {
                Email = response.CustomerEmail,
                Name = response.CustomerFirstName,
                Lastname = response.CustomerLastName,
                TotalAmount = response.BookingServices.Where(h => h.ServiceType == 3 || h.ServiceType == 1 || h.ServiceType == 2).Sum(t => t.ServiceCharge.ServiceAmountTotal),
                ServiceAmountPaid = response.BookingServices.Where(h => h.ServiceType == 3 || h.ServiceType == 1 || h.ServiceType == 2).Sum(t => t.ServiceCharge.ServiceAmountPaid),
                Currency = response.Currency,
                Id = response.BookingId,
                Type = type
            };
        }

        public static PaymentGatewayRequest Request(TravelItinerary response, ContentHotelResponse hotelContentResponse, SummaryResponse summaryResponse, List<PaymentGatewayConfigurationResponse> paymentConfiguration, SettingsOptions _options, PaymentGatewayConfiguration configuration, string sessionId, string id, string email, string keyValidation)
        {

            var channelConfig = _options.ChannelConfig.First();
            var paymentTokenConfig = new PaymentTokenConfig()
            {
                Is3DSecureProcessingEnabled = false,
                HostUrl = _options.SiteUrl,
                UrlRedirectTo = $"{_options.SiteUrl}{_options.RedirectToPath}?keyValidation={keyValidation}&id={id}&em={email}",
                RedirectToPaymentGatewayConfirmation = false,
                AnalyticsId = _options.GTM,
                Analytics = null,
                ChannelGroupId = channelConfig.ChannelGroupId,
                ChannelId = channelConfig.Desktop,
                PaymentGatewayApp = configuration.PaymentGatewayApp,
                ContactPhone = _options.ContactPhone,
                ThirdPartyCheckoutProvider = configuration.thirdPartyCheckoutProvider.ToList(),
                ExternalProvider = configuration.CheckoutProvider.ToList(),
                SessionId = sessionId,
                AffiliateId = _options.AffiliateId,
                AffiliateSiteId = _options.AffiliateSiteId,
            };

            var requestPaymentGateway = RequestToGeneratePaymentToken(paymentTokenConfig, response, hotelContentResponse, summaryResponse, paymentConfiguration, sessionId, keyValidation, _options.HoursDepositLimit);

            return requestPaymentGateway;
        }


        private static PaymentGatewayRequest RequestToGeneratePaymentToken(PaymentTokenConfig paymentTokenConfig, TravelItinerary responseBook, ContentHotelResponse hotelContentResponse, SummaryResponse summaryResponse, List<PaymentGatewayConfigurationResponse> paymentConfiguration, string sessionid, string keyValidation, int hoursDepositLimit)
        {
            var totalAmount = responseBook.BookingServices.Sum(b => b.ServiceCharge.ServiceAmountTotal);
            var type = GetProductType(responseBook.BookingServices);
            var enumeratorType = GetProductEnum(type);
            var services = new List<Models.PaymentGateway.Dtos.Service>();
            var engine = 0;
            var airlineIATACode = string.Empty;
            var paymentConfigurationByChannel = paymentConfiguration.First();
            var isProviderCollect = false;

            if (type == ProductType.HotelsToken)
            {
                services = PayOnlineMapper.GetHotelServices(responseBook.BookingServices, hotelContentResponse, type);
                isProviderCollect = services.FirstOrDefault().ProviderCollect;
            }

            if (type == ProductType.FlightsToken)
            {
                services = PayOnlineMapper.GetFlightServices(responseBook.BookingServices, responseBook, summaryResponse, type);
                engine = responseBook.BookingServices.FirstOrDefault().ServiceInfo.Engine;
                airlineIATACode = responseBook.BookingServices.FirstOrDefault().ServiceInfo.Segments.FirstOrDefault().DepartureCode;
            }

            if (type == ProductType.PackagesToken)
            {
                services = PayOnlineMapper.GetPackageServices(responseBook.BookingServices, responseBook, hotelContentResponse, summaryResponse, type);
                engine = responseBook.BookingServices.FirstOrDefault(b => b.ServiceType == 3).ServiceInfo.Engine;
                airlineIATACode = responseBook.BookingServices.FirstOrDefault(b => b.ServiceType == 3).ServiceInfo.Segments.FirstOrDefault().DepartureCode;
                totalAmount = responseBook.BookingServices.Where(h => h.ServiceType != 6).Sum(b => b.ServiceCharge.ServiceAmountTotal);

            }


            var paymentTokenRequest = new PaymentGatewayRequest
            {
                LocatorId = responseBook.BookingId,
                Reference = responseBook.BookingId.ToString(),
                Currency = responseBook.Currency,
                Process3DSecure = false,
                KeyValidation = keyValidation,
                SessionId = sessionid,
                Host = paymentTokenConfig.HostUrl,
                ResponseUrl = paymentTokenConfig.UrlRedirectTo,
                ReturnPaymentOption = true,
                ReturnPaymentProcessOption = true,
                PaymentConfigInfoRequest = new PaymentConfigInfoRequest
                {
                    Currency = responseBook.Currency,
                    Amount = decimal.Round((decimal)totalAmount, 2, MidpointRounding.AwayFromZero),
                    ExternalProviders = paymentTokenConfig.ExternalProvider,
                    BookingChannelId = responseBook.ChannelId,
                    ChannelGroupId = paymentConfigurationByChannel.ChannelGroupId,
                    Product = enumeratorType,
                    PaymentGatewayApp = paymentTokenConfig.PaymentGatewayApp,
                    AffiliateId = paymentTokenConfig.AffiliateId,
                    AffiliateSiteId = paymentTokenConfig.AffiliateSiteId,
                    FlightEngine = engine,
                    AirlineIATACode = airlineIATACode,
                    ProviderCollect = isProviderCollect
                },
                QuoteInfo = new QuoteInfo
                {
                    PaxName = $"{responseBook.CustomerFirstName} {responseBook.CustomerLastName}",
                    PaxEmail = responseBook.CustomerEmail,
                    Services = services,
                    DateLimit = DateTime.Now.AddHours(hoursDepositLimit).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ"),
                    DepositDateLimit = DateTime.Now.AddHours(hoursDepositLimit).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ"),
                    PaxPhone = "",
                    DocumentId = "",
                },
                TagManagerDataLayer = new TagManagerDataLayer
                {
                    TagManagerContainerId = paymentTokenConfig.AnalyticsId,
                    DataLayerItems = paymentTokenConfig.Analytics
                },
                ShowConfirmationPage = false,
                ContactPhone = paymentTokenConfig.ContactPhone,
                ThirdPartyCheckoutProvider = paymentTokenConfig.ThirdPartyCheckoutProvider,
            };


            return paymentTokenRequest;
        }


        private static List<Models.PaymentGateway.Dtos.Service> GetHotelServices(List<BookingItineraryService> items, ContentHotelResponse hotelContentResponse, string type, double totalAmountOverwritten = 0)
        {
            var services = new List<Models.PaymentGateway.Dtos.Service>();
            var rooms = new List<Rooms>();
            var firstRoom = items.First();
            var totalAmount = items.Sum(b => b.ServiceCharge.ServiceAmountTotal);
            var firstImageGallery = hotelContentResponse.Gallery.FirstOrDefault();
            var days = (firstRoom.EndDate - firstRoom.StartDate).Days;
            var roomsCount = items.Count;

            foreach (var item in items)
            {

                var roomDays = (item.EndDate - item.StartDate).Days;
                rooms.Add(new Rooms
                {
                    Type = item.ServiceCarrierDescription,
                    Adults = item.Adults,
                    Children = item.Kids,
                    Infants = 0,
                    Nights = roomDays,
                    MealPlan = item.MealPlan,
                    AmountPerRoom = (item.ServiceCharge.ServiceAmountTotal / roomsCount) / days,
                    AmountWithoutDiscountPerRoom = 0,
                    TaxAmountPerRoom = 0
                });
            }

            var hasTaxes = firstRoom.Rate.TaxScheme == 2 || firstRoom.Rate.TaxScheme == 7;


            var service = new Models.PaymentGateway.Dtos.Service()
            {
                Name = firstRoom.ServiceCarrierName,
                Id = int.Parse(firstRoom.ServiceCarrierCode),
                Destination = "",
                ZipCode = hotelContentResponse.Location?.PostalCode,
                CountyA2 = "",
                CityId = 0,
                CityName = string.IsNullOrWhiteSpace(hotelContentResponse.Location.City) &&
                    string.IsNullOrWhiteSpace(hotelContentResponse.Location.Country) ? string.Empty
                    : $"{hotelContentResponse.Location.City}{(string.IsNullOrWhiteSpace(hotelContentResponse.Location.City) || string.IsNullOrWhiteSpace(hotelContentResponse.Location.Country) ? string.Empty : ", ")}{hotelContentResponse.Location.Country}",
                Address = hotelContentResponse.Location?.Street,
                Phone = "",
                Category = Convert.ToInt32(hotelContentResponse.Stars),
                Days = days,
                DtStartService = firstRoom.StartDate.AddHours(15),
                DtEndService = firstRoom.EndDate.AddHours(12),
                PercentageDiscount = 0,
                Type = type,
                DiscountAmount = 0,
                HasTaxesAndOtherCharges = hasTaxes,
                Rooms = rooms,
                ThumbnailUrl = firstImageGallery is not null && !string.IsNullOrEmpty(firstImageGallery.CloudUri) ? firstImageGallery.CloudUri : string.Empty,
                TaxScheme = firstRoom.Rate.TaxScheme,
                TotalAmount = (decimal)totalAmount,
                TaxAmount = 0,
                Tax = 0,
                HasTaxes = hasTaxes,
                ShowLuggageInfo = false,
                ProviderCollect = firstRoom.CollectType == 2
            };

            if (totalAmountOverwritten > 0)
            {
                service.PackageTotalAmount = totalAmountOverwritten;
            }


            services.Add(service);


            return services;
        }

        private static List<Models.PaymentGateway.Dtos.Service> GetFlightServices(List<BookingItineraryService> items, TravelItinerary responseBook, SummaryResponse summaryResponse, string type)
        {
            var flightReservationSegment = new List<FlightReservationSegment>();
            var flightReservationSegmentIntermediatePoint = new List<FlightReservationSegmentIntermediatePoint>();
            var airPassengers = new List<AirPassengers>();
            var services = new List<Models.PaymentGateway.Dtos.Service>();

            var firstFlight = items.First();
            var totalAmount = items.Sum(b => b.ServiceCharge.ServiceAmountTotal);
            var isRoundtrip = items.Count == 2 || items.Any(i => i.Description.Contains("redondo")) || items.Any(i => i.Description.Contains("ida y regreso"));
            var isRoundtripByOneway = items.Count == 2;
            var totalPeople = firstFlight.Adults + firstFlight.Kids;
            var firstSegment = firstFlight.ServiceInfo.Segments.First();
            decimal taxAmount = 0;


            //back itinerario

            if (summaryResponse is not null && summaryResponse.Items is not null && summaryResponse.Items.Flights.Any())
            {
                foreach (var flights in summaryResponse.Items.Flights)
                {
                    taxAmount += (decimal)flights.Taxes.Sum(x => x.Amount);
                }

                var flightDeparture = summaryResponse.Items.Flights.FirstOrDefault()?.FlightDepartureSegments;

                var flightReturning = summaryResponse.Items.Flights.FirstOrDefault()?.FlightReturningSegments;

                var lastFlight = summaryResponse.Items.Flights.LastOrDefault();

                var segmentStart = GetSegment(flightDeparture);
                var segmentStopsStart = GetSegmentStops(flightDeparture);


                flightReservationSegment.Add(segmentStart);
                flightReservationSegmentIntermediatePoint.AddRange(segmentStopsStart);

                if (isRoundtrip || isRoundtripByOneway)
                {
                    flightReturning = flightReturning is not null && flightReturning.Count > 0 ? flightReturning : lastFlight.FlightDepartureSegments;

                    var segmentEnd = GetSegment(flightReturning, 2);
                    var segmentStopsEnd = GetSegmentStops(flightDeparture, 2);
                    flightReservationSegmentIntermediatePoint.AddRange(segmentStopsEnd);

                    flightReservationSegment.Add(segmentEnd);
                }

            }




            airPassengers.Add(new AirPassengers
            {
                Designation = "MR",
                FistName = responseBook.CustomerFirstName,
                LastName = responseBook.CustomerLastName,
            });



            var service = new Models.PaymentGateway.Dtos.Service()
            {
                Type = type,
                TotalAmount = ((decimal)totalAmount) - (taxAmount),
                TaxAmount = taxAmount,
                ServiceCharge = 0,
                HasTaxesAndOtherCharges = true,
                HasReturnFlights = isRoundtrip,
                FlightReservationSegment = flightReservationSegment,
                FlightReservationSegmentIntermediatePoint = flightReservationSegmentIntermediatePoint,
                AirPassengers = airPassengers,
                FlightDescription = "",
                FlightRateDescription = "",
                FlightCarryOnLuggage = "",
                FlightCheckedLuggage = "",
                FlightExtraInfo = "",
                FlightExtraLuggage = "",
                PNR = "",
                ShowLuggageInfo = false
            };

            services.Add(service);

            return services;
        }

        private static List<Models.PaymentGateway.Dtos.Service> GetPackageServices(List<BookingItineraryService> items, TravelItinerary responseBook, ContentHotelResponse hotelContentResponse, SummaryResponse summaryResponse, string type)
        {
            var flightReservationSegment = new List<FlightReservationSegment>();
            var flightReservationSegmentIntermediatePoint = new List<FlightReservationSegmentIntermediatePoint>();
            var airPassengers = new List<AirPassengers>();
            var services = new List<Models.PaymentGateway.Dtos.Service>();

            var totalAmount = items.Where(h => h.ServiceType == 3 || h.ServiceType == 1).Sum(b => b.ServiceCharge.ServiceAmountTotal);
            var hotel = items.Where(h => h.ServiceType == 1).ToList();
            var flights = items.Where(h => h.ServiceType == 3).ToList();
            var transfers = items.FirstOrDefault(h => h.ServiceType == 2);

            var hotelService = GetHotelServices(hotel, hotelContentResponse, type, totalAmount);
            var flightService = GetFlightServices(flights, responseBook, summaryResponse, ProductType.FlightsToken);



            services.AddRange(hotelService);
            services.AddRange(flightService);

            if (transfers is not null)
            {
                var transferService = GetTransfers(transfers, responseBook.BookingId);
                services.Add(transferService);
            }

            return services;
        }

        private static Models.PaymentGateway.Dtos.Service GetTransfers(BookingItineraryService item, int id)
        {
            return new Models.PaymentGateway.Dtos.Service
            {
                Type = "transfer",
                Id = id,
                Name = item.Description,
                TotalAmount = (decimal)item.ServiceCharge.ServiceAmountTotal,
                TransferDescription = item.ServiceCarrierCode,
                FlightRateDescription = "",
                PNR = ""
            };

        }

        #region Product type
        public static string GetProductType(List<BookingItineraryService> items)
        {

            var isPackage = items.Any(b => b.ServiceType == 6);

            var isHotel = items.All(s => s.ServiceType == 1);

            var isFlight = items.All(s => s.ServiceType == 3);

            if (isPackage)
            {
                return ProductType.PackagesToken;
            }

            if (isHotel)
            {
                return ProductType.HotelsToken;
            }

            if (isFlight)
            {
                return ProductType.FlightsToken;
            }

            return string.Empty;
        }

        private static int GetProductEnum(string type)
        {

            if (ProductType.PackagesToken == type)
            {
                return 2;
            }

            if (ProductType.HotelsToken == type)
            {
                return 1;
            }

            if (ProductType.FlightsToken == type)
            {
                return 3;
            }

            return 0;
        }

        private static string GetFlightDescription(bool IsRoundtrip, bool IsRoundtripByOneway, BookingItineraryService service, int totalPaxes)
        {
            var flightType = IsRoundtrip ? "ida y regreso" : "ida";
            var flightPeople = totalPaxes == 1 ? "persona" : "personas";
            var title = service.Description;

            if (IsRoundtripByOneway)
            {
                title = title.Replace("sencillo", "ida y regreso");
            }

            title = title.Replace("sencillo", "solo ida");
            title = title.Replace("redondo", "ida y regreso");

            //var text = $"{title} para {totalPaxes} {flightPeople}";
            var text = $"{title}";

            return text;
        }

        #endregion

        private static string GetFormattedDateTime(DateTime date)
        {
            var dateA = date.ToString("s").Split("T");
            return dateA[0] + "T" + dateA[1] + ".000Z";
        }

        private static FlightReservationSegment GetSegment(List<SummaryFlightSegment> flightSegments, int segmentId = 1)
        {
            if (flightSegments == null || !flightSegments.Any())
            {
                return new FlightReservationSegment();
            }

            var flightStart = flightSegments.Last();
            var flightEnd = flightSegments.First();

            return new FlightReservationSegment
            {
                SegmentId = segmentId,
                SegmentIndex = 0,
                AirlineCode = flightStart.OperatingAirlineCode,
                AirlineName = flightStart.OperatingAirline,

                DepartAirportCode = flightEnd.DepartureAirportCode,
                DepartCity = flightEnd.DepartureAirport,
                DepartureTime = GetFormattedDateTime(flightEnd.DepartureTime),
                DepartTimeZone = 0,

                ArrivalAirportCode = flightStart.ArrivalAirportCode,
                ArrivalCity = flightStart.ArrivalAirport,
                ArrivalTime = GetFormattedDateTime(flightStart.ArrivalTime),
                ArrivalTimeZone = 0,
               

                FlightNumber = flightStart.FlightNumber,
                TicketClass = flightStart.FamilyFareName,
            };
        }

        private static List<FlightReservationSegmentIntermediatePoint> GetSegmentStops(List<SummaryFlightSegment> flightSegments, int segmentId = 1)
        {
            var segments = new List<FlightReservationSegmentIntermediatePoint>();

            if (flightSegments.Count > 1)
            {
                for (int i = 1; i < flightSegments.Count; i++)
                {
                    var item = flightSegments[i];
                    segments.Add(new FlightReservationSegmentIntermediatePoint
                    {
                        SegmentId = segmentId,
                        LocationAirportCode = item.DepartureAirportCode,
                        ArrivalCity = item.DepartureAirport,
                        ArrivalTime = GetFormattedDateTime(item.ArrivalTime),
                        DepartureTime = GetFormattedDateTime(item.DepartureTime),
                        FlightNumber = item.FlightNumber,
                        TicketClass = item.FamilyFareName
                    });
                }
            }
            

            return segments;
        }

    }


}
