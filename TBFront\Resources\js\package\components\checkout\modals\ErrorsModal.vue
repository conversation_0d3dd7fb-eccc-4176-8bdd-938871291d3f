<template>
    <div class="modal fade" id="modal-error" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"  v-on:click="goBack()"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-12 d-flex justify-content-center mb-3">
                            <svg width="45" height="45" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M10.656 8.864q0-2.208 1.568-3.776t3.776-1.568 3.776 1.568 1.6 3.776q0 0.256-0.064 0.448l-1.76 6.944q-0.096 1.408-1.12 2.368t-2.432 0.96q-1.376 0-2.4-0.928t-1.152-2.304q-0.32-0.96-0.672-2.112t-0.736-2.784-0.384-2.592zM12.416 24.928q0-1.472 1.056-2.496t2.528-1.056 2.528 1.056 1.056 2.496q0 1.504-1.056 2.528t-2.528 1.056-2.528-1.056-1.056-2.528z"
                                    fill="#EFBC38"></path>
                            </svg>
                        </div>
                        <div class="col-12 d-flex justify-content-center mb-3 text-center">
                            <h4 style="color: #EFBC38;">{{ __('messages.error_reserv') }}</h4>
                        </div>

                        <div class="col-12 d-flex justify-content-center mb-3">
                            <button v-on:click="goBack()"
                                class="btn btn-outline-primary btn-block btn-lg py-3 btn-color-bg-alert-outline">
                                {{ __('messages.return_label') }} <i class="bi bi-x"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>


export default {
    props: {
        quote: {}
    },
    data() {
        return {
            strPrice: ""
        };
    }, async mounted() {
    },
    methods: {
        goBack() {
            window.location.href = this.quote.referralUrl;
        }

    }
}
</script>
