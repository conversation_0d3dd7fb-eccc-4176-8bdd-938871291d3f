import { defineStore } from 'pinia'
import { apiRequestService } from '../../utils/http'
import { useUserSelectionStore } from "./user-selection";

export const useFlightFiltersStore = defineStore({
	id: 'flightFilters',
	state: () => ({
		loading: true,
		flightFilters: [],
		flightFiltersApplied: {
			1: [],
			2: [],
			3: [],
			4: []
		}
	}),
	getters: {
		getLoading() {
			return this.loading
		},
		getFlightFilters() {
			return this.flightFilters
		},
		getDisplayNameByUri: (state) => {
			return (uri) => {
				let text = '';
				_.forEach(state.flightFilters, (f) => {
					const filter = _.filter(f.filters, { uri });
					if (filter && filter.length) {
						text = filter[0].uri.length == 2 ? filter[0].display : "messages." + filter[0].uri;
						return false;
					}
				});

				return text;
			}
		},
		getFlightFiltersApplied() {
			return this.flightFiltersApplied;
		}
	},
	actions: {
		async getFlightFiltersData(quoteToken, filter, arrFlightFilters) {
			this.loading = true;
			this.flightFilters = {};
			// const flightFilters = await apiRequestService({
			// 	method: 'get',
			// 	uri: '/filter'
			// }, { token: quoteToken })

			this.flightFilters = arrFlightFilters

			if (this.flightFilters && this.flightFilters.length) {
				for (const group of this.flightFilters) {
					const groupId = group.id;

					const filtersApplied = [];
					for (const filter of group.filters) {
						if (filter.isSelected) {
							filtersApplied.push(filter.uri);
						}
					}
					this.flightFiltersApplied[groupId] = filtersApplied;
				}
			}

			this.loading = false;
			if (filter) {
				const userSelectionStore = useUserSelectionStore();
				let filtersApplied = {};

				_.forEach(this.flightFilters, (f) => {
					const filter = _.filter(f.filters, { isSelected: true });
					let text = "";
					for (let index = 0; index < filter.length; index++) {
						if (index == 0) {
							text += filter[index].uri;
						} else {
							text += "," + filter[index].uri;
						}
					}
					filtersApplied[f.uri] = text;
				});
				
			}

		},
		cleanFilters() {
			this.flightFiltersApplied = {
				1: [],
				2: [],
				3: [],
				4: []
			}
		},
		changeLoading() {
			this.loading = true;
		},
		removeFilter(groupId, value) {
			_.remove(this.filtersApplied[groupId], (v) => v === value);
		},
		addFilter(groupId, value) {
			this.filtersApplied[groupId].push(value);
		},
	}
})