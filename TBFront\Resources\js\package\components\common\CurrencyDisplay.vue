<template>
    <div class="currency-display">
        {{ spacing( ` ${plusIndicator} ${currencySymbol} ${$filters.currency(amount,decimalsApply, applyConvertion)} `) }}
        <span v-if="showCurrencyCode" :style="{ fontSize: reduceIsoFont ? '1rem' : '' }">{{ currencyCodeMain }}</span>
    </div>
</template>

<script>

const { currencySymbol, currencyCodeName, decimalDigits } = __pt.settings.site;

export default {
    name: "CurrencyDisplay",
    props: ['amount', 'showCurrencyCode', 'currencyCode', 'applyDecimals', 'applyConvertion', 'applySymbol', 'reduceIsoFont', 'plusSymbol', 'applyCompression'],
    data() {
        return {
            currencySymbol: this.applySymbol ? "" : currencySymbol,
            currencyCodeMain: currencyCodeName,
            decimalsApply: this.applyDecimals ? decimalDigits : 0,
            plusIndicator: this.plusSymbol && (this.amount >= 0) ? "+" : "",
        };
    },
    async mounted() {
        this.currencyCodeMain = this.currencyCode ? this.currencyCode : currencyCodeName;
    },
    methods: {
        spacing(str){
            return this.applyCompression ? str.replace(/\s+/g, '') : str;
        }
    },
};
</script>

<style scoped>
.currency-display {
    display: inline-block;
    line-height: 1;
}
</style>