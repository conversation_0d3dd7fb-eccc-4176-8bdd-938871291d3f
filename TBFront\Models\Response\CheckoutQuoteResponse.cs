﻿

using TBFront.Models.Common;
using TBFront.Models.Flight.Quote;

namespace TBFront.Models.Response
{
    public class CheckoutQuoteResponse
    {
        public QuoteApiResponse Quote { get; set; }
        public Dictionary<string, TBFront.Models.Flight.Quote.Flight>? Flights { get; set; }
        public Dictionary<string, FareLeg>? Fares { get; set; }
        public string? Hash { get; set; }
        public string? Status { get; set; }
        public string? Message { get; set; }

        public CheckoutQuoteResponse()
        {

        }

    }

    public class QuoteApiResponse
    {
        public DateTime CheckIn { get; set; }
        public DateTime CheckOut { get; set; }
        public int Days { get; set; }
        public int? Adults { get; set; }
        public int? Children { get; set; }
        public int? TotalPaxes { get; set; }
        public string Currency { get; set; }
        public string Language { get; set; }
        public int ChannelId { get; set; }
        public int SiteId { get; set; }
        public string? Culture { get; set; }
        public int Organization { get; set; }
        public string Site { get; set; }
        public string? UserKey { get; set; }
        public DateTime QuoteExpirationDate { get; set; }
        public List<Pax> Paxes { get; set; }
        public RateSelect Rate { get; set; }
        public FlightItinerary FlightItinerary { get; set; }
        public ExtraInfoFlight? ExtraInfoFlight { get; set; }
        public string? Token { get; set; }
        public string Source { get; set; }
        public string? Thumbnail { get; set; }
        public string? LanguageCode { get; set; }
        public string? FareKey { get; set; }
        public string? ReferralUrl { get; set; }
        public bool IsDomesticRoute { get; set; }
        public string SessionId { get; set; } = string.Empty;
        public bool IsRoundTrip { get; set; } = false;
        public List<TaskIDItem>? QuoteTaskID { get; set; }
        public string? QuoteToken { get; set; }
        public List<bool>? Multiple { get; set; }

    }

    public class FlightItinerary

    {
        public FlightItemQuote Starting { get; set; }
        public FlightItemQuote? Returning { get; set; }
        public string? FamilyFareCode { get; set; }

        public FlightItinerary()
        {
            Starting = new FlightItemQuote();
            Returning = new FlightItemQuote();

        }
    }

    public class FlightItemQuote
    {
        public int Id { get; set; }
        //public int Engine { get; set; }
        public int ItineraryId { get; set; }
        public string? Airline { get; set; } = string.Empty;
        public string? AirlineCode { get; set; } = string.Empty;
        public string? LogoUri { get; set; }
        public FlightInformation Arrival { get; set; }
        public FlightInformation Departure { get; set; }
        public int Scales { get; set; }
        //public int DaysOfFlight { get; set; }
        public string? FlightDuration { get; set; }
        public string? FlightNumber { get; set; }
        public string? ArrivalFullName { get; set; }
        public string? DepartureFullName { get; set; }
        public string? ArrivalName { get; set; }
        public string? DepartureName { get; set; }
        public int FareId { get; set; }
        public string? FareGroup { get; set; } = string.Empty;
        public string? FareKey { get; set; } = string.Empty;
        public int? NegotiatedFareId { get; set; }
        public string? airlineLogoUrl { get; set; }
        public string? FamilyFareCode { get; set; }
        public FlightItemQuote()
        {
            Arrival = new FlightInformation();
            Departure = new FlightInformation();
        }
    }

    public class FlightInformation
    {
        public string? Date { get; set; }
        public string? Time { get; set; }
        public string? Airport { get; set; }
        public string? Terminal { get; set; }
    }


    public class ExtraInfoFlight
    {
        public bool CheckInWasModified { get; set; }
        public string StartingFrom { get; set; }
        public string ReturningFrom { get; set; }
        public string StartingFromDateTime { get; set; }
        public string ReturningFromDateTime { get; set; }
        public string? SelectedOutboundFlight { get; set; }
        public string? SelectedReturnFlight { get; set; }
        public string Farekey { get; set; }
        public bool IsUpsell { get; set; }

    }

    public class RateSelect
    {
        public bool IsAvailable { get; set; }
        public double AverageRate { get; set; }
        public double AverageRateWithTaxes { get; set; }
        public double Tax { get; set; }
        public double TotalAmount { get; set; }
        public double Cost { get; set; }
        public double PrePromotionalRate { get; set; }
        public double PrePromotionalRateWithTaxes { get; set; }
        public bool HasTaxes { get; set; }
        public int Discount { get; set; }
        public double DiscountAmount { get; set; }
        public double DiscountWithoutTaxAmount { get; set; }
        public BookNowPayLater BookNowPayLater { get; set; }
        public string? CampaignToken { get; set; }
        public double TaxesNight { get; set; }
        public int ChkSource { get; set; }
        public string? Site { get; set; }
        public int TaxScheme { get; set; }
        public double Amount { get; set; }
        public string Country { get; set; }
        public List<FareDetail>? Breakdown { get; set; }
        public bool IsRoundtrip { get; set; }


        public RateSelect()
        {
            BookNowPayLater = new BookNowPayLater();
        }

    }

    public class BookNowPayLater
    {
        public bool IsBookNowPayLater { get; set; }
        public DateTime DateLimitBookNowPayLater { get; set; }


    }


    public class RoomSelect
    {
        public string CheckInTime { get; set; }
        public string CheckOutTime { get; set; }
        public int RoomID { get; set; }
        public string Name { get; set; }
        public string Image { get; set; }
        public string Bedding { get; set; }
    }


}
