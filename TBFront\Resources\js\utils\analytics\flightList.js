import { setDatalayer, UTILS, getChildren } from "./main"

export default class FlightList {


    constructor() {
        this.settings = window.__pt.settings.site;
        this.request = window.__pt.data || {};
        if (window.__pt.data != undefined) {
            this.tripMode = window.__pt.data.isNational ? "national" : "international";
            this.mode = window.__pt.data.isRoundtrip ? "roundtrip" : "oneway";
            this.genericEventLabel = `${this.tripMode}|${this.request.startingFromAirport}-${this.request.returningFromAirport}|${this.mode}`
        }
    }

    flightNotAvailable() {
        const checkOut = !this.request.isRoundtrip ? "--" : this.request.checkOut;
        let event = {
            eventAction: UTILS.actions.ruta_sin_vuelos,
            eventCategory: UTILS.categories.error,
            eventLabel: `${this.genericEventLabel}|ida:${this.request.checkIn}|regreso:${checkOut}|adults:#${this.request.adults}|kids:#${this.request.kids}`,
            event: UTILS.events.gtmEvent,
            eventName: UTILS.events.gtmEvent,
            eventExtra: ''
        }
        setDatalayer(event);
    }

    modalDetail(airlineCode, fareCode, view = "", luggage = false) {
        const content = window.__pt.data.isNational ? "content:yes" : `content-${view}:yes`
        let event = {
            eventAction: luggage ? UTILS.actions.brands_modal : UTILS.actions.flight_modal,
            eventCategory: UTILS.categories.flights_list,
            eventLabel: `${this.genericEventLabel}|${airlineCode}|${fareCode}|${content}`,
            event: UTILS.events.gtmEvent,
            eventName: UTILS.events.gtmEvent,
            eventExtra: ''
        }
        setDatalayer(event);
    }

    eventListReturning() {
        let event = {
            eventCategory: UTILS.categories.flights_list,
            eventLabel: this.genericEventLabel,
            eventName: UTILS.events.select_item_returning,
            event: "ga4." + UTILS.events.trackEvent,
        }
        setDatalayer(event);
    }

    cheapestFlights() {
        let event = {
            eventAction: UTILS.actions.economic,
            eventCategory: UTILS.categories.flights_list,
            eventLabel: this.genericEventLabel,
            event: UTILS.events.gtmEvent,
            eventName: UTILS.events.gtmEvent,
            eventExtra: '',
        }
        setDatalayer(event);
    }

    nearbyDates() {
        let event = {
            eventAction: UTILS.actions.near_dates,
            eventCategory: UTILS.categories.flights_list,
            eventLabel: this.genericEventLabel,
            event: UTILS.events.gtmEvent,
            eventName: UTILS.events.gtmEvent,
            eventExtra: '',
        }
        setDatalayer(event);
    }

    flights(dataGroupsList, item_list = true) {
        const checkOut = !this.request.isRoundtrip ? "--" : this.request.checkOut;
        const children = getChildren(this.request.paxes[0]['children']);
        const landing = this.request.landing == "listado" ? "home" : this.request.landing;

        let consolidatedItems = [];

        dataGroupsList.forEach(dataGroup => {
            let cantidad = 0;
            if (item_list) {
                cantidad = !this.request.isNational ? dataGroup.departure.total_flights
                    : (this.request.isRoundtrip ? dataGroup.departure.total_flights + dataGroup.returning.total_flights : dataGroup.departure.total_flights);
            }

            let items = [{
                index: parseInt(dataGroup.index),
                item_brand: dataGroup.code,
                item_category: this.mode == 'roundtrip' ? "roundtrip" : "oneway",
                item_category2: this.request.isNational ? "national" : "international",
                item_id: `${this.request.startingFromAirport}-${this.request.returningFromAirport}-${dataGroup.code}`,
                item_name: `${this.request.startingAirportPlace.cityName}-${this.request.returningAirportPlace.cityName}-${dataGroup.code}`,
                price: this.request.isRoundtrip && !this.request.isNational ? dataGroup.departure.price + dataGroup.returning.price : dataGroup.departure.price,
                quantity: this.request.adults + this.request.kids
            }];

            if (this.request.isNational) {
                items[0]['item_variant'] = dataGroup.departure.view;
                if (!item_list) {
                    if (dataGroup.departure.view == 'returning') {
                        items[0]['item_id'] = `${this.request.returningFromAirport}-${this.request.startingFromAirport}-${dataGroup.code}`;
                    }
                    delete items[0].quantity;
                    delete items[0].item_name;
                }
                if (this.request.isRoundtrip && item_list) {
                    items.push({
                        index: parseInt(dataGroup.index),
                        item_brand: dataGroup.code,
                        item_category: this.mode == 'roundtrip' ? "roundtrip" : "oneway",
                        item_category2: this.request.isNational ? "national" : "international",
                        item_id: `${this.request.returningFromAirport}-${this.request.startingFromAirport}-${dataGroup.code}`,
                        item_name: `${this.request.returningAirportPlace.cityName}-${this.request.startingAirportPlace.cityName}-${dataGroup.code}`,
                        price: dataGroup.returning.price,
                        quantity: this.request.adults + this.request.kids,
                        item_variant: dataGroup.returning.view
                    });
                }
            }

            consolidatedItems.push(...items);
        });

        let event = {
            eventParams: {
                field_date1: this.request.checkIn,
                field_date2: checkOut,
                field_destination_iata: this.request.returningFromAirport,
                field_destination_name: this.request.returningAirportPlace.cityName,
                field_origin_iata: this.request.startingFromAirport,
                field_origin_name: this.request.startingAirportPlace.cityName,
                item_list_name: `flights_${landing}`,
                items: consolidatedItems,
                layer: 'flights',
                total_flights: consolidatedItems.length,
                travelers_adults: parseInt(this.request.adults),
                travelers_children: parseInt(this.request.kids),
                travelers_infants_inseat: parseInt(children.infants),
                travelers_infants_onlap: parseInt(children.kids)
            },
            event: 'ga4.' + UTILS.events.trackEvent,
            eventName: item_list ? UTILS.events.view_item_list : UTILS.events.select_item,

        };
        setDatalayer(event);

        if ((this.request.startingAirportPlace.cityName == "") || (this.request.returningAirportPlace.cityName == "")) {
            var action = this.request.startingAirportPlace.cityName == "" ? this.request.startingFromAirport : this.request.returningFromAirport;
            let event = {
                eventCategory: `no-place`,
                eventAction: action,
                event: UTILS.events.gtmEvent,
                eventName: UTILS.events.gtmEvent,
                eventLabel: `${this.request.startingFromAirport} - ${this.request.returningFromAirport}`,
                eventExtra: '',
            }
            setDatalayer(event);
        }
    }

    requoteModal(type, eventAction=null) {
        let eventType = "";
        if (type && type.length) {
            eventType = `:: ${type}`;
        }
        let event = {
            eventAction: eventAction || this.tripMode,
            eventCategory: UTILS.categories.recotizacion_lista,
            eventLabel: `${this.request.startingFromAirport}-${this.request.returningFromAirport} :: ${this.mode} ${eventType}`,
            eventExtra: '',
            event: UTILS.events.gtmEvent,
            eventName: UTILS.events.gtmEvent,
        }
        setDatalayer(event);
    }

    filtersWithoutFlights(filters) {
        let event = {
            event: UTILS.events.gtmEvent,
            eventName: UTILS.events.gtmEvent,
            eventAction: `${this.request.startingFromAirport}-${this.request.returningFromAirport} :: ${this.mode} :: ${this.request.startingFromDateTime} - ${this.request.returningFromDateTime}`,
            eventCategory: UTILS.categories.filtros_sin_vuelos,
            eventExtra: '',
            eventLabel: this.mapEventFilters(filters, false)
        }
        setDatalayer(event);
    }

    setNoPlaces(place) {
        let event = {
            eventAction: 'no-place',
            event: UTILS.events.gtmEvent,
            eventName: UTILS.events.gtmEvent,
            eventCategory: UTILS.categories.no_places,
            eventLabel: place,
            eventExtra: '',
        }
        setDatalayer(event);
    }

    flightFilters(filters) {
        let event = {
            eventAction: UTILS.events.matrix,
            eventCategory: UTILS.categories.flights_list,
            event: UTILS.events.gtmEvent,
            eventName: UTILS.events.gtmEvent,
            eventLabel: `${this.tripMode} | ${this.request.startingFromAirport}-${this.request.returningFromAirport} | ${this.mapEventFilters(filters, true)}${this.mode}`,
            eventExtra: '',
        }
        setDatalayer(event);
    }

    mapEventFilters(filters, matrix) {
        let categories = {
            stops: [],
            airlines: []
        };
        let cheapestFlights = false;
        let eventLabel = '';
        for (let id in filters) {
            if (filters[id] == 'nonStopsRates' || filters[id] == 'oneStopRates' || filters[id] == 'cheapestFlights') {
                if (filters[id] == 'cheapestFlights') {
                    cheapestFlights = true;
                } else {
                    categories['stops'].push(filters[id]);
                }
            } else {
                categories['airlines'].push(filters[id]);
            }
        }
        for (let category in categories) {
            if (categories[category].length > 0) {
                const categoryString = categories[category].toString();
                eventLabel += `${category}: ${categoryString} | `;
            }
        }
        if (cheapestFlights && !matrix) {
            eventLabel += "cheapestFlights";
        }
        return eventLabel;
    }
    selectRoundtripNationalFlight(departure_fare, flight, fare){
        let event = {
            eventAction: UTILS.events.selected_roundtrip_flight,
            eventCategory: UTILS.actions.round_special_rate,
            eventLabel: `${flight.airline.code}|${this.genericEventLabel}|${this.request?.checkIn}|${this.request?.checkOut}|${fare.fareKey}`,
            eventExtra: `ida:${departure_fare.fareKey}|regreso:${fare?.displayAmount - fare?.beforeDisplayAmount}`,
            event: UTILS.events.gtmEvent,
            eventName: UTILS.events.gtmEvent,
        }
        setDatalayer(event);
    }
    roundtripNationalFlight(departure_fare, flight, fare){
        let event = {
            eventAction: UTILS.events.roundtrip_flight,
            eventCategory: UTILS.actions.round_special_rate,
            eventLabel: `${flight.airline.code}|${this.genericEventLabel}|${this.request?.checkIn}|${this.request?.checkOut}`,
            eventExtra: `ida:${departure_fare.fareKey}|regreso:${fare.fareKey}|${fare?.displayAmount - fare?.beforeDisplayAmount}`,
            event: UTILS.events.gtmEvent,
            eventName: UTILS.events.gtmEvent,
        }
        setDatalayer(event);
    }
}

export const List = new FlightList();