﻿using TBFront.Infrastructure.HttpService.ContentDeliveryNetwork.Dtos;
using TBFront.Interfaces;

namespace TBFront.Infrastructure.HttpService.ContentDeliveryNetwork
{
    public static class ContentDeliveryNetworkServicesRegister
    {
        public static void AddContentDeliveryNetworkRegister(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddHttpClient<ContentDeliveryNetworkService>("");
            services.AddSingleton(s => configuration.GetSection("HttpCdnServiceConfiguration").Get<ContentDeliveryNetworkConfiguration>());
            services.AddSingleton<IContentDeliveryNetworkService, ContentDeliveryNetworkService>();
        }
    }
}
