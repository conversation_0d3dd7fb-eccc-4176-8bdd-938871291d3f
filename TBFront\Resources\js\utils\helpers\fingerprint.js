function checkIfMobile() {
    const isMobileUserAgent = /Mobi|Android|iPhone|iPad|iPod/.test(navigator.userAgent);
    const isTouchDevice = 'ontouchstart' in window;
    return isMobileUserAgent || isTouchDevice;
}

function generateUniqueId() {
    const randomValues = crypto.getRandomValues(new Uint32Array(4));
    return `${randomValues.join('-')}`;
}

function setCookie(name, value, days) {
    const date = new Date();
    date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
    const cookieOptions = `expires=${date.toUTCString()}; path=/; Secure; SameSite=Strict`;
    document.cookie = `${name}=${value}; ${cookieOptions}`;
}

function getCookie(name) {
    const nameEQ = name + "=";
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
        while (cookie.charAt(0) === ' ') cookie = cookie.substring(1);
        if (cookie.indexOf(nameEQ) === 0) return cookie.substring(nameEQ.length);
    }
    return null;
}

async function openIndexedDB(dbName, storeName) {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open(dbName, 1);
        request.onupgradeneeded = (event) => {
            const db = event.target.result;
            if (!db.objectStoreNames.contains(storeName)) {
                db.createObjectStore(storeName, { keyPath: "key" });
            }
        };
        request.onsuccess = () => resolve(request.result);
        request.onerror = () => reject(new Error("No se pudo abrir IndexedDB"));
    });
}

async function getFromIndexedDB(db, storeName, keyName) {
    return new Promise((resolve, reject) => {
        const transaction = db.transaction([storeName], "readonly");
        const store = transaction.objectStore(storeName);
        const request = store.get(keyName);
        request.onsuccess = () => resolve(request.result ? request.result.value : null);
        request.onerror = () => reject(new Error("No se pudo obtener el valor de IndexedDB"));
    });
}

async function saveToIndexedDB(db, storeName, keyName, value) {
    return new Promise((resolve, reject) => {
        const transaction = db.transaction([storeName], "readwrite");
        const store = transaction.objectStore(storeName);
        const request = store.put({ key: keyName, value });
        request.onsuccess = () => resolve();
        request.onerror = () => reject(new Error("No se pudo guardar en IndexedDB"));
    });
}

async function getOrCreateDeviceId() {
    const dbName = "MobileDeviceDB";
    const storeName = "DeviceStore";
    const keyName = "mobileDeviceId";

    try {
        const db = await openIndexedDB(dbName, storeName);
        let deviceId = await getFromIndexedDB(db, storeName, keyName);

        if (!deviceId) {
            deviceId = generateUniqueId();
            await saveToIndexedDB(db, storeName, keyName, deviceId);
        }

        return deviceId;
    } catch (error) {
        console.warn("IndexedDB no disponible, usando localStorage:", error);
        return getOrCreateDeviceIdFallback();
    }
}

function getOrCreateDeviceIdFallback() {
    let deviceId = null;
    if (typeof localStorage !== 'undefined') {
        deviceId = localStorage.getItem('mobileDeviceId');
    }
    if (!deviceId) {
        deviceId = getCookie('mobileDeviceId');
    }
    if (!deviceId) {
        deviceId = generateUniqueId();
        if (typeof localStorage !== 'undefined') {
            localStorage.setItem('mobileDeviceId', deviceId);
        }
        setCookie('mobileDeviceId', deviceId, 365);
    }
    return deviceId;
}

export async function getBrowserFingerprint() {
    try {
        const deviceId = await getOrCreateDeviceId();
        const deviceType = checkIfMobile() ? "mbl" : "dsk";

        return `${deviceType}-${deviceId}`;
    } catch (error) {
        console.error("Error generating fingerprint:", error);
        if (error.message.includes("IndexedDB")) {
            console.warn("IndexedDB no está disponible, usando localStorage.");
        }
        return 'fingerprint-generation-failed';
    }
}