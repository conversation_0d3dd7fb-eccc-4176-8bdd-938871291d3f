<template>
<div class="container mt-5 container-app">
    <div class="row">
        <div class="col-md-12">
            <div class="card mb-5">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-9">
                            <p class="h1">{{__("messages.success_voucher")}}</p>
                            <p class="h3 mt-2">{{__("messages.processing_booking")}}</p>
                        </div>
                        <div class="col-md-3">
                            <div class="border border-secondary text-center rounded py-2">
                                <p> {{__("messages.reservacion_id")}} </p>
                                <p class="font-weight-bold h4"> {{data.id}} </p>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <p class="mt-2">
                                {{__("messages.we_send_email")}} <span class="font-weight-bold text-success"> {{data.email}} </span>
                            </p>
                            <p class="h4 mt-5">  {{__("messages.description_voucher")}} </p>
                            <p>
                                {{__("messages.description_voucher_detail")}}
                            </p>
                        </div>

                        <div class="col-md-12 text-center" v-if="retry">
                            <p class="mt-2 font-weight-bold"> {{__("messages.please_wait_voucher")}} </p>
                            <div class="d-flex justify-content-center loader-retry"> <div class="loader__circle"></div> </div>
                        </div>

                        <div class="col-12 mt-5"><a :href="__('urls.url_go_home')" class="font-weight-bold">{{__("messages.go_home")}}</a></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</template>

<script>
    //import { AlgoliaServices } from '../../services/AlgoliaService'

    export default {
        props: ['data', 'retry'],
        data() {
            return {
                type: {}
            };
        },
        async mounted() {
            this.getEventAlgolia();
        },
        methods: {
            getEventAlgolia() {
                //AlgoliaServices.getEventAlgolia("Order");
            }
        },
        components: {

        }
    }
</script>
