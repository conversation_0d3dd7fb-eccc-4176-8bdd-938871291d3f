﻿import { __ } from "../../utils/helpers/translate";
class LuggageMappers {

    map(responses) {
        let groups = {};
        responses.forEach(response => {
            for (let code in response) {
                let item = {};
                item['luggage'] = {};
                item['extra'] = {};
                item['name'] = response[code].familyFareName;
                item['code'] = response[code].code;
                const luggages = { 1: false, 2: false };
                for (let index in response[code].familyFareContent) {
                    const category = response[code].familyFareContent[index].category;
                    if (category in luggages) {
                        luggages[category] = true;
                        item['luggage'][category] = response[code].familyFareContent[index];
                        item['extra'][category] = this.extraDataLuggage(category, response[code].familyFareContent[index].include);
                    }
                }
                for (let luggage in luggages) {
                    if (luggages[luggage] == false) {
                        item['extra'][luggage] = this.extraDataLuggage(luggage, 2);
                    }
                }
                groups[response[code].id] = item;
            }
        });
        return groups;
    }

    mapFamilyFare(responses) {
        let item = {
            luggage: {},
            extra: {},
            name: responses?.name ?? "Unknown" // Valor por defecto si name es null o undefined
        };
    
        const luggages = { 1: false, 2: false };
    
        if (Array.isArray(responses?.content)) {
            for (const element of responses.content) {
                const category = element?.category;
                if (category in luggages) {
                    luggages[category] = true;
                    item['luggage'][category] = element ?? {}; // Evitar null en element
                    item['extra'][category] = this.extraDataLuggage(category, element?.include ?? 0);
                }
            }
        }
    
        for (let luggage in luggages) {
            if (!luggages[luggage]) {
                item['extra'][luggage] = this.extraDataLuggage(luggage, 2);
            }
        }
    
        return item;
    }

    extraDataLuggage(type, include) {
        const data = {};
        data['class'] = type == 1 ? "icon icon-big-bag" + (include != 1 ? "-out" : " btn-link") : "icon icon-carry-on" + (include != 1 ? "-bag" : " btn-link");
        data['title'] = type == 1 ? `${__("flightList.baggage")}` : `${__("flightList.carryOn")}`;
        data['title'] += include != 1 ? ` ${__("flightList.notIncluded")}` : ` ${__("flightList.included")}`;
        return data;
    }

}
export const LuggageMapper = new LuggageMappers();