﻿
@import "../../css/foundations-tb.css";
@import "./base/typography.scss";

/*--------------------------------------- HELPERS ---------------------------------------*/

/* Background Colors */
.bg-primary { background-color: var(--bg-primary) !important; }
.bg-primary-hover:hover { background-color: var(--bg-primary-hover) !important; }
.bg-primary-subtle { background-color: var(--bg-primary-subtle) !important; }
.bg-primary-l1 { background-color: var(--bg-primary-level1) !important; }
.bg-primary-l2 { background-color: var(--bg-primary-level2) !important; }

.bg-secondary { background-color: var(--bg-secondary) !important; }
.bg-secondary-hover:hover { background-color: var(--bg-secondary-hover) !important; }
.bg-secondary-subtle { background-color: var(--bg-secondary-subtle) !important; }
.bg-secondary-l1 { background-color: var(--bg-secondary-level1) !important; }
.bg-secondary-l2 { background-color: var(--bg-secondary-level2) !important; }

.bg-base { background-color: var(--bg-base) !important; }
.bg-l1 { background-color: var(--bg-level1) !important; }
.bg-l2 { background-color: var(--bg-level2) !important; }
.bg-l3 { background-color: var(--bg-level3) !important; }
.bg-l4 { background-color: var(--bg-level4) !important; }
.bg-disabled { background-color: var(--bg-disabled) !important; }

/* Status Background Colors */
.bg-error { background-color: var(--bg-error) !important; }
.bg-error-subtle { background-color: var(--bg-error-subtle) !important; }
.bg-success { background-color: var(--bg-success) !important; }
.bg-success-subtle { background-color: var(--bg-success-subtle) !important; }
.bg-warning { background-color: var(--bg-warning) !important; }
.bg-warning-subtle { background-color: var(--bg-warning-subtle) !important; }
.bg-info { background-color: var(--bg-info) !important; }
.bg-info-subtle { background-color: var(--bg-info-subtle) !important; }

/* Border Colors */
.border-primary { border-color: var(--border-primary) !important; }
.border-primary-hover:hover { border-color: var(--border-primary-hover) !important; }
.border-primary-subtle { border-color: var(--border-primary-subtle) !important; }

.border-secondary { border-color: var(--border-secondary) !important; }
.border-secondary-hover:hover { border-color: var(--border-secondary-hover) !important; }
.border-secondary-subtle { border-color: var(--border-secondary-subtle) !important; }

.border-strong { border-color: var(--border-strong) !important; }
.border-strong-hover:hover { border-color: var(--border-strong-hover) !important; }
.border-subtle { border-color: var(--border-subtle) !important; }
.border-subtle-hover:hover { border-color: var(--border-subtle-hover) !important; }
.border-disabled { border-color: var(--border-disabled); }

.border-1 { border-width: var(--border-width-sm) !important; }
.border-2 { border-width: var(--border-width-lg) !important; }

.border-radius-4 { border-radius: var(--border-radius-xxs) !important; }
.border-radius-8 { border-radius: var(--border-radius-xs) !important; }
.border-radius-12 { border-radius: var(--border-radius-sm) !important; }
.border-radius-16 { border-radius: var(--border-radius-md) !important; }
.border-radius-full { border-radius: var(--border-radius-full) !important; }

/* Status Border Colors */
.border-error { border-color: var(--border-error) !important; }
.border-error-hover:hover { border-color: var(--border-error-hover) !important; }
.border-success { border-color: var(--border-success) !important; }
.border-success-hover:hover { border-color: var(--border-success-hover) !important; }
.border-warning { border-color: var(--border-warning) !important; }
.border-warning-hover:hover { border-color: var(--border-warning-hover) !important; }
.border-info { border-color: var(--border-info) !important; }
.border-info-hover:hover { border-color: var(--border-info-hover) !important; }

/* Icon Colors */
.icon-primary { color: var(--icon-primary) !important; }
.icon-primary-strong { color: var(--icon-primary-strong) !important; }
.icon-secondary { color: var(--icon-secondary) !important; }
.icon-secondary-strong { color: var(--icon-secondary-strong) !important; }  
.icon-link { color: var(--icon-link) !important; }
.icon-link-hover:hover { color: var(--icon-link-hover) !important; }    
.icon-main { color: var(--icon-main) !important; }
.icon-strong { color: var(--icon-strong) !important; }
.icon-subtle { color: var(--icon-subtle) !important; }
.icon-disabled { color: var(--icon-disabled) !important; }

/* Text Colors */
.text-primary { color: var(--text-primary) !important; }
.text-primary-strong { color: var(--text-primary-strong) !important; }
.text-secondary { color: var(--text-secondary) !important; }
.text-secondary-strong { color: var(--text-secondary-strong) !important; }

.text-link { color: var(--text-link) !important; }
.text-link-hover:hover { color: var(--text-link-hover) !important; }

.text-main { color: var(--text-main) !important; }
.text-strong { color: var(--text-strong) !important; }
.text-subtle { color: var(--text-subtle) !important; }
.text-disabled { color: var(--text-disabled) !important; }
.text-oncolor { color: var(--text-oncolor) !important; }

/* Status Text Colors */
.text-error { color: var(--text-error) !important; }
.text-error-strong { color: var(--text-error-strong) !important; }
.text-success { color: var(--text-success) !important; }
.text-success-strong { color: var(--text-success-strong) !important; }
.text-warning { color: var(--text-warning) !important; }
.text-info { color: var(--text-info) !important; }
.text-info-strong { color: var(--text-info-strong) !important; }

/* Typography */
.display-xl { font: var(--display-xl) !important; }
.display-lg { font: var(--display-lg) !important; }
.display-md { font: var(--display-md) !important; }
.display-sm { font: var(--display-sm) !important; }
.display-xs { font: var(--display-xs) !important; }
.display-xxs { font: var(--display-xxs) !important; }

.title-xl { font: var(--title-xl) !important; }
.title-lg { font: var(--title-lg) !important; }
.title-md { font: var(--title-md) !important; }
.title-sm { font: var(--title-sm) !important; }
.title-xs { font: var(--title-xs) !important; }
.title-xxs { font: var(--title-xxs) !important; }

.body { font: var(--body) !important; }
.body-bold { font: var(--body-bold) !important; }
.body-italic { font: var(--body-italic) !important; }
.body-strike { font: var(--body-strikethrough) !important; }

.body-sm { font: var(--body-sm) !important  ; }
.body-sm-bold { font: var(--body-sm-bold) !important; }
.body-sm-italic { font: var(--body-sm-italic) !important; }
.body-sm-strike { font: var(--body-sm-strikethrough) !important; }

.body-xs { font: var(--body-xs) !important; }
.body-xs-bold { font: var(--body-xs-bold) !important; }
.body-xs-italic { font: var(--body-xs-italic) !important; }
.body-xs-strike { font: var(--body-xs-strikethrough) !important; }

.tight { font: var(--tight) !important; }
.tight-bold { font: var(--tight-bold) !important; }
.tight-italic { font: var(--tight-italic) !important; }
.tight-strike { font: var(--tight-strikethrough) !important; }

.tight-sm { font: var(--tight-sm) !important; }
.tight-sm-bold { font: var(--tight-sm-bold) !important; }
.tight-sm-italic { font: var(--tight-sm-italic) !important; }
.tight-sm-strike { font: var(--tight-sm-strikethrough); }

.tight-xs { font: var(--tight-xs) !important; }
.tight-xs-bold { font: var(--tight-xs-bold) !important; }
.tight-xs-italic { font: var(--tight-xs-italic) !important; }
.tight-xs-strike { font: var(--tight-xs-strikethrough) !important; }

/* Font Size Helpers */
.font-12 { font-size: var(--font-12) !important; } /* 12px */
.font-14 { font-size: var(--font-14) !important; } /* 14px */
.font-16 { font-size: var(--font-16) !important; } /* 16px */
.font-18 { font-size: var(--font-18) !important; } /* 18px */
.font-20 { font-size: var(--font-20) !important; } /* 20px */
.font-24 { font-size: var(--font-24) !important; } /* 24px */
.font-28 { font-size: var(--font-28) !important; } /* 28px */
.font-32 { font-size: var(--font-32) !important; } /* 32px */
.font-36 { font-size: var(--font-36) !important; } /* 36px */

.italic { font-style: italic !important; }
.strike { text-decoration: line-through !important; }
.underline { text-decoration: underline !important; }

/* Font Weight Helpers */
.font-medium { font-weight: 500 !important; }
.font-semibold { font-weight: 600 !important; }

/* Elevation */
.shadow-100 { box-shadow: var(--shadow-100) !important; }
.shadow-200 { box-shadow: var(--shadow-200) !important; }
.shadow-300 { box-shadow: var(--shadow-300) !important; }
.shadow-400 { box-shadow: var(--shadow-400) !important; }
.shadow-500 { box-shadow: var(--shadow-500) !important; }

/* Text Alignment */
.text-right { text-align: right !important; }
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }

.d-list {
	display: list-item;
}

.flex-grow-1 {
    flex-grow: 1;
}
