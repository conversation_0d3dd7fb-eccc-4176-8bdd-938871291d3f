﻿const mix = require('laravel-mix');

const localPublicPath = 'wwwroot';

const pathAssets = process.env?.MIX_PATH_ASSETS ? process.env.MIX_PATH_ASSETS : "assets-tb-test";

mix.setPublicPath(localPublicPath);

mix.options({
	processCssUrls: true
});


mix.sass(`Resources/sass/tiquetesbaratos/home.scss`, `wwwroot/assets-tb/tiquetesbaratos/css`);
mix.sass(`Resources/sass/tiquetesbaratos/list.scss`, `wwwroot/assets-tb/tiquetesbaratos/css`);
mix.sass(`Resources/sass/tiquetesbaratos/promotion.scss`, `wwwroot/assets-tb/tiquetesbaratos/css`);
mix.sass(`Resources/sass/tiquetesbaratos/checkout.scss`, `wwwroot/assets-tb/tiquetesbaratos/css`);
mix.sass(`Resources/sass/tiquetesbaratos/error-page.scss`, `wwwroot/assets-tb/tiquetesbaratos/css`);
mix.sass(`Resources/sass/tiquetesbaratos/check-reservation.scss`, `wwwroot/assets-tb/tiquetesbaratos/css`);
mix.sass(`Resources/sass/tiquetesbaratos/checkin.scss`, `wwwroot/assets-tb/tiquetesbaratos/css`);
mix.sass(`Resources/sass/tiquetesbaratos/payonline.scss`, `wwwroot/assets-tb/tiquetesbaratos/css`);
mix.sass(`Resources/sass/tiquetesbaratos/groups.scss`, `wwwroot/assets-tb/tiquetesbaratos/css`);
mix.sass(`Resources/sass/tiquetesbaratos/confirmation.scss`, `wwwroot/assets-tb/tiquetesbaratos/css`);
mix.sass(`Resources/sass/tiquetesbaratos/write-us.scss`, `wwwroot/assets-tb/tiquetesbaratos/css`);
mix.sass(`Resources/sass/tiquetesbaratos/booker.scss`, `wwwroot/assets-tb/tiquetesbaratos/css`);


if (mix.inProduction()) {
	mix.sourceMaps();
}