<template>
    <section class="container c-checkout" v-if="is_valid">

        <div class="row pt-4 pb-2 d-none d-sm-block">
            <div class="col-12">
                <h1 class="title-lg">{{ __("checkout.title") }}</h1>
            </div>
        </div>


        <div class="row row py-4 pt-md-0">
            <div class="col-12 col-md-5">
                <summary-checkout :summary="data" :itineary="dataFly"></summary-checkout>
                
            </div>

            <div class="col-12 col-md-7 pr-md-0 c-payments-checkout">
                <h2 class="title-sm">{{ __("checkout.subtitle") }}</h2>
                <Form @submit="onSubmit" ref="observer" tag="form">
                    <checkout-one-form v-if="user.passengers.length > 0" :key="componentKey" :user="user" :data="data" :disabledSubmitButton="disabledSubmitButton"
                                       ref="ref_form"></checkout-one-form>
                </Form>
            </div>

        </div>

    </section>


    <error-checkout v-if="!loading && !is_valid" :quote="responseQuote"></error-checkout>

    <div v-if="loading" id="loader-page" class="loading-page d-center ">
        <div class="loader__logo"></div>
    </div>

    <ChangeAmountModal v-if="responseSummary && responseSummary.summary && notifyRevalidate"
                       v-bind:summary="responseSummary.summary" :productUrl="data.referralUrl" v-bind:quote="data" v-bind:totalAmountInit="totalAmountInit" />

    <ChangeBookingAmountModal v-if="bookingSummary && bookingSummary.detail" :summary="bookingSummary"
                              :productUrl="data.referralUrl" v-bind:quote="data" />

    <detail-flight-by-leg v-if="dataFly.detailStarting && dataFly.detailStarting.detail"
                          :detailFlightByLeg="dataFly.detailStarting.detail" :flightItinerary="data.flightItinerary.starting"
                          :idModal="'starting'" />

    <detail-flight-by-leg v-if="dataFly.detailReturning && dataFly.detailReturning.detail"
                          :detailFlightByLeg="dataFly.detailReturning.detail" :flightItinerary="data.flightItinerary.returning"
                          :idModal="'returning'" />

    <DetailFlightBaggage v-if="dataFly.detailStarting && dataFly.detailStarting.detailFamilyFare && showFamilyFare"
                         :detailFlightBaggage="dataFly.detailStarting.detailFamilyFare" :logoUri="data.flightItinerary.starting.logoUri"
                         :idModal="'modal-DetailFlightBaggage-starting'"
                         :style="{ overflow: 'hidden', maxHeight: '90vh' }" 
                         />

    <DetailFlightBaggage v-if="dataFly.detailReturning && dataFly.detailReturning.detailFamilyFare && showFamilyFare"
                         :detailFlightBaggage="dataFly.detailReturning.detailFamilyFare" :logoUri="data.flightItinerary.returning.logoUri"
                         :idModal="'modal-DetailFlightBaggage-returning'" 
                         :style="{ overflow: 'hidden', maxHeight: '90vh' }" 
                         />
    <ErrorsModal v-bind:quote="data" />
    <LoaderCheckout :summary="data" :itineary="dataFly" v-if="is_valid && showModalLoading" />
    <PolicyModal />
    <NationalModalDetail />
</template>

<script>
    import { Form } from 'vee-validate';
    import { StorageService } from '../../../services/StorageService';
    import { algoliaEvent } from "../../../utils/analytics/algolia";
    import { FlightsAnalytic } from '../../../utils/analytics/FlightsAnalytics';
    import DetailFlightByLeg from './modals/DetailFlightByLeg.vue';
    import DetailFlightBaggage from './modals/DetailFlightBaggage.vue';
    import ChangeAmountModal from './modals/ChangeAmountModal.vue';
    import ChangeBookingAmountModal from './modals/ChangeBookingAmountModal.vue';
    import ErrorsModal from './modals/ErrorsModal.vue';
    import ErrorCheckout from './ErrorCheckout.vue';
    import LoaderPage from '../common/LoaderPage.vue';
    import LoaderCheckout from './modals/LoaderCheckout.vue';
    import PolicyModal from './modals/PolicyModal.vue';
    import { conteinsItem } from '../../../utils/helpers/data';
    import { addPrefix, getFamilyFare, getModelForm, getSiteInformation } from '../../../utils/utils';
    import _ from 'lodash';
    import { Logger } from '../../../utils/helpers/logger';
    import { getParamsDetailFlight, getFamilyFare as getFamilyRevalidate } from '../../services/ApiFlightFrontServices';
    import { responsiveObserver } from "../../../utils/helpers/responsiveObserver";
    import { useCheckoutStore } from '../../stores/checkout';

    import { useFingerStore } from '../../stores/fingerprint';
    import { useFingerprintStore } from '../../stores/fingerprintSdk';


    const { v4: uuidv4 } = require('uuid');
    const settingsForm = window.__pt.settings.formData;
    const siteCulture = window.__pt.cultureData || {};
    const site = window.__pt.settings.site;
    const ip = window.__pt.ip;
    const exchange = window.__pt.exchange;
    const error_checkout = {
        error_quote: "get-quote",
        error_booking: "create-booking",
        error_revalidate: "get-revalidate",
        error_email: "send-email",

    }

    export default {
        data() {
            return {
                componentKey: 0,
                step: 1,
                rapd: false,
                loading: true,
                siteConfig: site,
                data: {},
                cancellation: {},
                places: {},
                hash: "",
                user: {
                    passengers: [],
                    email: "",
                    phone: "",
                    dial_code: "",
                    user_key: StorageService.get('userKey')
                },
                responseQuote: {},
                responseRevalidate: {},
                responseSummary: {},
                bookingSummary: {},
                is_valid: false,
                transfer: {},
                title: '',
                dataFly: {},
                masterLocatorRes: {},
                rangeTotalAmout: site.rangeTotalAmount,
                isNewRate: false,
                notifyRevalidate: false,
                useTwoOneWayTrip: false,
                revalidateOptions: [],
                isMultipleTicket: false,
                showModalLoading: false,
                modalCheckout: null,
                disabledSubmitButton: false,
                retry_attempts: 1,
                checkRevalidateVar: null,
                showFamilyFare: false,
                isNational:(sessionStorage.getItem('isNational') !== null && sessionStorage.getItem('isNational') === 'true'),
                fingerprintHash: "",
                totalAmountInit: 0,
                isMultiTicket: false
            }
        },
        props: {
        },
        setup() {
            const checkoutStore = useCheckoutStore();
            const storeFingerStore = useFingerStore();
            const isResponsiveRef = responsiveObserver.getResponsiveStatus();
            const { setFingerprintHash, getFingerprintHash } = storeFingerStore;
            const fingerprintStore = useFingerprintStore();
            const { setTotalAmount } = checkoutStore;

            return {
                isResponsiveRef, setFingerprintHash, getFingerprintHash, fingerprintStore, setTotalAmount
            };
        },
        computed: {
            isResponsive() {
                return this.isResponsiveRef;
            }
        },
        mounted() {
            this.getQuote();
            this.checkRevalidate();
            this.algoliaEventAddToCart();
        },
        methods: {
            async initForm(data) {
                const { paxes } = data
                this.user.passengers = getModelForm(paxes, settingsForm);
            },
            checkRevalidate() {
                this.checkRevalidateVar = setInterval(() => {
                    this.onRevalidate();
                }, site.revalidateTimeOut * 1000);
            },
            forceRerender() {
                this.componentKey += 1;
            },
            async initItineary(quote) {
                let itineary = window.sessionStorage.getItem("itineary");
                let itinearyParse = itineary ? JSON.parse(itineary) : {};
                let tokenReplace = quote.fareKey ? quote.fareKey.replace(/-/g, "").replace("~", "").replace("^", "") : "";
                if (!tokenReplace) {
                    return;
                }
                let itinearyData = itinearyParse[tokenReplace];
                if (itinearyData) {
                    this.dataFly = itinearyData;
                } else {
                    let detailStarting = await getFamilyFare(quote, "starting", null, site);
                    let detailReturning = this.data.rate.isRoundtrip ? await getFamilyFare(quote, "returning", null, site) : {};
                    this.dataFly = { detailStarting, detailReturning };
                    itinearyParse[tokenReplace] = { detailStarting, detailReturning }
                    window.sessionStorage.setItem("itineary", JSON.stringify(itinearyParse))
                }
            },
            async getQuote() {
                this.loading = true;
                this.is_valid = false;
                let params = this.getParams();
                let visitorId = '';
                       
                let response = await axios.post(site.quoteCheckoutStepOne, params).catch((data) => this.onError(data, error_checkout.error_quote));
                FlightsAnalytic.setSearchPage();
                FlightsAnalytic.setPageType();
                if (response && response.status == 200) {
                    try {
        
                        await this.fingerprintStore.fetchVisitorId();
                        visitorId = this.fingerprintStore.visitorId;
                        
                        this.setFingerprintHash(visitorId)
                    

                        if (!visitorId) {
                            console.log("No se pudo obtener el visitorId.");                        
                        }
                    } catch (error) {                        
                            console.log("Error en getQuote 216:", error);
                    }
           
                    this.responseQuote = response.data;
                    this.data = this.responseQuote.quote;
                    this.hash = this.responseQuote.hash;
                    this.totalAmountInit = this.data.rate.totalAmount;
                    this.setTotalAmount(this.totalAmountInit);

                    this.title = `${this.__("messages.checkout_title")} - ${site.appName}`;
                    await this.initForm(this.responseQuote.quote);
                    await this.initItineary(this.responseQuote.quote);
                    this.is_valid = true;

                    await this.onRevalidate();
                    FlightsAnalytic.setStepOne(this.responseQuote);
                    FlightsAnalytic.setEcommerce(this.responseQuote, this.dataFly);
                    this.getFingerPrint();
                }
                this.loading = false;
              
            },
            async getFingerPrint() {
                
                try {
                    
                   
                    await this.fingerprintStore.fetchVisitorId();                                                           
                    let visitorId = this.fingerprintStore.visitorId;                    
                    this.fingerprintHash = visitorId;
                    window.fingerprintHash = visitorId;                    
                    FlightsAnalytic.fingerPrint(visitorId);
                    
                    this.setFingerprintHash(visitorId)
                
                    if (!visitorId) {
                        console.log("No se pudo obtener el visitorId.");                        
                    }
                } catch (error) {
                        
                        console.log("Error en  :", error);
                }
               
            },
            getParams() {
                let params = null;
                let session = StorageService.getSession("checkoutFlightData")
                let paramsCheckout = window.__pt.data && window.__pt.data.checkoutData && window.__pt.data.checkoutData.length ? window.__pt.data : session;
                if (paramsCheckout) {
                    StorageService.setSession("checkoutFlightData", paramsCheckout);
                    params = {
                        Site: paramsCheckout.site
                    }

                    params = { ...params }
                }

                if (session && !params) {
                    paramsCheckout = session;
                }

                return paramsCheckout;
            },
            async onSubmit() {
                const isValid = await this.$refs.observer.validate();
                if (isValid && this.data.rate.totalAmount > 0 && !this.disabledSubmitButton) {
                    let validateAges = await this.$refs.ref_form.validateAges();
                    let copyFamily = [...validateAges.dataUser.passengers]

                    if (validateAges.isInvalidDate) {
                        this.user["passengers"] = copyFamily;
                    } else {
                        let familyExtraData = addPrefix(copyFamily)
                        this.user["passengers"] = familyExtraData;
                        this.loading = true;
                        this.showModalLoading = true;
                        this.disabledSubmitButton = true;
                        let loader = setTimeout(() => {
                            this.loading = false;
                            this.showModal('modal-loader-checkout');
                        }, 10000);
                        await this.onRevalidate();
                        await this.createMasterLocator();
                        clearTimeout(loader);
                    }

                    this.forceRerender();
                } else {
                    Logger.error("Error submit");
                }
            },
            async onRevalidate() {
                let response = null;
                let params = this.getParams();
                if (this.responseQuote.quote.isDomesticRoute || this.useTwoOneWayTrip) {
                    //nacional o revalidate 2 oneway
                    if (window.__pt.data.isRoundTrip) {
                        response = await this.tempNormal(); 
                    }      
                    if (!response && response?.status != 200) {
                        response = await this.twoOneWay();
                    }
                    if (response && response.status == 200) {
                        this.responseRevalidate = response.data.response;
                        let summary = response.data.summary;
                        await this.triggerFareChangeModal(summary, this.responseRevalidate);

                    } else {

                        this.responseRevalidate = null;
                        FlightsAnalytic.onError({ message: `error get revalidate` }, params, error_checkout.error_revalidate, this.retry_attempts);
                        clearInterval(this.checkRevalidateVar);
                        this.showModal("modal-error");
                    }
                } else {
                    // internacional
                    //validacion para lanzar solo cuando la cotizacion es roundtrip 
                    if (window.__pt.data.isRoundTrip) {
                        response = await this.tempNormal(); 
                    }      
                    if (!response && response?.status != 200) {
                        response = await this.twoOneWay();
                    }
                    if (response && response.status == 200) {
                        this.responseRevalidate = response.data.response;
                        let summary = response.data.summary;
                        await this.triggerFareChangeModal(summary, this.responseRevalidate);

                    } else {

                        this.responseRevalidate = null;
                        FlightsAnalytic.onError({ message: `error get revalidate` }, params, error_checkout.error_revalidate, this.retry_attempts);
                        clearInterval(this.checkRevalidateVar);
                        this.showModal("modal-error");
                    }
                }


            },
            async tempNormal() {
                let params = {
                    flights: this.responseQuote.flights,
                    fares: this.responseQuote.fares,
                    channel: this.responseQuote.quote.channelId,
                    currency: this.responseQuote.quote.currency,
                    isRoundtrip: this.responseQuote.quote.rate.isRoundtrip,
                    totalAmount: this.responseQuote.quote.rate.totalAmount,
                    negotiatedFareId: this.responseQuote.quote.flightItinerary.starting.negotiatedFareId,
                    quoteTaskID: this.responseQuote.quote.quoteTaskID && this.responseQuote.quote.quoteTaskID[0]['taskID']
                        ? this.responseQuote.quote.quoteTaskID[0]['taskID'] : uuidv4(),
                };
                let response = await axios.post(site.revalidateCheckout, params).catch((data) => FlightsAnalytic.onError({ message: `error get revalidate` }, params, error_checkout.error_revalidate, this.retry_attempts));
                let statusCode = _.get(response, ["data", "response", "statusCode"], 0)

                if (statusCode == 0 || statusCode == 2) {
                    return { status: 500 }
                }
                this.getInfoFlights([response.data]);

                return response
            },
            async twoOneWay() {
                this.isMultipleTicket = true;
                let mapPromise = [];
                let keyTaskID = 0;
                for (const key in this.responseQuote.flights) {
                    let params = {
                        flights: { 1: this.responseQuote.flights[key] },
                        fares: { 1: this.responseQuote.fares[key] },
                        channel: this.responseQuote.quote.channelId,
                        currency: this.responseQuote.quote.currency,
                        isRoundtrip: false,
                        totalAmount: this.responseQuote.quote.rate.totalAmount,
                        negotiatedFareId: key == 1 ? this.responseQuote.quote.flightItinerary.starting.negotiatedFareId : this.responseQuote.quote.flightItinerary.returning.negotiatedFareId,// this.data.upsell.negotiatedFareId
                        quoteTaskID: this.responseQuote.quote.quoteTaskID && this.responseQuote.quote.quoteTaskID[keyTaskID]['taskID'] ?
                            this.responseQuote.quote.quoteTaskID[keyTaskID]['taskID'] : uuidv4()
                    };
                    let response = axios.post(site.revalidateCheckout, params).catch((data) => this.onError(data, error_checkout.error_revalidate));
                    mapPromise.push(response);
                    keyTaskID++;
                }
                const results = await Promise.allSettled(mapPromise);
                const isRejected = results.filter(p => p.status === 'rejected');
                const isFail = results.filter(p => (p.status === 'fulfilled' && p?.value?.status != 200) ||
                    (p.status === 'fulfilled' && p?.value?.data?.response?.statusCode == 0) ||
                    (p.status === 'fulfilled' && p?.value?.data?.response?.statusCode == 2));

                if (isRejected.length > 0 || isFail.length > 0) {
                    return { status: 500 }
                }
                this.useTwoOneWayTrip = true;

                return this.combineRevalidates(results)

            },
            combineRevalidates(results) {
                if (results.length == 1) {
                    this.getInfoFlights([results[0].value.data]);
                    return results[0].value
                }
                let resDeparture = results[0].value;
                let resArrival = results[1].value;

                if (resDeparture?.data?.response?.bookingInfos?.[2] != null) {
                    resDeparture.data.response.bookingInfos[2] = resArrival?.data?.response?.bookingInfos?.[1];
                    resDeparture.data.response.faresByLeg[2] = resArrival?.data?.response?.faresByLeg?.[1];
                    resDeparture.data.response.flights[2] = resArrival?.data?.response?.flights?.[1];
                    resDeparture.data.response.recommendations[2] = resArrival?.data?.response?.recommendations?.[1];    
                }


                let combinedSumary = { ...resDeparture.data.summary };

                combinedSumary.statusCode = this.getStatus(resDeparture, resArrival);

                combinedSumary.totalAmount = resDeparture.data.summary.totalAmount + resArrival.data.summary.totalAmount;
                combinedSumary.breakdown = resDeparture.data.summary.breakdown.map((item, index) => {

                    let itemBreakdown = resArrival.data.summary.breakdown.find(x => x.type == item.type);

                    return {
                        ...item,
                        amount: item.amount + (itemBreakdown ? itemBreakdown.amount : 0)
                    };
                });

                this.getInfoFlights([resDeparture.data, resArrival.data]);
                resDeparture.data.summary = combinedSumary;

                return resDeparture;

            },
            getStatus(departure, arrival) {
                let status = 0
                if (departure.data.summary.statusCode === 1 && arrival.data.summary.statusCode === 1) {
                    status = 1
                } else {
                    status = departure.data.summary.statusCode !== 1 ? departure.data.summary.statusCode : arrival.data.summary.statusCode;
                }
                return status
            },
            getInfoFlights(fligths = []) {
                let revFligth = {};
                const codes = {};
                for (let index = 0; index < fligths.length; index++) {
                    codes[index] = fligths[index].response.recommendations[1]?.validatingCarrier.code;
                    revFligth[index + 1] = {
                        quoteTaskID: fligths[index].response.quoteTaskID,
                        fareKey: fligths[index].response.recommendations[1].fareCombinables[0].fareKey,
                        ta: fligths[index].summary.totalAmount
                    };
                }
                if (codes.hasOwnProperty("0") && codes.hasOwnProperty("1")) {
                    this.isMultiTicket = codes["0"] !== codes["1"];
                }
                this.revalidateOptions = revFligth;

            },
            async createMasterLocator() {
                let user = this.user;
                let isApiVersion = site.version >= 2;
                let passengersBooking = this.user.passengers.reduce((acc, passenger) => {
                    if (!acc[passenger.room]) {
                        acc[passenger.room] = { passengers: [] };
                    }
                    acc[passenger.room].passengers.push(passenger);
                    return acc;
                }, []);

                user.passengersBooking = passengersBooking;
                //Validar documento de identidad

                user.passengersBooking.forEach(pb => {
                    pb.passengers.forEach(p => {
                        if (p && !p.identityDocument) {
                            p.identityDocument = this.generateIdentityDocument(p);
                        }
                    });
                });

                const userCleaned = this.cleanCustumerProperties(user);

                let params = {
                    customer: userCleaned,
                    quote: this.data,
                    revalidate: this.responseRevalidate,
                    flightDetail: this.dataFly,
                    revalidateOptions: this.revalidateOptions,
                    isMultipleTicket: this.isMultipleTicket,
                    fingerprintHash: this.fingerprintHash,
                    exchangeInfoContainer: {
                        isActive: exchange.base != exchange.currency,
                        referenceAmount: exchange.rate,
                        referenceCurrency: exchange.currency,
                        referenceDescription: exchange.currency,
                        originalCurrency: exchange.base,
                        originalDescription: exchange.base,
                    },
                    siteInformation: getSiteInformation()
                };

                if (this.isMultiTicket) {
                    params.tags = [];
                    params.tags.push("multi" + (this.isNational ? "Nat" : "Int"));
                }

                params.customer.phone = `${params.customer.phone}`;
                params.customer.ip_client = ip;

                FlightsAnalytic.checkoutPaxes(user);

                let url = isApiVersion ? `${site.apiFlights.domain}${site.apiFlights.pathCheckout}` : site.bookingCheckout;
                let response = await axios.post(url, params).catch((data) => this.onError(data, error_checkout.error_booking, true));

                if (response && response.status == 200) {

                    if (isApiVersion) {
                        await this.sendEmailBooking(params, response);
                    }

                    this.onSuccess(response);
                } else {
                    this.onError(response, error_checkout.error_booking, true);
                }
            },
            onSuccess(response) {
                let data = response.data;
                if (data.detail.changeAmount || data.flightInfo.changeReservation || data.id == "") {
                    this.triggerBookingChangeModal(data);

                } else {

                    if (data.detail.totalAmountOld > 0 && data.detail.totalAmount - data.detail.totalAmountOld != 0) {
                        FlightsAnalytic.setChangeFare({ action: "no-mostrada", oldRate: data.detail.totalAmountOld, newRate: data.detail.totalAmount }, this.data);
                    }

                    this.onRedirect(response)
                }

            },
            async onError(data, step_error, isCheckout) {

                this.loading = false;
                this.is_valid = false;
                let params = this.getParams();
                let response = data && data.response && data.response.data ? data.response.data : null;
                let itemResponse = data && data.data ? data.data : {};


                if (step_error) {
                    FlightsAnalytic.onError(response || itemResponse, params, step_error, this.retry_attempts);
                }

                if (response) {
                    this.responseQuote = data.response.data;
                    if (this.responseQuote && this.responseQuote.urlRedirect) {
                        this.is_valid = true;
                        return this.onRedirect(data.response);
                    }
                }

                if (isCheckout && this.retry_attempts <= site.retryCheckout && conteinsItem(site.retryCheckoutAllowed, itemResponse.messageInternal)) {
                    ++this.retry_attempts;
                    this.loading = true;
                    this.is_valid = true;
                    FlightsAnalytic.bookingAttempts(this.responseQuote);
                    return await this.createMasterLocator()
                }


                this.hideModal('modal-loader-checkout');


            },
            onRedirect(response) {
                if (response.data.urlRedirect) {
                    this.hideModal('modal-loader-checkout');
                    this.loading = true;
                    this.showModalLoading = false;
                    let data = response.data;
                    const urlCulture = this.setCultureURL(data.urlRedirect);
                    location.href = urlCulture;

                } else {
                    this.onError(response, error_checkout.error_booking, true)
                }
            },

            setCultureURL(url) {
                const urlObj = new URL(url);
                urlObj.searchParams.set("culture", siteCulture.internalCultureCode);
                return urlObj.toString();
            },

            async sendEmailBooking(params, response) {
                let data = response.data;
                if (data && data.id) {
                    params.masterLocatorID = data.id;
                    let response = await axios.post(site.emailCheckouttUrl, params).catch((data) => this.onError(data, error_checkout.error_email, false));
                }
            },

            async triggerFareChangeModal(summary, revalidate) {
                this.responseSummary["summary"] = summary;
                this.data.rate.breakdown = summary.breakdown;
                this.data.rate.totalAmount = summary.totalAmount;
                this.data.rate.amount = summary.totalAmount;
                this.data.revalidate = {
                    faresByLeg: revalidate.faresByLeg
                };
                if (Object.keys(this.dataFly.detailStarting).length == 0) {
                    this.dataFly.detailStarting['detailFamilyFare'] = {};
                    this.dataFly.detailStarting['detailFamilyFare']['familyFareName'] = this.data.flightItinerary.starting.fareGroup;
                }
                this.data.familyFareName = this.dataFly.detailStarting.detailFamilyFare.familyFareName;
                /* Itinerary */
                if (summary.statusCode == 1) { }
                /* Itinerary change */
                if ((summary.statusCode == 2 || summary.statusCode == 3 || summary.statusCode == 0 || summary.statusCode == 4)) {
                    let diffPrice = Math.abs(summary.totalAmount - this.totalAmountInit);
                    if ((!this.notifyRevalidate && diffPrice >= site.rangeTotalAmount)) {
                        const tokenReplace = this.data.fareKey ? this.data.fareKey.replace(/-/g, "").replace("~", "").replace("^", "") + "NewFare" : "";
                        let itineary = window.sessionStorage.getItem("newItineary");
                        let itinearyParse = itineary ? JSON.parse(itineary) : {};
                        for (let index in revalidate.faresByLeg) {
                            const view = !this.isNational ? "starting" : (index > 1 ? "returning" : "starting");
                            if (revalidate.faresByLeg[index][1].familyFare.code != this.data.flightItinerary[view].fareGroup) {
                                let rq = getParamsDetailFlight({
                                    token: this.data.token,
                                    flightId: this.data.flightItinerary[view].id,
                                    flightType: view,
                                    airlineLogoUri: this.data.flightItinerary[view].airlineLogoUrl,
                                    airlineName: this.data.flightItinerary[view].airline,
                                    fareId: this.data.flightItinerary[view].fareId,
                                    familyFare: revalidate.faresByLeg[index][1].familyFare.code
                                });
                                let responseFamilyFare = await getFamilyRevalidate(rq);
                                if (responseFamilyFare.familyFareName) {
                                    this.dataFly['detail' + view.charAt(0).toUpperCase() + view.slice(1)].detailFamilyFare = responseFamilyFare;
                                    itinearyParse[tokenReplace] = this.dataFly;
                                    window.sessionStorage.setItem("newItineary", JSON.stringify(itinearyParse));
                                } else {
                                    this.dataFly = itinearyParse[tokenReplace];
                                }
                            }
                        }
                        this.data.newFamilyFareName = this.dataFly.detailStarting.detailFamilyFare.familyFareName;
                        this.notifyRevalidate = true;
                        setTimeout(() => {
                            this.showModal('modal-priceChange');
                        }, 150);
                    }
                }
                this.showFamilyFare = true;
            },
            triggerBookingChangeModal(summary) {

                $("#modal-loader-checkout").modal('hide');
                this.bookingSummary = summary;
                this.showModalLoading = false;
                setTimeout(() => {
                    this.showModal('modal-change-booking');
                }, 150);


            },
            generateIdentityDocument(passanger) {
                const apellidos = passanger.lastname.replace(/(DEL\s)*(LAS\s)*(DE\s)*(LA\s)*(Y\s)*(A\s)*/gi, "").split(" ");

                let rfc = "";
                rfc += apellidos[0].substr(0, 1);

                for (var i = 1; i < apellidos[0].length; i++) {
                    let c = apellidos[0].charAt(i);
                    if (/[AEIOU]/gi.test(c)) {
                        rfc += c;
                        break;
                    }
                }

                if (apellidos.length > 1) {
                    rfc += apellidos[1].substr(0, 1);
                }

                rfc += passanger.firstname.substr(0, 1) + passanger.year_selected.substr(2, 4) + passanger.month_selected + passanger.day_selected;

                return rfc.toUpperCase();
            },
            cleanCustumerProperties(user){
                let clonedUser = { ...user }
                const newPassenger = clonedUser.passengers.map(obj => this.clonePassenger(obj));
                const newPassengerBooking = clonedUser.passengersBooking.map(obj => {
                    obj.passengers = newPassenger
                    return obj;
                });

                clonedUser.passengers = newPassenger;
                clonedUser.passengersBooking = newPassengerBooking

                return clonedUser;
            },
            clonePassenger (passenger) {
                const newObj = { ...passenger };
                delete newObj.day_options;
                delete newObj.month_options;
                delete newObj.nation_options;
                delete newObj.year_options;
                return newObj;
            },

            algoliaEventAddToCart() {
                algoliaEvent("Add to Cart");
            },
            hideModal(id) {
                $(`#${id}`).modal("hide");
            },
            showModal(id) {
                $(`#${id}`).modal("show");
            }
        },
        components: {
            Form,
            DetailFlightByLeg,
            DetailFlightBaggage,
            ChangeAmountModal,
            ChangeBookingAmountModal,
            ErrorCheckout,
            LoaderPage,
            LoaderCheckout,
            PolicyModal,
            ErrorsModal
        }
    }
</script>