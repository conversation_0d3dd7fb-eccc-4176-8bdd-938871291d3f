namespace TBFront
{
    public class Program
    {
        public static void Main(string[] args)
        {
            CreateHostBuilder(args).Build().Run();
        }

        public static IHostBuilder CreateHostBuilder(string[] args)
        {
            return Host.CreateDefaultBuilder(args)
               .ConfigureWebHostDefaults(webBuilder => { webBuilder.UseStartup<Startup>(); })
               .ConfigureAppConfiguration((hostContext, config) =>
               {
                   var env = hostContext.HostingEnvironment;
                   config.AddJsonFile("appsettings.json", true, true);
                   config.AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")}.json", true, true);
                   config.AddJsonFile("usersettings.json", true, true);
                   config.AddJsonFile($"channelsettings.json", true, true);
                   config.AddJsonFile($"culturesettings.json", true, true);
                   config.AddJsonFile($"redirectsettings.json", true, true);

                   config.AddEnvironmentVariables();
               })
               .ConfigureWebHost(configure => configure.UseKestrel(options =>
               {
                   options.Limits.KeepAliveTimeout = TimeSpan.FromSeconds(61);
               }));
        }
    }
}


