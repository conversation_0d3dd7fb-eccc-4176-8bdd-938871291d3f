﻿namespace TBFront.Models.Request
{
    public class CheckoutRevalidateRequest
    {
        public int Channel { get; set; }
        public string Currency { get; set; }
        public bool IsRoundtrip { get; set; }
        public double TotalAmount { get; set; }
        public Dictionary<string, TBFront.Models.Flight.Quote.FareLeg> Fares { get; set; }
        public Dictionary<string, TBFront.Models.Flight.Quote.Flight> Flights { get; set; }
        public int? NegotiatedFareId { get; set; }
        public string? QuoteTaskID { get; set; }

    }
}
