﻿using Microsoft.Extensions.Options;
using TBFront.Interfaces;
using TBFront.Options;

namespace TBFront.Middleware
{
    public class CultureValidationMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly SettingsOptions _settingsOptions;
        private readonly ICommonHandler _commonHandler;
        private readonly List<string> _resourcesPaths = ["/assets/", "/img/", "/ue_sw.js"];
        public CultureValidationMiddleware(
            RequestDelegate next,
            IOptions<SettingsOptions> settingsOptions,
            ICommonHandler commonHandler
        )
        {
            _next = next;
            _settingsOptions = settingsOptions.Value;
            _commonHandler = commonHandler;
        }
        public async Task InvokeAsync(HttpContext context)
        {
            var requestPath = context.Request.Path.ToString().ToLower();
            var token = new CancellationTokenSource(60000).Token;
            if (_resourcesPaths.Any(path => requestPath.StartsWith(path)) || requestPath.StartsWith("/error"))
            {
                await _next(context);
                return;
            }

            context.Request.Cookies.TryGetValue("_currency", out var currency);
            context.Request.Cookies.TryGetValue("_country_simulation", out var countrySimulation);

            var segments = context.Request.Path.Value?.Split('/', StringSplitOptions.RemoveEmptyEntries) ?? [];
            var currencyCode = currency ?? string.Empty;
            var cultureCode = segments.FirstOrDefault() ?? string.Empty;

            if (string.IsNullOrEmpty(cultureCode))
            {
                context.Request.Cookies.TryGetValue("_culture", out var culture);
                cultureCode = culture ?? string.Empty;
            }

            var userLocation = CultureValidationMiddleware.GetUserLocation(context, _settingsOptions.CountryChannelDefault);
            var cultureSelected = await _commonHandler.QueryAsync(new Culture { CultureCode = cultureCode }, token);

            if (countrySimulation is not null)
            {
                userLocation.Country = countrySimulation.ToUpper();
                userLocation.UserCountry = userLocation.Country;
            }

            var channelConfigurationTask = _commonHandler.QueryAsync(new ChannelConfiguration { Id = userLocation.Country ?? "", Currency = currency ?? "" }, token);
            var currencySelectedTask = _commonHandler.QueryAsync(new Currency { CurrencyCode = !string.IsNullOrEmpty(currencyCode) ? currencyCode : cultureSelected.Currency }, token);
            var channelSelectedTask = _commonHandler.QueryAsync(new ChannelOptions { Country = userLocation.Country }, token);

            await Task.WhenAll(currencySelectedTask, channelSelectedTask, channelConfigurationTask);

            var currencySelected = await currencySelectedTask;
            var channelSelected = await channelSelectedTask;
            var channelConfiguration = await channelConfigurationTask;

            var cookieOptions = new CookieOptions
            {
                Expires = DateTimeOffset.UtcNow.AddYears(1),
                Path = "/",
                HttpOnly = true,
                Secure = true
            };

            if (!string.IsNullOrEmpty(currencyCode) && _settingsOptions.CurrencyExceptions.Contains(currencyCode))
            {
                userLocation.UserCountry = channelConfiguration.Countries.FirstOrDefault() ?? userLocation.Country ?? string.Empty;
            }

            if (currency is null)
            {
                context.Response.Cookies.Append("_currency", currencySelected.CurrencyCode, cookieOptions);
            }

            context.Response.Cookies.Append("_culture", cultureSelected.CultureCode, cookieOptions);

            context.Items["culture"] = cultureSelected.CultureCode;
            context.Items["currency"] = currencySelected.CurrencyCode;
            context.Items["channel"] = channelSelected;
            context.Items["countryCode"] = userLocation.Country;
            context.Items["userLocation"] = userLocation;

            await _next(context);
        }



        public static Models.Configuration.UserLocation GetUserLocation(HttpContext context, string country)
        {
            string countryCode = country, regionCode = string.Empty, cityCode = string.Empty, latitude = string.Empty, longitude = string.Empty;

            foreach (var header in context.Request.Headers)
            {
                switch (header.Key.ToLowerInvariant())
                {
                    case "cloudfront-viewer-country":
                        countryCode = header.Value.ToString();
                        break;
                    case "cloudfront-viewer-country-region":
                        regionCode = header.Value.ToString();
                        break;
                    case "cloudfront-viewer-city":
                        cityCode = header.Value.ToString();
                        break;
                    case "cloudfront-viewer-latitude":
                        latitude = header.Value.ToString();
                        break;
                    case "cloudfront-viewer-longitude":
                        longitude = header.Value.ToString();
                        break;
                }
            }
            return new Models.Configuration.UserLocation
            {
                City = cityCode,
                Latitude = latitude,
                Longitude = longitude,
                Country = countryCode.ToUpper(),
                Region = regionCode,
                UserCountry = countryCode.ToUpper(),
            };
        }

    }
}