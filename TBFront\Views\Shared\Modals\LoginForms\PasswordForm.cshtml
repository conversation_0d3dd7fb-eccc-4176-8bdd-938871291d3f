﻿@using TBFront.Helpers

@inject ViewHelper viewHelper




<div class="row">

    <div class="col-12">
        <p class="h4">@viewHelper.Localizer("enter_your_password")</p>
        <p class="mb-0"> @viewHelper.Localizer("password_set_string_1") {{ vm.userData.email }} </p>

        <p class="line_height on_error_login mb-0" ng-show="vm.currentErrorMsg && vm.currentErrorMsg.length">{{vm.currentErrorMsg}}</p>
    </div>

</div>


<div class="row my-4">
    <div class="col-12">
        <label for="password_login">@viewHelper.Localizer("password_string")</label>
        <div class="input-group mb-3" ng-class="{'is--danger' : vm.hasError(form_login, 'password') }">
            <div class="input-group-prepend">
                <span class="input-group-text">
                   <span class="font-icons icons-lock"></span>
                </span>
            </div>
            <input type="password" id="password_login" ng-model="vm.userData.password" name="password" required placeholder="@viewHelper.Localizer("password_string")" class="form-control input-custom">
        </div>
        <div class="invalid-feedback" ng-show="vm.hasError(form_login, 'password')">
            @viewHelper.Localizer("enter_your_password")
        </div>
    </div>

</div>


<div class="row">
    <div class="col-12">
        <button type="submit" name="button" class="btn btn-primary btn-block p-3">@viewHelper.Localizer("login_continue")</button>
    </div>
</div>
