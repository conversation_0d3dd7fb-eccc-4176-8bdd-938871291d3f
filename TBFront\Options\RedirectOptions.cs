﻿namespace TBFront.Options
{
    public class RedirectOptions
    {
        public string RootDomain { get; set; } = string.Empty;
        public List<SitesRedirect> SitesRedirect { get; set; } = [];
        public Dictionary<string, Dictionary<string, string>> Mappings { get; set; }
    }
    public class SitesRedirect
    {
        public string Host { get; set; } = string.Empty;
        public string Culture { get; set; } = string.Empty;
    }
}