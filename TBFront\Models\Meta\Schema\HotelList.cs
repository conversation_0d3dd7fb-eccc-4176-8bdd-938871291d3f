using System.Text.Json.Serialization;

namespace TBFront.Models.Meta.Schema
{
    public class HotelList
    {
        [JsonPropertyName("@context")]
        public string Context { get; set; } = "https://schema.org";
        [JsonPropertyName("@type")]
        public string Type { get; set; } = "Hotel";
        public string? name { get; set; }
        public string? image { get; set; }
        public PostalAddress? address { get; set; }
        public string? description { get; set; }
        public GeoCoordinates? geo { get; set; }
        public StarRating? starRating { get; set; }
        public string? priceRange { get; set; }
        public AggregateRating? aggregateRating { get; set; }


         public HotelList()
        {                     
            address = new PostalAddress();
            geo = new GeoCoordinates();
            starRating = new StarRating();
            aggregateRating = new AggregateRating();
        }
    }
}
