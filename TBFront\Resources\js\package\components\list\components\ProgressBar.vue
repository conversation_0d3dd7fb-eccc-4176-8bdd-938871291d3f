<template>
    <section class="container px-md-0 mt-4 cvi-bar" id="sectionBar" v-if="progressBar < 100">
        <div class="progress">
            <div class="progress-bar" role="progressbar" :style="`width: ${progressBar}%`" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
        </div>
    </section>
</template>

<script>
    export default {
        props: {
            progressBar: {
                type: Number,
                default: 0
            },
        }
    }
</script>
<style>
    .cvi-bar .progress-bar {
        background-color: #4496ec
    }
</style>