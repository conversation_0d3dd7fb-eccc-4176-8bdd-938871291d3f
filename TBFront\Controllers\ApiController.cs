﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Places.Standard.Dtos;
using System.Text.Json;
using TBFront.Application.Implementations;
using TBFront.Application.Mappers;
using TBFront.Infrastructure.HttpService.Kerberus;
using TBFront.Interfaces;
using TBFront.Mappers;
using TBFront.Models;
using TBFront.Models.Configuration;
using TBFront.Models.ContactForm.Request;
using TBFront.Models.Flight.Revalidate;
using TBFront.Models.Forms.Request;
using TBFront.Models.Forms.Response;
using TBFront.Models.Kerberus;
using TBFront.Models.Login;
using TBFront.Models.Places.Request;
using TBFront.Models.Places.Response;
using TBFront.Models.Request;
using TBFront.Models.Response;
using TBFront.Options;
using TBFront.Types;

namespace TBFront.Controllers
{
    [Route("vuelos/api-tb")]
    [ApiController]
    public class ApiController : Controller
    {
        private readonly ILogger<ApiController> _logger;
        private readonly IFlightQuoteHandler _flightQuoteServiceHandler;
        private readonly BookingHandler _booking;
        private readonly FlightContentMapper _flightMapper;
        private readonly BookingMapper _bookingMapper;
        private readonly MailHandler _mailHandler;
        private readonly SettingsOptions _options;
        private readonly SiteOptions _site;
        private readonly KerberusService _kerberus;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IOnlinePaymentHandler _onlinePaymentHandler;
        private readonly ICommonHandler _commonHandler;
        private static readonly string CountryBaseISO = "COL";
        private static readonly string CountryBase = "CO";
        private readonly IPlacesAirportService _apiPlace;
        private readonly IUserHandler _userHandler;
        public ApiController(
             ILogger<ApiController> logger,
             IFlightQuoteHandler flightQuoteServiceHandler,
             IHttpContextAccessor httpContextAccessor,
             BookingHandler booking,
             FlightContentMapper flightMapper,
             BookingMapper bookingMapper,
             MailHandler mailHandler,
             IOptions<SettingsOptions> options,
             IOptions<SiteOptions> site,
             KerberusService kerberus,
             IOnlinePaymentHandler onlinePaymentHandler,
             ICommonHandler commonHandler,
             IPlacesAirportService apiPlace,
             IUserHandler userHandler

        )
        {
            _flightMapper = flightMapper;
            _bookingMapper = bookingMapper;
            _flightQuoteServiceHandler = flightQuoteServiceHandler;
            _httpContextAccessor = httpContextAccessor;
            _booking = booking;
            _logger = logger;
            _mailHandler = mailHandler;
            _options = options.Value;
            _site = site.Value;
            _kerberus = kerberus;
            _onlinePaymentHandler = onlinePaymentHandler;
            _commonHandler = commonHandler;
            _apiPlace = apiPlace;
            _userHandler = userHandler;
        }

        [HttpPost("revalidate")]
        public async Task<ActionResult<RevalidateResponse>> Revalidate([FromBody] CheckoutRevalidateRequest request)
        {
            var response = new RevalidateResponse();
            try
            {
                using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(30000));
                response = await _flightQuoteServiceHandler.QueryAsync(request, cts.Token);
                return Ok(response);
            }
            catch (Exception e)
            {
                var error = new
                {
                    Guid = Guid.NewGuid(),
                    Message = e.Message,
                    Stack = e.StackTrace
                };

                _logger.LogError($"[Error] Revalidate Message: {e.Message} - Request: {JsonSerializer.Serialize(request)} - Response {JsonSerializer.Serialize(response)}");
                return StatusCode(500, error);
            }
        }


        [HttpPost("createbooking")]
        public async Task<ActionResult<CheckoutBookingResponse>> CreateBooking([FromBody] CheckoutBookingRequest request)
        {
            try
            {

                var json = JsonSerializer.Serialize(request);
                var requestCopy = JsonSerializer.Deserialize<CheckoutBookingRequest>(json);

                using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));

                var response = await _flightQuoteServiceHandler.QueryAsync(request, cts.Token);

                requestCopy.MasterLocatorID = response.Id;

                if (response.Id != "")
                {
                    var emailRequest = MailMapper.ConfirmationRequest(response.Id, requestCopy);
                    var responseEmail = await _mailHandler.SendMailFromSendGrid(emailRequest, requestCopy);
                }

                if (_options.ShowServerLogs)
                {
                    _logger.LogError($"[Info] CreateBooking Request: {JsonSerializer.Serialize(request)}");
                }

                return Ok(response);
            }
            catch (Exception e)
            {
                var error = new
                {
                    Guid = Guid.NewGuid(),
                };

                _logger.LogError($"[Error] CreateBooking Message: {e.Message} - Request: {JsonSerializer.Serialize(request)}");
                return StatusCode(500, error);
            }
        }


        [HttpPost("quote")]
        public async Task<ActionResult<CheckoutQuoteResponse>> Quote([FromBody] CheckoutQuoteRequest requestQuote)
        {
            var responseQuote = new CheckoutQuoteResponse();
            try
            {
                using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(30000));
                var sessionId = _httpContextAccessor.HttpContext.Request.Cookies["session_id"] ?? string.Empty;
                responseQuote = await _flightMapper.MapResponsesToQuoteResponse(requestQuote, sessionId, cts.Token);
                responseQuote.Status = StatusType.OK;

                return Ok(responseQuote);
            }
            catch (Exception e)
            {

                responseQuote.Message = e.Message;
                responseQuote.Status = StatusType.ERROR;


                _logger.LogError($"[Error] Quote Message: {e.Message} - Request: {JsonSerializer.Serialize(requestQuote)} - Response:  {JsonSerializer.Serialize(responseQuote)}");

                return StatusCode(500, responseQuote);
            }


        }

        //[EnableCors("AllOriginsAndMethodsAllowed")]
        [HttpGet("get-booking")]
        public async Task<ActionResult<GetBookingResponse>> GetBooking([FromQuery] GetBookingRequest request)
        {
            var responseQuote = new GetBookingResponse();
            var bookingParams = new VoucherInfo();
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(20000));

            try
            {
                var responsePlaces = new List<PlaceResponse>();
                bookingParams = _booking.DecryptParams(request);
                
                var itineraryTask = _booking.GetBooking(bookingParams);
                var clientTokenTask = _booking.GetClientTokenInfo(request.KeyValidation);

                await Task.WhenAll(itineraryTask, clientTokenTask);

                var itineraryResponse = await itineraryTask;
                var clientTokenResponse = await clientTokenTask;

                bookingParams.Channel = itineraryResponse.Data.TravelItinerary.ChannelId;
                var summaryResponse = await _flightQuoteServiceHandler.QueryAsync(bookingParams, cts.Token);
                
                var responseVoucher = _bookingMapper.ItineraryResponseToGetBookingResponse(itineraryResponse, clientTokenResponse, summaryResponse);

                var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);

                var req = PlaceMapper.Request(responseVoucher.FlightInformation.FirstOrDefault().OriginAirportCode, responseVoucher.FlightInformation.FirstOrDefault().DestinationAirportCode, userSelection.Culture.InternalCultureCode);

                if (req.Codes.Any())
                {
                    var response = await _apiPlace.QueryAsync(req, cts.Token);
                    responsePlaces = PlaceMapper.Map(response);
                    responseVoucher.IsDomestic = (responsePlaces.FirstOrDefault().LocationInfo.CountryISO == CountryBaseISO && responsePlaces.LastOrDefault().LocationInfo.CountryISO == CountryBaseISO) || (responsePlaces.FirstOrDefault().LocationInfo.CountryA2 == CountryBase && responsePlaces.LastOrDefault().LocationInfo.CountryA2 == CountryBase);
                }
                responseQuote.Reservation = responseVoucher;
                responseQuote.Itinerary = itineraryResponse;
                responseQuote.Info = bookingParams;

                var responseMailing = await _mailHandler.SendMail(responseVoucher, request.Id);

                responseQuote.Status = StatusType.OK;

                return Ok(responseQuote);
            }
            catch (Exception e)
            {
                responseQuote.Message = e.Message;
                responseQuote.Status = StatusType.ERROR;
                responseQuote.Info = bookingParams;


                return StatusCode(500, responseQuote);
            }
        }

        [Route("booking-mail")]
        [HttpPost]
        public async Task<ApiResponse<bool>> SendEmailAsync([FromBody] CheckoutBookingRequest request)
        {
            var response = new ApiResponse<bool>(true, "ok.");

            if (!string.IsNullOrEmpty(request.MasterLocatorID))
            {
                using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));
                var reservation = new GetBookingRequest
                {
                    Id = request.MasterLocatorID,
                    Em = request.Customer.Email,
                };
                var reservationR = await _userHandler.QueryAsync(reservation, cts.Token);
                var emailRequest = MailMapper.ConfirmationRequest(request.MasterLocatorID, request);
                response = await _mailHandler.SendMailFromSendGrid(emailRequest, request);
            }

            return response;
        }

        [Route("forms/contact")]
        [HttpPost]
        public Task<ApiResponse<bool>> SendContactForm([FromBody] ContactFormRequest contactForm)
        {
            var bcc = new List<string>();

            if (contactForm.CopyEmail)
            {
                bcc.Add(contactForm.Email);
            }

            var emailRequest = new EmailRequest()
            {
                Subject = "Formulario de contacto TiquetesBaratos.com",
                BCCs = bcc,
                NameView = "Forms/Contact.cshtml",
                Name = "Julian Ramírez",
                //ToEmail = "<EMAIL>"
                ToEmail = "<EMAIL>",
            };

            var response = _mailHandler.SendMailFromSendGrid(emailRequest, contactForm);

            return response;
        }

        [Route("forms/groups")]
        [HttpPost]
        public Task<ApiResponse<bool>> SendGroupsForm([FromBody] GroupsFormRequest groupForm)
        {
            var bcc = new List<string>() {
                groupForm.Email
            };

            var emailRequest = new EmailRequest()
            {
                Subject = "Formulario de contacto TiquetesBaratos.com",
                BCCs = bcc,
                NameView = "Forms/Groups.cshtml",
                Name = "Julian Ramírez",
                //ToEmail = "<EMAIL>"
                ToEmail = _options.GroupsEmailFrom ?? "<EMAIL>",
                From = _options.GroupsEmailFrom,
                FromName = _options.GroupsEmailFromName,
            };

            var response = _mailHandler.SendMailFromSendGrid(emailRequest, groupForm);

            return response;
        }

        [Route("forms/call")]
        [HttpPost]
        public async Task<ApiResponse<string>> SendCallForm([FromBody] CallFormRequest callForm)
        {
            var now = DateTime.Now;
            var dayConfiguration = _options.ContactMeConfiguration.AvailableTime.Find(at => at.Days.Contains(now.DayOfWeek));

            if (TimeSpan.Compare(now.TimeOfDay, dayConfiguration.Hours.Start) >= 0 && TimeSpan.Compare(dayConfiguration.Hours.End, now.TimeOfDay) >= 0)
            {
                // Horario valido
                using var ct = new CancellationTokenSource(TimeSpan.FromMilliseconds(5000));

                var request = new KerberusRequest()
                {
                    Attempts = 2,
                    Id = 1,
                    Phone = callForm.Phone,
                    Prefix = "57",
                    WaitingTime = 0
                };

                var kerberusResponse = await _kerberus.QueryAsync(request, ct.Token);


                var response = new ApiResponse<string>("Ok", "Ok", System.Net.HttpStatusCode.OK);
                return response;
            }
            else
            {
                // Horario invalido
                var bcc = new List<string>();
                var emailRequest = new EmailRequest()
                {
                    Subject = "Estamos fuera de línea, nos podremos en contacto contigo",
                    BCCs = bcc,
                    From = _options.ContactEmailFrom,
                    FromName = "Te llamamos offline",
                    Name = "Reservas",
                    NameView = "Forms/Groups.cshtml",
                    ToEmail = _options.ContactMeConfiguration.EmailTo
                };

                await _mailHandler.SendMailFromSendGrid(emailRequest, callForm);
                var errors = new List<string>() {
                    "No available"
                };
                var response = new ApiResponse<string>("No available", "No available", System.Net.HttpStatusCode.BadRequest, errors);

                return response;
            }

        }

        [Route("online-payment")]
        [HttpPost]
        public async Task<OnlinePaymentResponse> OnlinePayment(OnlinePaymentRequest request)
        {
            var response = new OnlinePaymentResponse();

            try
            {

                var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(30000));

                response = await _onlinePaymentHandler.QueryAsync(new OnlinePaymentRequest { Code = request.Code, Email = request.Email, RecaptchaToken = "" }, cts.Token);

            }
            catch (Exception ex)
            {
                response = new OnlinePaymentResponse();
                _logger.LogError($"OnlinePayment API - {ex.Message} - Request: {System.Text.Json.JsonSerializer.Serialize(request)}");
            }


            return response;

        }


        [Route("v1/settings")]
        [HttpGet]
        public SettingsInternalResponse Settings()
        {
            var response = new SettingsInternalResponse
            {
                QuoteConfiguration = _options.QuoteConfiguration,
                AirlineConfiguration = _options.AirlineConfiguration,
                FlightValidationStops = _options.FlightValidationStops,
                FormsConfiguration = _options.FormsConfiguration,
                AirlinesCheckIn = _site.airlinesCheckIn,
                Phones = _site.Phones,
                EmailDomains = _options.EmailDomains,
            };
            return response;
        }

        [HttpGet("change-currency/{currency}")]
        public async Task<IActionResult> SetCurrency(string currency, [FromQuery] string redirect = "")
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));
            var refererUrl = string.IsNullOrEmpty(redirect) ? Request.Headers.Referer.ToString() : redirect;
            var currencySelected = await _commonHandler.QueryAsync(new Currency { CurrencyCode = currency }, cts.Token);

            if (currencySelected is not null)
            {
                var cookieOptions = new CookieOptions
                {
                    Expires = DateTimeOffset.UtcNow.AddYears(1),
                    Path = "/",
                    HttpOnly = true,
                    Secure = Request.IsHttps
                };

                Response.Cookies.Append("_currency", currencySelected.CurrencyCode, cookieOptions);
            }

            return Redirect(string.IsNullOrEmpty(refererUrl) ? "/" : refererUrl);
        }

        [HttpGet("v1/places")]
        public async Task<IActionResult> GetPlaces([FromQuery] AppPlaceRequest request)
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));
            var req = new AirportsPlaceRequest()
            {
                Culture = request.Culture,
                Codes = request.Code.Split(","),
                Types = request.Type.Split(",").Select(x => int.Parse(x))
            };
            var places = await _apiPlace.QueryAsync(req, cts.Token);
            return Ok(places);
        }
    }
}
