<template>
    <div class="autocomplete bg-white autocomplete-control" :class="{'d-none': !isOpen }">
        <span class="ms-2 recentSearchsTitle d-block py-3" v-if="history.length && !suggestions.length">{{ __("booker.recentResearchs") }} </span>
        <ul id="cityContainer" class="list-group list-group-flush" v-if="!suggestions || !suggestions.length">
            <li class="suggestion cursor-pointer px-3 py-2 autocomplete-control list-group-item" v-for="item in history" @click="changeSelected(item, null, 'history')">
                <div class="autocomplete-control d-flex w-100">
                    <div class="autocomplete-control flex-grow-1">
                        <div class="d-flex justify-content-between autocomplete-control">
                            <div class="d-flex">
                                <img width="24" height="auto" src="https://viajes.tiquetesbaratos.com/img/icon_ui/Clock_History.svg" />
                                <div class="ms-3">
                                    <span v-html="item.displayText ?? item.cityName" class="autocomplete-control"></span>
                                    <small class="autocomplete-control d-block">{{item.displayDestinationHtml ?? item.cityCountryName}}</small>
                                </div>
                            </div>
                            <small v-if="item.type != 1" class="autocomplete-control text-uppercase codePlace">{{ item.code ?? item.airportCode }}</small>
                        </div>
                        <!--<div class="text-muted autocomplete-control">
                        <small class="autocomplete-control">{{item.displayDestinationHtml}}</small>
                    </div>-->
                    </div>
                </div>
            </li>
        </ul>

        <span class="ms-2 recentSearchsTitle d-block py-3" v-if="!suggestions || !suggestions.length">{{ input === "suggestion-input-starting" ? __("booker.mostOrigins") : __("booker.mostDestinations") }}</span>
        <ul id="cityContainer" class="list-group list-group-flush" v-if="!suggestions || !suggestions.length">
            <li class="suggestion cursor-pointer px-2 py-2 autocomplete-control list-group-item" v-for="destination in moreDestinations" @click="changeSelected(destination, null, 'most_serched')">
                <div class="autocomplete-control d-flex w-100">
                    <div class="autocomplete-control flex-grow-1">
                        <div class="d-flex justify-content-between autocomplete-control">
                            <div class="d-flex">
                                <img width="60" height="auto" class="rounded" :src="`https://viajes.tiquetesbaratos.com/assets/recent-search-imgs/iatas/${destination.code}.jpg`" />
                                <div class="ms-3">
                                    <span v-html="destination.displayHtml" class="autocomplete-control"></span>
                                    <small class="autocomplete-control d-block">{{destination.displayDestinationHtml}}</small>
                                </div>
                            </div>
                            <small v-if="destination.type != 1" class="autocomplete-control text-uppercase codePlace">{{ destination.code }}</small>
                        </div>
                        <!--<div class="text-muted autocomplete-control">
                        <small class="autocomplete-control">{{item.displayDestinationHtml}}</small>
                    </div>-->
                    </div>
                </div>
            </li>
        </ul>


        <ul id="cityContainer" class="list-group list-group-flush">
            <li :key="index" :id="'result' + index" class="suggestion cursor-pointer px-2 py-2 autocomplete-control list-group-item" v-for="(suggestion, index) in suggestions" @click="changeSelected(suggestion, index + 1)">
                <div class="d-flex justify-content-between autocomplete-control">
                    <div>
                        <span v-html="suggestion.displayHtml" class="autocomplete-control"></span>
                        <small class="autocomplete-control d-block">{{suggestion.displayDestinationHtml}}</small>
                    </div>
                    <small v-if="suggestion.type != 1" class="autocomplete-control text-uppercase codePlace">{{suggestion.code}}</small>
                </div>
            </li>
        </ul>
    </div>
</template>

<script setup>
    import { onMounted, ref } from 'vue';
    import { __ } from '../../../utils/helpers/translate';
    import { useBookerStore } from '../../stores/booker';

	const props = defineProps(['selected', 'isOpen', 'suggestions', 'input', 'history','moreDestinations']);
    const emit = defineEmits(['update:modelValue', 'toggle', 'changeText']);
    const { setExtraInformation } = useBookerStore();

    const changeSelected = (suggestion, position, type = 'suggestion') => {
        const input = props.input == "suggestion-input-starting" ? "origen" : "destino";
        setExtraInformation(type, input);
        const text = `${suggestion.displayText} - ${suggestion.displayDestinationHtml} (${suggestion.code})`;
        //localStorage.setItem(`selected_${ props.input == "suggestion-input-starting" ? "from" : "to"}_text`, `${suggestion.displayText}`)
		emit('update:modelValue', suggestion.code);
		emit('changeText', text, suggestion, position);

		hideAutocomplete();
	};

	const hideAutocomplete = () => {
		emit('toggle', false);
	};

	onMounted(() => {
		document.body.addEventListener('click', $event => {
			if (!$event.target.classList.contains("autocomplete"), !$event.target.classList.contains(props.input)) {
				emit('toggle', false);
			}
		});
	});
</script>

<style lang="scss">

    .recentSearchsTitle {
        font-weight: 600;
        color: #373535 !important;
        font-size: 14px;
    }

    .codePlace {
        font-weight: 400;
        color: #333 !important;
        font-size: 16px !important;
    }

    .autocomplete {
        width: 100%;
        max-width: 850px;
        z-index: 100;
        left: 0;
        color: black;

        @media((min-width: 991px) and (max-width: 1199px)) {
            width: 40vw;
            left: initial;
        }

        @media((min-width: 1200px) and (max-width: 1279px)) {
            width: 40vw;
            left: initial;
        }

        @media(min-width: 1280px) {
            width: 25vw;
            left: initial;
        }

        .suggestion {
            &.active {
                color: white;
            }

            &:hover {
                /*background-color: var(--bs-secondary);
                color: white !important;

                em, b {
                    color: white;
                }

                small {
                    color: white;
                }*/
            }

            em, b {
                font-style: normal;
                font-weight: bold;
                //color: var(--bs-secondary);
            }

            small {
                font-size: smaller;
            }
        }
    }

    small.autocomplete-control {
        color: #999;
    }

    li.suggestion.cursor-pointer.px-4.py-2.autocomplete-control.list-group-item {
        color: #666;
    }
</style>