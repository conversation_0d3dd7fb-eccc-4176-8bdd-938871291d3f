<template>
	<Skeleton v-show="(Object.keys(groups).length == 0 && getProgressBar <= 99) || getLoading" :typeSkeleton="'typeListOneWay'" v-for="skeleton in skeletons" />
	<section id="cBodyFlights" class="container px-md-0 mt-3 mt-md-0 mt-lg-3">
		<div :id="`cSticky${indexGroup}`" class="cn-flights-list rounded mb-4 bg-white shadow-md" v-for="(group, indexGroup) in groups">
			<div :id="`${group.departure.name}`">
				<div class="row cn-header px-3 py-2 px-md-5 position-relative"
					 :class="{ 'sticky-settings': configSticky[indexGroup] }">
					<div class="col-4 col-md-5 ps-0">
						<div class="cn-figure d-flex align-items-center h-100 w-100">
							<div>
								<span>
									<img v-lazy="group.departure.image" />
								</span>
								<span class="ps-md-2">
									<span class="strong">{{group.departure.name}}</span>
									<span v-if="!isMultipleAirport" class="cl-iata">
										({{group.departure.departure.airportCode}}-{{group.departure.arrival.airportCode}})
									</span>
								</span>
							</div>
						</div>
					</div>
					<div class="col-7 px-0 cn-rate text-right ms-auto">
						<div class="text-truncate txt-ida pe-0">
							<span>
								{{__("messages.by_passenger")}}<span class="i-sticky">, {{__("promotions.from")}}</span>:
							</span>
						</div>
						<div class="c-info-rate">
							<div class="f-bold txt-rate">
								<CurrencyDisplay
                                    :amount="group.price"
                                    :showCurrencyCode="false"
                                    :reduceIsoFont="true"
                                />
							</div>
							<div class="txt-info">{{__("flightList.fairesIncluded")}}</div>
						</div>
					</div>
				</div>
				<template v-for="(flights, indexFlight) in group.departure.flights">
					<div class="row px-2 py-2 pt-3 pt-md-2 px-md-5 pointer position-relative cn-line cursor-pointer"
						 v-show="hiddenRows[indexGroup][indexFlight]" :id="group.departure.code + indexFlight" @click="getUpsellList(flights, group)">

						<div class="col-7 col-md-4 cb-route cn-line-in cnli-container order-1 pe-5 pe-md-3">
							<div class="row h-100">
								<div class="col-3 col-md-2 d-flex align-items-center ps-0 justify-content-md-start">
									<span class="align-self-center position-relative">
										<span class="strong txt-time tt-info">{{flights.departure.time}}</span>
									</span>
								</div>
								<div class="col-6 col-md-8 cn-line-route position-relative px-0 px-md-3">
									<span class="icon icon-plane-right hide-md hide-lg hide-xs position-relative"></span>
									<div class="cn-lr-in in">
										<div class="cn-fight-time text-center">
											<template v-if="isMultipleAirport">
												<span class="c-iata-left">{{flights.departure.airportCode}}</span>
												<span class="c-iata-right">{{flights.arrival.airportCode}}</span>
											</template>
											<span class="c-hrs px-1">{{flights.flightDuration}}</span>
										</div>
									</div>
								</div>
								<div class="col-3 col-md-2 d-flex ps-0 ps-md-3 position-relative">
									<span class="align-self-center position-relative">
										<span class="strong txt-time tt-info">{{flights.arrival.time}}</span>
									</span>
									<span v-if="flights.flightDays > 0" class="ci-tooltip">
										+{{flights.flightDays}} <span class='cit-day'> {{flights.flightDays > 1 ? 'dias' : 'dia'}}</span>
										<span class="tooltiptext tooltip-top">Llegas el {{ $filters.date(flights.arrival.date, 'ddd DD MMM YYYY') }}.</span>
									</span>
								</div>
							</div>							
						</div>
						<div @click.stop.prevent="getFlightDetails(group.departure.name, group.departure.image, flights, group, 'starting')" class="col-4 col-md-4 d-flex align-items-center ps-md-5 ps-lg-0 justify-content-center text-center px-0 cb-scale order-4 order-md-3 mx-2 mx-md-0">
							<button class="btn btn-link a-link ps-0 ps-md-3 py-0 pb-0 ps-18-md">{{getScales(flights.stops) }}</button>
						</div>
						<template v-if="luggages[indexGroup][indexFlight]">
							<div class="c-eq col-1 col-md-2 col-lg-2 d-flex align-center align-items-center order-3 order-md-4 px-0"
								 @click.stop.prevent="getFlightDetails(group.departure.name, group.departure.image, flights, group, 'starting', true)">
								<span :id="`hand${indexGroup}${indexFlight}`" @mouseover="handleMouseOver($event)" data-bs-toggle="tooltip" data-bs-placement="top" :title="luggages[indexGroup][indexFlight]['extra'][2]['title']" :class="luggages[indexGroup][indexFlight]['extra'][2]['class']"></span>
								<span :id="`checked${indexGroup}${indexFlight}`" @mouseover="handleMouseOver($event)" data-bs-toggle="tooltip" data-bs-placement="top" :title="luggages[indexGroup][indexFlight]['extra'][1]['title']" :class="luggages[indexGroup][indexFlight]['extra'][1]['class']"></span>
							</div>
						</template>
						<template v-else>
							<div class="c-eq col-1 col-md-2 col-lg-2 d-flex align-center align-items-center order-3 order-md-4 px-0">
								<div class="col-5 col-md-2">
									<div class="c-card is-loading p-0 m-0 w-100">
										<div class="pe-1 py-2 p-md-0 p-lg-1">
											<p class="m-0 me-md-1 me-lg-0 py-2"></p>
										</div>
									</div>
								</div>
								<div class="col-5 col-md-2">
									<div class="c-card is-loading p-0 m-0 w-100">
										<div class="pe-1 py-2 p-md-0 p-lg-1">
											<p class="m-0 ms-md-1 ms-lg-0 py-2"></p>
										</div>
									</div>
								</div>
							</div>
						</template>
						<div class="col-5 col-md-2 pl-0 cb-rate-in text-end d-flex align-items-center justify-content-end position-relative cn-line-in position-relative cursor-pointer order-2 order-md-5" v-for="fares in flights.fares">
							<span class="fw-semibold pe-2 pe-md-3" :class="{'cn-cheap-active': flights.cheap}">
								<CurrencyDisplay :amount="getAmountPerPerson(fares.amount)" :showCurrencyCode="false" />
							</span>
							<span class="icon icon-keyboard-right i-link"></span>
						</div>
					</div>
				</template>
				<div v-if="configFooter[indexGroup]['active']"
					 class="col-12 py-3 cn-footer text-center position-relative cursor-pointer"
					 :class="{'cn-f-cheap': configFooter[indexGroup]['economic'] && !configFooter[indexGroup]['open']}"
					 @click="showRows(indexGroup)">
					<div class="pointer all-link underline-hover">
						<span v-if="configFooter[indexGroup]['economic'] && !configFooter[indexGroup]['open']">{{__("flightList.seeMoreEconomicFligthsFrom")}} 
							<strong>
								<CurrencyDisplay :amount="group.price" :showCurrencyCode="false" />
							</strong>
						</span>
						<span v-else-if="!configFooter[indexGroup]['economic'] && !configFooter[indexGroup]['open']">{{__("flightList.seeMoreFligths")}}</span>
						<span v-show="configFooter[indexGroup]['open']">{{__("flightList.seeLessFligths")}}</span>
						<span class="icon icon-expand font-20 position-relative" :class="{'i-rotate-180' : configFooter[indexGroup]['open']}"></span>
					</div>
				</div>
			</div>
		</div>
	</section>
	<ModalUpsell v-if="getShowUpsell" />
</template>

<script>
	import { storeToRefs } from 'pinia';
	import { useFlightStore } from '../../../stores/flight';
	import { useFlightUpsellStore } from '../../../stores/flightUpsell';
	import { useFlightDetailStore } from '../../../stores/flightDetail';
	import { useFlightFamilyFareStore } from '../../../stores/flightFamilyFare';
	import { useFlightRevalidateStore } from '../../../stores/flightRevalidate';
	import { useFlightMatrixStore } from '../../../stores/flightMatrix';
	import { List } from '../../../../utils/analytics/flightList.js';
	import { getDetail, getParamsDetailFlight, getFamilyFare, getParamsUpsell, getUpsell, getParamsRevalidate, getRevalidate } from '../../../services/ApiFlightFrontServices';
	import { Logger } from '../../../../utils/helpers/logger';
	import jsonData from '../../../../../json/iatasGrouper.json';
    import { useUserSelectionStore } from '../../../stores/user-selection';
	import CurrencyDisplay from '../../common/CurrencyDisplay.vue';

	const configFooter = {};
	let hiddenRows = {};

	export default {
		data() {
			return {
				configSite: window.__pt.settings.site.airlineConfiguration.international ?? [],
				configFooter: configFooter,
				hiddenRows: hiddenRows,
				paramsDetail: {},
				skeletons: [1, 2, 3],
				paramsUpsell: {},
				configSticky: {},
				iatasAgrouper: jsonData,
				configLuggage: {}
			}
		},
		setup() {
			const useFlightUpsell = useFlightUpsellStore();
			const storeFlight = useFlightStore()
			const flightDetailStore = useFlightDetailStore();
			const flightFamilyFareStore = useFlightFamilyFareStore();
			const flightRevalidateStore = useFlightRevalidateStore();
			const flightMatrixStore = useFlightMatrixStore();
            const userSelectionStore = useUserSelectionStore();

			const { getParams, getGroups, getFlightResponse, getProgressBar } = storeToRefs(storeFlight); //get
			const { getShowDetail } = storeToRefs(flightDetailStore);
			const { getFlightUpsell, getShowUpsell } = storeToRefs(useFlightUpsell);
			const { getLoading } = storeToRefs(flightMatrixStore);
			const { getLuggage } = storeToRefs(flightFamilyFareStore);
            const { getFiltersAppliedArray } = storeToRefs(userSelectionStore);

			const { changeOpenCloseModalUpsell, setFlightUpsell, setFlightSelected, activateLoadingUpsell } = useFlightUpsell; //set/actions
            const { setFlightDetailResponse, activeModalDetail, setExtraData } = flightDetailStore;
            const { setFlightFamilyFareResponse, setIsLuggage } = flightFamilyFareStore;
			const { setFlightRevalidateResponse, setRevalidateStatus } = flightRevalidateStore;

			return {
				changeOpenCloseModalUpsell,
				getParams, getGroups, getFlightResponse,
				setFlightDetailResponse, setFlightFamilyFareResponse,
				getFlightUpsell, setFlightUpsell,
				getShowUpsell, setFlightSelected, activateLoadingUpsell,
				getProgressBar, setFlightRevalidateResponse, setRevalidateStatus,
				getShowDetail, activeModalDetail, getLoading, setExtraData, getLuggage,
                setIsLuggage, getFiltersAppliedArray
			}
		},
        mounted() {
			window.addEventListener('scroll', this.handleScroll);
		},
		beforeDestroy() {
			window.removeEventListener('scroll', this.handleScroll);
		},
		computed: {
			groups() {
				this.sortAirlines((this.getGroups))
				for (let grupo in this.getGroups) {
					this.configSticky[grupo] = false;
                    hiddenRows[grupo] = {};
                    configFooter[grupo] = {};
                    configFooter[grupo]['economic'] = false;
                    configFooter[grupo]['active'] = Object.keys(this.getGroups[grupo]['departure']['flights']).length > 8;
                    configFooter[grupo]['open'] = false;
                    for (let index in this.getGroups[grupo]['departure']['flights']) {
                        hiddenRows[grupo][index] = index < 8;
                        if (index > 7 && this.getGroups[grupo]['departure']['flights'][index]['cheap']) {
                            configFooter[grupo]['economic'] = true;
                        }
                    }
				}
				return this.getGroups
			},
			luggages() {
                for (let grupo in this.getGroups) {
                    this.configLuggage[grupo] = {};
                    for (let index in this.getGroups[grupo]['departure']['flights']) {
                        let code = '';
                        for (let seg in this.getGroups[grupo].departure.flights[index].segments) {
                            code += this.getGroups[grupo].departure.flights[index].segments[seg].departure.code + "-";
                            code += this.getGroups[grupo].departure.flights[index].segments[seg].arrival.code + "-";
                            code += this.getGroups[grupo].departure.flights[index].segments[seg].operatingAirline.code + "-";
                            code += this.getGroups[grupo].departure.flights[index].segments[seg].marketingAirline.code + "-";
                        }
						code += this.getGroups[grupo].departure.flights[index].fares[0].fareGroup;
                        this.configLuggage[grupo][index] = (this.getLuggage || {})[code];
                    }
				}
                return this.configLuggage
			},
			information() {
				return this.getParams
			},
			isMultipleAirport() {
				const iataStarting = window.__pt.data.startingFromAirport;
                const iataReturning = window.__pt.data.returningFromAirport;
				return this.iatasAgrouper[iataStarting] != undefined && this.iatasAgrouper[iataStarting]
					|| this.iatasAgrouper[iataReturning] != undefined && this.iatasAgrouper[iataReturning];
            },
		},
		methods: {
			handleMouseOver(event) {
                if (!window.__pt.settings.site.isMobileDevice()) {
                    const span = document.getElementById(event.target.id);
                    const tooltip = new bootstrap.Tooltip(span);
                    tooltip.show();
				}
			},
			getScales(scales) {
				let scale = this.__(`messages.stops_0`);
				if (scales >= 1) {
					scale = scales == 1 ? `${scales} ${this.__("messages.stops_1")}` : `${scales} ${this.__("messages.stops_2")}`;
				}
				return scale;
			},
			showRows(indexGroup) {
				for (let clave in hiddenRows[indexGroup]) {
					if (!hiddenRows[indexGroup][clave] || clave > 7) {
						this.hiddenRows[indexGroup][clave] = !hiddenRows[indexGroup][clave];
					}
				}
				this.configFooter[indexGroup]['open'] = !configFooter[indexGroup]['open'];
			},
			getAmountPerPerson(amount) {
				const paxes = window.__pt.data.adults + window.__pt.data.kids;
				return amount / paxes;
			},
			async getFlightDetails(name, image, flight, group, type, luggage = false) {
				if (this.getShowDetail) return;
				this.activeModalDetail();
				const modalElement = document.getElementById('modalDetail');
				const modal = new bootstrap.Modal(modalElement);
                this.setExtraData({
                    airlineLogoUri: image,
                    airlineName: name
                });
                modal.show();
				this.paramsDetail = {
					token: group.quoteToken,
					flightId: flight.id,
					flightType: type,
					airlineLogoUri: image,
					airlineName: name,
                    fareId: flight.fares[0].fareId,
                    fareKey: flight.fares[0].fareKey,
				};
				let rq = getParamsDetailFlight(this.paramsDetail);

				let responseDetail = await getDetail(rq);
				let responseFamilyFare = await getFamilyFare(rq);

				this.setFlightDetailResponse(responseDetail);
				this.setFlightFamilyFareResponse(responseFamilyFare);
                List.modalDetail(group.departure.code, responseFamilyFare.familyFareName, 'ida', luggage);
				this.activeModalDetail();
                this.setIsLuggage(luggage);
			},
			async getUpsellList(flight, group) {
				const flights = {
					departureFlight: flight,
					totalAmount: this.getAmountPerPerson(flight.fares[0].amount)
				};
				const quoteTaskID = [{ taskID: group.quoteTokenFQS }];
				this.setFlightSelected(flights);
				this.setRevalidateStatus(false);
				this.paramsUpsell = {
					outboundFlightId: flight.id,
					OutputFareId: flight.fares[0].fareId,
					fareKey: flight.fares[0].fareKey,
					token: group.quoteToken,
				};
                this.setFlightUpsell({});
                this.activateLoadingUpsell();
                this.changeOpenCloseModalUpsell();
				this.paramsDetail = {
					token: group.quoteToken,
					flightId: flight.id,
					flightType: 'starting',
					airlineLogoUri: group.departure.image,
					airlineName: group.departure.name,
                    fareId: flight.fares[0].fareId,
                    fareKey: flight.fares[0].fareKey,
				};
				const promises = new Promise(async (resolve, reject) => {
					const responses = {};
					const rqRevalidate = getParamsRevalidate(this.paramsUpsell);
					const rqFamilyFare = getParamsDetailFlight(this.paramsDetail);

					responses['revalidate'] = await getRevalidate(rqRevalidate);
					responses['revalidate']['isSelected'] = true;
					responses['revalidate']['id'] = this.paramsUpsell.fareKey;
                    responses['revalidate']['taskID'] = quoteTaskID;
					responses['familyFare'] = await getFamilyFare(rqFamilyFare);

					if (responses['revalidate'] && responses['familyFare'] && !responses['revalidate'].error && !responses['familyFare'].error) {
						resolve(responses);
					}
					reject("Error revalidate");
				}).then(res => {
					this.setFlightRevalidateResponse(res['revalidate']);
					this.setFlightFamilyFareResponse(res['familyFare']);
				}).catch(err => {
					// Error handler when any requesst fails
					Logger.warn(err);
				}).finally(async () => {
					this.setRevalidateStatus(true);
				});
				await promises;

				let rq = getParamsUpsell(this.paramsUpsell);

				let responseUpsell = await getUpsell(rq);
				responseUpsell.quoteToken = group.quoteToken;
                responseUpsell.taskID = quoteTaskID;
				this.setFlightUpsell(responseUpsell);
			},
			handleScroll() {
				for (let group in this.configSticky) {
					const item = document.getElementById('cSticky' + group);
					let top = item.offsetTop;
					const scrollTop = Math.round(window.scrollY);
					/*window.scrollTo(0, scrollTop);*/
					setTimeout(() => {
						if (scrollTop >= top && !this.configSticky[group]) {
							this.configSticky[group] = true;
						} else if (scrollTop + 50 < top && this.configSticky[group]) {
							this.configSticky[group] = false;
						}
					}, 100);
				}
			},
			firstAirline(value) {
				value = value.toUpperCase();
				return (this.configSite || []).sort((a, b) => ((a.airlineCode || []).find(code => code.toUpperCase() === value) || (a.airlineName).toUpperCase() === value) ? -1 : 1);
			},
			sortAirlines(airlines) {
				if (this.information.addFirst && this.information.addFirst !== 'home') {
					this.configSite = this.firstAirline(this.information.addFirst)
				}
				else if (this.information.landing && this.information.landing !== 'home') {
					this.configSite = this.firstAirline(this.information.landing)
				}
				return (this.configSite).flatMap(
					setting => airlines.filter(
						airline => (setting.airlineCode || []).find(
							code => airline.departure?.code?.toUpperCase() === code.toUpperCase())))
			}
		},
        components: {
            CurrencyDisplay
        }
	}
</script>

<style lang="scss" scoped>
	$color_1: #999;
	$color_2: #333;
	$color_3: #e50000;
	$color_4: #003b98;
	$color_5: #186cdf;
	$font-family_1: icomoon;
	$background-color_1: #fff;
	$background-color_2: #ffff54;

	.cn-figure {
		line-height: 1;

		img {
			width: 40px;
		}

		.cl-iata {
			color: $color_1;
			font-size: 14px;
		}
	}

	.cn-header {
		background-color: #fff;
		margin: 0;
		position: sticky !important;
		top: 0;
		z-index: 98
	}

	@media (max-width:767px) {
		.cn-figure {
			span {
				display: block;
				text-align: center;
			}

			.cl-iata {
				* {
					font-size: 12px;
				}

				.cli-p {
					display: none !important;
				}
			}

			img {
				margin-bottom: 5px;
			}

			span + span {
				font-size: 12px;
			}
		}
	}

	.shadow-md {
		box-shadow: 0 .2rem 1rem rgba(0, 0, 0, .15) !important
	}

	.cn-cheap-active {
		background-color: $background-color_2;
		border: 1px solid #ccc;
		border-radius: 50px;
		padding: 2px 6px;
	}

	.c-capsule-active {
		* {
			color: $color_3;
		}
	}



	.cn-flights-list {

		&:last-of-type {
			@media (max-width:767px) {
				margin-bottom: 82px !important;
			}
		}

		hr {
			position: relative;
			z-index: 0;
		}

		.cn-rate {
			span {
				line-height: 1.2;
			}

			.txt-ida {
				font-size: 18px;

				.c-desde {
					position: relative;
				}

				strong {
					color: $color_3;
				}
			}

			.txt-rate {
				font-size: 24px;
				line-height: 1;
				color: #003b98 !important;
			}

			.txt-info {
				font-size: 12px;
			}
		}
	}


	@media (max-width:767px) {
		.cn-flights-list {
			.cn-rate {
				.txt-ida {
					font-size: 14px;
				}
			}
		}
	}

	@media (min-width:0) and (max-width:380px) {
		.cn-flights-list {
			.cn-rate {
				.txt-ida {
					font-size: 14px;
				}
			}
		}
	}

	.cb-rate-in {
		span {
			color: $color_4;
			font-size: 22px;
		}

		span + span {
			bottom: 0;
			color: $color_5;
			font-size: 34px;
			height: 34px;
			margin: auto;
			position: absolute;
			right: 1px;
			top: 0;
		}
	}

	@media (max-width:767px) {
		.cb-rate-in {
			span {
				font-size: 18px;
			}
		}
	}

	.all-link *,
	.i-link {
		color: #186cdf
	}

	.all-link-hover :hover,
	.underline-hover:hover {
		text-decoration: underline;
		-webkit-text-decoration-color: #186cdf;
		text-decoration-color: #186cdf
	}

	.cn-footer:before {
		content: "";
		border-bottom: 1px solid rgba(0, 0, 0, .1);
		left: 0;
		position: absolute;
		top: -1px;
		right: 0
	}

	.cn-f-cheap:after {
		background-color: #ffff54;
		border-radius: 0 0 5px 5px;
		bottom: 0;
		content: "";
		height: 10px;
		left: 0;
		position: absolute;
		right: 0
	}

	.cn-line-route .cn-fight-time {		
		font-size: 12px;
		left: 0;
		position: absolute;
		right: 0;
		top: -9px;
		@media (max-width:767px) {
			font-size: 11px;
		}		
	}

   
	.cn-line-route .icon {
		left: 19px;
		top: 5px;
		@media (max-width: 370px) {
			left: 10px;
    		top: 7px;
		}
		@media (max-width: 345px) {
			left: 12px;
    		top: 7px;
		}
		@media (max-width: 331px) {
			left: 13px;
    		top: 7px;
		}		
	}

	.cn-line-route .cns-scale-3-4:after {
		left: 0;
		right: 60%
	}
	.cn-line-route .cn-lr-in {
		border-bottom: 1px dashed #828282;
		bottom: 0;
		height: 1px;
		left: -10px;
		margin: auto;
		position: absolute;
		right: -10px;
		top: 0
	}

	.cn-line-route .cn-lr-in {
		@media (max-width:400px) {
			left: 2px;
			right: 0;
			top: 4px;				
		}
		@media (min-width: 401px) and (max-width: 425px) {		
			left: 0% !important;					
		}
		@media (min-width: 426px) and (max-width: 530px) {		
			left: -8% !important;					
		}
		@media (min-width: 531px) and (max-width: 767px) {		
			left: -12% !important;					
		}
		@media (min-width: 992px) and (max-width: 1279px) {		
			left: 7px !important;					
		}
		@media (min-width:768px) and (max-width: 991px) {		
			left: 18px !important;		
		}
		@media (min-width:768px) and (max-width:1024px) {		
			left: 15px		
		}
		@media (max-width:767px) {
			right: 2%;
		}	
	}	

	.cn-line-route .cn-lr-in:after {
		@media (max-width:767px) {
			font-size: 14px;
		}
	}
	
	.cn-header:before {
		bottom: 0;
		content: "";
		border-bottom: 1px solid rgba(0, 0, 0, .1);
		left: 0;
		position: absolute;
		right: 0
	}

	.sticky-settings {
		background-color: #f6fafa;
		padding: 9px 15px !important
	}

	.sticky-settings .cn-figure img {
		height: 30px;
		width: auto
	}

	@media (max-width:767px) {
		.sticky-settings .cn-figure img {
			display: none
		}
	}

	.sticky-settings .cn-figure span + span {
		-ms-flex-align: center !important;
		align-items: center !important;
		display: initial !important;
		height: 100% !important
	}

	@media (max-width:767px) {
		.sticky-settings .cn-figure span + span {
			display: block !important
		}
	}

	.sticky-settings .cn-rate {
		display: -ms-flexbox;
		display: flex;
		-ms-flex-align: center;
		align-items: center
	}

	.sticky-settings .cn-rate span {
		margin-left: auto
	}

	.sticky-settings .cn-rate .c-info-rate {
		display: none
	}

	.sticky-settings .txt-ida {
		padding-right: 25px
	}

	@media (max-width:767px) {
		.sticky-settings .txt-ida {
			font-size: 14px !important;
			padding-right: 0
		}
	}

	.strong {
		color: #646464;
		font-family: Roboto-Bold;
	}

    .ci-tooltip {
        display: inline-block;
        color: #828282;
        font-size: 11px;
        z-index: 97;
        top: 9px;
        margin-left: 4px;
        position: absolute;
        width: 45px;

        @media (min-width: 1280px) {
            right: -46px;
        }

        @media (min-width: 1025px) and (max-width: 1279px) {
            right: -64px;
        }

        @media (min-width: 992px) and (max-width: 1024px) {
            right: -60px;
        }

        @media (min-width: 768px) and (max-width: 991px) {
            right: -76px;
        }

        @media (max-width: 350px) {
            right: -56px !important;
        }

        .cit-day {
            @media (min-width: 371px) and (max-width: 380px) {
                display: none;
            }
        }
    }
	

	@media (max-width:767px) {
		.ci-tooltip {
			top: 7px;
			right: -55px;
		}
	}

	.ci-tooltip .tooltiptext {
		visibility: hidden;
		position: absolute;
		width: 190px;
		background-color: #fff;
		color: #333;
		text-align: center;
		border-radius: 6px;
		z-index: 1;
		opacity: 0;
		transition: opacity .6s;
		border: 1px solid #f1f1f1;
		padding: 1px 10px;
		box-shadow: 0 .125rem .25rem rgba(0,0,0,.075) !important
	}

	@media (max-width:767px) {
		.ci-tooltip .tooltiptext {
			display: none
		}
	}

	.ci-tooltip:hover .tooltiptext {
		visibility: visible;
		opacity: 1
	}

	.tooltip-top {
		top: -20px;
		left: -20px;
		margin-left: -72px
	}

	.sticky-settings .cn-rate, .sticky-settings .cn-rate .c-info-rate {
		display: block
	}

	.sticky-settings .cn-rate .c-info-rate .txt-rate {
		display: none
	}

	.c-info-rate .txt-rate, .cb-rate-in .f-bold {
		color: #003b98 !important
	}

	.text-right {
		text-align: right !important
	}

	.ml-auto, .mx-auto {
		margin-left: auto !important
	}

	.sticky-settings .txt-ida .i-sticky {
		display: none
	}


	@media (max-width:370px) {
		.cn-lr-in.in {
			display: none;
		}
		.cn-line-route {
			width: 30px !important;
		}
		.icon-plane-right {
			display: block !important;
		}
	}

	.c-iata-right {
		color: #828282;
		position: absolute;
		right: 0;
		top: -9px;
		@media (max-width: 390px) {
			right: -58% !important;				
		}
		@media (max-width: 767px) {
    		top: -15px;							
		}
		 @media (min-width: 530px) and (max-width: 767px) {
        right: -33% !important;
		}
		@media (min-width: 501px) and (max-width: 529px) {
			right: -36% !important;
		}
		@media (min-width: 454px) and (max-width: 500px) {
			right: -39% !important;
		}
		@media (min-width: 391px) and (max-width: 453px) {
			right: -46% !important;
		}   
	}





	.c-iata-left {
		color: #828282;
		position: absolute;
		left: 0;
		top: -9px;
		@media (max-width: 399px) {
			left: -55% !important;
			top: -15px;				
		}
		@media (min-width: 400px) and (max-width: 425px) {
			left: -50% !important;
			top: -14px;				
		}
		@media (min-width: 426px) and (max-width: 529px) {
			left: -40%;
    		top: -14px;							
		}
		@media (min-width: 530px) and (max-width: 767px) {
			position: relative;
			left: -75% !important;
			top: -14px;	
		}
	}
	

	.c-card.is-loading {
    .image,
    h2,
    p {
      background: #eee;
      background: linear-gradient(110deg, #ececec 8%, #f5f5f5 18%, #ececec 33%);
      border-radius: 5px;
      background-size: 200% 100%;
      animation: 1.5s shine linear infinite;
    }
    
    .s-line-20 {
        background: #eee;
        background: linear-gradient(110deg, #ececec 8%, #f5f5f5 18%, #ececec 33%);
        border-radius: 5px;
        background-size: 200% 100%;
        animation: 1.5s shine linear infinite;
    }
    .image {
      height: 200px;
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;
    }  
  }
  
  @keyframes shine {
    to {
      background-position-x: -200%;
    }
  }

  .c-hrs {
	  position: absolute;
		right: 0;
		left: 0;
		width: 64px;
		margin: auto;
		padding: 0;
		@media (max-width: 767px) {
			white-space: nowrap;
    		width: 53px;						
		}
  }

	@media (max-width: 767px) {
		.cb-scale .a-link {
			right: 0;
			padding-right: 20px;
		}
	}

	.cn-line .c-eq {
		@media (max-width: 767px) {
			.cb-scale .a-link {
				right: -10px;
			}
		}


	}
	.cn-line-in {
		.txt-time {
			@media (max-width: 767px) {
				font-size: 18px;
			}
		}
		.cn-line-route {
			@media (max-width: 767px) {
				right: -2px;
			}			
		}		
	}
	.cb-rate-in span {
		@media (max-width: 767px) {
			font-size: 16px;
		}
	}

	.cb-rate-in span + span {
		@media (max-width: 767px) {
			font-size: 32px;
			right: -11px !important;
			top: 2px;
		}		
	}

	.cn-line-route .cn-lr-in {
		@media (max-width: 767px) {
			border: none;
			&:before {
				display: block;
				content: "";
				border-bottom: 1px dashed #828282;
				right: 6px;
				left: 6px;
				position: absolute;
				top: -2px;
			}
		}
		@media (max-width: 730px) and (max-width: 445px) {
			&:before {
				left: 9px;
			}			
		}
	}
	
	
	.icon-big-bag .btn-link {
    &:before {
        color: var(--text-link) !important;
        font-size: 16px !important;
        margin-right: 0px !important;
        margin-left: 0px  !important;
        inset: 0px !important;
    }
}


	

	
</style>