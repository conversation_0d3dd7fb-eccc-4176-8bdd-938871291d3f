/* DS LINKS ---------------------------------------------- */
// Anchors reset
a {
    background-color: transparent;
    border: none;
    outline: none;
    font-style: normal;
    font-weight: normal;
    text-decoration: none;
    color: inherit;
    cursor: pointer;

    &:hover {
        color: inherit;
        text-decoration: none;
    }
    &:focus-visible {
        outline: 2px solid var(--border-info);
        outline-offset: 2px;
    }
}

.link {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    border-bottom: 1px solid transparent;
    font: var(--body-bold);
    line-height: 1em;
    letter-spacing: 0.01em;
    color: var(--text-link);
    transition: all 150ms ease-out;
    cursor: pointer;

    i {
        display: inline-flex;
        font-size: 1.25rem;
        pointer-events: none;
    }

    &:hover {
        border-color: var(--text-link);
        color: var(--text-link-hover);
    }
    &:disabled {
        color: var(--text-disabled);
        cursor: not-allowed;

        &:hover {
            color: var(--text-disabled);
        }
    }
    // Sizes
    &--md {
        font: var(--body-sm-bold);
        letter-spacing: 0.02em;
    }
    &--xs {
        font: var(--body-xs-bold);
        letter-spacing: 0.02em;
    }
}
/* DS LINKS ---------------------------------------------- */