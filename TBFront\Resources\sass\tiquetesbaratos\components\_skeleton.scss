//Se aplica cuando un tag está vacío
.skeleton-primary,
.skeleton-secondary {
  &:empty {
    position: relative;
    overflow: hidden;
    border-radius: 15px;
    display: inline-block;
    clear: left;
    // float: left;
    margin-top: 4px;
    margin-bottom: 4px;
    padding: 0.5rem;
    width: 100%;
    &:after {
      display: block;
      content: '';
      position: absolute;
      top: 0;
      width: 100%;
      height: 100%;
      z-index: 5;
      border-radius: 15px;
      transform: translateX(-100%);
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 1), transparent);
      animation: skeleton-loading 0.75s infinite;
    }
  }
}

.skeleton-primary,
.skeleton-secondary {
  &:empty {
    &:hover {
      cursor: default;
      box-shadow: none;
      outline: 0;
    }
  }
}

.skeleton-primary {
  &:empty {
    background-color: $gray-200;
    //Para iconos
    &:before {
      color: $gray-200;
    }
    &:hover {
      background-color: $gray-200;
    }
  }
}

.skeleton-secondary {
  &:empty {
    background-color: $gray-100;
    //Para iconos
    &:before {
      color: $gray-100;
    }
    &:hover {
      background-color: $gray-100;
    }
  }
}

@keyframes skeleton-loading {
  100% {
    transform: translateX(100%);
  }
}


.skeleton-image {
  &:empty {
    position: relative;
    overflow: hidden;
    display: inline-block;
    clear: left;
    // float: left;
    margin-top: 14px;
    margin-bottom: 14px;
    width: 100%;
    &:after {
      display: block;
      content: '';
      position: absolute;
      top: 0;
      width: 100%;
      height: 100%;
      z-index: 5;
      transform: translateX(-100%);
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 1), transparent);
      animation: skeleton-loading 0.75s infinite;
    }
  }
}

.skeleton-image{
  &:empty {
    &:hover {
      cursor: default;
      box-shadow: none;
      outline: 0;
    }
  }
}

.skeleton-image {
  &:empty {
    background-color: $gray-200;
    //Para iconos
    &:before {
      color: $gray-200;
    }
    &:hover {
      background-color: $gray-200;
    }
  }
}

.skeleton-image {
  &:empty {
    background-color: $gray-100;
    //Para iconos
    &:before {
      color: $gray-100;
    }
    &:hover {
      background-color: $gray-100;
    }
  }
}

.skeleton-container {
  height: 100%; 
  .skeleton-image  {
    height: 100%;
  }
}