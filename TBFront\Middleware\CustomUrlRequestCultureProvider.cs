﻿using Microsoft.AspNetCore.Localization;

namespace TBFront.Middleware
{
    public class CustomUrlRequestCultureProvider : IRequestCultureProvider
    {
        public Task<ProviderCultureResult> DetermineProviderCultureResult(HttpContext httpContext)
        {
            var culture = httpContext.Items["culture"] as string ?? "es-co";
            return Task.FromResult(new ProviderCultureResult(culture));
        }
    }
}
