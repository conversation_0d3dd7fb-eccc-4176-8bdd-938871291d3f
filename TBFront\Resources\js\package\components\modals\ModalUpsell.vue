<template>
    <section id="cn-sidebar" class="cn-sidebar" style="right: 0px;" :class="getClass(packageRates)">
        <div class="cns-header border-bottom p-3">
            <div class="position-relative">
                <span id="btn-close" class="icon icon-close pointer" @click="changeOpenCloseModalUpsell()"></span>
                <span class="pl-5 txt-title"
                      v-if="!this.getIsStepTwo" v-html="__('multiticket.departure_flight_upsell')"></span>
                <span class="pl-5 txt-title" v-else v-html="__('multiticket.returning_flight_upsell')"></span>
                <span v-if="!getLoading && packageRates && numberOfRates() > 1" class="txt-title" v-html="getTextQuantity()"></span>
            </div>
        </div>
        <div id="cnrContainer" class="cns-body" :class="showDetail ? 'cnr-container' : ''">
            <div id="headerRates" class="cnsb-head">
                <div class="package-item-flight-title pointer" @click="activeDetail()">
                    <span class="btn-link ps-2">{{__('messages.view_flight_details')}}</span>
                    <span class="icon icon-expand btn-link no-hover font-24"></span>
                </div>
            </div>
            <div id="containerDetailFlight" class="c-detail-flight-rate position-relative mb-2"
                :class="showDetail ? 'h-open' : 'd-none'">
                <item-flight-leg :type="'starting'" v-if="!this.getIsStepTwo"
                    :departureAirportCode="summaryDetailFlight.departureFlight.departure.airportCode"
                    :arrivalAirportCode="summaryDetailFlight.departureFlight.arrival.airportCode"
                    :departureTime="summaryDetailFlight.departureFlight.departure.time"
                    :arrivalTime="summaryDetailFlight.departureFlight.arrival.time"
                    :dateFlight="summaryDetailFlight.departureFlight.departure.date"
                    :stops="summaryDetailFlight.departureFlight.stops"
                    :flightDays="summaryDetailFlight.departureFlight.flightDays"
                    :imageAirline="setImgAirline(summaryDetailFlight.departureFlight.airline.code)"  />

                <item-flight-leg v-if="this.pt.data.isRoundtrip && this.getIsStepTwo" :type="'returning'"
                    :departureAirportCode="flightSelected.departureFlight.departure.airportCode"
                    :arrivalAirportCode="flightSelected.departureFlight.arrival.airportCode"
                    :departureTime="flightSelected.departureFlight.departure.time"
                    :arrivalTime="flightSelected.departureFlight.arrival.time"
                    :dateFlight="flightSelected.departureFlight.departure.date"
                    :stops="flightSelected.departureFlight.stops"
                    :flightDays="flightSelected.departureFlight.flightDays"
                    :imageAirline="setImgAirline(flightSelected.departureFlight.airline.code)"></item-flight-leg>
            </div>
            <div id="containerRates" class="cnsb-cont" :class="showDetail ? 'c-rate-open head-rate' : ''">
                <div v-if="!getLoading" class="px-0 py-0 mb-0 mt-4"></div>
                <div v-if="getLoading" class="c-card is-loading p-0 m-0 w-100 mb-4">
                    <div class="px-0 py-3">
                        <p class="m-0 py-2"></p>
                    </div>
                </div>
                <div id="cContainerScroll" class="c-scroll-steps h-99" ref="scrollContainer" @scroll="handleScroll">
                    <div class="row h-99 c-row-steps">
                        <div class="ch-container chc-rates mb-3 h-99">
                            <div v-if="getRevalidateStatus && getFlightRevalidate"
                                class="ch-column col-rate mr-3 px-3 pt-2 border ml-3 position-relative c-rate-active overflow-auto"
                                :key="getFlightFamilyFare.familyFareName">
                                <div class="c-rate-style"></div>
                                <div class="chc-header mt-1">
                                    <h3 class="m-0 ml-2 d-flex rate py-2">
                                        <div class="text-truncate text-ellipsis">
                                            {{ parseText(getFlightFamilyFare.familyFareName) }}
                                        </div>
                                        <div class="rate-selected ml-2 ml-auto m-0 d-flex align-items-center">
                                            <span class="icon icon-check-circle font-18"></span>
                                            <span
                                                class="font-selected-fare font-12 mt-1">{{__('messages.selected_fare')}}</span>
                                        </div>
                                    </h3>
                                    <div class="row d-flex c-btn-rate py-2 mt-2">
                                        <div class="col-7 chcf-01 pe-1 pe-md-3">
                                            <!--<p class="m-0 font-14"><strong class="m-0 font-14">Total ({ {paxText}})</strong></p>-->
                                            <p class="label m-0" v-if="!getIsStepTwo">
                                                {{ pt.data.isRoundtrip ?  __('multiticket.price_RoundTrip') : __('multiticket.price_from') }}
                                            </p>
                                            <p class="price m-0 f-bold">
                                                <CurrencyDisplay :amount="getTotalAmount > 0 ? getTotalAmount / (pt.data.adults + pt.data.kids) : flightSelected.totalAmount" :showCurrencyCode="true" :reduceIsoFont="true" :plusSymbol="getIsStepTwo" />
                                            </p>
                                            <p class="label m-0" v-if="getIsStepTwo">
                                                {{ __('multiticket.per_pax') }}
                                            </p>
                                            <p class="taxes m-0 font-12 fs-10-xs">{{__('messages.includes_all_taxes')}}</p>
                                        </div>
                                        <div
                                            class="col-5 ps-0 pe-3 chcf-02 d-flex justify-content-end align-items-center">
                                            <button id="defaultFamily"
                                                class="w-100 btn btnAddRate px-2 px-md-3 py-3 d-flex justify-content-center align-items-center"
                                                @click="sendData(getFlightRevalidate)">
                                                <span
                                                    class="btn-select ps-2">{{__('messages.continue_button_text')}}</span>
                                                <span class="icon icon-chevron-right"></span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="chc-body pr-3 mt-3 modal-info-family-details position-relative ifd">
                                    <div class="pb-5 font-14 c-divisor-line">
                                        <div v-for="content, index in mappingFamilyFare(getFlightFamilyFare.familyFareContent)" :key="index" class="mb-3">
                                            <div v-if="content.description != ' '">
                                                <p class="f-bold mb-1 font-14 position-relative">                                                    
                                                    <span class="icon ic-in" :class="content.class"></span> 
                                                    <span class="font-medium ps-4 d-inline-block">{{content.title}}:</span>  
                                                </p>
                                                <p class="mb-0 font-14 ps-24 font-regular">{{content.description}}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <template v-if="!getLoading && packageRates" v-for="rate, j in packageRates">
                                <div v-if="!rate.isSelected"
                                    class="ch-column col-rate mr-3 px-3 pt-2 border ml-3 position-relative overflow-auto"
                                    :class="'c-rate-continue family-rate-'+family(j)" :key="rate.name">
                                    <div class="c-rate-style"></div>
                                    <div class="chc-header mt-1">
                                        <h3 class="m-0 ml-2 text-ellipsis rate py-2">
                                            {{ parseText(rate.name) }}
                                        </h3>
                                        <div class="row d-flex c-btn-rate py-2 mt-2">
                                            <div class="col-7 chcf-01 pe-1 pe-md-3">
                                                <p class="label m-0" v-if="!getIsStepTwo">
                                                    {{ pt.data.isRoundtrip ?  __('multiticket.price_RoundTrip') : __('multiticket.price_from') }}
                                                </p>
                                                <p class="price m-0 f-bold">
                                                    <CurrencyDisplay :amount="rate.rate / (pt.data.adults + pt.data.kids)" :showCurrencyCode="true" :reduceIsoFont="true" :plusSymbol="getIsStepTwo" />
                                                </p>
                                                <p class="label m-0" v-if="getIsStepTwo">
                                                    {{ __('multiticket.per_pax') }}
                                                </p>
                                                <p class="taxes m-0 font-12 fs-10-xs">
                                                    {{__('messages.includes_all_taxes')}}
                                                </p>
                                            </div>
                                            <div
                                                class="col-5 ps-0 pe-3 chcf-02 d-flex justify-content-end align-items-center">
                                                <button type="submit"
                                                    class="w-100 btn btnAddRate mx-auto px-2 px-md-3 py-3 d-flex justify-content-center align-items-center"
                                                    @click="sendData(rate)">
                                                    <span
                                                        class="btn-select ps-2">{{__('messages.select_button_text')}}</span>
                                                    <span class="upsell-button icon icon-chevron-right"></span>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="chc-body pr-3 mt-3 modal-info-family-details position-relative ifd">
                                        <div class="pb-5 font-14 c-divisor-line">
                                            <div v-for="content, index in mappingFamilyFare(rate.content)" :key="index + 1" class="mb-3">
                                                <div v-if="content.description != ' '">
                                                    <p class="f-bold mb-1 font-14 position-relative">
                                                        <span class="icon ic-in" :class="content.class"></span> 
                                                        <span class="font-medium ps-4 d-inline-block">{{content.name}}:</span>                                                        
                                                    </p>
                                                    <p class="mb-0 font-14 ps-24 font-regular">{{content.description}}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </template>
                            <Skeleton v-if="!getRevalidateStatus" :isSelected="true" :typeSkeleton="'typeFamily'" />
                            <Skeleton v-if="getLoading" :isSelected="false" :typeSkeleton="'typeFamily'" />
                        </div>
                        <div v-if="packageRates && numberOfRates() > 1" class="c-f-paginator">
                            <div id="btnCtrlLeft" class="cfp-ctrl cfp-left shadow" @click=scrollLeftButton()
                                :class="{ 'd-none': isLeftHidden }">
                                <span class="icon icon-chevron-left font-50"></span>
                            </div>
                            <div id="btnCtrlRight" class="cfp-ctrl cfp-right shadow" @click="scrollRightButton()"
                                :class="{ 'd-none': isRightHidden }">
                                <span class="icon icon-chevron-right font-50"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <div class="overlay-upsell" @click="changeOpenCloseModalUpsell()"></div>
</template>

<script>
    import { storeToRefs } from 'pinia';
    import { useFlightUpsellStore } from '../../stores/flightUpsell';
    import { useUserSelectionStore } from '../../stores/user-selection';
    import { useLoaderPageStore } from '../../stores/loader-page';
    import { useFlightFamilyFareStore } from '../../stores/flightFamilyFare';
    import { useFlightRevalidateStore } from '../../stores/flightRevalidate';
    import { mappingFamilyFare } from '../../../utils/utils';
    import { useMultiTicketStore } from '../../stores/multiTicket';
    import { sleep, windowScrollTop } from '../../../utils/helpers';
    import { useFlightStore } from '../../stores/flight';
    import { getMatrix, getFilteredList, getLuggage } from '../../services/ApiFlightFrontServices';
    import { useFlightMatrixStore } from '../../stores/flightMatrix';
    import { getReturningFlights, actionFlights } from "../../services/fetchListService";
    import { usePromotionStore } from '../../stores/promotion';
    import CurrencyDisplay from '../common/CurrencyDisplay.vue';
    import {List} from "../../../utils/analytics/flightList";

    export default {
        data() {
            return {
                showDetail: false,
                pt: window.__pt,
                scrollPosition: 0,
                anchoScroll: 0,
                isLeftHidden: true,
                isRightHidden: false,
                culture: window.__pt.cultureData
            }
        },
        setup() {
            const storeFlight = useFlightStore();
            const useFlightUpsell = useFlightUpsellStore();
            const useUserSelection = useUserSelectionStore();
            const useLoaderPage = useLoaderPageStore()
            const storeFlightMatrix = useFlightMatrixStore();

            const flightFamilyFareStore = useFlightFamilyFareStore();
            const flightRevalidateStore = useFlightRevalidateStore();
            const promotionStore = usePromotionStore();

            const { getFlightUpsell, getFlightSelected, getRates, getLoading } = storeToRefs(useFlightUpsell);
            const { changeOpenCloseModalUpsell, setFlightSelected, getLuggageFamilyFare } = useFlightUpsell;
            const { getParams, getProgressBar, getAllQuoteTokens, getStartQuoteTokens, getreturningQuoteToken, getReturnQuoteTokens, getGroups } = storeToRefs(storeFlight);
            const { setFlightResponses, resetFlightResponse } = storeFlight;
            const { setLoading, setFlightMatrix } = storeFlightMatrix;
            const { paxText } = storeToRefs(useUserSelection);
            const { changeFilters, showRequoteModal } = useUserSelection;
            const { showLoaderPage } = useLoaderPage;
            const { setLuggage } = flightFamilyFareStore;

            const { getFlightRevalidate, getTotalAmount, getRevalidateStatus } = storeToRefs(flightRevalidateStore);
            const { getFlightFamilyFare } = storeToRefs(flightFamilyFareStore);

            const useMultiTicket = useMultiTicketStore();
            const { getIsStepTwo, getCheckOutDataStepOne } = storeToRefs(useMultiTicket);
            const { setCheckOutDataStepOne,  setIsStepTwo, setAirlinesFilters, setStopsFilters} = useMultiTicket;
            const { getTripMode } = storeToRefs(promotionStore);
            return {
                changeOpenCloseModalUpsell, getFlightSelected, getRates,
                getLoading, paxText, getFlightUpsell, showLoaderPage,
                getFlightRevalidate, getTotalAmount, getRevalidateStatus,
                getFlightFamilyFare, showRequoteModal, getLuggageFamilyFare,
                setCheckOutDataStepOne, getCheckOutDataStepOne, setIsStepTwo,
                getIsStepTwo, getReturnQuoteTokens, setFlightSelected, setLoading,
                setFlightMatrix, changeFilters, setFlightResponses, resetFlightResponse,
                getTripMode, getAllQuoteTokens, setAirlinesFilters, setStopsFilters, mappingFamilyFare,
                getreturningQuoteToken, setLuggage
            }
        },
        computed: {
            flightSelected() {                
                return this.getFlightSelected;
            },
            summaryDetailFlight(){
                if (this.pt.data.isRoundtrip  && !this.getIsStepTwo || !this.pt.data.isRoundtrip) {
                    return this.flightSelected
                }else {
                    return this.getCheckOutDataStepOne
                }
            },
            packageRates() {
                const groupToRemove = this.getFlightRevalidate.fares[0]?.fareGroup?.toUpperCase() ?? "";
                const rates = Array.isArray(this.getRates) ? this.getRates.filter(obj => obj.group.toUpperCase() !== groupToRemove) : this.getRates;
                return rates;
            },
        },
        methods: {
            activeDetail() {
                this.showDetail = !this.showDetail;
            },
            getClass(getRates) {
                const upsellClasses = { 1: "upsell-1", 2: "upsell-2", 3: "upsell-2-5", 4: "upsell-3" };
                let upsellClass = upsellClasses[2];
                if (!this.getLoading) {
                    if (getRates) {
                        const numberRates = this.numberOfRates();
                        const id = numberRates > 3 ? 4 : numberRates;
                        upsellClass = upsellClasses[id];
                    } else {
                        upsellClass = upsellClasses[1];
                    }
                }
                return upsellClass;
            },
            family(index) {
                if (index % 2 == 0) {
                    return "1"
                } else {
                    return "2"
                }
            },
            scrollLeftButton() {
                let slide = this.pt.settings.site.isMobile() ? -340 : -415;
                let scrollContainer = document.getElementById("cContainerScroll");
                scrollContainer.scrollBy({ left: (slide), behavior: 'smooth' })
                //scrollContainer.scrollLeft  -= 320 415
            },
            scrollRightButton() {
                let slide = this.pt.settings.site.isMobile() ? 340 : 415;
                let scrollContainer = document.getElementById("cContainerScroll");
                scrollContainer.scrollBy({ left: (slide), behavior: 'smooth' })
                //scrollContainer.scrollLeft +=  320
            },
            parseText(text) {
                if (!text) {
                    return '';
                }
                let arrText = text.split(' ');
                for (let i = 0; i < arrText.length; i++) {
                    // Convertir la primera letra de cada palabra a may�scula
                    arrText[i] = arrText[i].charAt(0).toUpperCase() + arrText[i].slice(1);
                }
                let textConvert = arrText.join(' ');
                return textConvert;
            },
            configFilter(code) {
				return (window.__pt.settings.site.airlineConfiguration.international || []).find(item => (String(item.airlineCode)).toUpperCase() === (String(code)).toUpperCase()) ?? {}
			},
            actionFlightSelected(flight_selected = this.flightSelected) {
                this.setFlightSelected(flight_selected)
            },
            selectInputFlight(step_action, clearAll=true) {
                let flight_selected = (this.flightSelected);
                setTimeout(() => {
                    if(this.pt.data.isRoundtrip){
                        const params = {
                            departureToken: this.flightSelected.groupFlight.departure.token,
                            flightQuoteId: this.flightSelected.departureFlight.fares[0].fareKey,
                            code: this.flightSelected.departureFlight.airline?.code
                        }
                        let configuration = this.configFilter(this.flightSelected.departureFlight.airline?.code) || {};
                        getReturningFlights(params, configuration, this.flightSelected.departureFlight.engine, (res)=>{
                            (Object.values(res.returning.flightList)).forEach((flightList)=>{
                                let roundtrip_fare = null;
                                let roundtrip_flight = null;
                                let diff_fare = 0
                                actionFlights(flightList.flights, {
                                    callbackFare: (fare, $indexFare, flight)=>{
                                        if(fare.isRoundTrip){
                                            const calc_dif = fare.beforeDisplayAmount - fare.displayAmount
                                            if(diff_fare === 0 || diff_fare < calc_dif){
                                                roundtrip_fare = fare;
                                                roundtrip_flight = flight
                                                diff_fare = calc_dif
                                            }
                                        }
                                    }
                                })
                            })
                        })   
                    }
                }, 500)
                this.actionFlightSelected(flight_selected)
                setTimeout(() => {
                    this.actionFlightSelected(flight_selected)
                }, 1000);
            },
            evtReturningFlights(data) {
                for (const element of data.response) {
                    (Object.values(element.returning.flightList)).forEach((flightList)=>{
                        let roundtrip_fare = null;
                        let roundtrip_flight = null;
                        let diff_fare = 0
                        actionFlights(flightList.flights, {
                            callbackFare: (fare, $indexFare, flight)=>{
                                if(fare.isRoundTrip){
                                    const calc_dif = fare.beforeDisplayAmount - fare.displayAmount
                                    if(diff_fare === 0 || diff_fare < calc_dif){
                                        roundtrip_fare = fare;
                                        roundtrip_flight = flight
                                        diff_fare = calc_dif
                                    }
                                }
                            }
                        })
                        if(roundtrip_fare && roundtrip_flight) List.roundtripNationalFlight(this.flightSelected.departureFlight.fares[0], roundtrip_flight, roundtrip_fare)
                    })   
                }
            },
            async searchLuggage(){
                if (this.getAllQuoteTokens && this.getAllQuoteTokens.length) {
                    const promises = new Promise(async (resolve, reject) => {
                        let getAllQuoteTokensSplit = this.getAllQuoteTokens.join(',').split(',');
                        let tokenSencillo = '';
 
                            tokenSencillo = this.getAllQuoteTokens.join(',') // TEMPORAL, HASTA QUE CORRIJA LA API

                        const response = await getLuggage({ token: tokenSencillo, site: window.__pt.settings.site.apiFlights.siteConfig });
                        if (response && !response.error) {
                            resolve(response);
                        }
                    }).then(res => {
                        this.setLuggage(res.luggages);
                    }).catch(err => {
                        // Error handler when any requesst fails
                        //Logger.warn(err);
                    });
                }
            },            
            async sendData(rate) {
                let checkoutData = this.mapCheckOutData(rate);
                if (!checkoutData.flights || !checkoutData.fares) {
                    return this.showRequoteModal("invalid-key");
                }

                if (this.pt.data.isRoundtrip  && !this.getIsStepTwo) {

                    const modal = new bootstrap.Modal(document.getElementById('LoaderFullPage'), null);
                    modal.show();
                    await sleep(1000)
                    

                    const params = {
                        token: this.getAllQuoteTokens.join(','),//(!getIsStepTwo.value ? getStartQuoteTokens.join(',') : getReturnQuoteTokens.join(',')),
                        filterApplied: '',
                        site: window.__pt.settings.site.apiFlights.siteConfig,
                        tripMode : this.getTripMode,
                        simpleFlightQuotes : true
                    };                
                    params.DepartureToken = checkoutData.summary.token;
                    params.FlightQuoteId = checkoutData.summary.fareKey;			
                    params.Step = true;
                    params.roundTripToken = this.getreturningQuoteToken.join(',')
                    params.allQuotes = !window.__pt.data.isNational;

                    const response = await getFilteredList(params);
                    this.evtReturningFlights(response);
                    this.resetFlightResponse();
                    await sleep(50)
                    this.changeFilters([]);
                    await this.setFlightResponses(response.response);
                    await this.setMatrix(checkoutData.summary.token, checkoutData.summary.fareKey);
                    await this.searchLuggage();
                    //this.selectInputFlight('returning', false);
                    this.changeOpenCloseModalUpsell();
                    
                    let group = rate.isSelected ? rate.fares[0].fareGroup : rate.group
                    let fs = {
                        departureFlight: this.flightSelected.departureFlight,
                        groupFlight: this.flightSelected.groupFlight,
                        luggages: this.getLuggageFamilyFare(group, rate.isSelected ? this.getFlightFamilyFare : {}),
                        totalAmount: rate.isSelected ? rate.fares[0].amount : rate.rate,
                    }
                    this.setCheckOutDataStepOne({...checkoutData, ...fs});  //  ->>>>>>>>>>>>>>>>>>>
                    this.setAirlinesFilters([]) //-> reiniciamos filtros
                    this.setStopsFilters('')
                    this.setIsStepTwo(true);
                    windowScrollTop();
                    await sleep(1000)
                    modal.hide();

                    return;
                }

                if (this.pt.data.isRoundtrip  && this.getIsStepTwo) {
                    
                    let dataStepOne = this.getCheckOutDataStepOne;
                    let dataStepTwo = {...checkoutData, ...this.flightSelected};

                    let checkoutDataMulti = {
                    flights : {
                        1 : !rate?.fares[0]?.isRoundTrip ? dataStepOne.flights[1] : rate.flights[1],
                        2 : !rate?.fares[0]?.isRoundTrip ? dataStepTwo.flights[1] : rate.flights[2]
                    },
                    fares : {
                        1 : !rate?.fares[0]?.isRoundTrip ? dataStepOne.fares[1] : rate.faresLegs[1],
                        2 : !rate?.fares[0]?.isRoundTrip ? dataStepTwo.fares[1] : rate.faresLegs[2]
                    },
                    summary: {
                        origin: dataStepOne.summary.origin,
                        destination: dataStepTwo.summary.origin,
                        fareKey: `${dataStepOne.summary.fareKey}|${dataStepTwo.summary.fareKey}`,
                        token: `${dataStepOne.summary.token},${dataStepTwo.summary.token}`,
                        departureName: this.pt.data.startingAirportPlace.cityName,
                        arrivalName: this.pt.data.returningAirportPlace.cityName,
                        quoteTaskID:  dataStepOne.summary.quoteTaskID.concat(dataStepTwo.summary.quoteTaskID),
                        quoteToken: this.getAllQuoteTokens.join(','),
                        multiple: [dataStepOne.departureFlight.fares[0].multiple, dataStepTwo.departureFlight.fares[0].multiple]
                    }
                }
                if(rate?.isRoundTrip){
                    checkoutDataMulti.flights = dataStepTwo.flights;
                    checkoutDataMulti.fares = rate.fares;
                }   
                if(dataStepOne?.departureFlight?.fares[0]?.isRoundTrip && !rate?.fares[0]?.isRoundTrip){
                    checkoutDataMulti.flights[1] = rate?.flightSelect?.flight
                    checkoutDataMulti.fares[1] = rate?.flightSelect?.fareLeg
                    checkoutDataMulti.summary.fareKey = rate?.id;
                }
                if (this.pt.data.isRoundtrip) {
                    checkoutDataMulti.summary.fareKey = rate?.id;
                }
                checkoutData = checkoutDataMulti;
                }
                let totalAmount = rate.isSelected ? rate.fares[0].amount : rate.rate;
                if (this.pt.data.isRoundtrip) {
                    totalAmount = rate.isSelected ? rate.fares[0].totalAmountRountrip : rate.totalAmountRountrip;
                }
                
                const params = {
                    site: null,
                    isBookNowPayLater: false,
                    lastFlightRate: totalAmount,
                    isPackage: false,
                    idAgentCall: 0,
                    checkIn: "0001-01-01T00:00:00",
                    checkOut: "0001-01-01T00:00:00",
                    hasSpecialDiscountAplied: false,
                    chkSource: 1,
                    rdmCksrreal: 1,
                    airline: this.flightSelected.departureFlight.airline.name,
                    tripMode: this.pt.data.isRoundtrip ? "RoundTrip" : "OneWay",
                    tripCabin: "N",
                    startingFromAirport: this.flightSelected.departureFlight.departure.airportCode,
                    startingFromDateTime: this.flightSelected.departureFlight.departureTime,
                    startingFromTime: "Anytime",
                    returningFromAirport: this.flightSelected.departureFlight.arrival.airportCode,
                    returningFromDateTime: this.pt.data.isRoundtrip
                        ? this.getCheckOutDataStepOne.departureFlight.departureTime : this.flightSelected.departureFlight.arrivalTime,
                    returningFromTime: "Anytime",
                    nonStopOnly: false,
                    selectedOutboundFlight: this.flightSelected.departureFlight.id,
                    selectedReturnFlight: this.pt.data.isRoundtrip ? this.getCheckOutDataStepOne.departureFlight.id : 0,
                    fareKeyQuote: rate.id,
                    quoteFlight: false,
                    quoteList: false,
                    referralUrl: window.location.pathname + window.location.search,
                    adults: this.pt.data.adults,
                    kids: this.pt.data.kids,
                    ageKids: this.pt.data.agekids,
                    infants: 0,
                    seniors: 0,
                    isDomesticRoute: false,
                    paxes: this.mapPaxes(),
                    flights: null,
                    fares: null,
                    isRoundTrip: rate?.fares[0]?.isRoundTrip || rate?.isRoundTrip,
                    checkoutData: JSON.stringify(checkoutData)
                };

                const urlWithCulture = `/${this.pt.cultureData.cultureCode}${this.pt.settings.site.checkoutUrl}`

                this.sendObjectPostForm(urlWithCulture, params);
                this.evtLayerIsRountrip(rate)
                this.showLoaderPage();
            },
            evtLayerIsRountrip(rate){
                //console.log(this.flightSelected.departureFlight.fares[0].isRoundTrip, rate, "this.getCheckOutDataStepOne", this.getCheckOutDataStepOne.departureFlight.fares[0])
                if(this.flightSelected.departureFlight.fares[0].isRoundTrip){
                    List.selectRoundtripNationalFlight(this.getCheckOutDataStepOne.departureFlight.fares[0], this.flightSelected.departureFlight, rate.fares[0])
                }
            },
            async setMatrix(token, fareKey) {
                
                if (this.getReturnQuoteTokens.length) {
                    this.setLoading(true);
                    const response = await getMatrix({ token: (this.getReturnQuoteTokens.join(',')), 
                                                        step: true, 
                                                        departureToken: token, 
                                                        flightQuoteId: fareKey, 
                                                        StepView: this.getIsStepTwo ? "starting" : "returning",
                                                        oneWayToken: this.getAllQuoteTokens.join(',')
                                                    });//this.getIsStepTwo
                    this.setFlightMatrix(response);
                    this.setLoading(false);
                }
            },
            sendObjectPostForm(url, obj) {
                let form = document.createElement("form");
                form.setAttribute("method", "POST");
                form.setAttribute("action", url);
                for (let key in obj) {
                    if (obj[key] != null) {
                        let hiddenField = document.createElement("input");
                        hiddenField.setAttribute("type", "hidden");
                        hiddenField.setAttribute("name", key);
                        hiddenField.setAttribute("value", obj[key]);
                        form.appendChild(hiddenField);
                    }
                }
                document.body.appendChild(form);
                form.submit();
            },
            mapCheckOutData(rate) {
                let routeRates = [];
                let rateId = rate.fares[0] ? rate.fares[0].fareKey : rate.id;
                let flights = rate.isSelected ? rate.flights : this.getFlightUpsell.flights;
                routeRates[0] = {
                    departureFullName: rate.isSelected ?
                        this.pt.data.startingAirportPlace.cityFullName : this.getFlightUpsell.routeRates[0].departureFullName,
                    arrivalFullName: rate.isSelected ?
                        this.pt.data.returningAirportPlace.cityFullName : this.getFlightUpsell.routeRates[0].arrivalFullName
                };
                if (this.getFlightUpsell.engine) {
                    flights[1].engine = this.getFlightUpsell.engine[rate.group ? rate.group : rate.fares[0].fareGroup];
                }
                else{
                    flights[1].engine = rate.fares[0].engine;
                }
                if(!flights?.[1]?.engine && rate?.fares?.[0]?.engine){
                    flights[1].engine = rate.fares[0].engine;
                }
                if(rate?.isRoundTrip && this.getIsStepTwo){
                    routeRates[1] = {
                        departureFullName: rate.isSelected ?
                            this.pt.data.returningAirportPlace.cityFullName : this.getFlightUpsell.routeRates[1].departureFullName,
                        arrivalFullName: rate.isSelected ?
                            this.pt.data.startingAirportPlace.cityFullName : this.getFlightUpsell.routeRates[1].arrivalFullName
                    };
                    if (this.getFlightUpsell.engine) {
                        flights[2].engine = this.getFlightUpsell.engine[rate.group ? rate.group : rate.fares[0].fareGroup];
                    }
                    else {
                        flights[2].engine = rate.fares[1].engine;
                    }
                    if (!flights?.[2]?.engine && rate?.fares?.[1]?.engine) {
                        flights[2].engine = rate.fares[1].engine;
                    }
                    rateId = rate.fares[1].fareKey ? rateId + "|" + rate.fares[1].fareKey : rate.id;
                }
                let objDepartureFlight = { ...this.flightSelected.departureFlight };
                let objReturningFlight = rate?.isRoundtrip && this.getIsStepTwo ? { ...this.flightSelected.departureFlight } : {};
                objDepartureFlight = { ...this.flightSelected.departureFlight };
                objDepartureFlight.fares[0].fareGroup = rate.isSelected ? rate.fares[0].fareGroup : rate.group;
                objDepartureFlight.fares[0].fareKey = rate.isSelected ?  rate.fares[0].fareKey : rate.id.split("|")[0];

                if (rate?.isRoundTrip && this.getIsStepTwo ) {
                    objReturningFlight = { ...this.flightSelected.departureFlight };
                    objReturningFlight.fares[0].fareGroup = rate.isSelected ? rate.fares[1].fareGroup : rate.group;
                    objReturningFlight.fares[0].fareKey = rate.isSelected ?  rate.fares[1].fareKey : rate.id.split("|")[1];
                }

                const data = {
                    flights: flights,
                    fares: rate.isSelected ? rate.faresLegs : rate.fares,
                    summary: {
                        origin: { ...objDepartureFlight, ...routeRates[0] },
                        destination: rate?.isRoundtrip && this.getIsStepTwo ? { ...objReturningFlight, ...routeRates[1] } : {},
                        fareKey: rateId == rate.id ? rate.id : rateId,
                        token: rate.isSelected ? rate.quoteTaskID : this.getFlightUpsell.quoteToken,
                        departureName: this.pt.data.startingAirportPlace.cityName,
                        arrivalName: this.pt.data.returningAirportPlace.cityName,
                        quoteTaskID: rate.isSelected ? rate.taskID : this.getFlightUpsell.taskID,
                        isSelected: rate.isSelected,
                        quoteToken: this.getAllQuoteTokens.join(','),
                    }
                }
                if (data.summary) {
                    if (data.summary.origin.fares) {
                        data.summary.origin.fares[0].negotiatedFareId = rate.isSelected ? rate.fares[0].negotiatedFareId : rate.negotiatedFareId
                    }
                    if (data.summary.destination && data.summary.destination.fares) {
                        data.summary.destination.fares[0].negotiatedFareId = rate.isSelected ? rate.fares[1].negotiatedFareId : rate.negotiatedFareId
                    }
                }
                return data;
            },
            mapPaxes() {
                let paxes = [
                    {
                        adults: this.pt.data.paxes[0]['adults'],
                        children: []
                    }
                ];
                for (let i = 0; i < this.pt.data.paxes[0]['children'].length; i++) {
                    paxes[0]['children'][i] = {
                        year: this.pt.data.paxes[0]['children'][i]
                    };
                }
                return paxes;
            },
            handleScroll() {
                const scrollContainer = this.$refs.scrollContainer;
                this.anchoScroll = scrollContainer.scrollWidth - scrollContainer.clientWidth;
                this.scrollPosition = scrollContainer.scrollLeft;
                this.isLeftHidden = Math.ceil(this.scrollPosition) <= 0;
                this.isRightHidden = this.anchoScroll > 0 && Math.ceil(this.scrollPosition) >= this.anchoScroll;
            },
            getFirstLowerCase(str) {
                if (!str) return str;
                return str.charAt(0).toLowerCase() + str.slice(1);
            },
            getTextQuantity() {
                let text = "";
                const numberRates = this.numberOfRates();
                if (!this.pt.settings.site.isMobile()) {
                    text = numberRates > 3 ? " <span class='hide-xs'>(" + numberRates + " " + this.__('messages.fares') + " " + this.__('messages.available') +")</span>" : "";
                } else {
                    text = " <span>(" + numberRates + " " + this.__('messages.options')+")</span>";
                }
                return text;
            },getPriceSelected() {
                 return this.getTotalAmount > 0 ?
                     this.getTotalAmount / (this.pt.data.adults + this.pt.data.kids) :
                     this.flightSelected.totalAmount;
            },
            setImgAirline(airlineCode){
                return `https://img.cdnpth.com/media/images/airlines-logos/${airlineCode}.svg`
            },
            numberOfRates() {
                const gruposVistos = new Set();
                const filtrados = [];
                for (const item of this.packageRates) {
                    if (!gruposVistos.has(item.group.toUpperCase())) {
                        gruposVistos.add(item.group.toUpperCase());
                        filtrados.push(item);
                    }
                }
                return filtrados.length + 1;
            },
        },
        components: {
            CurrencyDisplay
        }
    }
</script>

<style lang="scss" scoped>
.skeleton {
    animation: skeleton-loading 1s linear infinite alternate;
}

@media (max-width: 767px) {
    .btn-select {
        font-size:14px !important;
    }
}

@keyframes skeleton-loading {
    0% {
        background-color: hsl(200, 20%, 80%);
    }

    100% {
        background-color: hsl(200, 20%, 95%);
    }
}
.no-hover {
    text-decoration: none !important; /* Eliminar cualquier decoraci�n de texto, como subrayado */
    outline: none; /* Eliminar cualquier contorno */
}

    .cg-body .btnAddRate .icon {
        top: 3px !important;
    }

    .select-upsell {
        font-size: 17px !important;
    }
    

    .icon {
        top:2px !important;
        margin-right: 5px;
        position: relative;
    }
    .font-selected-fare {
        font-family: 'Roboto-Regular', sans-serif;
        white-space: nowrap !important;
    }
    .skeleton-title {
        width: 100%;
        height: 1.75rem;
        margin-bottom: 0.5rem;
        border-radius: 0.25rem;
    }

.skeleton-text {
    width: 100%;
    height: 1.25rem;
    margin-bottom: 0.5rem;
    border-radius: 0.25rem;
}
.c-recapcha .icon {
    color: #2196f3;
    font-size: 20px;
    font-weight: 900;
    position: relative;
    top: 1px
}

.c-recapcha .border-bottom {
    border-bottom-width: 3px!important
}
.row {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    /*margin-right: -8px;*/
    // margin-left: -15px
}

.col-5 {
    -ms-flex: 0 0 41.666667%;
    flex: 0 0 41.666667%;
    max-width: 41.666667%
}

.col-7 {
    -ms-flex: 0 0 58.333333%;
    flex: 0 0 58.333333%;
    max-width: 58.333333%
}
.btn {
    display: inline-block;
    font-weight: 400;
    color: #212529;
    text-align: center;
    vertical-align: middle;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-color: transparent;
    border: 1px solid transparent;
    padding: .375rem .75rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: .25rem;
    transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out
}

@media (prefers-reduced-motion:reduce) {
    .btn {
        transition: none
    }
}

.btn-group-vertical>.btn,.btn-group>.btn {
    position: relative;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto
}

.btn-group-vertical>.btn.active,.btn-group-vertical>.btn:active,.btn-group-vertical>.btn:focus,.btn-group-vertical>.btn:hover,.btn-group>.btn.active,.btn-group>.btn:active,.btn-group>.btn:focus,.btn-group>.btn:hover {
    z-index: 1
}

.btn-group>.btn-group:not(:first-child),.btn-group>.btn:not(:first-child) {
    margin-left: -1px
}

.btn-group>.btn-group:not(:last-child)>.btn,.btn-group>.btn:not(:last-child):not(.dropdown-toggle) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}

.btn-group>.btn-group:not(:first-child)>.btn,.btn-group>.btn:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}
.btn-group-sm>.btn+.dropdown-toggle-split,.btn-sm+.dropdown-toggle-split {
    padding-right: .375rem;
    padding-left: .375rem
}

.btn-group-lg>.btn+.dropdown-toggle-split,.btn-lg+.dropdown-toggle-split {
    padding-right: .75rem;
    padding-left: .75rem
}
.btn-group-vertical>.btn-group:not(:first-child),.btn-group-vertical>.btn:not(:first-child) {
    margin-top: -1px
}

.btn-group-vertical>.btn-group:not(:last-child)>.btn,.btn-group-vertical>.btn:not(:last-child):not(.dropdown-toggle) {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0
}

.btn-group-vertical>.btn-group:not(:first-child)>.btn,.btn-group-vertical>.btn:not(:first-child) {
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

.btn-group-toggle>.btn,.btn-group-toggle>.btn-group>.btn {
    margin-bottom: 0
}

.btn-group-toggle>.btn-group>.btn input[type=checkbox],.btn-group-toggle>.btn-group>.btn input[type=radio],.btn-group-toggle>.btn input[type=checkbox],.btn-group-toggle>.btn input[type=radio] {
    position: absolute;
    clip: rect(0,0,0,0);
    pointer-events: none
}

.input-group-append .btn,.input-group-prepend .btn {
    position: relative;
    z-index: 2
}

.input-group-append .btn:focus,.input-group-prepend .btn:focus {
    z-index: 3
}

.input-group-append .btn+.btn,.input-group-append .btn+.input-group-text,.input-group-append .input-group-text+.btn,.input-group-append .input-group-text+.input-group-text,.input-group-prepend .btn+.btn,.input-group-prepend .btn+.input-group-text,.input-group-prepend .input-group-text+.btn,.input-group-prepend .input-group-text+.input-group-text {
    margin-left: -1px
}

.input-group>.input-group-append:last-child>.btn:not(:last-child):not(.dropdown-toggle),.input-group>.input-group-append:last-child>.input-group-text:not(:last-child),.input-group>.input-group-append:not(:last-child)>.btn,.input-group>.input-group-append:not(:last-child)>.input-group-text,.input-group>.input-group-prepend>.btn,.input-group>.input-group-prepend>.input-group-text {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}

.input-group>.input-group-append>.btn,.input-group>.input-group-append>.input-group-text,.input-group>.input-group-prepend:first-child>.btn:not(:first-child),.input-group>.input-group-prepend:first-child>.input-group-text:not(:first-child),.input-group>.input-group-prepend:not(:first-child)>.btn,.input-group>.input-group-prepend:not(:first-child)>.input-group-text {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}

.btn .badge {
    position: relative;
    top: -1px
}

@keyframes a {
    0% {
        background-position: 1rem 0
    }

    to {
        background-position: 0 0
    }
}

@keyframes c {
    0% {
        transform: scale(0)
    }

    50% {
        opacity: 1
    }
}

.border {
    border: 1px solid #dee2e6!important
}

.border-bottom {
    border-bottom: 1px solid #dee2e6!important
}

.position-relative {
    position: relative!important
}
.m-0 {
    margin: 0!important
}

.mt-2,.my-2 {
    margin-top: .5rem!important
}

.mb-2,.my-2 {
    margin-bottom: .5rem!important
}
.p-3 {
    padding: 1rem!important
}
.pr-3,.px-3 {
    padding-right: 1rem!important
}
.pl-3,.px-3 {
    padding-left: 1rem!important
}

@media (min-width: 768px) {

    .m-md-3 {
        margin: 1rem!important
    }

    .pr-md-0,.px-md-0 {
        padding-right: 0!important
    }
    .pl-md-0,.px-md-0 {
        padding-left: 0!important
    }
}
@media print {
    *,:after,:before {
        text-shadow: none!important;
        box-shadow: none!important
    }

    a:not(.btn) {
        text-decoration: underline
    }

    abbr[title]:after {
        content: " (" attr(title) ")"
    }

    pre {
        white-space: pre-wrap!important
    }

    blockquote,pre {
        border: 1px solid #adb5bd;
        page-break-inside: avoid
    }

    thead {
        display: table-header-group
    }

    img,tr {
        page-break-inside: avoid
    }

    h2,h3,p {
        orphans: 3;
        widows: 3
    }

    h2,h3 {
        page-break-after: avoid
    }

    @page {
        size: a3
    }

    .container,body {
        min-width: 992px!important
    }
}

h1 {
    font-size: 25px;
    text-shadow: 2px 2px 6px rgba(0,0,0,.4)
}

@media (min-width: 1025px) {
    h1 {
        font-size:40px
    }
}

h2 {
    font-size: 18px;
    text-shadow: 2px 2px 6px rgba(0,0,0,.4)
}

@media (min-width: 1025px) {
    h2 {
        font-size:20px
    }
}

h3 {
    font-size: 28px;
    margin-bottom: 20px
}

@media (min-width: 1025px) {
    h3 {
        font-size:24px
    }
}

a {
    color: #303c42
}

a:hover {
    color: #0d98dc
}

.pointer {
    cursor: pointer
}

.c-menu .ni-button .nav-link .icon {
    color: #003b98;
    left: 0;
    position: absolute
}

@media (max-width: 767px) {
    .c-menu .ni-button .nav-link .icon {
        margin-right:5px;
        position: relative;
        top: 6px
    }
}

.c-header .c-writing .icon {
    position: relative;
    top: 3px
}

.c-header .c-help-call .icon {
    position: relative;
    top: 3px
}

.c-header .ch-dropdown .icon {
    position: relative;
    top: 3px
}
.c-passengers .c-icons .icon {
    background-color: #fff;
    border-radius: 50px;
    box-shadow: 0 3px 1px -2px rgb(0 0 0/2%),0 2px 2px 0 rgb(0 0 0/14%),0 1px 5px 0 rgb(0 0 0/12%);
    transition: .2s ease-in-out;
    position: relative!important;
    padding: 5px
}

.c-passengers .c-icons .icon:hover {
    background: rgba(92,70,156,.08);
    cursor: pointer
}

.c-minors .icon {
    right: 0
}

@media (min-width: 768px) and (max-width:1024px) {
    .c-box .tab-content .c-input-box .c-button .btn {
        font-size:12px
    }
}

@media (min-width: 1025px) {
    .c-box .tab-content .c-input-box .c-button .btn {
        position:relative;
        top: -17px
    }
}

.btn-ok {
    background: #428e1e;
    border: 1px solid #428e1e;
    border-radius: 4px;
    color: #fff!important;
    transition: all .2s linear
}

.btn-ok:hover {
    background: #3b7f1b
}

.btn-ok * {
    color: #fff
}

.c-banner-more .icon {
    color: #003b98
}

.c-line-schedule .icon {
    position: absolute;
    right: -5px;
    top: -8px
}

@media (max-width: 767px),(min-width:768px) and (max-width:1024px) {
    .c-line-schedule .icon {
        height:20px;
        margin: auto;
        left: 0;
        right: 0;
        top: 12px;
        width: 20px
    }
}

@media (max-width: 767px) {
    .c-line-schedule .icon {
        font-size:12px!important;
        left: 12px;
        top: 15px
    }
}
.title-list .icon {
    position: relative;
    top: 4px
}
.line-flights .bb-white .icon {
    color: #94a3b8
}


@media (min-width: 768px) and (max-width:1024px) {
    .cf-layer .title-list .icon {
        top:2px
    }
}

@media (max-width: 767px) {
    .view-detail .color-blue:before,.view-detail .icons-keyboard-right:before {
        position: relative;
        top: 2px
    }
}

@media (max-width: 767px) {
    .view-detail .icons-expand {
        display:none
    }
}
.c-info-flight .icon {
    color: #2196f3;
    position: relative;
    top: -2px
}

@media (max-width: 767px) {
    .c-box-flights .title-list .font-20 {
        font-size: 18px!important
    }
}
.c-click-detail .icon {
    position: absolute;
    right: -6px;
    top: 4px;
    z-index: 1
}

@media (max-width: 767px) {
    .c-click-detail .icon {
        display:none
    }
}

@media (min-width: 768px) and (max-width:1024px) {
    .c-click-detail .icon {
        right:18px;
        top: 8px
    }
    .cn-sidebar {
      max-width: 431px !important;
    }
}

@media (min-width: 1025px) {
    .c-click-detail .icon {
        right:24px;
        top: 8px
    }
}

.c-early-dates .border {
    border-color: #0e213a!important;
    border-radius: 4px
}
.c-scroll .icon {
    cursor: pointer;
    position: absolute;
    top: -8px
}
.c-scroll .icons-chevron-right {
    right: -10px
}
@media (max-width: 767px) {
    .c-mobile-filters .icon {
        color: #003b98;
        position: relative;
        top: 5px
    }
}

body {
    background-color: hsla(0,0%,98%,.5)
}

.c-scroll-steps {
    -webkit-overflow-scrolling: touch;
    -ms-overflow-style: -ms-autohiding-scrollbar;
    overflow: auto
}

.c-scroll-steps .row {
    display: inline-block;
    white-space: nowrap
}

@media (min-width: 768px) and (max-width:1024px),(min-width:1025px) {
    .c-scroll-steps .chc-rates {
        height:600px
    }
}

.ch-container {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: initial;
    flex-wrap: nowrap
}


.ch-column {
    -ms-flex: initial;
    flex: initial
}

.ch-column div {
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
}

.ch-column .c-pass-flight {
    font-size: 14px;
    position: absolute;
    left: 10px;
    top: -25px
}

.chc-02 .icon {
    position: relative;
    top: 1px
}

@media (max-width: 767px) {
    .chc-02 .icon {
        top:-1px
    }
}

.cn-footer .icon {
    top: 4px
}

.cn-sidebar {
    box-shadow: 0 0 15px #999;
    width: 100%;
    height: 100%;
    position: fixed;
    right: -50%;
    top: 0;
    background: #fff;
    z-index: 999;
    font-size: 14px;
    transition: all .3s ease;
}

@media (max-width: 767px) {
    .cn-sidebar {
        width:100%;
        right: -100%;
    }
}

@media (max-width: 767px) {
    .close-mobile {
        right:-450px!important
    }
}

.cns-header .icon {
  bottom: 0;
  font-size: 40px;
  margin: auto;
  height: 40px;
  position: absolute;
  top: 0;
}

.cns-header .txt-title {
    font-size: 20px
}

.cns-body {
    overflow-x: hidden;
    position: absolute;
    top: 63px;
    bottom: 0;
    left: 30px;
    right: 15px
}

.cns-body .cnsb-hour {
    font-size: 22px;
}

.cns-body .cnsb-date {
    font-size: 14px
}

.cns-body .c-scroll-steps {
    overflow-y: hidden;
    @media (max-width: 767px) {
        overflow-y: auto;
    }
}

.col-rate {
    border-radius: 8px;
    width: 400px;
}

@media (max-width: 767px) {
    .col-rate {
        width: 325px;
    }
}

.col-rate * {
    line-height: 1.2;
    white-space: normal
}

/*.chc-header {*/
    /*left: 15px;*/
    /*height: 120px;*/
    /*position: absolute;*/
    /*right: 15px;*/
    /*top: 5px*/
/*}*/

/*.chc-body,.chc-header {
    left: 12px;
    position: absolute
}

.chc-body {
    bottom: 0;
    left: 15px;
    overflow: auto!important;
    position: absolute;
    right: 0;
    top: 160px
}
*/
.chc-footer {
    background-color: #f3f3f3;
    margin-left: -16px;
    margin-right: -16px;
    margin-top: 15px;
    bottom: 0;
    left: 0;
    height: 86px;
    margin-left: 0;
    margin-right: 0;
    position: absolute;
    right: 0;
    border-radius: 10px;
}

.chc-footer .row {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: initial;
    flex-wrap: nowrap
}

.chc-footer .chcf-01 p {
    font-size: 12px
}

.chc-footer .chcf-01 p+p {
    font-size: 24px
}

.chc-footer .chcf-01 p+p+p {
    font-size: 10px
}

.chc-footer .chcf-02 .btn {
    float: right;
    padding: 10px 25px 10px 15px;
    top: 8px
}
.chcf-01 {
    @media (max-width: 767px) {
        .fs-10-xs {
            font-size: 11px !important;
        }
    }
}


@media (max-width: 767px) {
    .chc-footer .chcf-02 .btn {
        width:100%
    }
}

.chc-footer .chcf-02 .icon {
    font-size: 18px;
    position: absolute;
    right: 5px;
    top: 10px
}

.chc-f-active {
    background-color: #e4f3d1
}
.cnsb-head {
    height: 120px;
    top: 0
}

.cnsb-cont,.cnsb-head {
    left: 0;
    position: absolute;
    right: 0
}

.cnsb-cont {
    bottom: 20px;
    top: 25px
}

.c-rate-style {
    background-color: #ccc;
    border-radius: 4px 0 10px 0;
    left: 0;
    height: 55px;
    position: absolute;
    top: 0;
    width: 10px
}

.col-rate .btn * {
    color: #fff!important;
    transition: all .2s linear
}

.c-rate-continue {
    border-color: #eee!important
}

.c-rate-continue .chc-footer {
    background-color: #f3f3f3
}

.c-rate-continue .btn {
    background: #00a0e4;
    border: 1px solid #00a0e4
}

.c-rate-continue .btn:hover {
    background: #0090cd
}

.c-rate-active {
    border-color: #428e1e!important
}

.c-rate-active .chc-footer {
    background-color: #e4f3d1
}

.c-rate-active .btn {
    background: #428e1e;
    border: 1px solid #428e1e
}

.c-rate-active .btn:hover {
    background: #3b7f1b
}

.c-rate-active .c-rate-style {
    background-color: #428e1e
}

.c-rate-active .chc-header h3 {
    color: #428e1e
}

.c-rate-active .rate-selected * {
    color: #428e1e;
    font-size: 14px
}

.rate-economic .c-rate-style {
    background-color: #5c469c
}

.rate-economic .chc-header h3 {
    color: #5c469c
}

.rate-clasic .c-rate-style {
    background-color: #dc1f2f
}

.rate-clasic .chc-header h3 {
    color: #dc1f2f
}

.cnr-container .circle-top1 {
    left: 2px
}

.c-detail-conexion p {
    background-color: #f0f9ff;
    border-radius: 50px;
    color: #003b98;
    line-height: 1.2
}
.rounded-pill {
    border-radius: 50rem!important
}
.c-rate-open {
    height: 100%;
    position: relative!important
}

.head-rate {
    position: relative
}

.h-open {
    top: 40px;
}

.cnr-container {
    overflow-y: auto;
    right: 0
}

.cnr-container .circle-top1 {
    left: 2px
}

.c-detail-conexion p {
    background-color: #f0f9ff;
    border-radius: 50px;
    color: #003b98;
    line-height: 1.2
}

.rounded-top {
    border-top-left-radius: .25rem!important;
    border-top-right-radius: .25rem!important
}

.rounded-pill {
    border-radius: 50rem!important
}

.d-block {
    display: block!important
}

@media (min-width: 768px) {
    .d-md-none {
        display:none!important
    }
}

.text-left {
    text-align: left!important
}

.text-center {
    text-align: center!important
}

@media print {
    *,:after,:before {
        text-shadow: none!important;
        box-shadow: none!important
    }

    p {
        orphans: 3;
        widows: 3
    }
}

.font-12 {
    font-size: 12px!important
}

.font-20 {
    font-size: 20px!important
}
.color-blue {
    color: #003b98!important
}

.c-view-detail .gray {
    color: #64748b
}

.line-steps {
    position: relative;
    width: 5%;
    display: table-cell;
    height: 100%
}

.info-travel {
    position: relative;
    width: 90%;
    display: inline-block
}

.content-info {
    position: relative;
    display: inline-table;
    width: 100%;
    height: auto
}

.content-info p {
    width: inherit
}

.circle-top1 {
    border: 2px solid #c4c4c4;
    border-radius: 50px;
    height: 10px;
    margin: auto;
    width: 10px;
    z-index: 1;
    position: absolute;
    top: 5px;
    left: 6px;
    display: block;
    background-color: #fff
}

.line1 {
    bottom: -9px;
    left: 0;
    top: 20px;
    border: 1px dashed #c4c4c4;
    border-radius: 50px;
    margin: auto;
    position: absolute;
    right: 0;
    width: 1px;
    z-index: 0
}

.info-travel p {
    display: inline-block
}

.c-capsule-light {
    background-color: #f0f9ff;
    max-width: 300px
}

@media (max-width: 767px) {
    .content-info {
        height: auto
    }

    .line1 {
        left: 6px
    }
}

.line1 {
    top: 14px
}

@keyframes d {
    to {
        background-position-x: -200%
    }
}

.all-link *,.all-link-hover *,.i-link {
    color: #186cdf
}

.all-link-hover :hover,.underline-hover:hover {
    text-decoration: underline;
    -webkit-text-decoration-color: #186cdf;
    text-decoration-color: #186cdf
}

.no-hover:hover {
    text-decoration: none
}
.mr-3,.mx-3 {
    margin-right: 1rem!important;
}
.f-bold {
    font-weight: 600;
}
.pr-0,.px-0 {
    padding-right: 0!important;
}
.pl-5,.px-5 {
    padding-left: 3rem!important;
}
.package-item-flight-title {
    color: #454344;
    padding: 10px !important;
    display: flex;
    /*justify-content: space-between;*/
    align-items: center
}

.package-item-flight-title span:first-child {
    font-weight: bold;
    cursor: pointer;
}

.package-item-flight-content {
    padding-top: 0;
    font-size: 12px;
}

.btn-link:focus {
    box-shadow: none
}
.btn-link {
    font-weight: 400;
    text-decoration: none;
    border: none;
    background: transparent;
}


.btn-link.focus,.btn-link:focus,.btn-link:hover {
    text-decoration: underline
}

.btn-link.disabled,.btn-link:disabled {
    color: #6c757d;
    pointer-events: none
}
.modal-lg {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 7;
    outline: 0;
    background: transparent;
    width: 100%;
    height: 100%;
    padding: calc(0.8875rem + 0.15vw);
}

.ml-header {
    border-bottom: 1px solid #ddd;
    background: #f9f9f9
}

.ml-body {
    background-color: #fff
}

.sticky-f-mobile {
    position: fixed;
    bottom: 30px;
    right: 0;
    left: 0;
    z-index: 99
}

@media (min-width: 768px) and (max-width:1024px),(min-width:1025px) {
    .sticky-f-mobile {
        display:none!important
    }
}

.cvi-steps .fc-open {
    position: relative;
    padding-top: 15px
}

.cvi-steps .fc-open:before {
    background-color: #fff;
    bottom: -15px;
    content: "";
    height: 25px;
    left: 0;
    position: absolute;
    right: 0
}

.cvi-steps .c-tab-btn {
    border: none!important
}

.cmf-int {
    background-color: #fff;
    border: 1px solid #186cdf;
    width: 50%
}

.cmf-int * {
    color: #186cdf!important
}

.d-none-hard {
    display: none!important
}

.overflow-hidden-y {
    overflow: hidden
}

.all-link *,.all-link-hover *,.i-link {
    color: #186cdf
}

.all-link-hover :hover,.underline-hover:hover {
    text-decoration: underline;
    -webkit-text-decoration-color: #186cdf;
    text-decoration-color: #186cdf
}

.shadow-md {
    box-shadow: 0 .2rem 1rem rgba(0,0,0,.15)!important
}

.no-hover:hover {
    text-decoration: none
}

.c-f-paginator {
    bottom: 0;
    height: 30px;
    left: 0;
    margin: auto;
    position: absolute;
    right: 0;
    top: 0;
    z-index: 5;
}

    .c-f-paginator .cfp-ctrl {
        background-color: rgba(255, 255, 255, 0.5);
        border-radius: 50px;
        cursor: pointer;
        height: 50px;
        position: absolute;
        transition: all .2s linear;
        width: 50px
    }
    .c-f-paginator .cfp-ctrl:hover {
        background: rgba(0, 59, 152, .95) !important;
    }


@media (min-width: 768px) and (max-width:1024px),(min-width:1025px) {
    .c-f-paginator .cfp-ctrl:hover {
        background-color: #f9f9f9
    }
}

    .c-f-paginator .cfp-ctrl .icon {
        color: #fff;
        position: relative;
        top: 6px !important;
    }

.c-f-paginator .cfp-left {
    left: -3px
}

.c-f-paginator .cfp-left .icon {
    left: 6px
}

.c-f-paginator .cfp-right {
    right: 9px
}

.c-f-paginator .cfp-right .icon {
    right: -6px
}
.d-flex {
    display: -ms-flexbox!important;
    display: flex!important
}
.mt-1,.my-1 {
    margin-top: .25rem!important
}
.ml-2,.mx-2 {
    margin-left: .5rem!important
}
.pr-3,.px-3 {
    padding-right: 1rem!important
}
.pb-5,.py-5 {
    padding-bottom: 3rem!important
}

.m-auto {
    margin: auto!important
}

.mt-auto,.my-auto {
    margin-top: auto!important
}

.mr-auto,.mx-auto {
    margin-right: auto!important
}

.mb-auto,.my-auto {
    margin-bottom: auto!important
}

.ml-auto,.mx-auto {
    margin-left: auto!important
}
.c-rate-active .c-btn-rate {
    background-color: #e4f3d1
}

// .c-rate-active .c-btn-rate p {
//     line-height: 1
// }

.chc-f-active {
    background-color: #e4f3d1
}

.ch-column {
    -ms-flex: initial;
    flex: initial
}
.c-scroll-steps .row {
    display: inline-block;
    white-space: nowrap
}
.font-30 {
    font-size: 30px!important
}

.font-50 {
    font-size: 39px!important
}
.c-scroll-steps {
    -webkit-overflow-scrolling: touch;
    -ms-overflow-style: -ms-autohiding-scrollbar;
    overflow: auto
}




.c-scroll-steps {
    -webkit-overflow-scrolling: touch;
    -ms-overflow-style: -ms-autohiding-scrollbar;
    overflow: auto
}

.c-scroll-steps .row {
    display: inline-block;
    white-space: nowrap
}

@media (min-width: 768px) and (max-width:1024px),(min-width:1025px) {
    .c-scroll-steps .chc-rates {
        height:600px
    }
}

.ch-container {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: initial;
    flex-wrap: nowrap
}

.ch-column {
    -ms-flex: initial;
    flex: initial
}

.ch-column .c-pass-flight {
    font-size: 14px;
    position: absolute;
    left: 10px;
    top: -25px
}

.chc-01 * {
    font-size: 18px
}

@media (max-width: 767px) {
    .chc-01 * {
        font-size:16px
    }
}

.chc-02 * {
    font-size: 16px
}

@media (max-width: 767px) {
    .chc-02 * {
        font-size:14px
    }
}

.chc-02 .icon {
    position: relative;
    top: 1px
}

@media (max-width: 767px) {
    .chc-02 .icon {
        top:-1px
    }
}

.chc-03 {
    font-size: 14px
}

@media (max-width: 767px) {
    .chc-03 {
        font-size:12px
    }
}

.chc-in .i-link {
    font-size: 21px;
    top: 3px
}

@media (max-width: 767px) {
    .chc-in .i-link {
        font-size:18px
    }
}

.chc-in .icon-keyboard-right {
    font-size: 26px;
    top: 2px
}

.c-capsule {
    padding: 6px 15px
}

.c-capsule .f-title {
    left: 16px;
    top: -25px
}

@media (max-width: 767px) {
    .c-capsule .f-title {
        left:25px
    }
}

.c-capsule .c-change-title {
    top: -5px
}

.c-capsule * {
    color: #a6acb4
}

.c-capsule-active {
    background-color: #f6fafa;
    border: 1px solid #d5e8e8;
    border-radius: 50px
}

.c-capsule-active *,.cn-flights-list * {
    color: #333
}

.cn-flights-list hr {
    position: relative;
    z-index: 0
}

.cn-flights-list .cn-rate span {
    line-height: 1.2
}

.cn-flights-list .cn-rate .txt-ida {
    font-size: 18px
}

@media (max-width: 767px) {
    .cn-flights-list .cn-rate .txt-ida {
        font-size:16px
    }
}

@media (min-width: 0) and (max-width:380px) {
    .cn-flights-list .cn-rate .txt-ida {
        font-size:14px
    }
}

.cn-flights-list .cn-rate .txt-ida .c-desde {
    position: relative
}

.cn-flights-list .cn-rate .txt-ida strong {
    color: #e50000
}

.cn-flights-list .cn-rate .txt-rate {
    font-size: 28px;
    line-height: 1
}

.cn-flights-list .cn-rate .txt-info {
    font-size: 12px
}

.c-iata-right {
    right: -10px
}

.c-iata-left,.c-iata-right {
    bottom: -9px;
    font-size: 10px;
    margin: auto;
    position: absolute;
    top: 1px;
    width: 20px
}

.c-iata-left {
    left: -5px
}

.cn-figure {
    line-height: 1
}

.cn-figure img {
    width: 40px
}

@media (max-width: 767px) {
    .cn-figure span {
        display:block;
        text-align: center
    }
}

.c-rate-style {
    background-color: #ccc;
    border-radius: 4px 0 10px 0;
    left: 0;
    height: 30px;
    position: absolute;
    top: 0;
    width: 10px
}

.col-rate .btn * {
    color: #fff!important;
    transition: all .2s linear
}

.c-rate-continue {
    border-color: #eee!important
}

.c-rate-continue .chc-footer {
    background-color: #f3f3f3
}

.c-rate-continue .btn {
    background: #00a0e4;
    border: 1px solid #00a0e4
}

.c-rate-continue .btn:hover {
    background: #0090cd
}

.c-rate-active {
    border-color: #428e1e!important
}

.c-rate-active .chc-footer {
    background-color: #e4f3d1
}

.c-rate-active .btn {
    background: #428e1e;
    border: 1px solid #428e1e
}

.c-rate-active .btn:hover {
    background: #3b7f1b
}

.c-rate-active .c-rate-style {
    background-color: #428e1e
}

.c-rate-active .chc-header h3 {
    color: #428e1e
}

.c-rate-active .rate-selected * {
    color: #428e1e;
    font-size: 14px
}

.family-rate-1 .c-rate-style {
    background-color: #5c469c
}

.family-rate-1 .chc-header h3 {
    color: #5c469c
}

.family-rate-2 .c-rate-style {
    background-color: #dc1f2f
}

.family-rate-2 .chc-header h3 {
    color: #dc1f2f
}

.family-rate-3 .c-rate-style {
    background-color: #dc1f2f
}

.family-rate-3 .chc-header h3 {
    color: #dc1f2f
}

.c-one-family {
    max-width: 433px
}

.c-one-family .c-f-paginator {
    display: none
}

@media (max-width: 767px) {
    .c-one-family .c-scroll-steps {
        width:100%
    }
}

.c-one-family .c-row-steps,.c-one-family .ch-column,.c-one-family .cns-body {
    width: 100%
}

.cns-header .icon {
    bottom: 0;
    margin: auto;
    height: 36px;
    position: absolute;
    top: 0
}

.cns-header .txt-title {
    font-size: 20px
}

.c-scroll .icons-chevron-left {
    left: -10px
}

.c-scroll .icons-chevron-right {
    right: -10px
}

.c-ctrls .icon {
    background-color: #fff;
    border: 1px solid #c4c4c4;
    border-radius: 50px;
    height: 30px;
    position: absolute;
    top: 6px;
    width: 30px;
    z-index: 10
}

.c-ctrls .icon:hover {
    cursor: pointer;
    box-shadow: 0 10px 18px rgb(68 68 68/10%)
}

.c-ctrls .icons-chevron-left {
    left: -15px
}

.c-ctrls .icons-chevron-left:before {
    position: relative;
    left: 2px;
    top: 2px
}

.c-ctrls .icons-chevron-right {
    right: -15px
}

.c-ctrls .icons-chevron-right:before {
    position: relative;
    right: -2px;
    top: 2px
}

.p-2 {
    padding: .5rem!important
}

.pt-2,.py-2 {
    padding-top: .5rem!important;
}

.pr-2,.px-2 {
    padding-right: .5rem!important
}

.pb-2,.py-2 {
    padding-bottom: .5rem!important
}

.pl-2,.px-2 {
    padding-left: .5rem!important
}
.p-0 {
    padding: 0!important
}

.pt-0,.py-0 {
    padding-top: 0!important
}

.pr-0,.px-0 {
    padding-right: 0!important
}

.pb-0,.py-0 {
    padding-bottom: 0!important
}

.pl-0,.px-0 {
    padding-left: 0!important
}
.p-md-3 {
        padding: 1rem!important
    }

    .pt-md-3,.py-md-3 {
        padding-top: 1rem!important
    }

    .pr-md-3,.px-md-3 {
        padding-right: 1rem!important
    }

    .pb-md-3,.py-md-3 {
        padding-bottom: 1rem!important
    }

    .pl-md-3,.px-md-3 {
        padding-left: 1rem!important
    }
    .btn {
    display: inline-block;
    font-weight: 400;
    color: #212529;
    text-align: center;
    vertical-align: middle;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-color: transparent;
    border: 1px solid transparent;
    padding: .375rem .75rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: .25rem;
    transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out
}
.c-btn-rate {
    background-color:#eee; 
}

.c-btn-rate p {
    line-height: 1
}

/*.c-btn-rate .chcf-01 p {
    font-size: 14px
}

@media (max-width: 767px) {
    .c-btn-rate .chcf-01 p {
        font-size:12px
    }
}

.c-btn-rate .chcf-01 p+p {
    font-size: 24px
}

.c-btn-rate .chcf-01 p+p+p {
    font-size: 12px
}

@media (max-width: 767px) {
    .c-btn-rate .chcf-01 p+p+p {
        font-size:10px
    }
}*/

@media (max-width: 767px) {
    .c-btn-rate .chcf-02 .btn {
        font-size:14px;
        padding-bottom: 10px;
        padding-top: 10px;
        width: 100%
    }
}

button.btnAddRate {
    top: 10px;
    font-size: 17px !important;
}
.c-m-family {
    background-color: #f1f5f9;
    border: 1px solid #cdd9ea;
    border-radius: 5px;
    width: 100%
}
@media (min-width: 1025px) {
.one {
    right: 0px;
    width: 35% !important;
}
.all {
    right: 0px;
    width: 50% !important;
}
}

@media (max-width: 767px),(min-width:768px) and (max-width:1024px) {
    .upsell-2,.upsell-2-5,.upsell-3 {
        max-width:100%!important
    }
}

@media (min-width: 1025px) {
    .upsell-3 {
        max-width:1100px!important;
        right: -100%
    }
}

@media (min-width: 1025px) {
    .upsell-2-5 {
        max-width:1264px!important;
        right: -100%
    }

    .upsell-2-5 .c-f-paginator {
        display: none
    }
}

@media (min-width: 1025px) {
    .upsell-2 {
        max-width:847px!important
    }

    .upsell-2 .c-f-paginator {
        display: none
    }
}

@media (min-width: 1025px) {
    .upsell-1 {
        max-width:431px!important
    }

    .upsell-1 .c-f-paginator {
        display: none
    }
}

.c-m-family {
    width: fit-content !important;
}

@media (min-width: 768px) and (max-width:1024px),(min-width:1025px) {
    .c-m-family {
        width:-moz-fit-content!important;
        width: fit-content!important
    }
}
.font-14 {
    font-size: 14px!important
}

.h-90 {
  height: 90% !important;
}

.c-card {
    margin: 10px
}

.c-card .image img {
    max-width: 100%;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px
}

.c-card.is-loading .image, .c-card.is-loading .s-line-20, .c-card.is-loading h2, .c-card.is-loading p {
    background: #eee;
    background: linear-gradient(110deg,#ececec 8%,#f5f5f5 18%,#ececec 33%);
    border-radius: 5px;
    background-size: 200% 100%;
    animation: 1.5s d linear infinite
}

.c-card.is-loading .image {
    height: 200px;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0
}
.family-rate-1 .c-rate-style{
    background-color:#5c469c
}
.family-rate-1 .chc-header h3 span{
    color:#5c469c
}
.overlay-upsell {
    background-color: rgba(0, 0, 0, 0.8);
    bottom: 0;
    content: "";
    left: 0;
    position: fixed;
    right: 0;
    top: 0;
    z-index: 99;
}
.ifd {
    left: -5px;
}
.c-divisor-line > .mb-3:nth-of-type(2) {
    border-bottom: 1px solid #eee;
    padding-bottom: 15px;
}
 .icon.ic-in {
    top: -1px !important;
}
.h-99 {
    height: 99% !important;
}
.chcf-01 .price {
  font-size: 24px;
}

.chcf-01 .label {
  font-size: 14px;
}

.chcf-01 .taxes {
  font-size: 12px;
}

@media (max-width: 767px) {
  .chcf-01 .label {
    font-size: 12px;
  }

  .chcf-01 .taxes {
    font-size: 10px;
  }
}
</style>