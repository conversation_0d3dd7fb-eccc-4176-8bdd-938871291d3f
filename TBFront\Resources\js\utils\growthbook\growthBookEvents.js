import {ViewedExperimentModel} from "./models/viewedExperimentModel";
import {BeginCheckoutModel} from "./models/beginCheckoutModel";
import {GenericEventModel} from "./models/genericEventModel";
import {UTILS} from "../analytics/main";
import moment from "moment/moment";
import {AddShippingInModel} from "./models/addShippingInModel";
import {PurchaseModel} from "./models/purchaseModel";

export default class GrowthBookEvents {
    constructor(experiment = window.__pt.experiment) {
        if(!experiment){
            experiment = sessionStorage.getItem('experiment') ? JSON.parse(sessionStorage.getItem('experiment')) : undefined
        }
        
        this.experiment = experiment;
        if(experiment){
            sessionStorage.setItem('experiment', JSON.stringify(experiment))
            this.experimentIsActived = !experiment?.noExperiment && experiment.experiment?.active;
            if(window.__pt.fn?.analytics){
                this.analytics = window.__pt.fn?.analytics;
                this.events = window.__pt.fn.analytics.events;
                this.pages = window.__pt.fn.analytics.pages;
            }
            this.request = window.__pt.data || {};
            this.config = window.__pt.settings || {};
            this.fn = window.__pt.fn || {};
            if (window.__pt.data !== undefined) {
                this.tripMode = window.__pt.data?.isNational ? "national" : "international";
                this.mode = window.__pt.data?.isRoundtrip ? "roundtrip" : "oneway";
                this.genericEventLabel = `${this.tripMode}|${this.request.startingFromAirport}-${this.request.returningFromAirport}|${this.mode}`
            }
        }
    }
    PushEvent(eventName, action_path) {
        if(this.experimentIsActived){
            const data = new GenericEventModel({
                EventName: eventName,
                EventCategory: this.experiment.experiment.code,
                EventAction: UTILS.actions[action_path] ?? action_path,
                EventLabel: `${this.genericEventLabel}|ida:${this.request.startingFromAirport}|regreso:${this.request.returningFromAirport}|adults:#${this.request.adults}|kids:#${this.request.kids} | ${this.experiment.experiment.code} | ${this.experiment.isExperiment ? "experiment":"main"}`,
            })
            this.Push('generic-event', data);
        }
    }
    ViewedExperiment() {
        if(this.experimentIsActived) {
            let params = {
                ExperimentId: this.experiment.experiment.code,
                VariationId: this.experiment.isExperiment ? "1" : "0"
            }
            this.Push('viewed-experiment', new ViewedExperimentModel(params));
        }
    }
    PushBeginCheckout(args) {
        if(this.experimentIsActived) {
            let base = this.#getBaseData(args);
            let starting = base.flightItinerary.starting;
            let originName = starting.departureFullName;
            let destinationName = starting.arrivalFullName;
            let originIATA = base.startingFrom;
            let destinationIATA = base.returningFrom;
            let airlineCode = base.flightItinerary.starting.airlineCode;
            let airlineCodeReturning = base.returningAirlineCode ? base.returningAirlineCode : "";
            const params = new BeginCheckoutModel({
                Layer: "flights",
                ItemBrand: `${airlineCode}${airlineCodeReturning ? `|${airlineCodeReturning}` : ''}`,
                FlightType: base.isRoundtrip ? "roundtrip" : "oneway",
                RouteType: base.isDomesticRoute ? "national" : "international",
                FieldOriginIata: originIATA,
                FieldOriginName: originName,
                FieldDestinationIata: destinationIATA,
                FieldDestinationName: destinationName,
                FieldDate1: base.checkinFormat,
                FieldDate2: base.isRoundtrip ? base.checkoutFormat : "--",
                TravelersAdults: Number(base.adults),
                TravelersChildren: Number(base.children),
                TravelersInfants: Number(base.infant),
                TravelersInfantsOnlap: 0,
                TravelersInfantsInseat: 0,
                Value: base.totalAmount,
            });
            this.Push('begin-checkout-flights', params);
        }
    }

    PushAddShopping(args){
        if(this.experimentIsActived) {
            let base = this.#getBaseData(args);
            let starting = base.flightItinerary.starting;
            let originName = starting.departureFullName;
            let destinationName = starting.arrivalFullName;
            let originIATA = base.startingFrom;
            let destinationIATA = base.returningFrom;
            let airlineCode = base.flightItinerary.starting.airlineCode;
            let airlineCodeReturning = base.returningAirlineCode ? base.returningAirlineCode : "";
            let data = new AddShippingInModel({
                Layer: "flights",
                ItemBrand: `${airlineCode}${airlineCodeReturning ? `|${airlineCodeReturning}` : ''}`,
                FlightType: base.isRoundtrip ? "roundtrip" : "oneway",
                RouteType: base.isDomesticRoute ? "national" : "international",
                FieldOriginIata: originIATA,
                FieldOriginName: originName,
                FieldDestinationIata: destinationIATA,
                FieldDestinationName: destinationName,
                FieldDate1: base.checkinFormat,
                FieldDate2: base.isRoundtrip ? base.checkoutFormat : "--",
                TravelersAdults: Number(base.adults),
                TravelersChildren: Number(base.children),
                TravelersInfants: Number(base.infant),
                TravelersInfantsOnlap: 0,
                TravelersInfantsInseat: 0,
                Value: base.totalAmount
            });
            this.Push('add-shipping-info-flights', data);
        }
    }
    PushPurchase(args){
        if(this.experimentIsActived) {
            let reservation = this.#getReservation(args);
            let data = new PurchaseModel({
                Layer: "flights",
                Value: reservation.totalAmount,
                PaymentType: "CreditCard",
                TransactionId: String(reservation.bookingId)
            });
            this.Push('purchases-flights', data);
        }
    }
    Push(evt, params) {
        if(this.experimentIsActived) {
            fetch(`${this.config.site.apiB2C.uri}/api/experiment-tracker/` + evt, {
                method: 'POST',
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify(params)
            });
        }
    }
    clickEvent(eventName, action_path, params){
        if(this.experimentIsActived) {
            let text = this.#objToString(params)
            const data = new GenericEventModel({
                EventName: eventName,
                EventCategory: this.experiment.experiment.code,
                EventAction: UTILS.actions[action_path] ?? action_path,
                EventLabel: `${text}${this.genericEventLabel}|${this.experiment.experiment.code}|${this.experiment.isExperiment ? "experiment" : "main"}`,
            })
            this.Push('generic-event', data);
        }
    }
    #objToString(obj, path = '') {
        let str = '';
        for (let key in obj) {
            if (typeof obj[key] === 'object' && obj[key] !== null) {
                str += this.#objToString(obj[key], `${path}${key}`);
            } else {
                str += `${path}${key}:${obj[key]}|`;
            }
        }
        return str;
    }

    #getBaseData(args) {
        let data = {};
        let quote = args.quote || {};
        let checkinMoment = moment(quote.checkIn);
        let checkoutMoment = moment(quote.checkOut);
        let child = 0;
        let infant = 0;
        [quote.paxes ?? []].forEach(item => {

            [item.children ?? []].forEach(child => {
                if (child.year > 1) {
                    infant++;
                }else{
                    child++;
                }
            });
        });

        data.adults = quote.adults;
        data.children = child;
        data.infant = infant;
        data.checkIn = quote.checkIn;
        data.checkOut = quote.checkOut;
        data.culture = quote.culture;
        data.currency = quote.currency;
        data.isDomesticRoute = quote.isDomesticRoute;
        data.days = checkoutMoment.diff(checkinMoment, 'days');
        data.quoteExpirationDate = quote.quoteExpirationDate;
        data.totalAmount = quote.rate.totalAmount;
        data.isBookNowPayLater = quote.rate.bookNowPayLater?.isBookNowPayLater;
        data.detailIsBookNowPayLater = this.data?.isBookNowPayLater;
        data.checkinFormat = moment(data.checkIn).format('YYYY-MM-DD');
        data.checkoutFormat = moment(data.checkOut).format('YYYY-MM-DD');
        data.week = moment(data.checkOut).week();
        data.isWeekend = (checkinMoment.day() === 6) || (checkinMoment.day() === 0);
        data.channelId = quote.channelId;
        data.isRoundtrip = quote.rate.isRoundtrip;
        data.tripType = quote.rate.isRoundtrip ? 'roundtrip' : 'oneway';
        data.flightItinerary = quote.flightItinerary;
        data.weekDay = moment(data.checkOut).format('dddd');
        data.productUrl = quote.productUrl;
        data.startingFrom = quote.extraInfoFlight.startingFrom;
        data.returningFrom = quote.extraInfoFlight.returningFrom;
        data.startingAirline = quote.flightItinerary.starting.airline;
        data.returningAirline = quote.flightItinerary.returning.airline;
        data.startingAirlineCode = quote.flightItinerary.starting.airlineCode;
        data.returningAirlineCode = quote.flightItinerary.returning.airlineCode;

        return data;
    }
    #getReservation(args = {}) {
        let reservation = args.reservation || {};
        let isRoundtrip = reservation.isRoundTrip;
        let flightInformation = reservation.flightInformation || {};
        let segmentDeparture = flightInformation[0];
        let segmentArrival = flightInformation[1] || {};

        let checkinMoment = moment(segmentDeparture.startDate);
        let checkoutMoment = moment(isRoundtrip ? segmentArrival.startDate : segmentDeparture.endDate);
        let totalAmount = 0;
        reservation.payments.forEach(payment => {
            totalAmount = totalAmount + payment.paymentAmount
        });
        return {
            bookingId: reservation.bookingId,
            createdDate: reservation.createdDate,
            currency: reservation.currency,
            customerEmail: reservation.customerEmail,
            customerFirstName: reservation.customerFirstName,
            customerLastName: reservation.customerLastName,
            minServiceDate: reservation.minServiceDate,
            organizationId: reservation.organizationId,
            adults: segmentDeparture.adults,
            children: segmentDeparture.kids,
            infants: segmentDeparture.infants,
            checkout: segmentDeparture.endDate,
            checkin: segmentDeparture.startDate,
            isRoundtrip: isRoundtrip,
            tripType: isRoundtrip ? 'roundtrip' : 'oneway',
            tripTypeText: isRoundtrip ? 'roundtrip' : 'oneway',

            startingFrom: segmentDeparture.originAirportCode,
            returningFrom: isRoundtrip ? segmentArrival.originAirportCode : segmentDeparture.destinationAirportCode,

            startingFromName: segmentDeparture.originAirport,
            returningFromName: isRoundtrip ? segmentArrival.originAirport : segmentDeparture.destinationAirport,

            startingOperatingCode: segmentDeparture.airlineCode,
            startingOperatingName: segmentDeparture.serviceCarrierName,

            returningOperatingCode: isRoundtrip ? segmentArrival.airlineCode : segmentDeparture.airlineCode,
            returningOperatingName: isRoundtrip ? segmentArrival.serviceCarrierName: segmentDeparture.serviceCarrierName,

            isWeekend: (checkinMoment.day() === 6) || (checkinMoment.day() === 0),
            week: checkinMoment.week(),
            checkinFormat: checkinMoment.format('YYYY-MM-DD'),
            checkoutFormat: checkoutMoment.format('YYYY-MM-DD'),
            days: checkoutMoment.diff(checkinMoment, 'days'),
            weekDay: checkinMoment.day(),
            totalAmount: totalAmount,
            channelId: reservation.channelId,
            segmentDeparture,
            segmentArrival

        }
    }
}
//export const GrowthBookEvent = new GrowthBookEvents();