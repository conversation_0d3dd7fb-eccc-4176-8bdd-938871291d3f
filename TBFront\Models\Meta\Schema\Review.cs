﻿using System.Text.Json.Serialization;

namespace TBFront.Models.Meta.Schema
{
    public class Review
    {
        [JsonPropertyName("@type")]
        public string? Type { get; set; }

        [JsonPropertyName("author")]
        public Author? Author { get; set; }

        [JsonPropertyName("reviewRating")]
        public AggregateRating? ReviewRating { get; set; }

        public Review()
        {
            Author = new Author();
            ReviewRating = new AggregateRating();

        }

    }
}
