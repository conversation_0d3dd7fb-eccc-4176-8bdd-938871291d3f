﻿
using TBFront.Models.BookingItinerary;

namespace TBFront.Models.Response
{
    public class VoucherResponse
    {
        public int BookingId { get; set; }
        public int ChannelId { get; set; }
        public int OrganizationId { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime MinServiceDate { get; set; }
        public List<string> Tags { get; set; }
        public string? CustomerEmail { get; set; }
        public string? CustomerFirstName { get; set; }
        public string? CustomerLastName { get; set; }
        public string? Currency { get; set; }
        public bool IsRoundTrip { get; set; }
        public bool IsDomestic { get; set; } = false;
        public List<FlightInformationVoucher> FlightInformation { get; set; }
        public List<Payments> Payments { get; set; }

        public List<Dictionary<string, object>> Events { get; set; }

        public VoucherResponse()
        {
            Payments = new List<Payments>();
            Events = new List<Dictionary<string, object>>();

        }

    }



    public class FlightInformationVoucher
    {
        public string Description { get; set; }
        public string FamilyFareName { get; set; }
        public string Airline { get; set; }
        public string ServiceCarrierDescription { get; set; }
        public string ServiceCarrierName { get; set; }
        public string OriginAirportCode { get; set; }
        public string DestinationAirportCode { get; set; }
        public string OriginAirport { get; set; }
        public string DestinationAirport { get; set; }
        public string FlightNumber { get; set; }
        public double ServiceAmountTotal { get; set; }
        public double ServiceAmountBalance { get; set; }
        public double ServiceAmountPaid { get; set; }
        public string AirlineCode { get; set; }
        public DateTime EndDate { get; set; }
        public DateTime StartDate { get; set; }
        public int Kids { get; set; }
        public int Adults { get; set; }
        public int infants { get; set; }
        public bool IsRoundtrip { get; set; }
        
    }

 

    public class Payments
    {
        public string PaymentDescription { get; set; }
        public double PaymentAmount { get; set; }
        public int PaymentType { get; set; }

    }

}
