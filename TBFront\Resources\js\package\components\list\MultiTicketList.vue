<template>
    <Skeleton v-show="(Object.keys(groups).length == 0 && getProgressBar <= 99) || getLoading"
        :typeSkeleton="'typeList'" v-for="skeleton in skeletons" />
    <div class="container mb-5" v-for="(group, index) in groups" :key="'container' + index + getCode(group)"
        :id="`cSticky${index}`">
        <div class="row" v-if="(isStarting && group?.departure?.flights?.length > 0) || (!isStarting && group?.returning?.flights?.length > 0)" :id="getCode(group)">
            <div class="col-12 px-md-0">
                <div class="container table-list-container">
                    <div class="row table-list-header" :class="{ 'sticky-settings': configSticky[index] }">
                        <div class="col-4 col-md-5 px-sm-2 px-md-3 py-1"> 
                            <div class="mgs-airline">
                            <img loading="lazy" class="me-md-2 info-header-sticky" :src="(isStarting ? group?.departure?.image : group?.returning?.image)">
                            
                            <span class="ma-airline">{{ (isStarting ? group?.departure?.name : group?.returning?.name) }}</span>
                              
                                <span class="body-sm text-subtle ma-iata" v-if="!isMultipleAirport && isStarting">
                                   {{ `(${iataStarting}-${iataReturning})` }}
                                </span>
                                <span class="body-sm text-subtle ma-iata" v-if="!isMultipleAirport && !isStarting">
                                   {{ `(${iataReturning}-${iataStarting})` }}
                                </span>
                            </div>
                        </div>
                        <div class="col-8 col-md-7 px-sm-2 px-md-3">
                            <template v-if="isStarting">
                                <div class="mgs-price d-none d-md-grid">
                                    <span> {{ isRoundtrip ? __("multiticket.price_RoundTrip_from") :  __("multiticket.price_from")}}</span>
                                    <span style="font-size:20px">
                                        <CurrencyDisplay :amount="getTotalGroup(isStarting,getCode(group))" :showCurrencyCode="true" :reduceIsoFont="true" />
                                    </span>
                                    <span class="mp-tax">({{__("multiticket.tax_include")}})</span>
                                </div>
                                <div class="mgs-price d-md-none">
                                    <span class="mp-text">{{ isRoundtrip ? __("multiticket.price_RoundTrip_from") :  __("multiticket.price_from")}}</span>
                                    <span class="mp-price info-header-sticky">
                                        <CurrencyDisplay :amount="getTotalGroup(isStarting,getCode(group))" :showCurrencyCode="true" :reduceIsoFont="true" />
                                    </span>
                                    <span class="mp-tax">({{__("multiticket.tax_include")}})</span>
                                </div>
                            </template>
                            <template v-else>
                                <div class="mgs-price">
                                    <span class="mp-price info-header-sticky">
                                        <CurrencyDisplay :amount="getTotalGroup(isStarting,getCode(group))" :showCurrencyCode="true" :reduceIsoFont="true" :plusSymbol="!isStarting"/>
                                    </span>
                                    <span class="mp-text">{{__("multiticket.per_pax")}}</span>
                                    <span class="mp-tax">({{__("multiticket.tax_include")}})</span>
                                </div>
                            </template>
                        </div>
                    </div>
                    <div class="row table-list">
                        <MultiTicketListItem :group="group" :idxGroup="index" :isStarting="isStarting"></MultiTicketListItem>
                    </div>

                </div>
            </div>
        </div>
    </div>
    <ModalUpsell v-if="getShowUpsell" />
</template>

<script setup>
import { computed,onMounted, onBeforeUnmount, ref } from 'vue';
import { storeToRefs } from 'pinia';
import { useFlightStore } from '../../stores/flight';
import { useFlightUpsellStore } from '../../stores/flightUpsell';
import { useFlightMatrixStore } from '../../stores/flightMatrix';
import jsonData from '../../../../json/iatasGrouper.json';
import _ from 'lodash';
import CurrencyDisplay from '../common/CurrencyDisplay.vue';

const storeFlight = useFlightStore()
const useFlightUpsell = useFlightUpsellStore();
const useFlightMatrix = useFlightMatrixStore();
const { getParams, getGroups, getFlightResponse, getProgressBar, getTotalGroup } = storeToRefs(storeFlight);
const { getShowUpsell } = storeToRefs(useFlightUpsell);
const { getLoading } = storeToRefs(useFlightMatrix);

const skeletons = [1, 2, 3];
const configSticky = ref({});

const iatasAgrouper = computed(() => jsonData);
const iataStarting = window.__pt.data.startingFromAirport;
const iataReturning = window.__pt.data.returningFromAirport;
const isRoundtrip = window.__pt.data.isRoundtrip;

const { isStarting } = defineProps({
    isStarting: {
        type: Boolean,
        default: true
    }
});

const groups = computed(() => {
    let getGroupsSort = [];
    for (let grupo in getGroups.value) {
        configSticky.value[grupo] = false;
    }
    if (isStarting) {
        getGroupsSort = getGroups.value.sort((a, b) => {
            const priceA = a.departure?.cheapest ?? Infinity;
            const priceB = b.departure?.cheapest ?? Infinity; 

            return priceA - priceB; 
        });
    } else {
        getGroupsSort = getGroups.value.sort((a, b) => {
            const priceA = a.returning?.cheapest ?? Infinity;
            const priceB = b.returning?.cheapest ?? Infinity; 

            return priceA - priceB; 
        });
    } 
    return getGroupsSort;
});
 
const departureDate = computed(() =>  { 
    return isStarting ? getParams.value.checkIn : getParams.value.checkOut; 
})

const isRounTrip = () => {
    return getParams.value.isRoundtrip;
}

const getCode = (group) => {
    return isStarting ? group?.departure?.code : group?.returning?.code
}

const handleScroll = () => {
    const scrollTop = Math.round(window.scrollY); // Obtén el scroll actual una vez
    const offset = 50; // Margen para controlar la visibilidad

    Object.keys(configSticky.value).forEach(group => {
        const item = document.getElementById('cSticky' + group);
        if (!item) return; // Asegúrate de que el elemento existe

        const top = item.offsetTop;

        // Actualiza el estado solo si cambia
        if (scrollTop >= top && !configSticky.value[group]) {
            configSticky.value[group] = true;
        } else if (scrollTop + offset < top && configSticky.value[group]) {
            configSticky.value[group] = false;
        }
    });
};

onMounted(() => {
    window.addEventListener('scroll', handleScroll);
})

onBeforeUnmount(() => {
    window.removeEventListener('scroll', handleScroll);
})

const isMultipleAirport = computed(() => {
    
    return iatasAgrouper.value[iataStarting] != undefined && iatasAgrouper.value[iataStarting]
        || iatasAgrouper.value[iataReturning] != undefined && iatasAgrouper.value[iataReturning];
});
</script>