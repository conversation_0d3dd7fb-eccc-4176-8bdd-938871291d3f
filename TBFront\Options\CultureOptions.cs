﻿namespace TBFront.Options
{
    public class CultureOptions
    {
        public string CultureDefault { get; set; } = string.Empty;
        public List<Culture> Cultures { get; set; } = new List<Culture>();
    }
    public class Culture
    {
        public string CultureCode { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public string InternalCultureCode { get; set; } = string.Empty;
        public string CultureExternal { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Language { get; set; } = string.Empty;
        public int LanguageInternal { get; set; }
        public string BasePath { get; set; }
        public string Country { get; set; } = string.Empty;
        public string Currency { get; set; } = string.Empty;
        public string CurrencySymbol { get; set; } = string.Empty;
        public string CurrencyCodeName { get; set; } = string.Empty;
        public string SiteUrl { get; set; } = string.Empty;
        public string SiteUrlLegacy { get; set; } = string.Empty;
        public string SiteName { get; set; } = string.Empty;
        public string PathHotelList { get; set; } = string.Empty;
        public string PathHotelDetail { get; set; } = string.Empty;
        public string PathDestination { get; set; } = string.Empty;
        public string PathFavorites { get; set; } = string.Empty;
        public string PathCheckout { get; set; } = string.Empty;
        public string PathFlight { get; set; } = string.Empty;
        public string PathPackages { get; set; } = string.Empty;
        public string PathHome { get; set; } = string.Empty;
        public string SiteCode { get; set; } = string.Empty;
        public string Site { get; set; } = string.Empty;
        public FeaturedSwitches? FeaturedSwitches { get; set; }
        public List<TabsOptions> Tabs { get; set; } = new List<TabsOptions>();
        public Culture Clone()
        {
            return new Culture
            {
                CultureCode = this.CultureCode,
                Code = this.Code,
                InternalCultureCode = this.InternalCultureCode,
                CultureExternal = this.CultureExternal,
                Name = this.Name,
                Language = this.Language,
                Country = this.Country,
                Currency = this.Currency,
                CurrencySymbol = this.CurrencySymbol,
                CurrencyCodeName = this.CurrencyCodeName,
                SiteUrl = this.SiteUrl,
                SiteUrlLegacy = this.SiteUrlLegacy,
                SiteName = this.SiteName,
                PathCheckout = this.PathCheckout,
                PathFlight = this.PathFlight,
                Site = this.Site,
                Tabs = this.Tabs
            };
        }
    }
    public class FeaturedSwitches
    {
        public bool LoginActive { get; set; }
        public bool LoginForgot { get; set; }
        public bool LoginAutoOpen { get; set; }
        public bool ShowMapComponent { get; set; }
        public bool ShowSearchByHotelName { get; set; }
        public bool RecommenderDatesActive { get; set; }
        public bool ShowReviews { get; set; }
        public bool ShowRecentSearch { get; set; }
        public bool ShowStartList { get; set; }
        public bool ShowCalendarAvailability { get; set; }
        public bool ShowAds { get; set; }
        public bool ListSsrActive { get; set; }
        public bool ShowPromotionsTags { get; set; }
        public bool PreviewPrice { get; set; }
        public bool PaymentMethodActive { get; set; }
        public bool SeoHomeActive { get; set; }
    }
}
