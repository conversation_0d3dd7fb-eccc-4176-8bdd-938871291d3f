﻿namespace TBFront.Infrastructure.HttpService.PaymentGateway.Dtos
{
    public class PaymentGatewayConfiguration
    {
        public string Uri { get; set; }
        public string PathPayment { get; set; }
        public string PathPaymentGetClientInfo { get; set; }
        public string PathSearchPaymentGatewayConfiguration { get; set; }
        public int PaymentGatewayApp { get; set; }
        public IEnumerable<int> CheckoutProvider { get; set; }
        public IEnumerable<int> thirdPartyCheckoutProvider { get; set; }
        public bool Is3DSecureProcessingEnabled { get; set; }

    }
}
