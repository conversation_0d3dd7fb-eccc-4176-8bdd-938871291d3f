/**
 * NationalAirlineFlightsMappers class is responsible for mapping airline flights data.
 */
class NationalAirlineFlightsMappers {
    map(airline, settings){
        airline = this.mapFlights(airline, settings)
        return airline
    }
    /**
     * Maps the flights of an airline according to the specified settings.
     *
     * @param {Object} airline - The airline object.
     * @param {Object} settings - The settings object.
     *
     * @return {Object} - The modified airline object with mapped flights.
     */
    mapFlights(airline, settings) {
        const families = settings.families;
        airline.families = [];
        const travelTypes = ['departure', 'returning'];
        travelTypes.forEach(type => {
            if (airline[type]?.flights) {
                airline[type].flights = ((airline[type].flights).filter(f => this.validateFlight(f))).map(flight => ({
                    ...flight,
                    fares: this.mapFares(flight, families, airline, settings),
                }));
            }
        });
        airline.families = this.sortFamilies(this.filterByFamilyFareCode(airline.families), settings);
        return airline;
    }
    /**
     * Maps fares for a given flight and families based on certain conditions.
     *
     * @param {Object} flight - The flight object containing fares.
     * @param {Array} families - The array of families to map fares for.
     * @param {Object} airline - The airline object to update with mapped families.
     * @param {Object} settings - The settings object to define certain conditions.
     *
     * @return {Array} - The updated array of flight fares.
     */
    mapFares(flight, families, airline, settings) {
        flight.fares?.forEach((family, $index) =>{
            if (!families?.find((familyI) => {
                if (Array.isArray(familyI.familyFareCode)) {
                    return familyI.familyFareCode.some(code =>
                        String(code).toUpperCase() === String(family.fareGroup).toUpperCase());
                } else {
                    return String(familyI.familyFareCode).toUpperCase() === String(family.fareGroup).toUpperCase();
                }
            })) {
                families?.splice($index, 0, {
                    name:  family.fareGroup,
                    familyFareCode:  [family.fareGroup],
                    isAvailable: true
                });
            }
        })
        families = families?.reduce((mFares, family, $currentIndex) => {
            let fareExist;
            if (Array.isArray(family.familyFareCode)) {
                family.familyFareCode.some(code => {
                    const temp = flight.fares.find(
                        f => String(f.fareGroup).toUpperCase() === String(code).toUpperCase()
                    );
                    fareExist = fareExist || temp;
                    return fareExist;
                });
            } else {
                fareExist = flight.fares.find(
                    f => String(f.fareGroup).toUpperCase() === String(family.familyFareCode).toUpperCase()
                );
            }

            if (family.isAvailable) {
                if (fareExist && fareExist?.amount > 0) {
                    airline.families.splice($currentIndex, 0, family);
                } else if (!settings.hiddenEmptyFamily &&
                    !airline.families.find((familyI) =>
                        (Array.isArray(familyI.familyFareCode) ?
                            familyI.familyFareCode.includes(String(family.familyFareCode).toUpperCase())
                            : String(familyI.familyFareCode).toUpperCase() === String(family.familyFareCode).toUpperCase()))) {
                    airline.families.splice($currentIndex, 0, family);
                }
            }
            return flight.fares;
        }, []);
        return families
    }
    /**
     * Filter an array of family objects based on unique familyFareCode values.
     *
     * @param {Array} families - An array of family objects.
     * @returns {Array} - An array of family objects with unique familyFareCode values.
     */
    filterByFamilyFareCode(families) {
        return families.filter((family, i, self) => {
            // Check if familyFareCode is an array
            if (Array.isArray(family.familyFareCode)) {
                // If it's an array, every element in the familyFareCodes array should be unique
                return family.familyFareCode.every((code) => {
                    return self.findIndex(f => Array.isArray(f.familyFareCode) ? f.familyFareCode.includes(code) : f.familyFareCode === code) === i;
                });
            }
        });
    }
    sortFamilies(families, settings){
        //console.log(settings.families.familyFareCode)
        return families.sort((a, b) => {
            //console.log(settings.families, a.familyFareCode[0])
            const fa =  this.getFamilyIndex(a.familyFareCode[0], settings.families);
            const fb = this.getFamilyIndex(b.familyFareCode[0], settings.families);
            if (fa < fb) return -1;
            if (fa > fb) return 1;
            return 0;
        });
    }
    getFamilyIndex(fareCode, families) {
        return families.findIndex(family =>
            family.familyFareCode.some(code => (String(fareCode)).toUpperCase() === (String(code)).toUpperCase())
        );
    }
    
    get flightValidationStops(){
        return window.__pt.settings.site.flightValidationStops.national
    }
    validateFlight(flight){
        const departure = flight.departure.code.toUpperCase()
        const arrival = flight.arrival.code.toUpperCase()
        let fRoute = (this.flightValidationStops.routes || []).find(route => ((route.origin).toUpperCase()  === departure+arrival || (route.origin).toUpperCase()  === arrival+departure))
        let stops = this.flightValidationStops.default.value
        if(fRoute){
            
            let nStops = fRoute.airlineStops[flight.airline.code]
            if(nStops) stops = nStops
        }
        
        return flight.stops <= stops
    }
    
    generateLoading(airlines, settings, loading=false){
        airlines = (airlines|| []).filter(item=> {
            return !item.loading
        });
        settings.forEach((setting)=>{
            let airline = null
            setting.airlineCode.forEach(code =>{
                airline = this.findItemByNestedProperty(airlines, "departure.code", code) || {}
                if((Object.values(airline).length && airline.departure?.flights)){
                    airline.loading = false
                    delete airline.temporal
                }else{
                    airline = {
                        loading: loading,
                        departure:{
                            code: code
                        },
                        returning:{
                            code: code
                        }
                    }
                }
            } )
            if(airline.loading) airlines.push(airline)
        })
        return airlines
    }

    configFilter(configSite, code=null) {
        let config = (configSite || []);
        return code ? ((config).find(item => (item.airlineCode || []).find(aCode => (String(aCode)).toUpperCase() === (String(code)).toUpperCase())) ?? {}) : config
    }
    airlinesFilter(airlines, code=null) {
        airlines = (airlines || []);
        return code ? ((airlines).find(item => (item.airlineCode || []).find(aCode => (String(aCode)).toUpperCase() === (String(code)).toUpperCase())) ?? {}) : airlines
    }
    findItemByNestedProperty(items, propertyPath = null, value = null, caseInsensitive = true) {
        items = items || [];
        if (propertyPath && value !== null) {
            const propertyKeys = propertyPath.split('.');
            return items.find(item => {
                let currentProperty = item;
                for (const propertyKey of propertyKeys) {
                    if (currentProperty[propertyKey] !== undefined) {
                        currentProperty = currentProperty[propertyKey];
                    }
                    else {
                        return false;
                    }
                }
                const itemValue = String(currentProperty);
                const searchValue = String(value);
                return caseInsensitive ? itemValue.toUpperCase() === searchValue.toUpperCase() : itemValue === searchValue;
            }) || null;
        }
        return items;
    }
}
/**
 * A mapper class for National Airline flights.
 * This class is used to map and process data related to National Airline flights.
 *
 * @class
 */
export const NationalAirlineFlightsMapper = new NationalAirlineFlightsMappers();