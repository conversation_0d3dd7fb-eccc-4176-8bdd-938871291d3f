﻿using Microsoft.Extensions.Options;
using TBFront.Models.Response;
using TBFront.Options;
using TBFront.Types;
using TBFront.Models.Flight.Summary;
using TBFront.Infrastructure.HttpService.BookingService;
using TBFront.Models.BookingItinerary;

namespace TBFront.Mappers
{
    public class BookingMapper
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly SettingsOptions _options;
        private readonly string _ipDefault = "127.0.0.1";


        public BookingMapper(IHttpContextAccessor httpContextAccessor, IOptions<SettingsOptions> options)
        {
            _httpContextAccessor = httpContextAccessor;
            _options = options.Value;
        }


        public VoucherResponse ItineraryResponseToGetBookingResponse(ItineraryResponse itinerary, List<ClientTokenInfoResponse> clientInfo, SummaryResponse summary)
        {
            var response = new VoucherResponse();


            if (itinerary == null || itinerary.Data == null || itinerary.Data.TravelItinerary == null)
            {
                throw SetError(StatusType.VOUCHER_NOT_FOUND);
            }

            var travelItinerary = itinerary.Data.TravelItinerary;


            response.Currency = travelItinerary.Currency;
            response.ChannelId = travelItinerary.ChannelId;
            response.BookingId = travelItinerary.BookingId;
            response.OrganizationId = travelItinerary.OrganizationId;
            response.CreatedDate = travelItinerary.CreatedDate;
            response.MinServiceDate = travelItinerary.MinServiceDate;
            response.Tags = travelItinerary.TagsList;
            response.CustomerEmail = travelItinerary.CustomerEmail;
            response.CustomerFirstName = travelItinerary.CustomerFirstName;
            response.CustomerLastName = travelItinerary.CustomerLastName;
            response.FlightInformation = GetFlightInformation( summary);
            response.Payments = GetPaymentsBooking(summary);
            response.IsRoundTrip = response.FlightInformation.Count() > 1;
            response.Events = UserMapper.UserCheckoutEvents(travelItinerary);

            return response;

        }





        private static List<FlightInformationVoucher> GetFlightInformation(SummaryResponse summary)
        {
           
            var flights = new List<FlightInformationVoucher>();


            foreach (var (flightItem, index) in summary.Items.Flights.Select((value, i) => (value, i)))
            {
              
                var flight = GetFlightInformation(flightItem.FlightDepartureSegments, flightItem);

                flights.Add(flight);

                if (flightItem.TripMode == 1)
                {
                    var flightReturning = GetFlightInformation(flightItem.FlightReturningSegments, flightItem);
                    flights.Add(flightReturning);
                }
            }
            

            return flights;
        }


        private static FlightInformationVoucher GetFlightInformation(List<SummaryFlightSegment> segments, SummaryFlight flightItem)
        {
            var flight = new FlightInformationVoucher();

            var firstSegment = segments.First();
            var lastSegment = segments.Last();

            flight.Description = $"{firstSegment.DepartureAirport} - {lastSegment.ArrivalAirport}";
            flight.ServiceCarrierName = firstSegment.Airline;
            flight.Adults = flightItem.FlightPassengers.Count(p => p.Type == 0);
            flight.Kids = flightItem.FlightPassengers.Count(p => p.Type == 1 || p.Type == 2);
            flight.infants = flightItem.FlightPassengers.Count(p => p.Type == 4);
            flight.StartDate = firstSegment.DepartureDate;
            flight.EndDate = lastSegment.ArrivalDate;
            flight.ServiceAmountTotal = flightItem.TotalAmount;
            flight.ServiceAmountBalance = flightItem.TotalAmount;
            flight.ServiceAmountPaid = flightItem.TotalAmount;
            flight.IsRoundtrip = flightItem.TripMode == 1;
            flight.OriginAirport = firstSegment.DepartureAirport;
            flight.OriginAirportCode = firstSegment.DepartureAirportCode;
            flight.DestinationAirport = lastSegment.ArrivalAirport;
            flight.DestinationAirportCode = lastSegment.ArrivalAirportCode;
            flight.AirlineCode = firstSegment.AirlineCode;
            flight.FlightNumber = firstSegment.FlightNumber;
            flight.Airline = firstSegment.Airline;
            flight.FamilyFareName = firstSegment.FamilyFareName;

            return flight;
        }

        private static List<Payments> GetPaymentsBooking( SummaryResponse flightItem)
        {
            return new List<Payments>
            {
                new Payments()
                {
                    PaymentAmount = flightItem.TotalAmount,
                    PaymentDescription = $"",
                }
            };
        }

        private static ArgumentException SetError(string error)
        {
            throw new ArgumentException(error);
        }
    }
}
