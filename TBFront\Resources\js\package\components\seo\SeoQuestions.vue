<template>
  <div v-if="faqs && faqs.length > 0" class="container mt-5">
    <h2 class="mb-4" style="font-size: 24px;">
      {{ questionTitle }}
    </h2>
    <div class="row">
      <!-- Columna 1 -->
      <div class="col-md-6">
        <div class="mb-3">
          <div
            v-for="(question, index) in firstColumnQuestions"
            :key="`col1-${index}`"
            :id="`heading-${question.hash}`"
            class="question-item"
            style="border-bottom: 1px solid var(--border-subtle); margin-bottom: 16px;"
          >
            <h2>
              <button
                class="btn font-16 btn-block text-left"
                type="button"
                style="display: flex; justify-content: space-between; width: 100%; min-height: 48.19px;"
                :aria-expanded="question.isOpen"
                :aria-controls="`collapse-${question.hash}`"
                @click="toggleQuestion(question)"
              >
                <span v-html="question.question"></span>
                <i
                  class="icon-expand cp-icon collapse-icon"
                  style="display: inline-flex; align-items: center; justify-content: center; width: 20px; height: 20px;"
                  :class="{ 'rotated': question.isOpen }"
                ></i>
              </button>
            </h2>
            <div
              :id="`collapse-${question.hash}`"
              :aria-labelledby="`heading-${question.hash}`"
              class="collapse"
              :class="{ 'show': question.isOpen }"
            >
              <div class="card-body p-0 font-14" style="margin-bottom: 16px;">
                <div v-html="question.answer"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Columna 2 -->
      <div class="col-md-6">
        <div class="mb-3">
          <div
            v-for="(question, index) in secondColumnQuestions"
            :key="`col2-${index}`"
            :id="`heading-${question.hash}`"
            class="question-item"
            style="border-bottom: 1px solid var(--border-subtle); margin-bottom: 16px;"
          >
            <h2>
              <button
                class="btn font-16 btn-block text-left"
                type="button"
                style="display: flex; justify-content: space-between; width: 100%; min-height: 48.19px;"
                :aria-expanded="question.isOpen"
                :aria-controls="`collapse-${question.hash}`"
                @click="toggleQuestion(question)"
              >
                <span v-html="question.question"></span>
                <i
                  class="icon-expand cp-icon collapse-icon"
                  style="display: inline-flex; align-items: center; justify-content: center; width: 20px; height: 20px;"
                  :class="{ 'rotated': question.isOpen }"
                ></i>
              </button>
            </h2>
            <div
              :id="`collapse-${question.hash}`"
              :aria-labelledby="`heading-${question.hash}`"
              class="collapse"
              :class="{ 'show': question.isOpen }"
            >
              <div class="card-body p-0 font-14" style="margin-bottom: 16px;">
                <div v-html="question.answer"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Información SEO adicional -->
  <div v-if="paragraphs && paragraphs.length > 0" class="container mb-4 mt-5">
    <h2 class="mb-4" style="font-size: 24px;">{{ infoTitle }}</h2>
    <p v-for="(paragraph, index) in paragraphs" :key="index" class="font-14" v-html="paragraph"></p>
  </div>
</template>

<script>
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'SeoQuestions',
  props: {
    faqs: {
      type: Array,
      default: () => []
    },
    paragraphs: {
      type: Array,
      default: () => []
    },
    questionTitle: {
      type: String,
      default: ''
    },
    infoTitle: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      processedFaqs: []
    }
  },
  computed: {
    firstColumnQuestions() {
      const half = Math.ceil(this.processedFaqs.length / 2)
      return this.processedFaqs.slice(0, half)
    },
    secondColumnQuestions() {
      const half = Math.ceil(this.processedFaqs.length / 2)
      return this.processedFaqs.slice(half)
    }
  },
  methods: {
    toggleQuestion(question) {
      question.isOpen = !question.isOpen
    },
    generateHash(str) {
      // Función simple para generar un hash similar al GetHashCode() de C#
      let hash = 0
      if (str.length === 0) return hash
      for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i)
        hash = ((hash << 5) - hash) + char
        hash = hash & hash // Convert to 32bit integer
      }
      return Math.abs(hash)
    },
    processFaqs() {
      this.processedFaqs = this.faqs.map(faq => ({
        ...faq,
        isOpen: false,
        hash: this.generateHash(faq.Question + faq.Answer)
      }))
    }
  },
  watch: {
    faqs: {
      handler() {
        this.processFaqs()
      },
      immediate: true
    }
  },
  mounted() {
    this.processFaqs()
  }
})
</script>

<style scoped>
.collapse-icon {
  transition: transform 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transform-origin: center center;
}

.collapse-icon.rotated {
  transform: rotate(180deg);
}

.collapse {
  transition: max-height 0.35s ease-in-out, opacity 0.35s ease-in-out, padding 0.35s ease-in-out;
  overflow: hidden;
  max-height: 0;
  opacity: 0;
  padding-top: 0;
  padding-bottom: 0;
}

.collapse.show {
  max-height: 500px;
  opacity: 1;
  padding-top: 8px;
  padding-bottom: 8px;
}

.card-body {
  transition: all 0.35s ease-in-out;
}

.question-item button {
  background: none;
  border: none;
  padding: 0;
  text-align: left;
}

.question-item button:hover {
  background: none;
}

.question-item button:focus {
  outline: none;
  box-shadow: none;
}
</style>
