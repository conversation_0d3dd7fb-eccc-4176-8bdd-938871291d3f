import {BaseModel} from "./baseModel";

export class PurchaseModel extends BaseModel{
    constructor(args) {
        super();
        const defaults = {
            Currency: { required: false, default: "COP", type: "string" },
            Value: { required: true, default: 0, type: "number" },
            PaymentType: { required: true, default: "", type: "string" },
            TransactionId: { required: true, default: "", type: "string" },
            Layer: { required: false, default: "", type: "string" },
            Items: { required: false, default: "[]", type: "string" }
        };
        this.map(defaults, args)
    }

}