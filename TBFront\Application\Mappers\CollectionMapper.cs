﻿using TBFront.Models.MKTCollection.Response;

namespace TBFront.Application.Mappers
{
    public class CollectionMapper
    {
        public static Section Collection(MKTCollectionResponse collections, string code = "")
        {
            var section = new Section();

            if (collections?.Data?.Sections == null || collections.Data.Sections.Count() == 0)
            {
                return section;
            }

            if (code is not null)
            {
                section = collections.Data.Sections.FirstOrDefault(section =>  section.Title.Contains(code, StringComparison.CurrentCultureIgnoreCase));
            }

            if (section is null || !section.Active)
            {
                section = collections.Data.Sections.FirstOrDefault(section => section.Title.Contains("home", StringComparison.CurrentCultureIgnoreCase));
            }

            return section;
        }
    }
}
