@import './app', './base/_variables', './_variables.scss', 'base/_core', 'components/footer', 'components/header';

label {
    margin-bottom: 0.5rem;
}

.form-group {
    margin-bottom: 1rem;
}

.btn-confirmation {
    background: #2196f3;
    border: none;
    border-radius: 4px;
    color: #fff !important;
    font-family: Roboto-Medium;
    transition: all 0.2s linear;

    &:hover {
        background: #2196f3;
        border: none;
        border-radius: 4px;
        color: #fff !important;
        font-family: Roboto-Medium;
        transition: all 0.2s linear;
    }

    &:focus {
        background: #2196f3;
        border: none;
        border-radius: 4px;
        color: #fff !important;
        font-family: Roboto-Medium;
        transition: all 0.2s linear;
    }

    &:disabled {
        background: #2196f3;
        border: none;
        border-radius: 4px;
        color: #fff !important;
        font-family: Roboto-Medium;
        transition: all 0.2s linear;
    }
}



