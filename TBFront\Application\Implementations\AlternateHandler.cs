﻿using Microsoft.Extensions.Options;
using TBFront.Application.Mappers;
using TBFront.Helpers;
using TBFront.Interfaces;
using TBFront.Models.Meta.Alternate;
using TBFront.Models.Places.Request;
using TBFront.Options;

namespace TBFront.Application.Implementations
{
    public class AlternateHandler : IAlternateHandler
    {
        private readonly CultureOptions _cultureOptions;
        private readonly IPlaceHandler _placeHandler;
        private readonly LocalizerHelper _localizer;
        public AlternateHandler(IOptions<CultureOptions> cultureOptions, IPlaceHandler placeHandler, LocalizerHelper localizer)
        {
            _cultureOptions = cultureOptions.Value;
            _placeHandler = placeHandler;
            _localizer = localizer;
        }

        public async Task<AlternateMain> QueryAsync(PlaceRequest request, CancellationToken ct)
        {
            var alternates = new AlternateMain();
            var key = string.Empty;
            var pathToTranslate = string.Empty;
            switch (request.Type)
            {
                case Types.PageType.Generic:
                    alternates = AlternateMapper.Map(_cultureOptions.Cultures, request);
                    key = "alternate_path_generic";
                    break;
                case Types.PageType.FlightList:
                    alternates = await ListQueryAsync(request, ct);
                    key = "alternate_path_flights";
                    break;
                case Types.PageType.Home:
                    alternates = AlternateMapper.Map(_cultureOptions.Cultures, request);
                    key = request.Route == "/" ? "alternate_path_home_default" : "alternate_path_generic";
                    pathToTranslate = request.Route != "/" ? $"alternate_{request.Path}" : string.Empty;
                    break;
                case Types.PageType.Airlines:
                    alternates = AlternateMapper.Map(_cultureOptions.Cultures, request);
                    key = $"alternate_path_a_{request.Route}";
                    break;
                case Types.PageType.DestinationsTB:
                    alternates = AlternateMapper.Map(_cultureOptions.Cultures, request);
                    key = $"alternate_path_d_{request.Route}" ;
                    break;
                case Types.PageType.DestinationFlightList:
                    alternates = await ListQueryAsync(request, ct);
                    key = "alternate_path_flights_destinations" ;
                    break;

                default:
                    throw new ArgumentException($"Unsupported PageType: {request.Type}");
            }

            

            alternates = CreateUrl(alternates, key, pathToTranslate);
            return alternates;
        }
        private async Task<AlternateMain> ListQueryAsync(PlaceRequest request, CancellationToken ct)
        {
            var tasks = _cultureOptions.Cultures.Select(culture =>
            {
                var placeRequest = new PlaceRequest
                {
                    InternalCulture = culture.InternalCultureCode,
                    Culture = request.Culture,
                    StartingAirport = request.StartingAirport,
                    ReturninAirport = request.ReturninAirport
                };
                return _placeHandler.QueryAsync(placeRequest, ct);
            });

            var results = await Task.WhenAll(tasks);
            var alternates = AlternateMapper.Map([.. results],request.Type);

            alternates.Culture = request.Culture;

            return alternates;
        }

        private AlternateMain CreateUrl(AlternateMain alternates, string key, string path = "")
        {
            foreach (var culture in _cultureOptions.Cultures)
            {

                var alternate = alternates.Alternates.FirstOrDefault(x => x.InternalCulture == culture.InternalCultureCode);
                if (alternate != null)
                {
                    var url = alternate.Url;
                    if (!string.IsNullOrEmpty(path))
                    {
                        url = _localizer.GetTranslation(path, culture.CultureCode);
                    }

                    if (alternate is not null)
                    {
                        alternate.Name = culture.Name;
                        alternate.Culture = culture.CultureCode;
                        alternate.UrlPath = _localizer.GetTranslation(key, culture?.CultureCode, culture.CultureCode, url);

                        if (alternate.IsUrlList)
                        {
                            alternate.UrlPath = FlightParamsHelper.ReplaceDoubleHyphen(alternate.UrlPath.Replace("starting", alternate.StartingUri).Replace("returing", alternate.ReturingUri));
                        }
                    }
                    if (string.Equals(alternates.Culture, culture.CultureCode, StringComparison.OrdinalIgnoreCase))
                    {
                        alternate.IsDefault = true;
                    }
                }

            }
            var alternateDefault = alternates.Alternates.FirstOrDefault(x => x.IsDefault);
            if (alternateDefault != null)
            {
                alternates.Culture = alternateDefault.Culture;
                alternates.Name = alternateDefault.Name;
                alternates.Url = alternateDefault.UrlPath;
            }
            return alternates;
        }

    }
}