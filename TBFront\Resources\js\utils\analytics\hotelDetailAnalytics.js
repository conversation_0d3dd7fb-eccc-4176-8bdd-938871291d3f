import { getDate } from "../helpers/dates";
import { getPaxes, isoWeekNumber, setDatalayer, UTILS, countNight  } from "./main"

export default class HotelDetailAnalytics {


    constructor() {
        this.settings = window.__pt.settings.site;
        this.request = window.__pt.data || {};
        this.hotel = window.__pt.hotel ? window.__pt.hotel.content : {};
        this.places = window.__pt.places_info;

    }

    productDetail(quote) {

        let starting = this.places.starting;
        let returning = this.places.returning;
        let paxes = getPaxes(this.request.paxes)
        let checkIn = getDate(this.request.checkIn);
        let checkOut = getDate(this.request.checkOut);
        let flightItinerary = quote.flightItinerary || {};
        let rate = quote.roomRateMinimun || {};
        let countNightValue = countNight(checkIn, checkOut);

        let event = {
            EECProductDetailAdults: paxes.adults,
            EECProductDetailAvailable: !!rate.totalAmount,
            EECProductDetailCheckin: this.request.checkIn,
            EECProductDetailCheckout: this.request.checkOut,
            EECProductDetailCity: this.hotel.address.city,
            EECProductDetailCountry: this.hotel.address.country,
            EECProductDetailMealplan: window.i18n.mealplans[rate.mealPlanCode],
            EECProductDetailFlightType: "Roundtrip",
            EECProductDetailDestinationAirline: flightItinerary.returning ? flightItinerary.returning.airlineCode : "",
            EECProductDetailDestinationAirport: flightItinerary.returning ? flightItinerary.returning.departure.airport : "",
            EECProductDetailOriginAirline: flightItinerary.starting ? flightItinerary.starting.airlineCode : "",
            EECProductDetailOriginAirport: flightItinerary.starting ? flightItinerary.starting.departure.airport : "",
            EECProductDetailOriginAirlineText: flightItinerary.starting ? flightItinerary.starting.airline : "",
            EECProductDetailDestinationAirlineText: flightItinerary.starting ? flightItinerary.returning.airline : "",
            EECProductDetailNightlyRate: rate.totalAmountPerPax || 0,
            EECProductDetailTotalRate: rate.totalAmount || 0,
            EECProductDetailId: this.hotel.hotelId,
            EECProductDetailKids: paxes.children,
            EECProductDetailName: this.hotel.title,
            EECProductDetailNights: countNightValue,
            EECProductDetailPlaceId: returning.placeId,
            EECProductDetailQuantity: this.request.paxes.length,
            EECProductDetailRating: this.hotel.surveyAverage.average,
            EECProductDetailStars: this.hotel.start,
            EECProductDetailState: this.hotel.address.state,
            EECProductDetailWeek: isoWeekNumber(),
            EECProductDetailWeekDay: checkIn.getDay(),
            EECProductDetailOriginPlaceText: starting.description,
            EECProductDetailDestinationPlaceText: returning.description,
            EECProductDetailProductUrl: `${this.settings.siteUrl}${this.settings.pathHotelDetail}${this.hotel.uri}${this.settings.uriHotelDetailPath}`,
            EECProductDetailImageUrl: this.hotel.gallery.length ? this.hotel.gallery[0].url : '',
            event: UTILS.events.event_product_detail
        }

        setDatalayer(event);
    }

    allInclusive(rate) {

        let event = {
            event: UTILS.events.gtmEvent,
            eventName: UTILS.events.gtmEvent,
            eventCategory: UTILS.categories.modal,
            eventAction: "All Inclusive Display",
            eventLabel: `${this.hotel.title} | ${this.hotel.hotelId} | ${rate.roomId}`
        }

        setDatalayer(event);
    }

    viewFlightDetail(flightItinerary, itinerary) {

        let event = {
            event: UTILS.events.gtmEvent,
            eventName: UTILS.events.gtmEvent,
            eventCategory: UTILS.categories.view_flight_detail,
            eventAction: UTILS.actions.hotel_detail,
            eventLabel: `${flightItinerary.starting.departure.airport}-${flightItinerary.returning.departure.airport} | ${itinerary.airlineCode}`
        }

        setDatalayer(event);
    }

    addToCart(rate, flightItinerary) {
        try {
            let starting = this.places.starting;
            let returning = this.places.returning;
            let paxes = getPaxes(this.request.paxes)


            let event = {
                event: UTILS.events.add_to_card,
                data: {
                    ecommerce: {
                        currencyCode: this.settings.currency,
                        add: {
                            actionField: {
                                list: UTILS.categories.hotel_detail_page
                            },
                            products: [
                                {
                                    id: this.hotel.hotelId,
                                    name: this.hotel.title,
                                    category: `${this.hotel.address.country}/${this.hotel.address.state}/${this.hotel.address.city}/`,
                                    variant: "Available",
                                    brand: this.hotel.title,
                                    quantity: this.request.paxes.length,
                                    dimension5: UTILS.misc.pricetravel, //<---
                                    dimension6: rate.mealPlanCode,
                                    dimension7: isoWeekNumber(),
                                    dimension8: paxes.adults,
                                    dimension9: paxes.children,
                                    dimension10: flightItinerary.starting.departure.airport,
                                    dimension11: starting.code,
                                    dimension12: flightItinerary.starting.airlineCode,
                                    dimension13: flightItinerary.returning.departure.airport,
                                    dimension14: returning.code,
                                    dimension15: flightItinerary.returning.airlineCode,
                                    metric2: this.hotel.start,
                                    metric3: this.hotel.surveyAverage.average
                                }
                            ]
                        }
                    }
                }
            }

            setDatalayer(event);
        } catch (e) {

        }
        
    }


    requote(display = false) {

        let event = {
            event: UTILS.events.gtmEvent,
            eventName: UTILS.events.gtmEvent,
            eventAction: !display ? UTILS.actions.search_again_display : UTILS.actions.search_again_click,
            eventCategory: UTILS.categories.modal,
            eventLabel: UTILS.actions.hotel_detail
        }

        setDatalayer(event);
    }




} 

export const HotelDetailAnalytic = new HotelDetailAnalytics();