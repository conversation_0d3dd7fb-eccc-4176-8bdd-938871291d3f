<template>
    <div class="container-fluid shadow m-tooltip-original bg-white rounded-3 position-absolute pb-3">
        <div class="row">
            <div class="col-12 px-2 text-end"> <span class="icon icon-close cursor-pointer font-18 " @click="toggle()"></span></div>
        </div>

        <div class="row" v-for="breakdown in breakdowns">
            
            <div class="col-7 px-2 small fw-bold" v-if="breakdown.type == 2">
                {{ __("breakdown.taxes_and_fees") }}
            </div>
            <div class="col-7 px-2 small fw-bold" v-if="breakdown.type == 3">
                {{ __("breakdown.service_charge_txt") }}
            </div>

            <div class="col-5 px-2 text-end small"  v-if="breakdown.type == 2 || breakdown.type == 3">{{ $filters.currency(breakdown.amount) }}</div>
        </div>

    </div>

</template>

<script>
export default {
        data() {
            return {

            }
        },
        props: ["breakdowns", "toggle"],

        mounted() {
        },
        methods: {

        }
}
</script>

<style>
@media (min-width: 1280px) {
    .m-tooltip-original {
        width: 80%;
    }
}

</style>