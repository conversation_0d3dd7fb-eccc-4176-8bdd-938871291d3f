﻿using TBFront.Helpers;
using TBFront.Models.Configuration;
using TBFront.Models.ContentDeliveryNetwork.Seo;
using TBFront.Models.Meta.Alternate;
using TBFront.Models.Meta.Metatags;
using TBFront.Models.Meta.Schema;
using TBFront.Models.Request;
using TBFront.Options;
using TBFront.Types;

namespace TBFront.Mappers
{
    public class MetaMapper
    {
        public static MetaTag Home(SettingsOptions _options, ViewHelper helper, UserSelection? userSelection, LocalizerHelper _localizer, string path = "", string route = "", string pahtO = "")
        {
            var pathLower = path.ToLower();

            var content = MetaMapper.GetPathHome(pathLower, helper);
            var lang = _options.Language;
            var root = _options.SiteUrl;
            var appName = _options.AppName;
            var canonical = root;
            var title = content.Title;
            var description = helper.Localizer("meta_description");
            var author = helper.Localizer("author");
            var img = helper.Localizer("img_home");
            var meta = new MetaTag();

            if (string.Equals(pathLower, "home"))
            {
                var fullpath = "";
                if (route != "/")
                {
                    fullpath = $"/{helper.Localizer("alternate_flights")}";
                }

                meta.SiteTitle = "";//helper.Localizer("author");
                canonical = $"{root}/{userSelection!.Culture.CultureCode}{fullpath}";
                content.Separator = "";
            }
            else
            {
                //meta.SiteTitle = helper.Localizer(ProductType.Airlines.Contains(pathLower) ? "meta_description_airlines" : "meta_description_offers", content.Title);
                meta.SiteTitle = helper.Localizer("author");
                description = helper.Localizer($"meta_description_{pathLower}");
                var key = $"alternate_path_a_{pahtO}";

                canonical = helper.ReplaceDoubleHyphen(_localizer.GetTranslation(key, userSelection!.Culture.CultureCode, $"{root}/{userSelection.Culture.CultureCode}", pathLower));
                content.Separator = " | ";
            }

            meta.Title = title;
            meta.Separator = content.Separator;
            meta.Description = description;
            meta.Author = author;
            meta.Img = img;
            meta.Url = canonical;
            meta.AppName = appName;
            meta.Domain = root;
            meta.Lang = lang;
            meta.Root = root;
            meta.SchemaMain = MetaMapper.GetMainJson(meta, _options, helper);
            meta.MobileApp = MetaMapper.GetAppMobileJson(meta, _options, helper);

            return meta;
        }


        public static MetaTag List(SettingsOptions _options, ViewHelper helper, FlightRequest request, UserSelection? userSelection, AlternateMain alternate, PageType pageType, SeoResponse seoResponse)
        {
            var content = MetaMapper.GetList(request, helper, pageType);
            var lang = _options.Language;
            var root = _options.SiteUrl;
            var appName = _options.AppName;
            var title = content.Title;
            var description = "";

            if (pageType == PageType.DestinationFlightList)
            {
                var destination = request.ReturningAirportPlace.CityName;
                description = helper.Localizer("meta_description_destination", destination, destination);
            }
            else
            {
                description = helper.Localizer("meta_description");
            }



            var author = helper.Localizer("author");
            var img = $"https://www.tiquetesbaratos.com/assets/recent-search-imgs/iatas/{request.ReturningAirportPlace.AirportCode}.jpg";
            var airlinePath = GetPathAirline(request.AddFirst, helper);
            var canonical = $"{root}/{alternate.Url}";


            var meta = new MetaTag
            {
                SiteTitle = helper.Localizer("author"),
                Title = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Title) ? seoResponse.Seo.Meta.Title : title,
                Separator = content.Separator,
                Description = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Description) ? seoResponse.Seo.Meta.Description : description,
                Author = author,
                Img = img,
                Url = canonical.ToLower(),
                AppName = appName,
                Domain = root,
                Lang = lang,
                Root = root
            };
            meta.Question = GetQuestionJson(seoResponse);
            meta.SchemaMain = MetaMapper.GetMainJson(meta, _options, helper);
            meta.MobileApp = MetaMapper.GetAppMobileJson(meta, _options, helper);

            return meta;
        }
        public static MetaTag Generic(SettingsOptions _options, UserSelection userSelection, ViewHelper helper,SeoResponse seoResponse, string path = "")
        {
            var pathLower = path.ToLower();

            var content = MetaMapper.GetPathHome("", helper);
            var lang = _options.Language;
            var root = _options.SiteUrl;
            var appName = _options.AppName;
            var canonical = root;
            var title = content.Title;
            var description = helper.Localizer("meta_description");
            var author = helper.Localizer("author");
            var img = helper.Localizer("img_home");
            var meta = new MetaTag();

            meta.SiteTitle = helper.Localizer("author");
            canonical = $"{root}/{userSelection!.Culture.CultureCode}/{pathLower}";

            meta.Title = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Title) ? seoResponse.Seo.Meta.Title : title;
            meta.Separator = content.Separator;
            meta.Description = !string.IsNullOrEmpty(seoResponse.Seo.Meta.Description) ? seoResponse.Seo.Meta.Description : description;
            meta.Author = author;
            meta.Img = img;
            meta.Url = canonical;
            meta.AppName = appName;
            meta.Domain = root;
            meta.Lang = lang;
            meta.Root = root;
            meta.SchemaMain = MetaMapper.GetMainJson(meta, _options, helper);
            meta.MobileApp = MetaMapper.GetAppMobileJson(meta, _options, helper);
            meta.Question = GetQuestionJson(seoResponse);
            return meta;
        }

        public static MetaTag Destination(SettingsOptions _options, ViewHelper helper, FlightRequest rquest, UserSelection userSelection, string path, LocalizerHelper _localizer)
        {
            var content = MetaMapper.GetDestination(rquest, helper);
            var lang = _options.Language;
            var root = _options.SiteUrl;
            var appName = _options.AppName;
            var canonical = root;
            var title = content.Title;
            var description = helper.Localizer("meta_description");
            var author = helper.Localizer("author");
            var img = $"https://www.tiquetesbaratos.com/assets/recent-search-imgs/iatas/{rquest.StartingAirportPlace.AirportCode}.jpg";
            var key = $"alternate_path_d_{path}";

            canonical = helper.ReplaceDoubleHyphen(_localizer.GetTranslation(key, userSelection.Culture.CultureCode, $"{root}/{userSelection.Culture.CultureCode}", $"{rquest.StartingAirportPlace.AirportCode}/{rquest.ReturningAirportPlace.AirportCode}/" + FlightParamsHelper.GenerateSlug($"{rquest.StartingAirportPlace.CityName}{rquest.ReturningAirportPlace.CityName}")));



            var meta = new MetaTag
            {
                SiteTitle = author,
                Title = title,
                Separator = content.Separator,
                Description = description,
                Author = author,
                Img = img,
                Url = canonical.ToLower(),
                AppName = appName,
                Domain = root,
                Lang = lang,
                Root = root
            };

            meta.SchemaMain = MetaMapper.GetMainJson(meta, _options, helper);
            meta.MobileApp = MetaMapper.GetAppMobileJson(meta, _options, helper);

            return meta;
        }


        public static MetaTag Promotions(SettingsOptions _options, ViewHelper helper, string type, UserSelection userSelection, string destination = "", string originCode = "")

        {

            var lang = _options.Language;
            var root = _options.SiteUrl;
            var appName = _options.AppName;
            var canonical = $"{root}/{userSelection.Culture.CultureCode}/vuelos/promociones-tiquetes-aereos-{type}";
            var title = helper.Localizer($"meta_title_promotion_{type}");
            var description = helper.Localizer($"meta_description_{type}");
            var author = helper.Localizer("author");
            var img = helper.Localizer("img_home");




            if (!string.IsNullOrEmpty(destination) && !string.IsNullOrEmpty(originCode))
            {
                string normalizedDestination = FlightParamsHelper.GenerateSlug(destination);
                canonical = $"{root}/{userSelection.Culture.CultureCode}/vuelos/promociones-tiquetes-aereos-a-{normalizedDestination}/{originCode}";
                title = helper.Localizer($"meta_title_promotion_destino_a") + " " + destination;
            }

            var meta = new MetaTag
            {
                SiteTitle = helper.Localizer("author"),
                Title = title,
                Separator = " | ",
                Description = description,
                Author = author,
                Img = img,
                Url = canonical,
                AppName = appName,
                Domain = root,
                Lang = lang,
                Root = root
            };

            meta.SchemaMain = MetaMapper.GetMainJson(meta, _options, helper);
            meta.MobileApp = MetaMapper.GetAppMobileJson(meta, _options, helper);

            return meta;
        }



        private static ServiceTag GetPathHome(string path, ViewHelper _viewHelper)
        {
            var service = new ServiceTag();
            path = path.Replace("/", "");
            service.Title = _viewHelper.Localizer("meta_title_home");

            if (!string.IsNullOrEmpty(path))
            {
                service.Separator = _viewHelper.Localizer("separator");
                service.Title = _viewHelper.Localizer($"meta_title_{path}");
            }

            return service;
        }

        private static ServiceTag GetDestination(FlightRequest rquest, ViewHelper _viewHelper)
        {
            var service = new ServiceTag();
            service.Separator = _viewHelper.Localizer("separator");
            service.Title = _viewHelper.Localizer($"meta_destination_title", rquest.StartingAirportPlace.CityName, rquest.ReturningAirportPlace.CityName);

            return service;
        }

        private static ServiceTag GetList(FlightRequest rquest, ViewHelper _viewHelper, PageType pageType)
        {
            var service = new ServiceTag();
            service.Separator = _viewHelper.Localizer("separator");
            service.Title = pageType == PageType.DestinationFlightList
                ? _viewHelper.Localizer($"meta_list_title_destinations", rquest.ReturningAirportPlace.CityName)
                : _viewHelper.Localizer($"meta_list_title", rquest.ReturningAirportPlace.CityName, rquest.StartingAirportPlace.CityName);

            return service;
        }

        private static SchemaMain GetMainJson(MetaTag meta, SettingsOptions _options, ViewHelper viewHelper)
        {
            var sameAs = viewHelper.Localizer("same_as");
            var contact = GetContactPoint(viewHelper);
            var schema = new SchemaMain
            {
                Context = SchemaType.Context,
                Type = SchemaType.Organization,
                Name = meta.Author,
                AlternateName = $"{meta.AppName}.com",
                Logo = $"{_options.CloudCdn}{viewHelper.Localizer("img")}",
                Url = meta.Root,
                SameAs = sameAs.Split(",").ToList(),
            };
            schema.ContactPoint.Add(contact);

            return schema;
        }

        private static ContactPoint GetContactPoint(ViewHelper viewHelper)
        {
            return new ContactPoint
            {
                ContactType = viewHelper.Localizer("customer_service"),
                Telephone = viewHelper.Localizer("phone_phone_format"),
                Type = SchemaType.ContactPoint
            };
        }

        private static MobileApplication GetAppMobileJson(MetaTag meta, SettingsOptions _options, ViewHelper viewHelper)
        {
            var schema = new MobileApplication
            {
                Context = SchemaType.Context,
                Type = SchemaType.MobileApplication,
                Name = meta.Author,
                OperatingSystema = viewHelper.Localizer("operating_system"),
                FileSize = "32mb",
                ApplicationCategory = SchemaType.ShoppingApplication,
                Description = viewHelper.Localizer("description_app"),
                InteractionCount = 100000
            };

            schema.AggregateRating.Type = SchemaType.AggregateRating;
            schema.AggregateRating.RatingValue = viewHelper.Localizer("rating_value");
            schema.AggregateRating.BestRating = "5";
            schema.AggregateRating.RatingCount = viewHelper.Localizer("ratingCount");
            schema.Offer.Type = SchemaType.Offer;
            schema.Offer.Price = 0;
            schema.Offer.PriceCurrency = _options.Currency;
            schema.Author.Type = SchemaType.Organization;
            schema.Author.Name = meta.Author;

            return schema;
        }


        private static string GetPathAirline(string airline, ViewHelper helper)
        {
            var path = "";
            //Se comenta parte de asignacion de aerolinea hasta tener claro el flujo por parte de negocio
            /*
            if (!string.IsNullOrEmpty(airline)) {
                if (!string.Equals(airline, "home", StringComparison.OrdinalIgnoreCase))
                {
                    path = helper.GenerateSlug(airline) + "/";
                }
            }*/

            return path;
        }
        private static Question GetQuestionJson(SeoResponse seoResponse)
        {
            return new Question
            {
                Context = SchemaType.Context,
                Type = SchemaType.TypeFAQ,
                mainEntity = GetQuestionList(seoResponse)
            };
        }
        private static List<QuestionList> GetQuestionList(SeoResponse seoResponse)
        {
            var questionList = new List<QuestionList>();
            if (seoResponse.Seo.Faqs is not null && seoResponse.Seo.Faqs != null)
            {
                if (seoResponse.Seo.Faqs.Count > 0)
                {
                    foreach (var questionArr in seoResponse.Seo.Faqs)
                    {
                        var question = new QuestionList
                        {
                            Type = SchemaType.TypeQs,
                            Name = questionArr.Question
                        };

                        var answerList = new AnswerList
                        {
                            Type = SchemaType.TypeAns,
                            Text = questionArr.Answer
                        };
                        question.acceptedAnswer = answerList;
                        questionList.Add(question);
                    }
                }

            }
            return questionList;
        }



    }
}
