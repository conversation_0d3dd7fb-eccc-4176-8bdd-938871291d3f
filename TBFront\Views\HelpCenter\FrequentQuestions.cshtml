﻿@using TBFront.Helpers
@using Microsoft.Extensions.Options
@using TBFront.Models.Request;
@using TBFront.Options;
@using TBFront.Models.ContentDeliveryNetwork.FaqContent;
@using TBFront.Types;
@using TBFront.Models.Meta.Metatags
@inject IOptions<SettingsOptions> settingOptions
@inject IOptions<CurrencyOptions> currencyOptions

@inject ViewHelper viewHelper
@inject ViewHelper _
@inject StaticHelper staticHelper
@{
    var resolution = _.GetImageResolution();
    var isMobile = resolution.Device == DeviceType.Mobile;
    var isRobot = _.IsRobot();
    var faqContent = ViewData["FaqContent"] as FaqContentResponse;
    var culture = ViewData["CultureData"] as Culture;
    var faqSelected = faqContent?.Content.Where(f => f.Code == culture.CultureCode).ToList();

    int chunkSize = 10;

    // Convertir la lista en una matriz jagged en la vista
    int totalChunks = (int)Math.Ceiling((double)faqSelected[0].Content.Count() / chunkSize);
    var faqMatrix = new List<List<FrequentlyAskedQuestion>>();

    for (int i = 0; i < totalChunks; i++)
    {
        faqMatrix.Add(faqSelected[0].Content.Skip(i * chunkSize).Take(chunkSize).ToList());
    }
    ViewData["Page"] = "frequent-questions";
    ViewData["IsRobot"] = isRobot;
    ViewData["isMobile"] = isMobile;
    ViewData["Resolution"] = resolution;

}

@await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.Code}/_Header.cshtml", new
    ViewDataDictionary(ViewData) { { "navs", true }, { "login", isRobot } })


<div class="container-fluid h-groups py-5">
    <div class="container">
        <h1 class="text-white mb-0 title-xl">@_.Localizer("frequentQuestionsTitle")</h1>
    </div>
</div>
<frequent-questions></frequent-questions>

@* <div id="accordions-container" class="container mt-5 mb-5">
    @for (int i = 0; i < faqMatrix.Count; i++){
        <div class="accordion mt-4" id="accordionFlushExample">

            @foreach (var item in faqMatrix[i])
            {
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button accordion-button-custom collapsed" type="button" data-bs-toggle="collapse" data-bs-target="@($"#flush-collapse-{i}")" aria-expanded="false" aria-controls="@($"flush-collapse-{i}")">
                            @item.Question
                        </button>
                    </h2>
                    <div id="@($"flush-collapse-{i}")" class="accordion-collapse collapse" data-bs-parent="#accordionFlushExample">
                        <div class="accordion-body">
                            @item.Answer
                        </div>
                    </div>
                </div>
            }
        </div>
    }
</div>

<div class="container">
    <nav class="mt-4 cursor-pointer" aria-label="Page navigation example">
        <ul class="pagination justify-content-end">
            <li class="page-item">
                <a id="prev" class="page-link" href="#" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            </li>

            @for (int a = 0; a < faqMatrix.Count; a++){
                <li class="page-item"><a class="page-link page-link-number" data-page="@($"{a + 1}")">@(a + 1)</a></li>
            }

            <li class="page-item">
                <a id="next" class="page-link" href="#" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            </li>
        </ul>
    </nav>
</div> *@

<div>
    @await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.Code}/_Footer.cshtml", new
            ViewDataDictionary(ViewData) { { "newsletter", false } })
</div>

@section Meta {
    @await Html.PartialAsync("_MetaMain", new ViewDataDictionary(ViewData) { { "Meta", ViewData["MetaTag"] } })
}

@section Preload {
    <link rel="preconnect" href="@settingOptions.Value.CloudCdn">
    <link rel="preload"
        href="@staticHelper.GetVersion($"/{settingOptions.Value.Code}/css/write-us.css", settingOptions.Value.Assets)"
        as="style" />
}

@section Css {
    <link type="text/css" rel="stylesheet"
        href="@staticHelper.GetVersion($"/{settingOptions.Value.SiteName}/css/write-us.css", settingOptions.Value.Assets)">
}

@section ScriptsPriority {
}

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const accordionPerPage = 1;
            const dataContainer = document.getElementById('accordions-container');
            const prevButton = document.getElementById('prev');
            const nextButton = document.getElementById('next');
            const pageLinks = document.querySelectorAll('.page-link-number');


            const accordions =
                Array.from(dataContainer.getElementsByClassName('accordion'));

            const totalPages = Math.ceil(accordions.length / accordionPerPage);
            let currentPage = 1;

            function displayPage(page) {
                const startIndex = (page - 1) * accordionPerPage; // 1
                const endIndex = startIndex + accordionPerPage; // 2
                accordions.forEach((accordion, index) => {
                    if (index >= startIndex && index < endIndex) {
                        accordion.style.display = 'block';
                    } else {
                        accordion.style.display = 'none';
                    }
                });
                window.scrollTo(0, 0);
            }

            function updatePagination() {
                prevButton.disabled = currentPage === 1;
                nextButton.disabled = currentPage === totalPages;
                pageLinks.forEach((link) => {
                    const page = parseInt(link.getAttribute('data-page'));
                    link.classList.toggle('active', page === currentPage);
                });
            }

            prevButton.addEventListener('click', () => {
                if (currentPage > 1) {
                    currentPage--;
                    displayPage(currentPage);
                    updatePagination();
                }
            });

            nextButton.addEventListener('click', () => {
                if (currentPage < totalPages) {
                    currentPage++;
                    displayPage(currentPage);
                    updatePagination();
                }
            });

            pageLinks.forEach((link) => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    const page = parseInt(link.getAttribute('data-page'));
                    if (page !== currentPage) {
                        currentPage = page;
                        displayPage(currentPage);
                        updatePagination();
                    }
                });
            });
            displayPage(currentPage);
            updatePagination();
        });
    </script>

    <script>
        window.__pt.cultureData = @Json.Serialize((ViewData["CultureData"]));
        window.__pt.exchange = @Json.Serialize((ViewData["Exchange"]));
        window.__pt.alternates = @Json.Serialize((ViewData["Alternates"]));
        window.__pt.currency = @Json.Serialize((ViewData["CurrencyData"]));
        window.__pt.currencies = @Json.Serialize((currencyOptions.Value.Currencies));
    </script>

    <script src="@staticHelper.GetVersion("/js/vue/app.js", settingOptions.Value.Assets)"></script>
}