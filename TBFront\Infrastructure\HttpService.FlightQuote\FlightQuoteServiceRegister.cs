﻿using TBFront.Infrastructure.HttpService.FlightQuote.Dtos;
using TBFront.Interfaces;

namespace TBFront.Infrastructure.HttpService.FlightQuote
{
    public static class FlightQuoteServiceRegister
    {
        public static void AddFlightQuoteDependencies(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddHttpClient<FlightQuoteService>("");

            services.AddSingleton(s => configuration.GetSection("HttpFlightQuoteServiceConfigurations").Get<FlightQuoteConfiguration>());

            services.AddSingleton<IFlightService, FlightQuoteService>();

        }
    }
}
