﻿using System.Threading.Channels;
using TBFront.Models.Flight.Summary;
using TBFront.Models.Request;
using TBFront.Models.Response;

namespace TBFront.Application.Mappers
{
    public class SummaryMapper
    {
        public static SummaryRequest Request(string id, string email,int channel)
        {
            return new SummaryRequest
            {
                CustomerEmail = email,
                IdReservation = id,
                Channel = channel
            };
        }

        public static CheckoutFlightInfo FlightInfo(SummaryResponse summaryResponse, CheckoutBookingRequest request)
        {
            var flightInfo = new CheckoutFlightInfo();


            var familyFares = new List<CheckoutFamilyFare>();
            var hasFamilyFareChanged = false;
            if (summaryResponse != null && summaryResponse.Items != null)
            {
                foreach (var (flight, index) in summaryResponse.Items.Flights.Select((value, i) => (value, i)))
                {
                    var firstFlight = index == 0;
                    //BLOCK Check family fare change
                    // Determinar la tarifa familiar para el vuelo de ida y vuelta en caso de ser dos oneways
                    var detailFamilyFare = firstFlight ? request.FlightDetail.DetailStarting : request.FlightDetail.DetailReturning;
                    var detailFamilyFareOptional = firstFlight ? request.Quote.FlightItinerary.Starting : request.Quote.FlightItinerary.Returning;


                    // Verificar si hay un cambio en la tarifa familiar en los segmentos de salida
                    var changedSegmentDeparture = GetSegmentFlight(flight.FlightDepartureSegments, detailFamilyFare, detailFamilyFareOptional);

                    if (changedSegmentDeparture != null)
                    {
                        hasFamilyFareChanged = true;
                        familyFares.Add(GetCheckoutFamilyFare(changedSegmentDeparture, detailFamilyFare, flight.FlightDepartureSegments, index, detailFamilyFareOptional));
                    }

                    // Verificar si hay un cambio en la tarifa familiar en los segmentos de regreso en caso de ser un roundtrip
                    if (flight.FlightReturningSegments.Any())
                    {
                        var changedSegmentReturning = GetSegmentFlight(flight.FlightReturningSegments, request.FlightDetail.DetailReturning, request.Quote.FlightItinerary.Returning);

                        if (changedSegmentReturning != null)
                        {
                            hasFamilyFareChanged = true;
                            familyFares.Add(GetCheckoutFamilyFare(changedSegmentReturning, request.FlightDetail.DetailReturning, flight.FlightReturningSegments, 1, request.Quote.FlightItinerary.Returning));
                        }
                    }
                }

                flightInfo.ChangeReservation = hasFamilyFareChanged;
                flightInfo.ChangeFamilyFare = hasFamilyFareChanged;
                flightInfo.FamilyFares = familyFares;

            }

            return flightInfo;
        }

        private static CheckoutFamilyFare GetCheckoutFamilyFare(SummaryFlightSegment segment, RouteFlightDetail detail, List<SummaryFlightSegment> segments, int flightSegment, FlightItemQuote familyQuote)
        {
            var firstSegment = segments.First();
            var lastSegment = segments.Last();

            return new CheckoutFamilyFare
            {
                FamilyFare = segment.FamilyFareName,
                FamilyFareOld = !string.IsNullOrEmpty(detail.DetailFamilyFare.FamilyFareName) ? detail.DetailFamilyFare.FamilyFareName: familyQuote.FareGroup,
                Origin = $"{firstSegment.DepartureAirportCode}",
                Destination = $"{lastSegment.ArrivalAirportCode}",
                FlightSegment = flightSegment
            };
        }

        private static SummaryFlightSegment GetSegmentFlight(List<SummaryFlightSegment> segments, RouteFlightDetail flightDetail, FlightItemQuote familyQuote)
        {
            if (string.IsNullOrEmpty(flightDetail.DetailFamilyFare.FamilyFareName))
            {
                return segments.FirstOrDefault(x => !string.Equals(x.FamilyFareName, familyQuote.FareGroup, StringComparison.OrdinalIgnoreCase));
            }

            return segments.FirstOrDefault(x => !string.Equals(x.FamilyFareName, flightDetail.DetailFamilyFare.FamilyFareName, StringComparison.OrdinalIgnoreCase));
        }



    }
}
