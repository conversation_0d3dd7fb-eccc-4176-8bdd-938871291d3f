﻿using TBFront.Options;

namespace TBFront.Models.Configuration
{
    public class AppConfiguration
    {

        public string? SiteName { get; set; }
        public string? SiteUrl { get; set; }
        public string? Domain { get; set; }
        public string? Currency { get; set; }
        public string? CurrencySymbol { get; set; }
        public string? CurrencyCodeName { get; set; }
        public int DecimalDigits { get; set; }
        public string? Language { get; set; }
        public int Property { get; set; }
        public int Organization { get; set; }
        public string? Culture { get; set; }
        public bool Mobile { get; set; }
        public string? GoogleMapsApiKey { get; set; }
        public bool ShowConsoleLogs { get; set; }
        public string? RecaptchaKey { get; set; }
        public string? Channel { get; set; }
        public int ChannelLegacy { get; set; }
        public string? CheckoutUrl { get; set; }
        public string? ListUrl { get; set; }
        public string? EmailCheckouttUrl { get; set; }
        public string? QuoteCheckoutStepOne { get; set; }
        public int RangeTotalAmount { get; set; }
        public string? RevalidateTimeOut { get; set; }
        public string? VoucherCheckout { get; set; }
        public string? RevalidateCheckout { get; set; }
        public string? BookingCheckout { get; set; }
        public string? Sufix { get; set; }
        public string? CloudCdn { get; set; }
        public int TimeToShowRequoteModal { get; set; }
        public string? Code { get; set; }
        public int Retry { get; set; }
        public int RetryTimeOut { get; set; }
        public string Assets { get; set; }
        public int Version { get; set; }
        public SectionResolution ImageResolution { get; set; }
        public LoginConfiguration Login { get; set; }
        public ChannelOptions? ChannelConfig { get; set; }
        public BookerConfig Booker { get; set; }
        public ApiFlights ApiFlights { get; set; }
        public ApiB2C ApiB2C { get; set; }
        public QuoteConfiguration QuoteConfiguration { get; set; }        
        public AirlineConfiguration AirlineConfiguration { get; set; }
        
        public FlightValidationStops FlightValidationStops { get; set; }
        public FormsConfiguration FormsConfiguration { get; set; }
        public List<AvailableTime> ContactMeAvailableTime { get; set; }
        public string AlgoliaFlightIndex { get; set; }
        public string FingerPrintkey { get; set; }
        public string FingerPrintURL { get; set; }
        public bool FingerPrintkeyEnableSdk { get; set; }
        public List<int> BreakdownList { get; set; }
        public int RetryCheckout { get; set; }
        public List<string> RetryCheckoutAllowed { get; set; } = new List<string>();
        public InternalAPI InternalAPI { get; set; }
        public List<string> EmailDomains { get; set; }
        public AppConfiguration()
        {
            Login = new LoginConfiguration();
            ImageResolution = new SectionResolution();
            Booker = new BookerConfig();
        }
    }
}
