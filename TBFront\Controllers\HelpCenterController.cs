﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using TBFront.Helpers;
using TBFront.Mappers;
using TBFront.Options;
using TBFront.Interfaces;
using TBFront.Models.Forms.Request;
using TBFront.Models.Configuration;
using TBFront.Models.ContentDeliveryNetwork.FaqContent;
using TBFront.Models.Places.Request;
using TBFront.Types;

namespace TBFront.Controllers
{
    public class HelpCenterController : Controller
    {
        public const string SessionKeyName = "session_id";
        private readonly ILogger<HelpCenterController> _logger;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IUserHandler _userHandler;
        private readonly SettingsOptions _options;
        private readonly ViewHelper _helper;
        private readonly IOnlinePaymentHandler _onlinePaymentHandler;
        private readonly ICommonHandler _commonHandler;
        private readonly IContentDeliveryNetworkHandler _contentDeliveryNetworkHandler;
        private readonly IAlternateHandler _alternateHandler;

        public HelpCenterController(ILogger<HelpCenterController> logger,
            IHttpContextAccessor httpContextAccessor,
            IUserHandler userHandler,
            IOptions<SettingsOptions> options,
            ICommonHandler commonHandler,
            IContentDeliveryNetworkHandler contentDeliveryNetworkHandler,
            ViewHelper helper,
            IOnlinePaymentHandler onlinePaymentHandler,
            IAlternateHandler alternateHandler
        )
        {
            _logger = logger;
            _options = options.Value;
            _helper = helper;
            _httpContextAccessor = httpContextAccessor;
            _userHandler = userHandler;
            _onlinePaymentHandler = onlinePaymentHandler;
            _commonHandler = commonHandler;
            _contentDeliveryNetworkHandler = contentDeliveryNetworkHandler;
            _alternateHandler = alternateHandler;

        }


        [Route("/vuelos/consultar-reservas")]
        [HttpGet("{culture}/vuelos/{name:regex(consultar-reservas)}")]
        [HttpGet("{culture}/flights/{name:regex(consultar-reservas)}")]
        [HttpGet]
        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> CheckYourReservation(string name)
        {
            var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(30000));
            var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
            var meta = MetaMapper.Generic(_options, userSelection, _helper, new Models.ContentDeliveryNetwork.Seo.SeoResponse(), "vuelos/consultar-reservas");
            var user = await _userHandler.QueryAsync(new Models.User.UserRequest() { }, cts.Token);
            var route = HomeMapper.Path(Request.Path.Value ?? "");

            meta.Title = $"{_helper.Localizer("meta-check-reservation")} - {meta.Title}";

            var alternates = await _alternateHandler.QueryAsync(new PlaceRequest { Culture = userSelection.Culture.CultureCode, Id = 0, Path = $"vuelos/{name}", Route = route, Type = PageType.Generic }, cts.Token);

            ViewData["Alternates"] = alternates;
            ViewData["MetaTag"] = meta;
            ViewData["User"] = user;
            ViewData["CultureData"] = userSelection.Culture;
            ViewData["CurrencyData"] = userSelection.Currency;
            ViewData["Exchange"] = userSelection.ExchangeClient;
            ViewData["UserLocation"] = userSelection.Context.Location;


            return View();
        }


        [Route("/vuelos/check-in")]
        [HttpGet("{culture}/vuelos/{name:regex(check-in)}")]
        [HttpGet("{culture}/flights/{name:regex(check-in)}")]

        [HttpGet]
        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> CheckIn(string name)
        {
            var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(30000));
            var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
            var meta = MetaMapper.Generic(_options, userSelection, _helper,new Models.ContentDeliveryNetwork.Seo.SeoResponse() ,"vuelos/check-in");

            var route = HomeMapper.Path(Request.Path.Value ?? "");
            var path = HomeMapper.GetPath(route);

            meta.Title = $"{_helper.Localizer("meta-checkin")} - {meta.Title}";

            var alternates = await _alternateHandler.QueryAsync(new PlaceRequest { Culture = userSelection.Culture.CultureCode, Id = 0, Path = path, Route = route, Type = PageType.Generic }, cts.Token);
            var user = await _userHandler.QueryAsync(new Models.User.UserRequest() { }, cts.Token);

            ViewData["Alternates"] = alternates;
            ViewData["MetaTag"] = meta;
            ViewData["User"] = user;
            ViewData["CultureData"] = userSelection.Culture;
            ViewData["CurrencyData"] = userSelection.Currency;
            ViewData["Exchange"] = userSelection.ExchangeClient;
            ViewData["UserLocation"] = userSelection.Context.Location;

            return View();
        }

     

        [Route("/vuelos/pago-en-linea")]
        [HttpGet("{culture}/vuelos/{name:regex(pago-en-linea)}")]
        [HttpGet("{culture}/flights/{name:regex(pago-en-linea)}")]
        [HttpGet]
        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> OnlinePayment(string name)
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(60000));
            var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
            var meta = MetaMapper.Generic(_options, userSelection, _helper, new Models.ContentDeliveryNetwork.Seo.SeoResponse(), "vuelos/pago-en-linea");

            var route = HomeMapper.Path(Request.Path.Value ?? "");
            

            meta.Title = $"{_helper.Localizer("meta-payment-online")} - {meta.Title}";
            var alternates = await _alternateHandler.QueryAsync(new PlaceRequest { Culture = userSelection.Culture.CultureCode, Id = 0, Path = $"vuelos/{name}", Route = route, Type = PageType.Generic }, cts.Token);
            var user = await _userHandler.QueryAsync(new Models.User.UserRequest() { }, cts.Token);

            ViewData["Alternates"] = alternates;
            ViewData["MetaTag"] = meta;
            ViewData["User"] = user;
            ViewData["CultureData"] = userSelection.Culture;
            ViewData["CurrencyData"] = userSelection.Currency;
            ViewData["Exchange"] = userSelection.ExchangeClient;
            ViewData["UserLocation"] = userSelection.Context.Location;


            return View();

        }

        [Route("/vuelos/pago-en-linea")]
        
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> OnlinePaymentData(OnlinePaymentRequest request, string culture)
        {

            if (request.NoValid())
            {
                return BadRequest();
            }


            try
            {

                var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(30000));
                var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
                var meta = MetaMapper.Generic(_options, userSelection, _helper, new Models.ContentDeliveryNetwork.Seo.SeoResponse(), "vuelos/pago-en-linea");

                var route = HomeMapper.Path(Request.Path.Value ?? "");
                var path = HomeMapper.GetPath(route);


                meta.Title = $"{_helper.Localizer("meta-payment-online")} - {meta.Title}";

                var response = await _onlinePaymentHandler.QueryAsync(new OnlinePaymentRequest { Code = request.Code, Email = request.Email, RecaptchaToken = "" }, cts.Token);
                var alternates = await _alternateHandler.QueryAsync(new PlaceRequest { Culture = userSelection.Culture.CultureCode, Id = 0, Path = path, Route = route, Type = PageType.Generic }, cts.Token);

                ViewData["Alternates"] = alternates;
                ViewData["MetaTag"] = meta;
                ViewData["Response"] = response;
                ViewData["CultureData"] = userSelection.Culture;
                ViewData["CurrencyData"] = userSelection.Currency;
                ViewData["Exchange"] = userSelection.ExchangeClient;
                ViewData["UserLocation"] = userSelection.Context.Location;


                return Ok(response);
            }
            catch (Exception ex)
            {

                _logger.LogError($"OnlinePaymentData - {ex.Message} - Request: {System.Text.Json.JsonSerializer.Serialize(request)}");

                return ErrorPage(ex.Message, 500);
            }

        }

        [Route("/vuelos/grupos")]
        [HttpGet("{culture}/vuelos/{name:regex(grupos)}")]
        [HttpGet("{culture}/flights/{name:regex(grupos)}")]
        [HttpGet]
        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> Groups(string name)
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(60000));
            var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
            var meta = MetaMapper.Generic(_options, userSelection, _helper, new Models.ContentDeliveryNetwork.Seo.SeoResponse(), "vuelos/grupos");
            var route = HomeMapper.Path(Request.Path.Value ?? "");
            
            var alternates = await _alternateHandler.QueryAsync(new PlaceRequest { Culture = userSelection.Culture.CultureCode, Id = 0, Path = $"vuelos/{name}", Route = route, Type = PageType.Generic }, cts.Token);
            meta.Title = $"{_helper.Localizer("meta-groups")} - {meta.Title}";
            var user = await _userHandler.QueryAsync(new Models.User.UserRequest() { }, cts.Token);

            ViewData["Alternates"] = alternates;
            ViewData["MetaTag"] = meta;
            ViewData["User"] = user;
            ViewData["CultureData"] = userSelection.Culture;
            ViewData["CurrencyData"] = userSelection.Currency;
            ViewData["Exchange"] = userSelection.ExchangeClient;
            ViewData["UserLocation"] = userSelection.Context.Location;

            return View();
        }


        [Route("/vuelos/escribenos")]
        [HttpGet("{culture}/vuelos/{name:regex(escribenos)}")]
        [HttpGet("{culture}/flights/{name:regex(escribenos)}")]
        [HttpGet]
        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> WriteUs(string name)
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(60000));
            var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
            var meta = MetaMapper.Generic(_options, userSelection, _helper, new Models.ContentDeliveryNetwork.Seo.SeoResponse(), "vuelos/escribenos");
            var route = HomeMapper.Path(Request.Path.Value ?? "");

            meta.Title = $"{_helper.Localizer("meta-write-us")} - {meta.Title}";

            var alternates = await _alternateHandler.QueryAsync(new PlaceRequest { Culture = userSelection.Culture.CultureCode, Id = 0, Path = $"vuelos/{name}", Route = route, Type = PageType.Generic }, cts.Token);
            meta.Title = $"{_helper.Localizer("meta-write-us")} - {meta.Title}";
            var user = await _userHandler.QueryAsync(new Models.User.UserRequest() { }, cts.Token);

            ViewData["Alternates"] = alternates;
            ViewData["MetaTag"] = meta;
            ViewData["User"] = user;
            ViewData["CultureData"] = userSelection.Culture;
            ViewData["CurrencyData"] = userSelection.Currency;
            ViewData["Exchange"] = userSelection.ExchangeClient;
            ViewData["UserLocation"] = userSelection.Context.Location;

            return View();
        }


        [Route("/vuelos/preguntas-frecuentes")]
        [HttpGet("{culture}/vuelos/{name:regex(preguntas-frecuentes)}")]
        [HttpGet("{culture}/flights/{name:regex(preguntas-frecuentes)}")]
        [HttpGet]
        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> FrequentQuestions(string name)
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(60000));
            var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
            var meta = MetaMapper.Generic(_options, userSelection, _helper, new Models.ContentDeliveryNetwork.Seo.SeoResponse(), "vuelos/preguntas-frecuentes");

            var route = HomeMapper.Path(Request.Path.Value ?? "");

            var content = await _contentDeliveryNetworkHandler.QueryAsync(new FaqContentRequest() { UserCountry = userSelection.Context.Location.Country }, cts.Token);
            var alternates = await _alternateHandler.QueryAsync(new PlaceRequest { Culture = userSelection.Culture.CultureCode, Id = 0, Path = $"vuelos/{name}", Route = route, Type = PageType.Generic }, cts.Token);
            meta.Title = $"{_helper.Localizer("meta-write-us")} - {meta.Title}";
            var user = await _userHandler.QueryAsync(new Models.User.UserRequest() { }, cts.Token);

            ViewData["Alternates"] = alternates;
            ViewData["MetaTag"] = meta;
            ViewData["CultureData"] = userSelection.Culture;
            ViewData["CurrencyData"] = userSelection.Currency;
            ViewData["Exchange"] = userSelection.ExchangeClient;
            ViewData["UserLocation"] = userSelection.Context.Location;

            ViewData["FaqContent"] = content;
            ViewData["User"] = user;
            return View();
        }

        [Route("/index.php/component/preguntas/")]
        [HttpGet]
        public IActionResult RedirectToFrequentQuestions()
        {
            return RedirectPermanent($"/vuelos/preguntas-frecuentes");
        }

        [Route("/index.php/component/contactar/")]
        [HttpGet]
        public IActionResult RedirectToWriteUs()
        {
            return RedirectPermanent($"/vuelos/escribenos");
        }

        [Route("/index.php/component/tiquetes-aereos-para-grupos/")]
        [HttpGet]
        public IActionResult RedirectToGroups()
        {
            return RedirectPermanent($"/vuelos/grupos");
        }

        [Route("/index.php/component/pagoenlinea2/")]
        [HttpGet]
        public IActionResult RedirectToOnlinePayment()
        {
            return RedirectPermanent($"/vuelos/pago-en-linea");
        }

        [Route("/index.php/component/checkin/")]
        [HttpGet]
        public IActionResult RedirectToCheckIn()
        {
            return RedirectPermanent($"/vuelos/check-in");
        }


        [Route("/index.php/component/consultereserva/")]
        [HttpGet]
        public IActionResult RedirectToCheckYourReservation()
        {
            return RedirectPermanent($"/vuelos/consultar-reservas");
        }


        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public ActionResult ErrorPage(string errorMgs, int statusCode)
        {
            ViewData["ErrorMgs"] = errorMgs;
            _httpContextAccessor.HttpContext.Response.StatusCode = statusCode;
            return View("~/Views/Error/Index.cshtml");
        }
    }
}
