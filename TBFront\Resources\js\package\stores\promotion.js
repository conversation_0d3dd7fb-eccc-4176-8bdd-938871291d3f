import { defineStore } from "pinia";
import { datesCalendarMapper } from '../mappers/datesCalendarMapper'

export const usePromotionStore = defineStore('promotion', {
	state: () => ({
        promotions: [],
        originSelected: "BOG",
        objectSelected: {
            "code": "BOG",
            "name": "Bogot<PERSON>",
        },
        destinationObjectSelected: {
            "code": "",
            "name": "",
        },
        origin: "",
        destination: "",
        startingFromDepartureDateTime: "",
        startingFromDateTime: "",
        returningFromDateTime: "",
        dateTimeStartingSelected: "",
        dateTimeReturningSelected: "",
        adults: 1,
        tripMode: 0,
        calendar: null,
        calendarRoundTrip: null,
        tooltipId: "",
        fromLanding: "",
        airline: "",
        changeCalendarSelected: "",
        loading: true,
        loadingRoundtrip: true,
	}),
	getters: {
        getPromotions: (state) => state.promotions,
        getOrigins: (state) => state.promotions.map(({ name, code,isNational }) => ({ name, code ,isNational})),
        getOriginSelected: (state) => state.originSelected,
        getSelected: (state) => state.objectSelected,
        getOrigin: (state) => state.origin,
        getDestination: (state) => state.destination,
        getStartDepartureDate: (state) => state.startingFromDepartureDateTime,
        getStartDate: (state) => state.startingFromDateTime,
        getReturningDate: (state) => state.returningFromDateTime,
        getTripMode: (state) => state.tripMode,
        getCalendar: (state) => state.calendar,
        getCalendarRoundTrip: (state) => state.calendarRoundTrip,
        getDestinationObjectSelected: (state) => state.destinationObjectSelected,
        getIdTooltip: (state) => state.tooltipId,
        getSmallerTaxeOneWay(state) {
            const stateRemaped = [...state.calendar.dates_departure];
            const taxes = stateRemaped.map((element) => element.rates);
            return Math.min(...taxes[0].map(item => item.price));
        },
        getSmallerTaxeRoundTrip(state) {
            const stateRemaped = state.calendarRoundTrip.dates_departure.map((element) => element.rates);
            const allTaxes = stateRemaped.flat(1);
            return Math.min(...allTaxes.map(item => item.price));
        },
        getNewQueryString() {
            const queryString = {
                evento: "busquedaHome",
                transporte: this.tripMode === 0 ? "unavia" : "redondo",
                origenName: `${this.objectSelected.name}`,
                origen: this.origin,
                paisvueloida: null,
                destinoName: `${this.destinationObjectSelected.name}`,
                destino: this.destination,
                paisvueloregreso: null,
                from: this.dateTimeStartingSelected,
                to: this.tripMode == 1 ? this.dateTimeReturningSelected : this.dateTimeStartingSelected,
                adultos: this.adults,
                ninos: 1,
                evento: "busquedaHome",
                radiogroup: "on",
                addfirst: this.airline || "home",
                option: "com_sabre",
                view: "lowfare",
                landing: "home"
            };
            return queryString;
        },
        getDateStartingSelected: (state) => state.dateTimeStartingSelected,
        getDateReturningSelected: (state) => state.dateTimeReturningSelected,
        getFromLanding: (state) => state.fromLanding,
        getAirline: (state) => state.airline,
        getCalendarChange: (state) => state.changeCalendarSelected,
        getIsLoading: (state) => state.tripMode == 0 ? state.loading : state.loadingRoundtrip,
	},
	actions: {
		setOriginSelected(val) {
            this.originSelected = val;
        },
        setPromotionsList(val) {
            const filteredOptionsSelect = val.filter((obj) => obj.national.length > 0 || obj.international.length > 0);
            this.promotions = filteredOptionsSelect;
        },
        setSelected(val) {
            this.objectSelected = val;
        },
        setOrigin(val) {
            this.origin = val; 
        },
        setDestination(val) {
            this.destination = val;
        },
        setStartingDepartureDate(val) {
            this.startingFromDepartureDateTime = val;
        },
        setStartingDate(val) {
            this.startingFromDateTime = val;
        },
        setReturningDate(val) {
            this.returningFromDateTime = val;
        },
        setTripMode(val) {
            this.tripMode = val;
        },
        setDestinationObjectSelected(val) {
            this.destinationObjectSelected = val;
        },
        setPromotionsResponse(response, type, params) {

            if (type === 2) {
                this.calendar = response;
            } else {
                this.calendar = datesCalendarMapper.map(response, type, params);
            }

        },
        setPromotionResponseRoundtrip(response, type, params) {

            if (type === 2) {
                this.calendarRoundTrip = response;
            } else {
                this.calendarRoundTrip = datesCalendarMapper.map(response, type, params);

            }

        },
        setIdTootltip(val) {
            this.tooltipId = val;
        },
        setDateStaringSelected(val) {
            this.dateTimeStartingSelected = val;
        },
        setDateReturningSelected(val) {
            this.dateTimeReturningSelected = val;
        },
        setFromLanding(val) {
            this.fromLanding = val;
        },
        setAirline(val) {
            this.airline = val;
        },
        noShowTooltip(index, position, display) {
            this.calendar.dates_departure[index].rates[position].show = display;
        },
        noShowTooltipRoundTrip(index, position, display) {
            this.calendarRoundTrip.dates_departure[index].rates[position].show = display;
        },
        hideTooltipOneWay() {
            this.calendar.dates_departure.forEach((item) => {
                item.rates.forEach((element) => element.show = false);
            })
        },
        hideTooltipRoundtrip() {
            this.calendarRoundTrip.dates_departure.forEach((item) => {
                item.rates.forEach((element) => element.show = false);
            });
        },
        setIsLoading(val, type) {
            if (type == 0) {
                this.loading = val;
            } else {
                this.loadingRoundtrip = val;
            }
        }
	}
});