<template>
    <h1 class="text-strong">{{__('payOnline.title')}}</h1>
    <p>{{__('payOnline.subtitle')}}</p>
    <div class="row">
        <Form @submit="handleFormSubmit" ref="payonline" tag="form" id="payonline">
            <div class="mt-2">
                <Field :validateOnInput="true" v-model="form.code" :name="`code`" rules="required"
                       :ref="`codigo`" v-slot="{ field, errors, errorMessage }">

                    <label class="px-1">{{__('payOnline.code')}}</label>
                    <input type="tel" id="codigo" name="code"
                           class="form-control" :class="{ 'is-invalid': errors && errors.length }"
                           v-bind="field" :placeholder="__('payOnline.codePlaceHolder')" />
                    <span v-if="errors[0]" class="invalid-feedback">{{ errors[0] }}</span>
                </Field>
            </div>
            <div class="mt-2">
                <Field :validateOnInput="true" v-model="form.email" :name="`email`" rules="required"
                       :ref="`email`" v-slot="{ field, errors, errorMessage }">

                    <label class="px-1">{{__('payOnline.email')}}</label>
                    <input type="email" id="email" name="email"
                           class="form-control" :class="{ 'is-invalid': errors && errors.length }"
                           v-bind="field" :placeholder="__('payOnline.emailPlaceholder')" />
                    <span v-if="errors[0]" class="invalid-feedback">{{ errors[0] }}</span>
                </Field>
            </div>

            <input type="hidden" id="recaptchaToken" name="recaptchaToken" v-model="recaptchaToken">

            <div class="row mt-3">
                <div class="cap col-12 col-lg-8">
                    <div class="g-recaptcha-grupos">
                        <div class="g-recaptcha" :class="{'border-error': recaptchaToken === '' && submitCount > 0}" id="recaptcha-check-reservation" :data-sitekey="config.recaptchaKey"></div>
                    </div>
                    <p class="invalid-feedback text-left d-block mb-0" v-if="recaptchaToken === '' && submitCount > 0">
                        {{__('errors.recaptcha_error')}}
                    </p>
                </div>
                <div class="pt-4 col-12 col-lg-4">
                    <button id="consultarItinerario" class="btn btn-blue py-10 px-5 ml-auto" :disabled="recaptchaToken !== '' && submitCount > 0">{{__('payOnline.consult')}}</button>
                </div>
            </div>
        </Form>
    </div>
    <ModalMessages :msg="msg" :title='__("payOnline.without_results")' :btn='__("payOnline.try_again")' />
</template>

<script>
    import { __ } from '../../../utils/helpers/translate';
    import { Form, Field, useForm } from 'vee-validate';
    import { Generic } from "../../../utils/analytics/generics";
    import { getWgetIdCaptcha, submitFormRc } from "../../../utils/helpers/recaptcha";
    import { useLoaderPageStore } from '../../stores/loader-page';

    export default {
        data() {
            return {
                submitCount: 0,
                recaptchaToken: "",
                config: window.__pt.settings.site || {},
                form: {
                    code: "",
                    email: ""
                },
                msg: ""
            }
        },
        setup() {
            const useLoaderPage = useLoaderPageStore();
            const { showLoaderPage, hideLoaderPage } = useLoaderPage;
            return { showLoaderPage, hideLoaderPage }
        },
        props: {
        },
        mounted() {

        },
        methods: {
            async handleFormSubmit() {
                this.submitCount += 1
                this.recaptchaToken = this.validateCaptcha()
                if (this.recaptchaToken !== '' && this.submitCount > 0) {
                    this.showLoaderPage();
                    let _token = "";

                    let formAnti = document.getElementById('AntiForgeryToken');
                    if (formAnti) {
                        let csrfTokenInputAnti = formAnti.querySelector('input[name="_token"]');
                        if (csrfTokenInputAnti && csrfTokenInputAnti.value) {
                            _token = csrfTokenInputAnti.value;
                        }
                    }

                    setTimeout(() => {
                        const form = document.getElementById('payonline');
                        const dataForm = new URLSearchParams();
                        dataForm.append("code", form.codigo.value);
                        dataForm.append("email", form.email.value);
                        dataForm.append("_token", _token);
                        dataForm.append("recaptchaToken", form.recaptchaToken.value);
                        fetch("/vuelos/pago-en-linea", {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/x-www-form-urlencoded"
                            },
                            body: dataForm.toString() // Serializar los datos
                        }).then(response => {
                            if (!response.ok) {
                                throw new Error(`Error HTTP: ${response.status}`);
                            }
                            return response.json();
                        }).then(data => {
                            let client = data.client || { id: data.request.code };
                            Generic.paymentOnline(client.id, data.status, client.type);
                            if (data.status != "not-found" && data.urlRedirect != "") {
                                window.location.href = data.urlRedirect;
                            } else {
                                this.submitCount = 0;
                                this.msg = "";
                                this.hideLoaderPage();
                                this.msg += `<p class="mb-2 font-18 fw-bold">${this.__("payOnline.not_found") }</p>`
                                this.msg += `<p class="body">${this.__("payOnline.description_not_found")} <span class="text-link">${this.__('payOnline.tel') }</span></p>`;
                                const modalElement = document.getElementById('modalMessages');
                                const modal = new bootstrap.Modal(modalElement);
                                modal.show();
                            }
                        }).catch(error => {
                            console.error("Error en la solicitud:", error);
                        });
                    }, 200)
                }
            },
            validateCaptcha() {
                const wgetId = getWgetIdCaptcha("#recaptcha-check-reservation")
                const recaptchaRes = grecaptcha.getResponse(wgetId);

                if (recaptchaRes !== "") {
                    grecaptcha.reset(wgetId)
                }
                return recaptchaRes;
            }
        },
        components: {
            Field,
            Form
        },
    }
</script>

<style>
    .loading-page {
        position: fixed;
        width: 100%;
        height: 100%;
        top: 0;
        background-color: rgba(255, 255, 255, 0.4);
        z-index: 999;
    }

    .loader__logo {
        display: inline-block;
        width: 64px;
        height: 64px;
    }

    .loader__logo::before {
        content: "";
        display: inline-block;
        width: 64px;
        height: 64px;
        background-image: url(/assets-tb/img/tiquetesbaratos/loader-logo.svg);
        background-repeat: no-repeat;
        background-size: 100%;
        animation: rotatepulse 2s linear;
        animation-iteration-count: infinite;
    }

    @keyframes rotatepulse {
        0% {
            transform: rotate(0) scale(1);
            animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
        }

        50% {
            transform: rotate(400deg) scale(0.6);
            animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
        }

        100% {
            transform: rotate(1080deg) scale(1);
        }
    }
</style>