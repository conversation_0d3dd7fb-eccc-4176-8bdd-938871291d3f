export function getPaxesRates(roomPax) {
    let boxLength = roomPax && roomPax.length;
    let paxes = {
        paxes: [],
        adults: 0,
        children: 0,
        paxesFormat: []
    };
    for (let i = 0; roomPax && i < boxLength; i++) {
        const pax = roomPax[i];
        let paxFormat = "";
        paxes.adults += pax.adults;
        paxFormat += `${pax.adults}`;

        if (pax.children && pax.children.length) {
            paxes.children += pax.children.length;
            paxFormat += `|${pax.children.map(it => it).join(',')}`;
        }

        paxes.paxesFormat.push(paxFormat);
       
    }
    paxes.paxes = paxRateFamily(paxes.paxesFormat);
    
    return paxes;
}

export function matchFamilyRule(params) {
    let paxesRooms = params.paxesFormat;
    let matchPaxes = []
    let adultsTotal = 0;
    let childrenTotal = 0;
    let validRequote = false;

    for (const paxesRoom of paxesRooms) {
        let paxSplit = paxesRoom.split('|');
        let adults = paxSplit[0] ? Number(paxSplit[0]) : 0;
        let kidsSlit = paxSplit[1] ? paxSplit[1].split(',') : [];
        let kids = kidsSlit.length
        
        adultsTotal = adultsTotal + adults;
        childrenTotal = childrenTotal + kids;

        if ((adults + kids >= 4)) {
            let rulesPax = assingFamily({ adults, kids, kidsSlit });
            matchPaxes = [].concat(matchPaxes, rulesPax);
            validRequote = true;

        } else {
            matchPaxes.push(paxesRoom)
        } 

    }
    
    if (matchPaxes.includes(null)) {
        return null;
    }

    return {
        paxes: paxRateFamily(matchPaxes),
        adults: adultsTotal,
        children: childrenTotal,
        paxesFormat: matchPaxes,
        validRequote
    };

}

export function assingFamily(params) {
    const { adults, kids, kidsSlit } = params;


    let rulesPaxes = {
        "2x2": [`1|${kidsSlit[0]}`, `1|${kidsSlit[1]}`],
        "3x2": [`2|${kidsSlit[0]}`, `1|${kidsSlit[1]}`],
        "3x3": [`2|${kidsSlit[0]},${kidsSlit[1]}`, `1|${kidsSlit[2]}`],
        "2x4": [`1|${kidsSlit[0]},${kidsSlit[1]}`, `1|${kidsSlit[2]},${kidsSlit[3]}`],
        "3x4": [`2|${kidsSlit[0]},${kidsSlit[1]}`, `1|${kidsSlit[2]},${kidsSlit[3]}`],
        "4x4": [`2|${kidsSlit[0]},${kidsSlit[1]}`, `2|${kidsSlit[2]},${kidsSlit[3]}`],
        "5x0": [`3`, `2`],
        "5x1": [`3`, `2|${kidsSlit[0]}`],
        "5x2": [`3|${kidsSlit[0]}`, `2|${kidsSlit[1]}`],
        "4x2": [`2|${kidsSlit[0]}`, `2|${kidsSlit[1]}`],
        "6x0": [`3`, `3`],
        "6x1": [`2`, `2`, `2|${kidsSlit[0]}`],
        "7x1": [`3`, `2`, `2|${kidsSlit[0]}`],
        "7x2": [`3`, `2|${kidsSlit[0]}`, `2|${kidsSlit[1]}`],
        "8x2": [`3`, `3|${kidsSlit[0]}`, `2|${kidsSlit[1]}`],
        "8x0": [`4`, `4`]
    }
    return rulesPaxes[`${adults}x${kids}`] ? rulesPaxes[`${adults}x${kids}`] : null;
}

export function paxRateFamily(params) {
    if (!params) return [];
    let newPax = [];
    for (const param of params) {
        let paxSplit = param.split('|');
        let adults = paxSplit[0] ? Number(paxSplit[0]) : 0;
        let kidsSlit = paxSplit[1] ? paxSplit[1].split(',') : [];
        let kids = kidsSlit.map(kid => Number(kid) )
        newPax.push({ adults, children: kids })

    }

    return newPax;

}